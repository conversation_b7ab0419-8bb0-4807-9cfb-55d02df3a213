import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { FileType } from "@prisma/client";

export class CreateFileDto {
  @ApiProperty({
    description: "The kind of the file",
    example: "CONTRACT",
    enum: FileType,
  })
  type: FileType;

  @ApiProperty({
    description: "The required information ID",
    example: 1,
  })
  required_information_id?: number;

  @ApiProperty({
    description: "The contract ID",
    example: 1,
  })
  contract_id?: number;

  @ApiProperty({
    description: "The certificate ID",
    example: 1,
  })
  certificate_id?: number;

  @ApiProperty({
    description: "The license ID",
    example: 1,
  })
  license_id?: number;

  @ApiProperty({
    description: "The termination ID",
    example: 1,
  })
  termination_id?: number;

  @ApiProperty({
    description: "The general information ID",
    example: 1,
  })
  general_information_id?: number;

  @ApiPropertyOptional({
    description: "The marketing material ID",
    example: 1,
  })
  marketing_material_id?: number;

  @ApiPropertyOptional({
    description: "The partner contract ID",
    example: 1,
  })
  partner_contract_id?: number;

  @ApiPropertyOptional({
    description: "The order ID",
    example: 1,
  })
  order_id?: number;
}
