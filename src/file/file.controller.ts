import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
  Req,
  Res,
  UploadedFile,
  UseInterceptors,
} from "@nestjs/common";
import { FileInterceptor } from "@nestjs/platform-express";
import { ApiOperation } from "@nestjs/swagger";
import { Response } from "express";

import { CreateFileDto } from "./dto/create-file.dto";
import { RequestPresignedUrlDto } from "./dto/request-presigned-url.dto";
import { FILE_RELATIONS, FileService } from "./file.service";
import { Roles } from "src/shared/auth/role.decorator";
import { Role } from "src/shared/auth/role.enum";
import { PublicRoute } from "@/shared/auth/public-route.decorator";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK, Role.CUSTOMER)
@Controller("files")
export class FileController {
  constructor(private readonly fileService: FileService) {}

  @PublicRoute()
  @Get("/relation")
  async getFileByRelative(@Query("relation") relation: string, @Query("id") id: string, @Res() res: Response) {
    const { file, buffer } = await this.fileService.getFileByRelative(relation as (typeof FILE_RELATIONS)[number], id);

    res.setHeader("Content-Type", file.extension);
    res.setHeader("Content-Disposition", `attachment; filename="${file.original_name}"`);
    res.send(buffer);
  }

  @PublicRoute()
  @Get(":id")
  async getFile(@Param("id") fileId: string, @Res() res: Response) {
    const { file, buffer } = await this.fileService.getFile(fileId);

    res.setHeader("Content-Type", file.extension);
    res.setHeader("Content-Disposition", `attachment; filename="${file.original_name}"`);
    res.send(buffer);
  }

  @PublicRoute()
  @Post("request-presigned-url")
  @ApiOperation({ summary: "Request presigned url" })
  requestUrl(@Body() requestPresignedUrlDto: RequestPresignedUrlDto) {
    return this.fileService.requestUrl(requestPresignedUrlDto);
  }

  @Post()
  @ApiOperation({ summary: "Save file to storage and database" })
  @UseInterceptors(FileInterceptor("file"))
  create(@Body() { type, ...params }: CreateFileDto, @Req() req: any, @UploadedFile() file: Express.Multer.File) {
    const data = {
      type,
      user: {
        id: req.headers["x-user-id"] as string,
        role: req.headers["x-user-role"] as string,
      },
      ...params,
    };

    return this.fileService.uploadFile(data, file);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete file from storage and database" })
  delete(@Param("id") fileId: string) {
    return this.fileService.deleteFile(fileId);
  }
}
