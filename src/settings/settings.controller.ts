import { Roles } from "@/shared/auth/role.decorator";
import { Role } from "@/shared/auth/role.enum";
import { Body, Controller, Delete, Get, Param, Put } from "@nestjs/common";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { UpsertSettingDto } from "./dto/upsert-setting.dto";
import { SettingsService } from "./settings.service";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK)
@Controller("settings")
@ApiTags("Settings")
export class SettingsController {
  constructor(private readonly settingsService: SettingsService) {}

  @Get()
  @ApiOperation({ summary: "Get all settings" })
  @ApiResponse({
    status: 200,
    description: "Settings retrieved successfully",
    schema: {
      type: "array",
      items: {
        type: "object",
        properties: {
          id: { type: "number" },
          key: { type: "string" },
          value: { type: "string" },
          term_or_condition_file_id: { type: "string", nullable: true },
          created_at: { type: "string", format: "date-time" },
          updated_at: { type: "string", format: "date-time" },
          deleted_at: { type: "string", format: "date-time", nullable: true },
        },
      },
    },
    example: [
      {
        id: 1,
        key: "setting1",
        value: "value1",
      },
    ],
  })
  findAll() {
    return this.settingsService.findAll();
  }

  @Get(":key")
  @ApiOperation({ summary: "Get setting by key" })
  @ApiResponse({
    status: 200,
    description: "Setting retrieved successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        key: { type: "string" },
        value: { type: "string" },
        term_or_condition_file_id: { type: "string", nullable: true },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time", nullable: true },
      },
    },
    example: {
      id: 1,
      key: "setting1",
      value: "value1",
    },
  })
  @ApiResponse({ status: 404, description: "Setting not found" })
  findOne(@Param("key") key: string) {
    return this.settingsService.findOne(key);
  }

  @Put(":key")
  @ApiOperation({ summary: "Upsert setting by key" })
  @ApiResponse({
    status: 200,
    description: "Setting upserted successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        key: { type: "string" },
        value: { type: "string" },
        term_or_condition_file_id: { type: "string", nullable: true },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time", nullable: true },
      },
    },
    example: {
      id: 1,
      key: "setting1",
      value: "value1",
    },
  })
  @ApiResponse({ status: 404, description: "Setting not found" })
  @ApiResponse({ status: 400, description: "Invalid value" })
  upsert(@Param("key") key: string, @Body() dto: UpsertSettingDto) {
    return this.settingsService.upsert(key, dto);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete setting by key" })
  @ApiResponse({
    status: 200,
    description: "Setting deleted successfully",
  })
  @ApiResponse({ status: 404, description: "Setting not found" })
  remove(@Param("key") key: string) {
    return this.settingsService.remove(key);
  }
}
