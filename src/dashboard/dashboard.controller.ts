import { DashboardService } from "@/dashboard/dashboard.service";
import { AccountsAndObligationsOutputDto } from "@/dashboard/dto/accounts-and-obligations-output.dto";
import { AverageRevenueOutputDto } from "@/dashboard/dto/average-revenue-output.dto";
import { AverageRevenueDto } from "@/dashboard/dto/average-revenue.dto";
import { OpenBalanceOutputDto } from "@/dashboard/dto/open-balance-output.dto";
import { OpenBalanceDto } from "@/dashboard/dto/open-balance.dto";
import { PaymentMethodsSummaryOutputDto } from "@/dashboard/dto/payment-methods-output.dto";
import { RevenueAndContractsOutputDto } from "@/dashboard/dto/revenue-and-contracts-output.dto";
import { RevenueAndContractsDto } from "@/dashboard/dto/revenue-and-contracts.dto";
import { ServicesCustomersOutputDto } from "@/dashboard/dto/services-customers-output.dto";
import { ServicesCustomersDto } from "@/dashboard/dto/services-customers.dto";
import { ServicesRevenueOvertimeOutputDto } from "@/dashboard/dto/services-revenue-overtime-output.dto";
import { TerminationsOutputDto } from "@/dashboard/dto/terminations-output.dto";
import { TerminationsDto } from "@/dashboard/dto/terminations.dto";
import { TopServicesOutputDto } from "@/dashboard/dto/top-services-output.dto";
import { TotalCustomersOutputDto } from "@/dashboard/dto/total-customers-output.dto";
import { TotalCustomersDto } from "@/dashboard/dto/total-customers.dto";
import { Controller, Get, HttpStatus, Query, UsePipes, ValidationPipe } from "@nestjs/common";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { Roles } from "@/shared/auth/role.decorator";
import { Role } from "@/shared/auth/role.enum";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK, Role.MARKETING_MANAGER, Role.BROKER_MANAGER)
@ApiTags("Dashboard")
@Controller("dashboard")
@UsePipes(
  new ValidationPipe({
    whitelist: true,
    transform: true,
    transformOptions: {
      enableImplicitConversion: true,
    },
    errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
  })
)
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}

  @Get("total-customers")
  @ApiOperation({ summary: "Return a customers numbers summary" })
  @ApiResponse({ status: 200, type: TotalCustomersOutputDto })
  async totalCustomers(@Query() options: TotalCustomersDto): Promise<TotalCustomersOutputDto> {
    return await this.dashboardService.totalCustomers(options);
  }

  @Get("open-balance")
  @ApiOperation({ summary: "Return a open balance summary" })
  @ApiResponse({ status: 200, type: OpenBalanceOutputDto })
  async openBalance(@Query() options: OpenBalanceDto): Promise<OpenBalanceOutputDto> {
    return await this.dashboardService.openBalance(options);
  }

  @Get("average-revenue")
  @ApiOperation({ summary: "Return a average revenue summary" })
  @ApiResponse({ status: 200, type: OpenBalanceOutputDto })
  async averageRevenue(@Query() options: AverageRevenueDto): Promise<AverageRevenueOutputDto> {
    return await this.dashboardService.averageRevenue(options);
  }

  @Get("revenue-and-contracts")
  @ApiOperation({ summary: "Return a revenue and contracts summary" })
  @ApiResponse({ status: 200, type: RevenueAndContractsOutputDto })
  async revenueAndContracts(@Query() options: RevenueAndContractsDto): Promise<RevenueAndContractsOutputDto> {
    return await this.dashboardService.revenueAndContracts(options);
  }

  @Get("services-customers")
  @ApiOperation({ summary: "Return a services customers summary" })
  @ApiResponse({ status: 200, type: ServicesCustomersOutputDto })
  async servicesCustomers(@Query() options: ServicesCustomersDto): Promise<ServicesCustomersOutputDto> {
    return await this.dashboardService.servicesCustomers(options);
  }

  @Get("top-services")
  @ApiOperation({ summary: "Return a top services summary" })
  @ApiResponse({ status: 200, type: TopServicesOutputDto })
  async topServices(@Query("year") year: number): Promise<TopServicesOutputDto> {
    return await this.dashboardService.topServices(year);
  }

  @Get("services-revenue-overtime")
  @ApiOperation({ summary: "Return services revenue overtime per year" })
  @ApiResponse({ status: 200, type: ServicesRevenueOvertimeOutputDto })
  async servicesRevenueOvertime(@Query("year") year: number): Promise<ServicesRevenueOvertimeOutputDto> {
    return await this.dashboardService.servicesRevenueOvertime(year);
  }

  @Get("accounts-obligations")
  @ApiOperation({ summary: "Return a summary of accounts created and obligation assessments" })
  @ApiResponse({ status: 200, type: AccountsAndObligationsOutputDto })
  async accountsAndObligations(): Promise<AccountsAndObligationsOutputDto> {
    return await this.dashboardService.accountsAndObligations();
  }

  @Get("payment-methods")
  @ApiOperation({ summary: "Return a payment methods summary for the specified year" })
  @ApiResponse({ status: 200, type: PaymentMethodsSummaryOutputDto })
  async paymentMethods(@Query("year") year: number): Promise<PaymentMethodsSummaryOutputDto> {
    return await this.dashboardService.paymentMethods(year);
  }

  @Get("terminations")
  @ApiOperation({ summary: "Return a termination summary for the specified year and service type" })
  @ApiResponse({ status: 200, type: TerminationsOutputDto })
  async termination(@Query() options: TerminationsDto): Promise<TerminationsOutputDto> {
    return await this.dashboardService.terminationsData(options);
  }
}
