import { ApiProperty } from "@nestjs/swagger";
import { IsDate, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "class-validator";

export class TotalCustomersDto {
  @ApiProperty({ name: "year", required: true, type: Number })
  @IsNumber()
  @Min(1)
  @Max(9999)
  year: number;

  @ApiProperty({ name: "start_date", required: false, type: Date })
  @IsOptional()
  @IsDate()
  start_date?: Date;

  @ApiProperty({ name: "end_date", required: false, type: Date })
  @IsOptional()
  @IsDate()
  end_date?: Date;
}
