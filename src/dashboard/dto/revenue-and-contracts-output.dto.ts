import { ApiProperty } from "@nestjs/swagger";

export class RevenueAndContractsCountryOutputDto {
  @ApiProperty()
  code: string;

  @ApiProperty()
  total_revenue: number;

  @ApiProperty()
  contracts_number: number;

  @ApiProperty()
  country_flag: string;

  @ApiProperty()
  country_name: string;
}

export class RevenueAndContractsOutputDto {
  @ApiProperty()
  countries: RevenueAndContractsCountryOutputDto[];
}
