import { ApiProperty } from "@nestjs/swagger";

export class CreateInviteDto {
  @ApiProperty({
    description: "The product type of the invite",
  })
  product: string;

  @ApiProperty({
    description: "The commission of the invite",
  })
  commission: number;

  @ApiProperty({
    description: "The order number",
  })
  orderNumber: string;

  @ApiProperty({
    description: "The lead source of invite",
  })
  leadSource: string;

  @ApiProperty({
    description: "The customer id",
  })
  customerId: number;

  @ApiProperty({
    description: "The invited customer id",
  })
  invitedCustomerId: number;
}
