import { Test, TestingModule } from "@nestjs/testing";
import { INestApplication } from "@nestjs/common";
import * as request from "supertest";
import { AppModule } from "../app.module";
import { Role } from "../shared/auth/role.enum";
import { HEADER_SYSTEM_API_KEY, HEADER_USER_ID, HEADER_USER_ROLE, HEADER_USER_EMAIL } from "../shared/auth/const";
import { DatabaseService } from "../database/database.service";
import { CriteriaMode, CriteriaType, CriteriaInputType } from "@prisma/client";

jest.setTimeout(30000);

describe("CriteriaController (e2e)", () => {
  let app: INestApplication;
  let databaseService: DatabaseService;

  const validApiKey = "test-api-key";

  beforeEach(() => {
    jest.clearAllMocks();

    jest.spyOn(console, "error").mockImplementation(() => {});
    jest.spyOn(console, "log").mockImplementation(() => {});
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  beforeAll(() => {
    process.env.SYSTEM_API_KEY = validApiKey;
  });

  const authHeaders = {
    [HEADER_SYSTEM_API_KEY]: validApiKey,
    [HEADER_USER_ID]: "1",
    [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
    [HEADER_USER_EMAIL]: "<EMAIL>",
  };

  const mockCriteria = {
    id: 1,
    mode: CriteriaMode.CALCULATOR,
    type: CriteriaType.PACKAGING_SERVICE,
    title: "Test Criteria",
    help_text: "This is a test criteria",
    input_type: CriteriaInputType.SELECT,
    country_id: 1,
    packaging_service_id: 1,
    calculator_type: null,
    required_information_id: null,
    created_at: "2025-04-07T22:58:17.004Z",
    updated_at: "2025-04-07T22:58:17.004Z",
    deleted_at: null,
  };

  const mockCriteriaList = [
    mockCriteria,
    {
      id: 2,
      mode: CriteriaMode.COMMITMENT,
      type: CriteriaType.REPORT_SET,
      title: "Another Criteria",
      help_text: "This is another test criteria",
      input_type: CriteriaInputType.RADIO,
      country_id: 1,
      packaging_service_id: null,
      calculator_type: null,
      required_information_id: null,
      created_at: "2025-04-07T22:58:17.004Z",
      updated_at: "2025-04-07T22:58:17.004Z",
      deleted_at: null,
    },
  ];

  const mockCriteriaWithOptions = {
    ...mockCriteria,
    options: [
      {
        id: 1,
        criteria_id: 1,
        option_value: "Option 1",
        option_to_value: null,
        value: "100",
        created_at: "2025-04-07T22:58:17.004Z",
        updated_at: "2025-04-07T22:58:17.004Z",
        deleted_at: null,
      },
    ],
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(DatabaseService)
      .useValue({
        criteria: {
          create: jest.fn().mockResolvedValue(mockCriteria),
          findMany: jest.fn().mockResolvedValue(mockCriteriaList),
          findUnique: jest.fn().mockResolvedValue(mockCriteria),
          update: jest.fn().mockResolvedValue({ ...mockCriteria, title: "Updated Criteria" }),
        },
        $transaction: jest.fn().mockImplementation((callback) =>
          callback({
            criteria: {
              create: jest.fn().mockResolvedValue(mockCriteria),
              update: jest.fn().mockResolvedValue({ ...mockCriteria, title: "Updated Criteria" }),
            },
            criteriaOption: {
              createMany: jest.fn().mockResolvedValue({}),
              deleteMany: jest.fn().mockResolvedValue({}),
            },
          })
        ),
      })
      .compile();

    app = moduleFixture.createNestApplication();
    databaseService = moduleFixture.get<DatabaseService>(DatabaseService);

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("/criterias (GET)", () => {
    it("should return criteria list when authenticated as SUPER_ADMIN with valid API key", () => {
      return request(app.getHttpServer())
        .get("/criterias")
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(databaseService.criteria.findMany).toHaveBeenCalledWith({
            where: { deleted_at: null },
          });
          expect(response.body).toEqual(mockCriteriaList);
        });
    });

    it("should return criteria list when authenticated as ADMIN with valid API key", () => {
      const adminHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.ADMIN,
      };

      return request(app.getHttpServer()).get("/criterias").set(adminHeaders).expect(200);
    });

    it("should return criteria list when authenticated as CLERK with valid API key", () => {
      const clerkHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.CLERK,
      };

      return request(app.getHttpServer()).get("/criterias").set(clerkHeaders).expect(200);
    });

    it("should reject when authenticated with unauthorized role even with valid API key", () => {
      const unauthorizedHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.CUSTOMER,
      };

      return request(app.getHttpServer()).get("/criterias").set(unauthorizedHeaders).expect(403);
    });

    it("should reject when API key is invalid regardless of role", () => {
      const invalidApiKeyHeaders = {
        ...authHeaders,
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
      };

      return request(app.getHttpServer()).get("/criterias").set(invalidApiKeyHeaders).expect(401);
    });

    it("should reject when API key is missing regardless of role", () => {
      const noApiKeyHeaders = {
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/criterias").set(noApiKeyHeaders).expect(401);
    });

    it("should reject when not authenticated at all", () => {
      return request(app.getHttpServer()).get("/criterias").expect(401);
    });
  });

  describe("/criterias/:id (GET)", () => {
    it("should return a single criteria when authenticated with valid API key", () => {
      return request(app.getHttpServer())
        .get("/criterias/1")
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(databaseService.criteria.findUnique).toHaveBeenCalledWith({
            where: { id: 1, deleted_at: null },
          });
          expect(response.body).toEqual(mockCriteria);
        });
    });

    it("should reject when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        ...authHeaders,
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
      };

      return request(app.getHttpServer()).get("/criterias/1").set(invalidApiKeyHeaders).expect(401);
    });

    it("should reject when not authenticated", () => {
      return request(app.getHttpServer()).get("/criterias/1").expect(401);
    });
  });

  describe("/criterias (POST)", () => {
    const createCriteriaDto = {
      mode: CriteriaMode.CALCULATOR,
      type: CriteriaType.PACKAGING_SERVICE,
      title: "Test Criteria",
      help_text: "This is a test criteria",
      input_type: CriteriaInputType.SELECT,
      country_id: 1,
      packaging_service_id: 1,
      options: [
        {
          criteria_id: 1,
          option_value: "Option 1",
          option_to_value: null,
          value: "100",
        },
      ],
    };

    it("should create a new criteria when authenticated with valid API key", () => {
      return request(app.getHttpServer())
        .post("/criterias")
        .set(authHeaders)
        .send(createCriteriaDto)
        .expect(201)
        .then((response) => {
          expect(databaseService.$transaction).toHaveBeenCalled();
          expect(response.body).toEqual(mockCriteria);
        });
    });

    it("should reject creation when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        ...authHeaders,
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
      };

      return request(app.getHttpServer())
        .post("/criterias")
        .set(invalidApiKeyHeaders)
        .send(createCriteriaDto)
        .expect(401);
    });

    it("should reject creation when not authenticated", () => {
      return request(app.getHttpServer()).post("/criterias").send(createCriteriaDto).expect(401);
    });

    it("should reject creation when authenticated with unauthorized role", () => {
      const unauthorizedHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.CUSTOMER,
      };

      return request(app.getHttpServer())
        .post("/criterias")
        .set(unauthorizedHeaders)
        .send(createCriteriaDto)
        .expect(403);
    });
  });

  describe("/criterias/:id (PUT)", () => {
    const updateCriteriaDto = {
      title: "Updated Criteria",
      options: [
        {
          id: 1,
          criteria_id: 1,
          option_value: "Updated Option",
          option_to_value: null,
          value: "200",
        },
      ],
    };

    it("should update a criteria when authenticated with valid API key", () => {
      return request(app.getHttpServer())
        .put("/criterias/1")
        .set(authHeaders)
        .send(updateCriteriaDto)
        .expect(200)
        .then((response) => {
          expect(databaseService.criteria.findUnique).toHaveBeenCalledWith({
            where: { id: 1 },
          });
          expect(databaseService.$transaction).toHaveBeenCalled();
          expect(response.body).toEqual({ ...mockCriteria, title: "Updated Criteria" });
        });
    });

    it("should reject update when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        ...authHeaders,
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
      };

      return request(app.getHttpServer())
        .put("/criterias/1")
        .set(invalidApiKeyHeaders)
        .send(updateCriteriaDto)
        .expect(401);
    });

    it("should reject update when not authenticated", () => {
      return request(app.getHttpServer()).put("/criterias/1").send(updateCriteriaDto).expect(401);
    });

    it("should reject update when authenticated with unauthorized role", () => {
      const unauthorizedHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.CUSTOMER,
      };

      return request(app.getHttpServer())
        .put("/criterias/1")
        .set(unauthorizedHeaders)
        .send(updateCriteriaDto)
        .expect(403);
    });
  });

  describe("/criterias/:id (DELETE)", () => {
    it("should reject deletion when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        ...authHeaders,
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
      };

      return request(app.getHttpServer()).delete("/criterias/1").set(invalidApiKeyHeaders).expect(401);
    });

    it("should reject deletion when not authenticated", () => {
      return request(app.getHttpServer()).delete("/criterias/1").expect(401);
    });

    it("should reject deletion when authenticated with unauthorized role", () => {
      const unauthorizedHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.CUSTOMER,
      };

      return request(app.getHttpServer()).delete("/criterias/1").set(unauthorizedHeaders).expect(403);
    });
  });

  describe("System role access", () => {
    it("should allow access with SYSTEM role and valid API key", () => {
      const systemHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ROLE]: Role.SYSTEM,
      };

      return request(app.getHttpServer()).get("/criterias").set(systemHeaders).expect(200);
    });

    it("should reject access with SYSTEM role but invalid API key", () => {
      const invalidSystemHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ROLE]: Role.SYSTEM,
      };

      return request(app.getHttpServer()).get("/criterias").set(invalidSystemHeaders).expect(401);
    });

    it("should reject when user role is missing even with valid API key", () => {
      const missingRoleHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ID]: "1",
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/criterias").set(missingRoleHeaders).expect(403);
    });

    it("should reject when user ID is missing even with valid API key and role", () => {
      const missingIdHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/criterias").set(missingIdHeaders).expect(401);
    });
  });

  describe("Error handling", () => {
    it("should return 404 when getting a non-existent criteria", () => {
      jest.spyOn(databaseService.criteria, "findUnique").mockResolvedValueOnce(null);

      return request(app.getHttpServer())
        .get("/criterias/999")
        .set(authHeaders)
        .expect(404)
        .then((response) => {
          expect(response.body.message).toBe("Criteria not found");
        });
    });

    it("should return 400 when deleting with invalid id", () => {
      return request(app.getHttpServer())
        .delete("/criterias/abc")
        .set(authHeaders)
        .expect(400)
        .then((response) => {
          expect(response.body.message).toBe("Invalid criteria ID");
        });
    });

    it("should return 404 when deleting a non-existent criteria", () => {
      jest.spyOn(databaseService.criteria, "findUnique").mockResolvedValueOnce(null);

      return request(app.getHttpServer())
        .delete("/criterias/999")
        .set(authHeaders)
        .expect(404)
        .then((response) => {
          expect(response.body.message).toBe("Criteria not found");
        });
    });

    it("should return 400 when creating a criteria with invalid data", () => {
      jest.spyOn(databaseService, "$transaction").mockImplementationOnce(() => {
        throw new Error("Database error");
      });

      const invalidCriteriaDto = {
        mode: CriteriaMode.CALCULATOR,
        type: CriteriaType.PACKAGING_SERVICE,
        country_id: 1,
      };

      return request(app.getHttpServer())
        .post("/criterias")
        .set(authHeaders)
        .send(invalidCriteriaDto)
        .expect(400)
        .then((response) => {
          expect(response.body.message).toBe("Error creating criteria");
        });
    });
  });
});
