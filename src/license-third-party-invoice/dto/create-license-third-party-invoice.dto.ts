import { ApiProperty } from "@nestjs/swagger";
import { LicenseThirdPartyInvoiceIssuer, LicenseThirdPartyInvoiceStatus } from "@prisma/client";

export class CreateLicenseThirdPartyInvoiceDto {
  @ApiProperty({
    description: "The license ID",
  })
  license_id: number;

  @ApiProperty({
    description: "The title of the invoice",
  })
  title: string;

  @ApiProperty({
    description: "The status of the invoice",
    enum: LicenseThirdPartyInvoiceStatus,
    example: LicenseThirdPartyInvoiceStatus.OPEN,
  })
  status: LicenseThirdPartyInvoiceStatus;

  @ApiProperty({
    description: "The price of the invoice (in cents)",
  })
  price: number;

  @ApiProperty({
    description: "The issuer of the invoice",
    example: LicenseThirdPartyInvoiceIssuer.OTHER_THIRD_PARTY,
    enum: LicenseThirdPartyInvoiceIssuer,
  })
  issuer: LicenseThirdPartyInvoiceIssuer;

  @ApiProperty({
    description: "The issued date of the invoice",
  })
  issued_at: string;

  @ApiProperty({
    description: "The due date of the invoice",
  })
  due_date: string;

  @ApiProperty({
    description: "The file ID of the invoice",
    required: false,
  })
  file_id?: string;
}
