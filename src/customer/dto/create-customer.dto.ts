import { ApiProperty } from "@nestjs/swagger";

export class CreateCustomerDto {
  @ApiProperty({
    description: "The first name of the customer",
  })
  first_name: string;

  @ApiProperty({
    description: "The last name of the customer",
  })
  last_name: string;

  @ApiProperty({
    description: "The company name of the customer",
  })
  company_name?: string;

  @ApiProperty({
    description: "The salutation of the customer",
  })
  salutation: string;

  @ApiProperty({
    description: "The email of the customer",
  })
  email: string;

  @ApiProperty({
    description: "The password of the customer",
  })
  password?: string;

  @ApiProperty({
    description: "The token magic link of the customer",
  })
  token_magic_link?: string;

  @ApiProperty({
    description: "The document id of the customer",
  })
  document_id?: number;

  @ApiProperty({
    description: "The user id of the customer",
  })
  user_id: number;

  @ApiProperty({
    description: "Language of the customer",
  })
  language?: string;

  @ApiProperty({
    description: "Currency of the customer",
    example: "EUR",
  })
  currency?: string;

  @ApiProperty()
  phones: string[];
}
