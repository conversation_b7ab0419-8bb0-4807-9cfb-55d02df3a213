import { ApiProperty } from "@nestjs/swagger";
import { ContractType } from "@prisma/client";

export class UpsertTutorialCustomersDto {
  @ApiProperty({
    required: false,
    description: "Tutorial id number",
  })
  tutorial_id?: number;

  @ApiProperty({
    required: true,
    description: "Customer id number",
  })
  customer_id: number;

  @ApiProperty({
    required: true,
    description: "Is finished boolean",
  })
  is_finished: boolean;

  @ApiProperty({
    required: true,
    description: "Customer service type",
    enum: ["EU_LICENSE", "DIRECT_LICENSE", "ACTION_GUIDE"],
  })
  service_type: ContractType;
}
