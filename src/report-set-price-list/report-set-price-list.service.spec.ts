import { Test, TestingModule } from "@nestjs/testing";
import { ReportSetPriceListService } from "./report-set-price-list.service";
import { DatabaseService } from "../database/database.service";
import { CreateReportSetPriceListDto } from "./dto/report-set-price-list-create.dto";
import { UpdateReportSetPriceListDto } from "./dto/report-set-price-list-update.dto";
import { NotFoundException } from "@nestjs/common";

describe("ReportSetPriceListService", () => {
  let service: ReportSetPriceListService;
  let databaseServiceMock: any;

  beforeEach(async () => {
    databaseServiceMock = {
      reportSetPriceList: {
        create: jest.fn(),
        findMany: jest.fn(),
        findUnique: jest.fn(),
        update: jest.fn(),
        updateMany: jest.fn(),
      },
      $transaction: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [ReportSetPriceListService, { provide: DatabaseService, useValue: databaseServiceMock }],
    }).compile();

    service = module.get<ReportSetPriceListService>(ReportSetPriceListService);
  });

  describe("create", () => {
    it("should create a report set price list successfully", async () => {
      const createDto: CreateReportSetPriceListDto = {
        report_set_id: 1,
        fraction_id: 1,
        fraction_code: "CODE",
        title: "Test Report",
        start_date: new Date(),
        end_date: new Date(),
        type: "FIXED_PRICE",
        fixed_price: 100,
        base_price: 50,
        minimum_fee: 10,
        license_year: 2023,
      };

      const mockResponse = {
        data: {
          report_set_id: 1,
          fraction_id: 1,
          fraction_code: "CODE",
          title: "Test Report",
          start_date: createDto.start_date,
          end_date: createDto.end_date,
          type: createDto.type,
          fixed_price: createDto.fixed_price,
          base_price: createDto.base_price,
          minimum_fee: createDto.minimum_fee,
        },
      };

      databaseServiceMock.reportSetPriceList.create.mockResolvedValue(mockResponse);

      const result = await service.create(createDto);

      expect(result).toEqual(mockResponse);
    });
  });

  describe("remove", () => {
    it("should remove a report set price list successfully", async () => {
      const mockResponse = { message: "Deleted successfully" };

      databaseServiceMock.reportSetPriceList.findUnique.mockResolvedValue({
        id: 1,
        report_set_id: 1,
        fraction_id: 1,
        fraction_code: "CODE",
        title: "Test Report",
        start_date: new Date(),
        end_date: new Date(),
        type: "FIXED_PRICE",
        fixed_price: 100,
        base_price: 50,
        minimum_fee: 10,
      });

      databaseServiceMock.$transaction.mockResolvedValue(mockResponse);

      const result = await service.remove(1);

      expect(result).toEqual(mockResponse);
      expect(databaseServiceMock.$transaction).toHaveBeenCalled();
    });
  });

  describe("findAll", () => {
    it("should return a list of report set price lists", async () => {
      const mockResponse = [
        {
          id: 1,
          report_set_id: 1,
          fraction_id: 1,
          fraction_code: "CODE",
          title: "Test Report",
          start_date: new Date(),
          end_date: new Date(),
          type: "FIXED_PRICE",
          fixed_price: 100,
          base_price: 50,
          minimum_fee: 10,
        },
      ];

      databaseServiceMock.reportSetPriceList.findMany.mockResolvedValue(mockResponse);

      const result = await service.findAll();

      expect(result).toEqual(mockResponse);
      expect(databaseServiceMock.reportSetPriceList.findMany).toHaveBeenCalledWith({
        where: { deleted_at: null },
      });
    });
  });

  describe("findOne", () => {
    it("should return a report set price list by ID", async () => {
      const mockResponse = {
        id: 1,
        report_set_id: 1,
        fraction_id: 1,
        fraction_code: "CODE",
        title: "Test Report",
        start_date: new Date(),
        end_date: new Date(),
        type: "FIXED_PRICE",
        fixed_price: 100,
        base_price: 50,
        minimum_fee: 10,
      };

      databaseServiceMock.reportSetPriceList.findUnique.mockResolvedValue(mockResponse);

      const result = await service.findOne(1);

      expect(result).toEqual(mockResponse);
      expect(databaseServiceMock.reportSetPriceList.findUnique).toHaveBeenCalledWith({
        where: { id: 1, deleted_at: null },
      });
    });

    it("should throw a NotFoundException if the report set price list is not found", async () => {
      databaseServiceMock.reportSetPriceList.findUnique.mockResolvedValue(null);

      try {
        await service.findOne(1);
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        expect(error.message).toBe("Report set price list not found");
      }
    });
  });

  describe("update", () => {
    it("should update a report set price list successfully", async () => {
      const updateDto: UpdateReportSetPriceListDto = {
        title: "Updated Test Report",
      };

      const mockResponse = {
        id: 1,
        report_set_id: 1,
        fraction_id: 1,
        fraction_code: "CODE",
        title: "Updated Test Report",
        start_date: new Date(),
        end_date: new Date(),
        type: "FIXED_PRICE",
        fixed_price: 100,
        base_price: 50,
        minimum_fee: 10,
      };

      databaseServiceMock.reportSetPriceList.findUnique.mockResolvedValue(mockResponse);
      databaseServiceMock.reportSetPriceList.update.mockResolvedValue(mockResponse);

      const result = await service.update(1, updateDto);

      expect(result).toEqual(mockResponse);
      expect(databaseServiceMock.reportSetPriceList.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: updateDto,
      });
    });

    it("should throw a NotFoundException if the report set price list is not found", async () => {
      databaseServiceMock.reportSetPriceList.findUnique.mockResolvedValue(null);

      try {
        await service.update(1, { title: "Updated Test Report" });
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        expect(error.message).toBe("Report set price list not found");
      }
    });
  });
});
