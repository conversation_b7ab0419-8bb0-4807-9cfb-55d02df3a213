import { Body, Controller, Delete, Get, Param, Post, Put } from "@nestjs/common";
import { CreateLicenseVolumeReportDto } from "./dto/create-license-volume-report.dto";
import { UpdateLicenseVolumeReportDto } from "./dto/update-license-volume-report.dto";
import { LicenseVolumeReportService } from "./license-volume-report.service";
import { Roles } from "src/shared/auth/role.decorator";
import { Role } from "src/shared/auth/role.enum";
import { ApiOperation } from "@nestjs/swagger";
import { DeclineLicenseVolumeReportDto } from "./dto/decline-license-volume-report.dto";
import { AuthenticatedUser, User } from "@/shared/auth/user.decorator";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK, Role.CUSTOMER)
@Controller("license-volume-report")
export class LicenseVolumeReportController {
  constructor(private readonly licenseVolumeReportService: LicenseVolumeReportService) {}

  @Post()
  create(@Body() createLicenseVolumeReportDto: CreateLicenseVolumeReportDto) {
    return this.licenseVolumeReportService.create(createLicenseVolumeReportDto);
  }

  @Get()
  findAll() {
    return this.licenseVolumeReportService.findAll();
  }

  @Get(":id")
  findOne(@Param("id") id: string, @User() user: AuthenticatedUser) {
    return this.licenseVolumeReportService.findOne(+id, user);
  }

  @Put(":id")
  update(
    @Param("id") id: string,
    @Body() updateLicenseVolumeReportDto: UpdateLicenseVolumeReportDto,
    @User() user: AuthenticatedUser
  ) {
    return this.licenseVolumeReportService.update(+id, updateLicenseVolumeReportDto, user);
  }

  @Post(":id/decline")
  @ApiOperation({ summary: "Decline a license volume report" })
  decline(@Param("id") id: number, @Body() data: DeclineLicenseVolumeReportDto, @User() user: AuthenticatedUser) {
    return this.licenseVolumeReportService.decline(id, data, user);
  }

  @Delete(":id")
  remove(@Param("id") id: string, @User() user: AuthenticatedUser) {
    return this.licenseVolumeReportService.remove(+id, user);
  }
}
