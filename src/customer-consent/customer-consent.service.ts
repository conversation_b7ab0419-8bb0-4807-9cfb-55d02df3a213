import { DatabaseService } from "@/database/database.service";
import { BadRequestException, ForbiddenException, Injectable, NotFoundException } from "@nestjs/common";
import { CustomerConsent, Prisma } from "@prisma/client";
import { CreateCustomerConsentDto } from "./dto/create-customer-consent";
import { UpdateCustomerConsentDto } from "./dto/update-customer-consent";
import { CustomerIoService } from "@/customer-io/customer-io.service";
import { AuthenticatedUser } from "@/shared/auth/user.decorator";
import { Role } from "@/shared/auth/role.enum";
import { HttpModuleService } from "@/http/http.service";
import { lastValueFrom } from "rxjs";

@Injectable()
export class CustomerConsentService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly customerIoService: CustomerIoService,
    private readonly httpModuleService: HttpModuleService
  ) {}

  async create(dto: CreateCustomerConsentDto): Promise<CustomerConsent> {
    try {
      const date = new Date();
      const customerConsent = await this.databaseService.customerConsent.create({
        data: {
          given: dto.given,
          givenAt: dto.given ? date : null,
          revokedAt: dto.given ? null : date,
          created_at: date,
          updated_at: date,
          customer: {
            connect: { id: +dto.customer_id },
          },
          consent: {
            connect: { id: +dto.consent_id },
          },
        },
        include: {
          customer: true,
          consent: true,
        },
      });

      // TODO Change id matching to enum
      if (customerConsent.consent.name === `Result by mail – we need you consent to send you e-mails*`) {
        this.customerIoService.updateAttributesByCustomerId(customerConsent.customer_id, {
          cio_subscription_preferences: {
            topics: {
              topic_1: !!customerConsent?.given,
              topic_3: !!customerConsent?.given,
              topic_5: !!customerConsent?.given,
              topic_6: !!customerConsent?.given,
            },
          },
        });
      }

      return customerConsent;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === "P2025") {
          throw new NotFoundException("Customer or Consent not found");
        }
        if (error.code === "P2002") {
          throw new BadRequestException("A CustomerConsent with this combination already exists");
        }
      }
      throw new BadRequestException("Unable to create CustomerConsent");
    }
  }

  async createMany(dtos: CreateCustomerConsentDto[]): Promise<CustomerConsent[]> {
    try {
      const date = new Date();

      const existingConsents = await this.databaseService.customerConsent.findMany({
        where: {
          OR: dtos.map((dto) => ({
            customer_id: +dto.customer_id,
            consent_id: +dto.consent_id,
          })),
        },
      });

      const updates: CreateCustomerConsentDto[] = [];
      const creates: CreateCustomerConsentDto[] = [];

      dtos.forEach((dto) => {
        const exists = existingConsents.some(
          (consent) => consent.customer_id === +dto.customer_id && consent.consent_id === +dto.consent_id
        );
        if (exists) {
          updates.push(dto);
        } else {
          creates.push(dto);
        }
      });

      const result = await this.databaseService.$transaction(async (tx) => {
        const updatePromises = updates.map((dto) =>
          tx.customerConsent.update({
            where: {
              customer_id_consent_id: {
                customer_id: +dto.customer_id,
                consent_id: +dto.consent_id,
              },
            },
            data: {
              given: dto.given,
              givenAt: dto.given ? date : null,
              revokedAt: dto.given ? null : date,
              updated_at: date,
            },
          })
        );

        const createPromises = creates.length
          ? [
              tx.customerConsent.createMany({
                data: creates.map((dto) => ({
                  given: dto.given,
                  givenAt: dto.given ? date : null,
                  revokedAt: dto.given ? null : date,
                  created_at: date,
                  updated_at: date,
                  customer_id: +dto.customer_id,
                  consent_id: +dto.consent_id,
                })),
              }),
            ]
          : [];

        await Promise.all([...updatePromises, ...createPromises]);

        return tx.customerConsent.findMany({
          where: {
            OR: dtos.map((dto) => ({
              customer_id: +dto.customer_id,
              consent_id: +dto.consent_id,
            })),
          },
          include: {
            customer: true,
            consent: true,
          },
        });
      });

      return result;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === "P2025") {
          throw new NotFoundException("One or more Customers or Consents not found");
        }
        if (error.code === "P2002") {
          throw new BadRequestException("One or more CustomerConsents with this combination already exist");
        }
      }
      throw new BadRequestException("Unable to create/update CustomerConsents");
    }
  }

  async findAll() {
    return this.databaseService.customerConsent.findMany();
  }

  async findOne(id: number, user: AuthenticatedUser): Promise<CustomerConsent> {
    try {
      await this.validatingUserPermissionCustomerConsent(id, user);

      const customerConsent = await this.databaseService.customerConsent.findUnique({
        where: { id },
        include: {
          customer: true,
          consent: true,
        },
      });

      return customerConsent;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Error retrieving CustomerConsent: ${error.message}`);
    }
  }

  async findByCustomerId(customerId: number, user: AuthenticatedUser): Promise<CustomerConsent[]> {
    try {
      // await this.validatingUserPermissionCustomerConsent(customerId, user);

      const customerConsents = await this.databaseService.customerConsent.findMany({
        where: {
          customer_id: customerId,
        },
        include: {
          customer: true,
          consent: true,
        },
      });

      if (customerConsents.length === 0) {
        throw new NotFoundException(`No CustomerConsents found for customer ID ${customerId}`);
      }

      return customerConsents;
    } catch (error) {
      if (error instanceof BadRequestException || error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Error retrieving CustomerConsents: ${error.message}`);
    }
  }

  async update(id: number, dto: UpdateCustomerConsentDto, user: AuthenticatedUser): Promise<CustomerConsent> {
    await this.validatingUserPermissionCustomerConsent(id, user);

    try {
      const date = new Date();

      const updatedConsent = await this.databaseService.customerConsent.update({
        where: { id },
        data: {
          ...dto,
          updated_at: date,
          givenAt: dto.given ? date : null,
          revokedAt: dto.given ? null : date,
        },
        include: {
          customer: true,
          consent: true,
        },
      });

      // TODO Change id matching to enum
      if (updatedConsent.consent.name === `Result by mail – we need you consent to send you e-mails*`) {
        this.customerIoService.updateAttributesByCustomerId(updatedConsent.customer_id, {
          cio_subscription_preferences: {
            topics: {
              topic_1: !!updatedConsent?.given,
              topic_3: !!updatedConsent?.given,
              topic_5: !!updatedConsent?.given,
              topic_6: !!updatedConsent?.given,
            },
          },
        });
      }

      try {
        await lastValueFrom(
          this.httpModuleService.admin({
            url: "/emails/send-message",
            params: {
              transactional_message_id: "24",
              identifiers: {
                email: user.email,
              },
              to: user.email,
              from: "Lizenzero <<EMAIL>>",
              subject: "Consent status changed",
            },
            method: "post",
          })
        ).catch((error) => {
          console.error(error?.response?.data);
        });
      } catch (error) {
        console.error(error);
      }

      return updatedConsent;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === "P2025") {
          throw new NotFoundException(`CustomerConsent with ID ${id} not found`);
        }
      }
      throw new BadRequestException(`Error updating CustomerConsent: ${error.message}`);
    }
  }

  async updateMany(dto: UpdateCustomerConsentDto[]): Promise<CustomerConsent[]> {
    try {
      const date = new Date();

      const updatedConsents = await Promise.all(
        dto.map(async (consent) => {
          const { id, given } = consent;
          return this.databaseService.customerConsent.update({
            where: {
              id: id,
            },
            data: {
              given,
              updated_at: date,
              givenAt: consent.given ? date : null,
              revokedAt: consent.given ? null : date,
            },
          });
        })
      );

      return updatedConsents;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === "P2025") {
          throw new NotFoundException(`One or more CustomerConsents were not found`);
        }
      }
      throw new BadRequestException(`Error updating CustomerConsents: ${error.message}`);
    }
  }

  async validatingUserPermissionCustomerConsent(id: number, user: AuthenticatedUser) {
    if (!id || isNaN(id)) {
      throw new BadRequestException("Invalid CustomerConsent ID");
    }

    const customerConsent = await this.databaseService.customerConsent.findUnique({
      where: { id },
      include: {
        customer: true,
      },
    });

    if (!customerConsent) {
      throw new NotFoundException("Customer consent not found");
    }

    const { customer } = customerConsent;
    if (!customer) {
      throw new NotFoundException("Customer not found");
    }

    if (user.role === Role.CUSTOMER && customer.user_id !== +user.id) {
      throw new ForbiddenException("You do not have permission to access this customer consent");
    }
  }
}
