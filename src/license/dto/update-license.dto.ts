import { ApiProperty } from "@nestjs/swagger";
import { LicenseClerkControlStatus, LicenseContractStatus, LicenseRegistrationStatus } from "@prisma/client";

export class UpdateLicenseDto {
  @ApiProperty({
    description: "The registration status of the contract",
    enum: LicenseRegistrationStatus,
    example: LicenseRegistrationStatus.DONE,
  })
  registration_status: LicenseRegistrationStatus;

  @ApiProperty({
    description: "The clerk control status of the contract",
    enum: LicenseClerkControlStatus,
    example: LicenseClerkControlStatus.DONE,
  })
  clerk_control_status: LicenseClerkControlStatus;

  @ApiProperty({
    description: "The contract status of the contract",
    enum: LicenseContractStatus,
    example: LicenseContractStatus.ACTIVE,
  })
  contract_status: LicenseContractStatus;

  @ApiProperty({
    description: "The termination ID of the contract",
    example: 1,
  })
  termination_id: number;
}
