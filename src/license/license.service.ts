import { DatabaseService } from "@/database/database.service";
import { BadRequestException, ForbiddenException, Injectable, NotFoundException } from "@nestjs/common";
import { CreateLicenseDto } from "./dto/create-license.dto";
import { UpdateLicenseDto } from "./dto/update-license.dto";
import {
  Certificate,
  Contract,
  File,
  License,
  LicensePackagingService,
  LicensePriceList,
  LicenseRequiredInformation,
  LicenseThirdPartyInvoice,
  LicenseVolumeReport,
  LicenseOtherCost,
  LicenseRepresentativeTier,
  ServiceNextStep,
  Termination,
} from "@prisma/client";
import * as crypto from "node:crypto";
import { Role } from "@/shared/auth/role.enum";
import { AuthenticatedUser } from "@/shared/auth/user.decorator";

type FullLicense = License & {
  packaging_services: (LicensePackagingService & { volume_reports: LicenseVolumeReport[] })[];
  required_informations: LicenseRequiredInformation[];
  third_party_invoices: LicenseThirdPartyInvoice[];
  next_steps: ServiceNextStep[];
  contract: Contract & { files: File[] };
  certificates: Certificate[];
  termination: Termination;
  price_list: LicensePriceList[];
  other_costs: LicenseOtherCost[];
  representative_tiers: LicenseRepresentativeTier[];
};

type LicensePendencyType =
  | "REQUIRED_INFORMATIONS"
  | "VOLUME_REPORTS"
  | "INVOICES"
  | "TERMINATION_REQUESTED"
  | "TERMINATED";

export interface LicensePendency {
  type: LicensePendencyType;
  label: string;
}

export function getLicensePendencies(license: FullLicense) {
  const pendencies: LicensePendency[] = [];

  if (license.termination) {
    if (license.termination.status !== "COMPLETED") {
      pendencies.push({ type: "TERMINATION_REQUESTED", label: "Termination requested" });

      return {
        pendencies,
        pendencies_status: "IN_REVIEW",
      };
    }

    pendencies.push({ type: "TERMINATED", label: "Terminated" });

    return {
      pendencies,
      pendencies_status: "IN_REVIEW",
    };
  }

  const isRequiredInformationsPending = license.required_informations.some(
    (requiredInformation) => requiredInformation.status === "OPEN" || requiredInformation.status === "DECLINED"
  );
  if (isRequiredInformationsPending) {
    pendencies.push({ type: "REQUIRED_INFORMATIONS", label: "Required informations" });
  }

  const isVolumeReportsPending = license.packaging_services.some((packagingService) =>
    packagingService.volume_reports.some(
      (volumeReport) => volumeReport.status === "OPEN" || volumeReport.status === "DECLINED"
    )
  );
  if (isVolumeReportsPending) {
    pendencies.push({ type: "VOLUME_REPORTS", label: "Volume reports" });
  }

  const isThirdPartyInvoicesPending = license.third_party_invoices.some((invoice) => invoice.status === "OPEN");
  if (isThirdPartyInvoicesPending) {
    pendencies.push({ type: "INVOICES", label: "Invoices" });
  }

  return {
    pendencies,
    pendencies_status: !!pendencies.length ? "OPEN_TO_DOS" : "DONE",
  };
}

@Injectable()
export class LicenseService {
  constructor(private databaseService: DatabaseService) {}

  async findAll(contract_id?: number) {
    if (!contract_id) {
      return await this.databaseService.license.findMany({
        where: {
          deleted_at: null,
          ...(contract_id && { contract_id: contract_id }),
        },
      });
    }

    const licenses = await this.databaseService.license.findMany({
      where: {
        deleted_at: null,
        ...(contract_id && { contract_id: contract_id }),
      },
      include: {
        contract: { include: { files: { where: { deleted_at: null } } } },
        price_list: { where: { deleted_at: null } },
        required_informations: {
          where: {
            deleted_at: null,
          },
        },
        third_party_invoices: {
          where: {
            deleted_at: null,
          },
        },
        representative_tiers: {
          where: {
            deleted_at: null,
          },
        },
        other_costs: {
          where: {
            deleted_at: null,
          },
        },
        packaging_services: {
          include: {
            report_set_frequency: true,
            report_set: true,
            volume_reports: {
              where: {
                deleted_at: null,
              },
              include: {
                volume_report_items: {
                  where: {
                    deleted_at: null,
                  },
                },
              },
            },
          },
        },
        certificates: {
          where: {
            deleted_at: null,
          },
          include: {
            files: true,
          },
        },
        next_steps: true,
        termination: {
          include: {
            files: {
              where: {
                deleted_at: null,
              },
            },
          },
        },
        files: {
          where: {
            deleted_at: null,
          },
        },
      },
      orderBy: {
        id: "desc",
      },
    });

    return licenses.map((license) => ({
      ...license,
      ...getLicensePendencies(license),
    }));
  }

  async findOne(id: number, user: AuthenticatedUser) {
    await this.validatingUserPermissionLicenseOtherCost(id, user);

    const license = await this.databaseService.license.findUnique({
      where: {
        deleted_at: null,
        id: Number(id),
      },
      include: {
        contract: { include: { files: { where: { deleted_at: null } } } },
        price_list: { where: { deleted_at: null } },
        required_informations: {
          where: {
            deleted_at: null,
          },
        },
        third_party_invoices: {
          where: {
            deleted_at: null,
          },
        },
        representative_tiers: {
          where: {
            deleted_at: null,
          },
        },
        other_costs: {
          where: {
            deleted_at: null,
          },
        },
        packaging_services: {
          include: {
            report_set_frequency: true,
            report_set: true,
            volume_reports: {
              where: {
                deleted_at: null,
              },
              include: {
                volume_report_items: {
                  where: {
                    deleted_at: null,
                  },
                },
              },
            },
          },
        },
        certificates: {
          where: {
            deleted_at: null,
          },
        },
        next_steps: true,
        termination: {
          include: {
            files: {
              where: {
                deleted_at: null,
              },
            },
          },
        },
        files: {
          where: {
            deleted_at: null,
          },
        },
      },
    });

    return {
      ...license,
      ...getLicensePendencies(license),
    };
  }

  async create(data: CreateLicenseDto) {
    return await this.databaseService.license.create({
      data: {
        contract_id: data.contract_id,
        country_id: data.country_id,
        country_code: data.country_code,
        country_name: data.country_name,
        country_flag: data.country_flag,
        registration_number: crypto.randomUUID().split("-")[0],
        registration_status: data.registration_status,
        clerk_control_status: data.clerk_control_status,
        contract_status: data.contract_status,
        year: data.year,
        start_date: data.start_date,
        end_date: data.end_date,
        termination_id: data.termination_id,
        created_at: new Date(),
        updated_at: new Date(),
      },
    });
  }

  async update(id: number, data: UpdateLicenseDto, user: AuthenticatedUser) {
    await this.validatingUserPermissionLicenseOtherCost(id, user);

    return await this.databaseService.license.update({
      where: { id: Number(id) },
      data: {
        ...data,
        updated_at: new Date(),
      },
    });
  }

  async remove(id: number, user: AuthenticatedUser) {
    await this.validatingUserPermissionLicenseOtherCost(id, user);

    return await this.databaseService.license.update({
      where: { id },
      data: {
        deleted_at: new Date(),
      },
    });
  }

  async validatingUserPermissionLicenseOtherCost(id: number, user: AuthenticatedUser) {
    if (!id || Number.isNaN(Number(id))) {
      throw new BadRequestException("License ID is invalid");
    }

    const licenseService = await this.databaseService.license.findUnique({
      where: {
        deleted_at: null,
        id: Number(id),
      },
      include: {
        contract: {
          include: {
            customer: true,
          },
        },
      },
    });

    if (!licenseService) {
      throw new NotFoundException("License not found");
    }

    const { contract } = licenseService;
    if (!contract) {
      throw new NotFoundException("Contract not found");
    }
    const { customer } = contract;
    if (!customer) {
      throw new NotFoundException("Customer not found");
    }

    if (user.role === Role.CUSTOMER && customer.user_id !== +user.id) {
      throw new ForbiddenException("You do not have permission to access this license service");
    }
  }
}
