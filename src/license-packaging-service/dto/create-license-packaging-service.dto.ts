import { ApiProperty } from "@nestjs/swagger";

export class CreateLicensePackagingServiceDto {
  @ApiProperty({
    description: "The license ID",
    example: 1,
  })
  license_id: number;

  @ApiProperty({
    description: "The setup packaging service ID",
    example: 1,
  })
  setup_packaging_service_id: number;

  @ApiProperty({
    description: "The name of the packaging service",
    example: "Standard Packaging",
  })
  name: string;

  @ApiProperty({
    description: "The description of the packaging service",
    example: "Basic packaging service with standard features",
  })
  description: string;
}
