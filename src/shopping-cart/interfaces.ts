interface Option {
  label: string;
  value: string;
}

interface Question {
  id: string;
  mode: string;
  packagingId?: string;
  type: string;
  title: string;
  helpText: string;
  inputType: string;
  options: Option[];
  answer: string;
}

interface Commitment {
  filled: boolean;
  questions: { [key: string]: Question };
}

interface Fraction {
  key: string;
  name: string;
  multiplier: number;
  value?: string;
}

interface ItemChild {
  id: number;
  name: string;
  description: string;
  price: number;
  level: number;
}

interface Item {
  id: number;
  name: string;
  description: string;
  price: number;
  children?: ItemChild[];
}

interface ReportSet {
  id: string;
  type: string;
  name: string;
  mode: string;
  file: string;
  items: Item[];
}

interface Package {
  id: string;
  title: string;
  added: boolean;
  required: boolean;
  price: number;
  service: string;
  reportSet: ReportSet;
  fractions: { [key: string]: Fraction };
  fractionsCost: number;
}

interface PriceList {
  id: string;
  title: string;
  serviceType: string;
  description: string;
  conditionType: {
    condition: string;
    value: string;
  };
  startDate: string;
  endDate: string;
  price?: number;
  basePrice: number;
  minPrice: number;
  registrationFee: number;
  handlingFee: number;
  variableHandlingFee: number;
}

interface RepresentativeTier {
  id: string;
  name: string;
  price: number;
}

interface License {
  type: string;
  year: string;
  commitment: Commitment;
  packagingServices: { [key: string]: Package };
  priceList: PriceList;
  representativeTier: RepresentativeTier;
}

interface Country {
  id: number;
  code: string;
  name: string;
  flag: string;
}

export interface ShopData {
  country: Country;
  license: License;
  actionGuide: Record<string, any>;
}

export interface ShopItem {
  [codeCountry: string]: ShopData;
}
