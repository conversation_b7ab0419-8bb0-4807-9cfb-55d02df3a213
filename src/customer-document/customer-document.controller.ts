import { Controller, Post, Body, Get, Param, Put, Delete } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from "@nestjs/swagger";
import { CustomerDocumentService } from "./customer-document.service";
import { CreateCustomerDocumentDto } from "./dto/create-customer-document.dto";
import { UpdateCustomerDocumentDto } from "./dto/update-customer-document.dto";
import { Roles } from "src/shared/auth/role.decorator";
import { Role } from "src/shared/auth/role.enum";
import { AuthenticatedUser, User } from "@/shared/auth/user.decorator";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK, Role.CUSTOMER)
@ApiTags("customer-document")
@Controller("customer-document")
export class CustomerDocumentController {
  constructor(private readonly customerdocumentService: CustomerDocumentService) {}

  @Post()
  @ApiOperation({ summary: "Create customer document" })
  @ApiResponse({
    status: 201,
    description: "The customer document has been successfully created.",
  })
  @ApiBody({ type: CreateCustomerDocumentDto })
  create(@Body() createCustomerdocumentDto: CreateCustomerDocumentDto) {
    return this.customerdocumentService.create(createCustomerdocumentDto);
  }

  @Get()
  @ApiOperation({ summary: "Get all customer documents" })
  @ApiResponse({ status: 200, description: "List of customer documents" })
  findAll() {
    return this.customerdocumentService.findAll();
  }

  @Get(":id")
  @ApiOperation({ summary: "Get customer document by id" })
  @ApiResponse({ status: 200, description: "The customer document details" })
  findOne(@Param("id") id: string, @User() user: AuthenticatedUser) {
    return this.customerdocumentService.findOne(Number(id), user);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update customer document by id" })
  @ApiResponse({
    status: 200,
    description: "The customer document has been successfully updated.",
  })
  @ApiBody({ type: UpdateCustomerDocumentDto })
  update(
    @Param("id") id: string,
    @Body() updateCustomerdocumentDto: UpdateCustomerDocumentDto,
    @User() user: AuthenticatedUser
  ) {
    return this.customerdocumentService.update(Number(id), updateCustomerdocumentDto, user);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete customer document by id" })
  @ApiResponse({
    status: 200,
    description: "The customer document has been successfully deleted.",
  })
  remove(@Param("id") id: string, @User() user: AuthenticatedUser) {
    return this.customerdocumentService.remove(Number(id), user);
  }
}
