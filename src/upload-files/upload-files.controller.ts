import { CustomHeadersDto } from "@/shared/dto/custom-headers.dto";
import {
  Body,
  Controller,
  ForbiddenException,
  Get,
  Param,
  Post,
  Req,
  Res,
  UploadedFile,
  UseInterceptors,
} from "@nestjs/common";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { Response } from "express";
import { CreateFileDto } from "./dto/create-file.dto";
import { RequestPresignedUrlDto } from "./dto/request-presigned-url.dto";
import { UploadFilesService } from "./upload-files.service";

import { PublicRoute } from "@/shared/auth/public-route.decorator";
import { FileInterceptor } from "@nestjs/platform-express";
@ApiTags("Upload files to s3")
@Controller("upload-files")
export class UploadFilesController {
  constructor(private readonly uploadFilesService: UploadFilesService) {}

  @PublicRoute()
  @Get(":id")
  @ApiOperation({ summary: "Get file by ID" })
  @ApiResponse({
    status: 200,
    description: "File retrieved successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "string" },
        original_name: { type: "string" },
        name: { type: "string" },
        extension: { type: "string" },
        size: { type: "string" },
        creator_type: { type: "string" },
        document_type: { type: "string" },
        user_id: { type: "string", nullable: true },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time", nullable: true },
        country_id: { type: "number", nullable: true },
      },
    },
    example: {
      id: "1",
      original_name: "example.pdf",
      name: "example",
      extension: "pdf",
      size: "100",
      creator_type: "user",
      document_type: "contract",
      user_id: "1",
      created_at: "2021-01-01T00:00:00.000Z",
      updated_at: "2021-01-01T00:00:00.000Z",
      country_id: 1,
    },
  })
  @ApiResponse({ status: 404, description: "File not found" })
  @ApiResponse({ status: 400, description: "Error downloading PDF file" })
  async getFile(@Param("id") fileId: string, @Req() req: any, @Res() res: Response) {
    const customHeadersDto: CustomHeadersDto = {
      userId: req.headers["x-user-id"] as string,
      userRole: req.headers["x-user-role"] as string,
    };

    if (!customHeadersDto.userId || !customHeadersDto.userRole) {
      throw new ForbiddenException("User must be authenticated");
    }

    const { file, buffer } = await this.uploadFilesService.getFile(fileId);

    res.setHeader("Content-Type", "application/pdf");
    res.setHeader("Content-Disposition", `attachment; filename=contract-${file.original_name}.pdf`);
    res.send(buffer);
  }

  @PublicRoute()
  @Post("request-presigned-url")
  @ApiOperation({ summary: "Request to presigned url" })
  requestUrl(@Body() requestPresignedUrlDto: RequestPresignedUrlDto) {
    return this.uploadFilesService.requestUrl(requestPresignedUrlDto);
  }

  @Post()
  @ApiOperation({ summary: "Save file" })
  @UseInterceptors(FileInterceptor("file"))
  @ApiResponse({
    status: 200,
    description: "File created successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "string" },
        original_name: { type: "string" },
        name: { type: "string" },
        extension: { type: "string" },
        size: { type: "string" },
        creator_type: { type: "string" },
        document_type: { type: "string" },
        user_id: { type: "string", nullable: true },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time", nullable: true },
        country_id: { type: "number", nullable: true },
      },
    },
    example: {
      id: "1",
      original_name: "example.pdf",
      name: "example",
      extension: "pdf",
      size: "100",
      creator_type: "user",
      document_type: "contract",
      user_id: "1",
      created_at: "2021-01-01T00:00:00.000Z",
      updated_at: "2021-01-01T00:00:00.000Z",
      country_id: 1,
    },
  })
  @ApiResponse({ status: 400, description: "Presigned url or Fields not found!" })
  @ApiResponse({ status: 400, description: "Invalid country ID" })
  @ApiResponse({ status: 404, description: "Country not found" })
  create(@Body() createFileDto: CreateFileDto, @Req() req: any, @UploadedFile() file: Express.Multer.File) {
    const customHeadersDto: CustomHeadersDto = {
      userId: req.headers["x-user-id"] as string,
      userRole: req.headers["x-user-role"] as string,
    };

    return this.uploadFilesService.uploadFile(createFileDto, customHeadersDto, file);
  }
}
