import { ApiProperty } from "@nestjs/swagger";
import {
  LicenseRequiredInformationType,
  LicenseRequiredInformationStatus,
  LicenseRequiredInformationKind,
} from "@prisma/client";

export class CreateLicenseRequiredInformationDto {
  @ApiProperty({
    description: "The setup required information ID. If its country related information.",
    example: 1,
  })
  setup_required_information_id: number;

  @ApiProperty({
    description: "The license ID. If its country related information.",
    example: 1,
  })
  license_id?: number;

  @ApiProperty({
    description: "The setup required information ID. If its general information.",
    example: 1,
  })
  setup_general_information_id: number;

  @ApiProperty({
    description: "The contract ID. If its contract related information.",
    example: 1,
  })
  contract_id?: number;

  @ApiProperty({
    description: "The type of required information",
    enum: LicenseRequiredInformationType,
    example: LicenseRequiredInformationType.TEXT,
  })
  type: LicenseRequiredInformationType;

  @ApiProperty({
    description: "The kind of required information",
    enum: LicenseRequiredInformationKind,
    example: LicenseRequiredInformationKind.REQUIRED_INFORMATION,
  })
  kind: LicenseRequiredInformationKind;

  @ApiProperty({
    description: "The status of required information",
    enum: LicenseRequiredInformationStatus,
    example: LicenseRequiredInformationStatus.OPEN,
  })
  status: LicenseRequiredInformationStatus;

  @ApiProperty({
    description: "The name of required information",
    example: "Company Registration",
  })
  name: string;

  @ApiProperty({
    description: "The description of required information",
    example: "Please provide company registration details",
  })
  description: string;

  @ApiProperty({
    description: "The question to be answered",
    example: "What is your company registration number?",
    required: false,
  })
  question?: string;

  @ApiProperty({
    description: "The file ID",
    example: "123e4567-e89b-12d3-a456-426614174000",
    required: false,
  })
  file_id?: string;

  @ApiProperty({
    description: "The answer provided",
    example: "12345678",
    required: false,
  })
  answer?: string;
}
