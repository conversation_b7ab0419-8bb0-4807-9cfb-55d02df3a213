import { Body, Controller, Delete, Get, Param, Post, Put, Query } from "@nestjs/common";
import { ApiQuery } from "@nestjs/swagger";
import { CreateLicenseRequiredInformationDto } from "./dto/create-license-required-information.dto";
import { UpdateLicenseRequiredInformationDto } from "./dto/update-license-required-information.dto";
import { LicenseRequiredInformationService } from "./license-required-information.service";
import { ApiOperation, ApiTags } from "@nestjs/swagger";
import { FindLicenseRequiredInformationsDto } from "./dto/find-license-required-informations.dto";
import { DeclineLicenseRequiredInformationDto } from "./dto/decline-license-required-information.dto";
import { AuthenticatedUser, User } from "@/shared/auth/user.decorator";

@Controller("required-informations")
@ApiTags("required-informations")
export class LicenseRequiredInformationController {
  constructor(private readonly licenseRequiredInformationService: LicenseRequiredInformationService) {}

  @Get()
  @ApiQuery({ name: "license_id", required: false, type: Number })
  @ApiOperation({ summary: "Get all required informations" })
  findAll(@Query() query: FindLicenseRequiredInformationsDto) {
    return this.licenseRequiredInformationService.findAll({
      license_id: query.license_id ? Number(query.license_id) : undefined,
      contract_id: query.contract_id ? Number(query.contract_id) : undefined,
    });
  }

  @Get(":id")
  @ApiOperation({ summary: "Get a required information by id" })
  findOne(@Param("id") id: number, @User() user: AuthenticatedUser) {
    return this.licenseRequiredInformationService.findOne(id, user);
  }

  @Post()
  @ApiOperation({ summary: "Create a required information" })
  create(@Body() data: CreateLicenseRequiredInformationDto) {
    return this.licenseRequiredInformationService.create(data);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update a required information" })
  update(@Param("id") id: number, @Body() data: UpdateLicenseRequiredInformationDto, @User() user: AuthenticatedUser) {
    return this.licenseRequiredInformationService.update(id, data, user);
  }

  @Post(":id/decline")
  @ApiOperation({ summary: "Decline a required information" })
  decline(@Param("id") id: number, @Body() data: DeclineLicenseRequiredInformationDto) {
    return this.licenseRequiredInformationService.decline(id, data);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete a required information" })
  remove(@Param("id") id: number, @User() user: AuthenticatedUser) {
    return this.licenseRequiredInformationService.remove(id, user);
  }
}
