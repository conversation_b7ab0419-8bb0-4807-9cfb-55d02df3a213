import { ApiProperty } from "@nestjs/swagger";
import { IsArray, IsDateString, IsEnum, IsNumber, IsObject, IsString } from "class-validator";
import { ClusterStatus } from "@prisma/client";

export class CreateClusterDto {
  @ApiProperty({
    description: "Name of the cluster",
    example: "random name",
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: "Registration start date",
    example: "2025-03-06T00:00:33.944Z",
  })
  @IsDateString()
  registration_start_date: string;

  @ApiProperty({
    description: "Registration end date",
    example: "2025-03-06T00:00:33.944Z",
  })
  @IsDateString()
  registration_end_date: string;

  @ApiProperty({
    description: "Status of the cluster",
    example: "ACTIVE",
  })
  @IsEnum(ClusterStatus)
  status: ClusterStatus;

  @ApiProperty({
    description: "Minimum household packaging (in cents)",
    example: 100,
  })
  @IsNumber()
  min_household_packaging: number;

  @ApiProperty({
    description: "Maximum household packaging (in cents)",
    example: 100,
  })
  @IsNumber()
  max_household_packaging: number;

  @ApiProperty({
    description: "Type of services",
    example:
      "{ license_service_sales_packaging: true, license_service_b2b_packaging: false, action_guide: true, direct_license: false }",
  })
  @IsObject()
  type_of_services: Record<string, boolean>;

  @ApiProperty({
    description: "Participating countries",
    example: ["FR", "DE", "ES"],
  })
  @IsArray()
  @IsString({ each: true })
  participating_countries: string[];

  @ApiProperty({
    description: "Customers",
    example: [1, 2, 3],
  })
  @IsArray()
  @IsNumber({}, { each: true })
  customers: number[];
}
