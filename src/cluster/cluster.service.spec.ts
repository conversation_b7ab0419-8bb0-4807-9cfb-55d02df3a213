import { Test, TestingModule } from "@nestjs/testing";
import { ClusterService } from "./cluster.service";
import { DatabaseService } from "@/database/database.service";
import { BadRequestException, NotFoundException } from "@nestjs/common";
import { ClusterStatus, Prisma } from "@prisma/client";

describe("ClusterService", () => {
  let service: ClusterService;
  let mockDatabaseService: { cluster: any; customer: any };

  const mockCluster = {
    id: 1,
    name: "Test Cluster",
    registration_start_date: new Date("2024-01-01"),
    registration_end_date: new Date("2024-12-31"),
    status: ClusterStatus.ACTIVE,
    min_household_packaging: 1000,
    max_household_packaging: 5000,
    type_of_services: {
      license_service_sales_packaging: true,
      license_service_b2b_packaging: false,
      action_guide: true,
      direct_license: false,
    },
    participating_countries: ["FR", "DE"],
    customers: [{ id: 1, customer_id: 1 }],
    created_at: new Date(),
    updated_at: new Date(),
    deleted_at: null,
  };

  beforeEach(async () => {
    mockDatabaseService = {
      cluster: {
        create: jest.fn().mockReturnValue(Promise.resolve(mockCluster)),
        findMany: jest.fn().mockReturnValue(Promise.resolve([mockCluster])),
        findUnique: jest.fn().mockReturnValue(Promise.resolve(mockCluster)),
        update: jest.fn().mockReturnValue(Promise.resolve(mockCluster)),
        count: jest.fn().mockReturnValue(Promise.resolve(1)),
      },
      customer: {
        findMany: jest.fn().mockReturnValue(Promise.resolve([{ id: 1 }])),
      },
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ClusterService,
        {
          provide: DatabaseService,
          useValue: mockDatabaseService,
        },
      ],
    }).compile();

    service = module.get<ClusterService>(ClusterService);
  });

  describe("create", () => {
    const createDto = {
      name: "Test Cluster",
      registration_start_date: "2024-01-01T00:00:00.000Z",
      registration_end_date: "2024-12-31T00:00:00.000Z",
      status: ClusterStatus.ACTIVE,
      min_household_packaging: 1000,
      max_household_packaging: 5000,
      type_of_services: {
        license_service_sales_packaging: true,
        license_service_b2b_packaging: false,
        action_guide: true,
        direct_license: false,
      },
      participating_countries: ["FR", "DE"],
      customers: [1],
    };

    it("should create a cluster successfully", async () => {
      const result = await service.create(createDto);

      expect(result).toEqual(mockCluster);
      expect(mockDatabaseService.cluster.create).toHaveBeenCalledWith({
        data: {
          ...createDto,
          customers: {
            create: createDto.customers.map((customer_id) => ({ customer_id })),
          },
        },
        include: {
          customers: true,
        },
      });
    });

    it("should throw BadRequestException if customers do not exist", async () => {
      mockDatabaseService.customer.findMany.mockReturnValue(Promise.resolve([]));

      await expect(service.create(createDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe("findAll", () => {
    it("should return all non-deleted clusters", async () => {
      const result = await service.findAll();

      expect(result).toEqual([mockCluster]);
      expect(mockDatabaseService.cluster.findMany).toHaveBeenCalledWith({
        where: { deleted_at: null },
        include: { customers: true },
      });
    });
  });

  describe("findOne", () => {
    it("should return a cluster by id", async () => {
      const result = await service.findOne(1);
      expect(result).toEqual(mockCluster);
    });

    it("should return null if cluster not found", async () => {
      mockDatabaseService.cluster.findUnique.mockReturnValue(Promise.resolve(null));

      const result = await service.findOne(999);
      expect(result).toBeNull();
    });
  });

  describe("update", () => {
    const updateDto = {
      name: "Updated Cluster",
      customers: [1, 2],
    };

    it("should throw NotFoundException if cluster not found", async () => {
      mockDatabaseService.cluster.findUnique.mockReturnValue(Promise.resolve(null));

      await expect(service.update(999, updateDto)).rejects.toThrow(NotFoundException);
    });

    it("should throw BadRequestException if customers do not exist", async () => {
      mockDatabaseService.customer.findMany.mockReturnValue(Promise.resolve([{ id: 1 }]));

      await expect(service.update(1, updateDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe("remove", () => {
    it("should soft delete a cluster", async () => {
      const deletedAt = new Date();
      mockDatabaseService.cluster.update.mockReturnValue(Promise.resolve({ ...mockCluster, deleted_at: deletedAt }));

      const result = await service.remove(1);
      expect(result).toBeDefined();
    });
  });

  describe("findAllPaginated", () => {
    const paginationQuery = {
      page: 1,
      limit: 10,
      name: "Test",
      start_date: "2024-01-01",
      end_date: "2024-12-31",
    };

    it("should return paginated clusters", async () => {
      const result = await service.findAllPaginated(paginationQuery);

      expect(result).toEqual({
        clusters: [mockCluster],
        count: 1,
        pages: 1,
        current_page: 1,
        limit: 10,
      });

      const expectedWhereInput: Prisma.ClusterWhereInput = {
        deleted_at: null,
        name: { startsWith: "Test", mode: "insensitive" },
        registration_start_date: { gte: paginationQuery.start_date },
        registration_end_date: { lte: paginationQuery.end_date },
      };

      expect(mockDatabaseService.cluster.findMany).toHaveBeenCalledWith({
        where: expectedWhereInput,
        skip: 0,
        take: 10,
      });
    });

    it("should use default pagination values", async () => {
      const result = await service.findAllPaginated({});

      expect(result.current_page).toBe(1);
      expect(result.limit).toBe(10);
    });
  });
});
