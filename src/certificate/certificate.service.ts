import { CustomerService } from "@/customer/customer.service";
import { DatabaseService } from "@/database/database.service";
import { FileService } from "@/file/file.service";
import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import * as fs from "fs-extra";
import * as path from "path";
import { CreateCertificatePdfDto } from "./dto/create-certificate-pdf.dto";
import { CreateCertificateDto } from "./dto/create-certificate.dto";
import { UpdateCertificateDto } from "./dto/update-certificate.dto";
import { generatePdf } from "@/shared/utils/generate-pdf";

function getImageAsBase64(filePath: string) {
  const fileBuffer = fs.readFileSync(filePath);
  return `data:image/png;base64,${fileBuffer.toString("base64")}`;
}

@Injectable()
export class CertificateService {
  constructor(
    private databaseService: DatabaseService,
    private readonly customerService: CustomerService,
    private readonly fileService: FileService
  ) {}

  async findAll(licenseId: number) {
    if (!licenseId || Number.isNaN(Number(licenseId))) {
      throw new BadRequestException("License ID is invalid");
    }

    return await this.databaseService.certificate.findMany({
      where: {
        deleted_at: null,
        license_id: Number(licenseId),
      },
      include: {
        license: {
          select: {
            id: true,
            registration_number: true,
            country_code: true,
          },
        },
        files: {
          where: {
            deleted_at: null,
          },
        },
      },
    });
  }

  async findOne(id: number) {
    if (!id || Number.isNaN(Number(id))) {
      throw new BadRequestException("Certificate ID is invalid");
    }

    const certificate = await this.databaseService.certificate.findUnique({
      where: { id: Number(id), deleted_at: null },
      include: {
        files: {
          where: {
            deleted_at: null,
          },
        },
      },
    });

    if (!certificate) {
      throw new NotFoundException("Certificate not found");
    }

    return certificate;
  }

  async create(data: CreateCertificateDto) {
    const license = await this.databaseService.license.findUnique({
      where: { id: data.license_id, deleted_at: null },
    });

    if (!license) {
      throw new NotFoundException("License not found");
    }

    return await this.databaseService.certificate.create({
      data: {
        license_id: data.license_id,
        name: data.name,
      },
    });
  }

  async generateSavePdf(infos: CreateCertificatePdfDto) {
    const { certificate_id, company, ...restInfo } = infos;

    const { address, lucid, name: companyName } = company;

    const today = new Date();
    const day = String(today.getDate()).padStart(2, "0");
    const month = String(today.getMonth() + 1).padStart(2, "0"); // Janeiro é 0
    const year = today.getFullYear();
    const formattedDate = `${day}.${month}.${year}`;

    const certificatePdf = await generatePdf({
      templateFile: "certificate-direct.hbs",
      fileName: "certificate.pdf",
      data: {
        logoLizPath: getImageAsBase64(path.join(process.cwd(), "src", "shared", "templates", "images", "logo_liz.png")),
        logoInterPath: getImageAsBase64(
          path.join(process.cwd(), "src", "shared", "templates", "images", "logo_inter.png")
        ),
        signature: {
          firstName: `Markus Müller-Drexel`,
          firstSignature: getImageAsBase64(
            path.join(process.cwd(), "src", "shared", "templates", "images", "first_signature.png")
          ),
          secondName: `Frank Kurrat`,
          secondSignature: getImageAsBase64(
            path.join(process.cwd(), "src", "shared", "templates", "images", "second_signature.png")
          ),
        },
        date: formattedDate,
        ...restInfo,
        agreementNumber: certificate_id,
        companyName,
        lucid,
        street: address?.street_and_number,
        zip: address?.zip_code,
        country: address?.country_code,
      },
      headerTemplate: `<div style="display: none;"></div>`,
      displayHeaderFooter: false,
      margin: {
        top: 48,
        right: 80,
        bottom: 75,
        left: 80,
      },
    });

    return certificatePdf;
  }

  async update(id: number, data: UpdateCertificateDto) {
    if (!id || Number.isNaN(Number(id))) {
      throw new BadRequestException("Certificate ID is invalid");
    }

    const certificate = await this.databaseService.certificate.findUnique({
      where: { id: Number(id), deleted_at: null },
    });

    if (!certificate) {
      throw new NotFoundException("Certificate not found");
    }

    return await this.databaseService.certificate.update({
      where: { id: Number(id) },
      data: {
        name: data.name,
        updated_at: new Date(),
      },
    });
  }

  async remove(id: number) {
    if (!id || Number.isNaN(Number(id))) {
      throw new BadRequestException("Certificate ID is invalid");
    }

    const certificate = await this.databaseService.certificate.findUnique({
      where: { id, deleted_at: null },
    });

    if (!certificate) {
      throw new NotFoundException("Certificate not found");
    }

    await this.databaseService.certificate.update({
      where: { id },
      data: {
        deleted_at: new Date(),
      },
    });
  }
}
