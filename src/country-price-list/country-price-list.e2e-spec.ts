import { Test, TestingModule } from "@nestjs/testing";
import { INestApplication } from "@nestjs/common";
import * as request from "supertest";
import { AppModule } from "../app.module";
import { Role } from "../shared/auth/role.enum";
import { HEADER_SYSTEM_API_KEY, HEADER_USER_ID, HEADER_USER_ROLE, HEADER_USER_EMAIL } from "../shared/auth/const";
import { DatabaseService } from "../database/database.service";

jest.setTimeout(30000);

describe("CountryPriceListController (e2e)", () => {
  let app: INestApplication;
  let databaseService: DatabaseService;

  const validApiKey = "test-api-key";

  beforeAll(() => {
    process.env.SYSTEM_API_KEY = validApiKey;
  });

  const authHeaders = {
    [HEADER_SYSTEM_API_KEY]: validA<PERSON><PERSON><PERSON>,
    [HEADER_USER_ID]: "1",
    [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
    [HEADER_USER_EMAIL]: "<EMAIL>",
  };

  const mockCountryPriceList = {
    id: 1,
    country_id: 1,
    price_list_id: 1,
    created_at: "2025-04-07T22:58:17.004Z",
    updated_at: "2025-04-07T22:58:17.004Z",
    deleted_at: null,
  };

  const mockCountryPriceListList = [
    mockCountryPriceList,
    {
      id: 2,
      country_id: 2,
      price_list_id: 2,
      created_at: "2025-04-07T22:58:17.004Z",
      updated_at: "2025-04-07T22:58:17.004Z",
      deleted_at: null,
    },
  ];

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(DatabaseService)
      .useValue({
        countryPriceList: {
          create: jest.fn().mockResolvedValue(mockCountryPriceList),
          findMany: jest.fn().mockResolvedValue(mockCountryPriceListList),
          findUnique: jest.fn().mockResolvedValue(mockCountryPriceList),
          update: jest.fn().mockResolvedValue({ ...mockCountryPriceList, deleted_at: "2025-04-07T22:58:17.004Z" }),
        },
      })
      .compile();

    app = moduleFixture.createNestApplication();
    databaseService = moduleFixture.get<DatabaseService>(DatabaseService);

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("/country-price-lists (GET)", () => {
    it("should return country price lists when authenticated as SUPER_ADMIN with valid API key", () => {
      return request(app.getHttpServer())
        .get("/country-price-lists")
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(databaseService.countryPriceList.findMany).toHaveBeenCalled();
          expect(response.body).toEqual(mockCountryPriceListList);
        });
    });

    it("should return country price lists when authenticated as ADMIN with valid API key", () => {
      const adminHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.ADMIN,
      };

      return request(app.getHttpServer()).get("/country-price-lists").set(adminHeaders).expect(200);
    });

    it("should return country price lists when authenticated as CLERK with valid API key", () => {
      const clerkHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.CLERK,
      };

      return request(app.getHttpServer()).get("/country-price-lists").set(clerkHeaders).expect(200);
    });

    it("should reject when authenticated with unauthorized role even with valid API key", () => {
      const unauthorizedHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.CUSTOMER,
      };

      return request(app.getHttpServer()).get("/country-price-lists").set(unauthorizedHeaders).expect(403);
    });

    it("should reject when API key is invalid regardless of role", () => {
      const invalidApiKeyHeaders = {
        ...authHeaders,
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
      };

      return request(app.getHttpServer()).get("/country-price-lists").set(invalidApiKeyHeaders).expect(401);
    });

    it("should reject when API key is missing regardless of role", () => {
      const noApiKeyHeaders = {
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/country-price-lists").set(noApiKeyHeaders).expect(401);
    });

    it("should reject when not authenticated at all", () => {
      return request(app.getHttpServer()).get("/country-price-lists").expect(401);
    });
  });

  describe("/country-price-lists/:id (GET)", () => {
    it("should return a single country price list when authenticated with valid API key", () => {
      return request(app.getHttpServer())
        .get("/country-price-lists/1")
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(databaseService.countryPriceList.findUnique).toHaveBeenCalledWith({
            where: { id: 1, deleted_at: null },
          });
          expect(response.body).toEqual(mockCountryPriceList);
        });
    });

    it("should reject when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        ...authHeaders,
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
      };

      return request(app.getHttpServer()).get("/country-price-lists/1").set(invalidApiKeyHeaders).expect(401);
    });

    it("should reject when not authenticated", () => {
      return request(app.getHttpServer()).get("/country-price-lists/1").expect(401);
    });
  });

  describe("/country-price-lists (POST)", () => {
    const createCountryPriceListDto = {
      country_id: 1,
      price_list_id: 1,
    };

    it("should create a new country price list when authenticated with valid API key", () => {
      return request(app.getHttpServer())
        .post("/country-price-lists")
        .set(authHeaders)
        .send(createCountryPriceListDto)
        .expect(201)
        .then((response) => {
          expect(databaseService.countryPriceList.create).toHaveBeenCalledWith({
            data: createCountryPriceListDto,
          });
          expect(response.body).toEqual(mockCountryPriceList);
        });
    });

    it("should reject creation when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        ...authHeaders,
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
      };

      return request(app.getHttpServer())
        .post("/country-price-lists")
        .set(invalidApiKeyHeaders)
        .send(createCountryPriceListDto)
        .expect(401);
    });

    it("should reject creation when not authenticated", () => {
      return request(app.getHttpServer()).post("/country-price-lists").send(createCountryPriceListDto).expect(401);
    });

    it("should reject creation when authenticated with unauthorized role", () => {
      const unauthorizedHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.CUSTOMER,
      };

      return request(app.getHttpServer())
        .post("/country-price-lists")
        .set(unauthorizedHeaders)
        .send(createCountryPriceListDto)
        .expect(403);
    });
  });

  describe("/country-price-lists/:id (DELETE)", () => {
    it("should delete a country price list when authenticated with valid API key", () => {
      const deletedCountryPriceList = {
        ...mockCountryPriceList,
        deleted_at: "2025-04-07T22:58:17.004Z",
      };

      return request(app.getHttpServer())
        .delete("/country-price-lists/1")
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(databaseService.countryPriceList.update).toHaveBeenCalledWith({
            where: { id: 1 },
            data: { deleted_at: expect.any(Date) },
          });
          expect(response.body).toEqual(
            expect.objectContaining({
              id: mockCountryPriceList.id,
              deleted_at: expect.any(String),
            })
          );
        });
    });

    it("should reject deletion when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        ...authHeaders,
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
      };

      return request(app.getHttpServer()).delete("/country-price-lists/1").set(invalidApiKeyHeaders).expect(401);
    });

    it("should reject deletion when not authenticated", () => {
      return request(app.getHttpServer()).delete("/country-price-lists/1").expect(401);
    });

    it("should reject deletion when authenticated with unauthorized role", () => {
      const unauthorizedHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.CUSTOMER,
      };

      return request(app.getHttpServer()).delete("/country-price-lists/1").set(unauthorizedHeaders).expect(403);
    });
  });

  describe("System role access", () => {
    it("should allow access with SYSTEM role and valid API key", () => {
      const systemHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ROLE]: Role.SYSTEM,
      };

      return request(app.getHttpServer()).get("/country-price-lists").set(systemHeaders).expect(200);
    });

    it("should reject access with SYSTEM role but invalid API key", () => {
      const invalidSystemHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ROLE]: Role.SYSTEM,
      };

      return request(app.getHttpServer()).get("/country-price-lists").set(invalidSystemHeaders).expect(401);
    });

    it("should reject when user role is missing even with valid API key", () => {
      const missingRoleHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ID]: "1",
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/country-price-lists").set(missingRoleHeaders).expect(403);
    });

    it("should reject when user ID is missing even with valid API key and role", () => {
      const missingIdHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/country-price-lists").set(missingIdHeaders).expect(401);
    });
  });

  describe("Error handling", () => {
    it("should return 404 when getting a non-existent country price list", () => {
      jest.spyOn(databaseService.countryPriceList, "findUnique").mockResolvedValueOnce(null);

      return request(app.getHttpServer())
        .get("/country-price-lists/999")
        .set(authHeaders)
        .expect(404)
        .then((response) => {
          expect(response.body.message).toBe("Country price list not found");
        });
    });

    it("should return 400 when deleting with invalid id", () => {
      return request(app.getHttpServer())
        .delete("/country-price-lists/abc")
        .set(authHeaders)
        .expect(400)
        .then((response) => {
          expect(response.body.message).toBe("Invalid country price list ID");
        });
    });

    it("should return 404 when deleting a non-existent country price list", () => {
      jest.spyOn(databaseService.countryPriceList, "findUnique").mockResolvedValueOnce(null);

      return request(app.getHttpServer())
        .delete("/country-price-lists/999")
        .set(authHeaders)
        .expect(404)
        .then((response) => {
          expect(response.body.message).toBe("Country price list not found");
        });
    });
  });
});
