import { ApiProperty } from "@nestjs/swagger";

export class CreateLicenseOtherCostDto {
  @ApiProperty({
    description: "The license ID",
  })
  license_id: number;

  @ApiProperty({
    description: "The setup other cost ID",
  })
  setup_other_cost_id: number;

  @ApiProperty({
    description: "The name of the other cost",
  })
  name: string;

  @ApiProperty({
    description: "The price of the invoice (in cents)",
  })
  price: number;
}
