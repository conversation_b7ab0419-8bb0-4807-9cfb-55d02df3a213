import { Body, Controller, Delete, Get, Param, Post, Put, Query } from "@nestjs/common";
import { ApiQuery } from "@nestjs/swagger";
import { ApiOperation, ApiTags } from "@nestjs/swagger";
import { CreateLicenseOtherCostDto } from "./dto/create-license-other-cost.dto";
import { UpdateLicenseOtherCostDto } from "./dto/update-license-other-cost.dto";
import { LicenseOtherCostService } from "./license-other-cost.service";
import { Roles } from "src/shared/auth/role.decorator";
import { Role } from "src/shared/auth/role.enum";
import { AuthenticatedUser, User } from "@/shared/auth/user.decorator";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK, Role.CUSTOMER)
@Controller("other-costs")
@ApiTags("other-costs")
export class LicenseOtherCostController {
  constructor(private readonly licenseOtherCostService: LicenseOtherCostService) {}

  @Get()
  @ApiQuery({ name: "license_id", required: false, type: Number })
  @ApiOperation({ summary: "Get all other costs" })
  findAll(@Query("license_id") license_id?: string) {
    return this.licenseOtherCostService.findAll(license_id ? parseInt(license_id) : undefined);
  }

  @Get(":id")
  @ApiOperation({ summary: "Get an other cost by id" })
  findOne(@Param("id") id: number, @User() user: AuthenticatedUser) {
    return this.licenseOtherCostService.findOne(id, user);
  }

  @Post()
  @ApiOperation({ summary: "Create an other cost" })
  create(@Body() data: CreateLicenseOtherCostDto) {
    return this.licenseOtherCostService.create(data);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update an other cost" })
  update(@Param("id") id: number, @Body() data: UpdateLicenseOtherCostDto, @User() user: AuthenticatedUser) {
    return this.licenseOtherCostService.update(id, data, user);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete a third party invoice" })
  remove(@Param("id") id: string, @User() user: AuthenticatedUser) {
    return this.licenseOtherCostService.remove(+id, user);
  }
}
