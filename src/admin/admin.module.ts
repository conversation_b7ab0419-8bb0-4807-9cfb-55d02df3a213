import { Module } from "@nestjs/common";
import { DatabaseModule } from "../database/database.module";
import { HttpApiModule } from "../http/http.module";
import { AdminController } from "./admin.controller";
import { AdminService } from "./admin.service";

@Module({
  imports: [DatabaseModule, HttpApiModule],
  controllers: [AdminController],
  providers: [AdminService],
})
export class AdminModule {}
