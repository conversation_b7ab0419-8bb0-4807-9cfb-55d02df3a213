import { Injectable } from "@nestjs/common";
import { EnUstIdError, UstIdError } from "@/interface/errors.interface";

@Injectable()
export class UstIdErrorService {
  getErrorMessageByCode(code: string): { code: UstIdError; valid: boolean } {
    switch (code) {
      case "200":
        return { code: UstIdError.Code200, valid: true };
      case "201":
        return { code: UstIdError.Code201, valid: false };
      case "202":
        return { code: UstIdError.Code202, valid: false };
      case "203":
        return { code: UstIdError.Code203, valid: false };
      case "204":
        return { code: UstIdError.Code204, valid: false };
      case "205":
        return { code: UstIdError.Code205, valid: false };
      case "206":
        return { code: UstIdError.Code206, valid: false };
      case "208":
        return { code: UstIdError.Code208, valid: false };
      case "209":
        return { code: UstIdError.Code209, valid: false };
      case "210":
        return { code: UstIdError.Code210, valid: false };
      case "211":
        return { code: UstIdError.Code211, valid: false };
      case "212":
        return { code: UstIdError.Code212, valid: false };
      case "213":
        return { code: UstIdError.Code213, valid: false };
      case "214":
        return { code: UstIdError.Code214, valid: false };
      case "215":
        return { code: UstIdError.Code215, valid: false };
      case "216":
        return { code: UstIdError.Code216, valid: false };
      case "217":
        return { code: UstIdError.Code217, valid: false };
      case "218":
        return { code: UstIdError.Code218, valid: true };
      case "219":
        return { code: UstIdError.Code219, valid: true };
      case "221":
        return { code: UstIdError.Code221, valid: false };
      case "223":
        return { code: UstIdError.Code223, valid: true };
      case "999":
        return { code: UstIdError.Code999, valid: false };

      default:
        return { code: UstIdError.Code999, valid: false };
    }
  }
}

Injectable();
export class EnUstIdErrorService {
  getErrorMessageByCode(code: string): { code: EnUstIdError; valid: boolean } {
    switch (code) {
      case "200":
        return { code: EnUstIdError.Code200, valid: true };
      case "201":
        return { code: EnUstIdError.Code201, valid: false };
      case "202":
        return { code: EnUstIdError.Code202, valid: false };
      case "203":
        return { code: EnUstIdError.Code203, valid: false };
      case "204":
        return { code: EnUstIdError.Code204, valid: false };
      case "205":
        return { code: EnUstIdError.Code205, valid: false };
      case "206":
        return { code: EnUstIdError.Code206, valid: false };

      case "208":
        return { code: EnUstIdError.Code208, valid: false };
      case "209":
        return { code: EnUstIdError.Code209, valid: false };
      case "210":
        return { code: EnUstIdError.Code210, valid: false };
      case "211":
        return { code: EnUstIdError.Code211, valid: false };
      case "212":
        return { code: EnUstIdError.Code212, valid: false };
      case "213":
        return { code: EnUstIdError.Code213, valid: false };
      case "214":
        return { code: EnUstIdError.Code214, valid: false };
      case "215":
        return { code: EnUstIdError.Code215, valid: false };
      case "216":
        return { code: EnUstIdError.Code216, valid: false };
      case "217":
        return { code: EnUstIdError.Code217, valid: false };
      case "218":
        return { code: EnUstIdError.Code218, valid: true };
      case "219":
        return { code: EnUstIdError.Code219, valid: true };
      case "221":
        return { code: EnUstIdError.Code221, valid: false };
      case "223":
        return { code: EnUstIdError.Code223, valid: true };
      case "999":
        return { code: EnUstIdError.Code999, valid: false };

      default:
        return { code: EnUstIdError.Code999, valid: false };
    }
  }
}
