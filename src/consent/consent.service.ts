import { DatabaseService } from "@/database/database.service";
import { Injectable, NotFoundException } from "@nestjs/common";
import { CreateConsentDto } from "./dto/create-consent";
import { UpdateConsentDto } from "./dto/update-consent";
import { Consent, ConsentType } from "@prisma/client";

@Injectable()
export class ConsentService {
  constructor(private readonly databaseService: DatabaseService) {}

  async create(dto: CreateConsentDto): Promise<Consent> {
    const consent = await this.databaseService.consent.create({
      data: {
        ...dto,
        version: 1,
        created_at: new Date(),
        updated_at: new Date(),
      },
    });

    return consent;
  }

  async findAll(): Promise<Consent[]> {
    return this.databaseService.consent.findMany();
  }

  async findOne(id: number): Promise<Consent> {
    return this.databaseService.consent.findUnique({
      where: {
        id,
      },
    });
  }

  async update(id: number, dto: UpdateConsentDto): Promise<Consent> {
    const currentConsent = await this.databaseService.consent.findUnique({
      where: { id },
      select: { version: true },
    });

    if (!currentConsent) {
      throw new NotFoundException(`Consent with ID ${id} not found`);
    }

    const updatedConsent = await this.databaseService.consent.update({
      where: { id },
      data: {
        ...dto,
        version: currentConsent.version + 1,
        updated_at: new Date(),
      },
    });

    return updatedConsent;
  }

  async remove(id: number) {
    return this.databaseService.consent.delete({
      where: {
        id,
      },
    });
  }

  async findManyByType(type: ConsentType) {
    return this.databaseService.consent.findMany({
      where: {
        type,
      },
    });
  }
}
