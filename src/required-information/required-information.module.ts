import { Modu<PERSON> } from "@nestjs/common";
import { DatabaseModule } from "../database/database.module";
import { RequiredInformationController } from "./required-information.controller";
import { RequiredInformationService } from "./required-information.service";

@Module({
  imports: [DatabaseModule],
  controllers: [RequiredInformationController],
  providers: [RequiredInformationService],
})
export class RequiredInformationModule {}
