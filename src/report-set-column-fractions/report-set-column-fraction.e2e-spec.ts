import { Test, TestingModule } from "@nestjs/testing";
import { INestApplication } from "@nestjs/common";
import * as request from "supertest";
import { AppModule } from "../app.module";
import { Role } from "../shared/auth/role.enum";
import { HEADER_SYSTEM_API_KEY, HEADER_USER_ID, HEADER_USER_ROLE, HEADER_USER_EMAIL } from "../shared/auth/const";
import { DatabaseService } from "../database/database.service";

jest.setTimeout(30000);

describe("ReportSetColumnFractionsController (e2e)", () => {
  let app: INestApplication;
  let databaseService: DatabaseService;

  const validApiKey = "test-api-key";

  beforeAll(() => {
    process.env.SYSTEM_API_KEY = validApiKey;
  });

  const authHeaders = {
    [HEADER_SYSTEM_API_KEY]: validA<PERSON><PERSON><PERSON>,
    [HEADER_USER_ID]: "1",
    [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
    [HEADER_USER_EMAIL]: "<EMAIL>",
  };

  const mockReportSetColumnFraction = {
    id: 1,
    column_code: "COL-001",
    fraction_code: "FRAC-001",
    created_at: "2025-04-07T22:58:17.004Z",
    updated_at: "2025-04-07T22:58:17.004Z",
    deleted_at: null,
  };

  const mockReportSetColumnFractions = [
    mockReportSetColumnFraction,
    {
      id: 2,
      column_code: "COL-002",
      fraction_code: "FRAC-002",
      created_at: "2025-04-07T22:58:17.004Z",
      updated_at: "2025-04-07T22:58:17.004Z",
      deleted_at: null,
    },
  ];

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(DatabaseService)
      .useValue({
        reportSetColumnFraction: {
          create: jest.fn().mockResolvedValue(mockReportSetColumnFraction),
          findMany: jest.fn().mockResolvedValue(mockReportSetColumnFractions),
          findUnique: jest.fn().mockResolvedValue(mockReportSetColumnFraction),
          update: jest.fn().mockImplementation((params) => {
            return Promise.resolve({
              ...mockReportSetColumnFraction,
              ...params.data,
            });
          }),
        },
        $transaction: jest.fn().mockImplementation(async (callback) => {
          return callback({
            reportSetColumnFraction: {
              update: jest.fn().mockResolvedValue({
                ...mockReportSetColumnFraction,
                deleted_at: new Date(),
              }),
            },
          });
        }),
      })
      .compile();

    app = moduleFixture.createNestApplication();
    databaseService = moduleFixture.get<DatabaseService>(DatabaseService);

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("/report-set-column-fractions (GET)", () => {
    it("should return column fractions when authenticated as SUPER_ADMIN with valid API key", () => {
      return request(app.getHttpServer())
        .get("/report-set-column-fractions")
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(databaseService.reportSetColumnFraction.findMany).toHaveBeenCalledWith({
            where: { deleted_at: null },
          });
          expect(response.body).toEqual(mockReportSetColumnFractions);
        });
    });

    it("should return column fractions when authenticated as ADMIN with valid API key", () => {
      const adminHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.ADMIN,
      };

      return request(app.getHttpServer()).get("/report-set-column-fractions").set(adminHeaders).expect(200);
    });

    it("should return column fractions when authenticated as CLERK with valid API key", () => {
      const clerkHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.CLERK,
      };

      return request(app.getHttpServer()).get("/report-set-column-fractions").set(clerkHeaders).expect(200);
    });

    it("should reject when authenticated with unauthorized role even with valid API key", () => {
      const unauthorizedHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.CUSTOMER,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/report-set-column-fractions").set(unauthorizedHeaders).expect(403);
    });

    it("should reject when API key is invalid regardless of role", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/report-set-column-fractions").set(invalidApiKeyHeaders).expect(401);
    });

    it("should reject when not authenticated at all", () => {
      return request(app.getHttpServer()).get("/report-set-column-fractions").expect(401);
    });
  });

  describe("/report-set-column-fractions/:id (GET)", () => {
    it("should return a single column fraction when authenticated with valid API key", () => {
      return request(app.getHttpServer())
        .get("/report-set-column-fractions/1")
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(databaseService.reportSetColumnFraction.findUnique).toHaveBeenCalledWith({
            where: { id: 1, deleted_at: null },
          });
          expect(response.body).toEqual(mockReportSetColumnFraction);
        });
    });

    it("should return 404 when column fraction does not exist", () => {
      jest.spyOn(databaseService.reportSetColumnFraction, "findUnique").mockResolvedValueOnce(null);

      return request(app.getHttpServer()).get("/report-set-column-fractions/999").set(authHeaders).expect(404);
    });

    it("should reject when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/report-set-column-fractions/1").set(invalidApiKeyHeaders).expect(401);
    });

    it("should reject when not authenticated", () => {
      return request(app.getHttpServer()).get("/report-set-column-fractions/1").expect(401);
    });
  });

  describe("/report-set-column-fractions (POST)", () => {
    const createReportSetColumnFractionDto = {
      column_code: "COL-NEW",
      fraction_code: "FRAC-NEW",
    };

    it("should create a new column fraction when authenticated with valid API key", () => {
      return request(app.getHttpServer())
        .post("/report-set-column-fractions")
        .set(authHeaders)
        .send(createReportSetColumnFractionDto)
        .expect(201)
        .then((response) => {
          expect(databaseService.reportSetColumnFraction.create).toHaveBeenCalledWith({
            data: createReportSetColumnFractionDto,
          });
          expect(response.body).toEqual(mockReportSetColumnFraction);
        });
    });

    it("should reject creation when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer())
        .post("/report-set-column-fractions")
        .set(invalidApiKeyHeaders)
        .send(createReportSetColumnFractionDto)
        .expect(401);
    });

    it("should reject creation when not authenticated", () => {
      return request(app.getHttpServer())
        .post("/report-set-column-fractions")
        .send(createReportSetColumnFractionDto)
        .expect(401);
    });

    it("should reject creation for unauthorized roles", () => {
      const customerHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.CUSTOMER,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer())
        .post("/report-set-column-fractions")
        .set(customerHeaders)
        .send(createReportSetColumnFractionDto)
        .expect(403);
    });
  });

  describe("/report-set-column-fractions/:id (DELETE)", () => {
    it("should delete a column fraction when authenticated with valid API key", () => {
      return request(app.getHttpServer())
        .delete("/report-set-column-fractions/1")
        .set(authHeaders)
        .expect(200)
        .then(() => {
          expect(databaseService.reportSetColumnFraction.findUnique).toHaveBeenCalledWith({
            where: { id: 1 },
          });
          expect(databaseService.$transaction).toHaveBeenCalled();
        });
    });

    it("should return 404 when attempting to delete non-existent column fraction", () => {
      jest.spyOn(databaseService.reportSetColumnFraction, "findUnique").mockResolvedValueOnce(null);

      return request(app.getHttpServer()).delete("/report-set-column-fractions/999").set(authHeaders).expect(404);
    });

    it("should reject deletion when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer())
        .delete("/report-set-column-fractions/1")
        .set(invalidApiKeyHeaders)
        .expect(401);
    });

    it("should reject deletion when not authenticated", () => {
      return request(app.getHttpServer()).delete("/report-set-column-fractions/1").expect(401);
    });

    it("should reject deletion for unauthorized roles", () => {
      const customerHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.CUSTOMER,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).delete("/report-set-column-fractions/1").set(customerHeaders).expect(403);
    });
  });

  describe("System role access", () => {
    it("should allow access with SYSTEM role and valid API key", () => {
      const systemHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ROLE]: Role.SYSTEM,
      };

      return request(app.getHttpServer()).get("/report-set-column-fractions").set(systemHeaders).expect(200);
    });

    it("should reject access with SYSTEM role but invalid API key", () => {
      const invalidSystemHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ROLE]: Role.SYSTEM,
      };

      return request(app.getHttpServer()).get("/report-set-column-fractions").set(invalidSystemHeaders).expect(401);
    });
  });
});
