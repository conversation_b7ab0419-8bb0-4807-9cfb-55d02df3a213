import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import { DatabaseService } from "../database/database.service";
import { CreateOtherCostDto } from "./dto/other-cost-create.dto";
import { UpdateOtherCostDto } from "./dto/other-cost-update.dto";

@Injectable()
export class OtherCostService {
  constructor(private readonly databaseService: DatabaseService) {}

  async create(data: CreateOtherCostDto) {
    const country = await this.databaseService.country.findUnique({
      where: { id: data.country_id },
    });

    if (!country) {
      throw new NotFoundException("Country not found");
    }

    if (data.price < 0) {
      throw new BadRequestException("Price must be greater than 0");
    }

    return this.databaseService.otherCost.create({
      data: {
        name: data.name,
        price: data.price,
        country_id: data.country_id,
      },
    });
  }

  async findAll() {
    const otherCosts = await this.databaseService.otherCost.findMany({
      where: { deleted_at: null },
    });

    return otherCosts;
  }

  async findOne(id: number) {
    if (!id || Number.isNaN(Number(id))) throw new BadRequestException("Invalid other cost ID");

    const otherCost = await this.databaseService.otherCost.findUnique({
      where: { id, deleted_at: null },
    });

    if (!otherCost) {
      throw new NotFoundException("Other cost not found");
    }

    return otherCost;
  }

  async update(id: number, data: UpdateOtherCostDto) {
    if (!id || Number.isNaN(Number(id))) throw new BadRequestException("Invalid other cost ID");

    const otherCost = await this.databaseService.otherCost.findUnique({
      where: { id, deleted_at: null },
    });

    if (!otherCost) {
      throw new NotFoundException("Other cost not found");
    }

    return this.databaseService.otherCost.update({
      where: { id },
      data,
    });
  }

  async remove(id: number) {
    if (!id || Number.isNaN(Number(id))) throw new BadRequestException("Invalid other cost ID");

    const otherCost = await this.databaseService.otherCost.findUnique({
      where: { id, deleted_at: null },
    });

    if (!otherCost) {
      throw new NotFoundException("Other cost not found");
    }

    await this.databaseService.otherCost.update({
      where: { id },
      data: { deleted_at: new Date() },
    });
  }
}
