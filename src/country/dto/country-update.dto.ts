import { ApiProperty, PartialType } from "@nestjs/swagger";
import { CreateCountryDto } from "./country-create.dto";
import { IsOptional } from "class-validator";
import { IsBoolean } from "class-validator";

export class UpdateCountryDto extends PartialType(CreateCountryDto) {
  @IsOptional()
  @IsBoolean()
  @ApiProperty({
    required: false,
    description: "Flag URL of the country",
  })
  authorize_representative_obligated?: boolean;

  @IsOptional()
  @IsBoolean()
  @ApiProperty({
    required: false,
    description: "Flag URL of the country",
  })
  other_costs_obligated?: boolean;

  @IsOptional()
  @IsBoolean()
  @ApiProperty({
    description: "The published status of the admin",
    example: "true",
  })
  is_published?: boolean;
}
