import { ApiProperty } from "@nestjs/swagger";

export class TopPartnerDto {
  @ApiProperty({ description: "Partner's full name" })
  partner_name: string;

  @ApiProperty({ description: "Total revenue in thousands of euros" })
  revenue: number;

  @ApiProperty({ description: "Total earnings in thousands of euros" })
  earnings: number;
}

export class TopPartnersResponseDto {
  @ApiProperty({ description: "Year for which the data is shown" })
  year: number;

  @ApiProperty({ description: "List of top 5 partners with their revenue and earnings", type: [TopPartnerDto] })
  partners: TopPartnerDto[];
}
