import { Body, Controller, Delete, Get, Param, Post, Put, Query } from "@nestjs/common";
import { ApiOperation, ApiTags, ApiQuery, ApiResponse } from "@nestjs/swagger";

import { PackagingServiceService } from "./packaging-service.service";
import { CreatePackagingServiceDto } from "./dto/packaging-service-create.dto";
import { UpdatePackagingServiceDto } from "./dto/packaging-service-update.dto";
import { Roles } from "@/shared/auth/role.decorator";
import { Role } from "@/shared/auth/role.enum";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK)
@ApiTags("Packaging Services")
@Controller("packaging-services")
export class PackagingServiceController {
  constructor(private readonly packagingServiceService: PackagingServiceService) {}

  @Post()
  @ApiOperation({ summary: "Create a new packaging service" })
  @ApiResponse({
    status: 201,
    description: "Packaging service created successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        description: { type: "string" },
        name: { type: "string" },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time", nullable: true },
        country_id: { type: "number" },
      },
    },
    example: {
      id: 1,
      description: "Packaging service description",
      name: "Packaging service name",
      created_at: "2024-03-20T10:00:00Z",
      updated_at: "2024-03-20T10:00:00Z",
    },
  })
  @ApiResponse({ status: 400, description: "Invalid country ID" })
  create(@Body() data: CreatePackagingServiceDto) {
    return this.packagingServiceService.create(data);
  }

  @Get()
  @ApiOperation({ summary: "Get all packaging services" })
  @ApiQuery({ name: "countryId", required: false, type: Number })
  @ApiResponse({
    status: 200,
    description: "Packaging services retrieved successfully",
    schema: {
      type: "array",
      items: {
        type: "object",
        properties: {
          id: { type: "number" },
          description: { type: "string" },
          name: { type: "string" },
          created_at: { type: "string", format: "date-time" },
          updated_at: { type: "string", format: "date-time" },
          deleted_at: { type: "string", format: "date-time", nullable: true },
          country_id: { type: "number" },
        },
      },
      example: [
        {
          id: 1,
          description: "Packaging service description",
          name: "Packaging service name",
          created_at: "2024-03-20T10:00:00Z",
          updated_at: "2024-03-20T10:00:00Z",
          deleted_at: null,
          country_id: 1,
        },
      ],
    },
  })
  findAll(@Query("countryId") countryId?: number) {
    return this.packagingServiceService.findAll({ countryId });
  }

  @Get(":id")
  @ApiOperation({ summary: "Get packaging service by ID" })
  @ApiResponse({
    status: 200,
    description: "Packaging service retrieved successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        description: { type: "string" },
        name: { type: "string" },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time", nullable: true },
        country_id: { type: "number" },
      },
    },
    example: {
      id: 1,
      description: "Packaging service description",
      name: "Packaging service name",
      created_at: "2024-03-20T10:00:00Z",
      updated_at: "2024-03-20T10:00:00Z",
      deleted_at: null,
      country_id: 1,
    },
  })
  @ApiResponse({ status: 400, description: "Invalid packaging service ID" })
  @ApiResponse({ status: 404, description: "Packaging service not found" })
  findOne(@Param("id") id: string) {
    return this.packagingServiceService.findOne(+id);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update packaging service by ID" })
  @ApiResponse({
    status: 200,
    description: "Packaging service updated successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        description: { type: "string" },
        name: { type: "string" },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time", nullable: true },
        country_id: { type: "number" },
      },
    },
    example: {
      id: 1,
      description: "Packaging service description",
      name: "Packaging service name",
      created_at: "2024-03-20T10:00:00Z",
      updated_at: "2024-03-20T10:00:00Z",
      deleted_at: null,
      country_id: 1,
    },
  })
  @ApiResponse({ status: 400, description: "Invalid packaging service ID" })
  @ApiResponse({ status: 404, description: "Packaging service not found" })
  update(@Param("id") id: string, @Body() data: UpdatePackagingServiceDto) {
    return this.packagingServiceService.update(+id, data);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete packaging service by ID" })
  @ApiResponse({ status: 200, description: "Packaging service deleted successfully" })
  @ApiResponse({ status: 400, description: "Invalid packaging service ID" })
  @ApiResponse({ status: 404, description: "Packaging service not found" })
  remove(@Param("id") id: string) {
    return this.packagingServiceService.remove(+id);
  }
}
