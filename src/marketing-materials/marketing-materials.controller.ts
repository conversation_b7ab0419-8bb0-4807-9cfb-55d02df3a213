import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Query,
  ParseIntPipe,
  UsePipes,
  ValidationPipe,
  Put,
  Req,
  UploadedFiles,
  UseInterceptors,
  HttpStatus,
} from "@nestjs/common";
import { MarketingMaterialsService } from "./marketing-materials.service";
import { CreateMarketingMaterialDto } from "./dto/create-marketing-material.dto";
import { UpdateMarketingMaterialDto } from "./dto/update-marketing-material.dto";
import { FindAllMarketingMaterialsDto } from "./dto/find-all-marketing-materials.dto";
import { ApiTags } from "@nestjs/swagger";
import { FilesInterceptor } from "@nestjs/platform-express";
import { Roles } from "src/shared/auth/role.decorator";
import { Role } from "src/shared/auth/role.enum";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK, Role.CUSTOMER, Role.MARKETING_MANAGER, Role.PARTNER)
@ApiTags("marketing-materials")
@Controller("marketing-materials")
@UsePipes(
  new ValidationPipe({
    whitelist: true,
    transform: true,
    transformOptions: {
      enableImplicitConversion: true,
    },
    errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
  })
)
export class MarketingMaterialsController {
  constructor(private readonly marketingMaterialsService: MarketingMaterialsService) {}

  @Post()
  @UseInterceptors(FilesInterceptor("files"))
  create(
    @Body() createMarketingMaterialDto: CreateMarketingMaterialDto,
    @Req() req: any,
    @UploadedFiles() files: Express.Multer.File[]
  ) {
    return this.marketingMaterialsService.create({
      ...createMarketingMaterialDto,
      files,
      user: {
        id: req.headers["x-user-id"] as string,
        role: req.headers["x-user-role"] as string,
      },
    });
  }

  @Get()
  findAll(@Query() query: FindAllMarketingMaterialsDto) {
    return this.marketingMaterialsService.findAll(query);
  }

  @Get(":id")
  findOne(@Param("id", ParseIntPipe) id: number) {
    return this.marketingMaterialsService.findOne(+id);
  }

  @Put(":id")
  @UseInterceptors(FilesInterceptor("files"))
  update(
    @Req() req: any,
    @Param("id", ParseIntPipe) id: number,
    @Body() updateMarketingMaterialDto: UpdateMarketingMaterialDto,
    @UploadedFiles() files: Express.Multer.File[]
  ) {
    return this.marketingMaterialsService.update(+id, {
      ...updateMarketingMaterialDto,
      files,
      user: {
        id: req.headers["x-user-id"] as string,
        role: req.headers["x-user-role"] as string,
      },
    });
  }

  @Delete(":id")
  remove(@Param("id", ParseIntPipe) id: number) {
    return this.marketingMaterialsService.remove(+id);
  }
}
