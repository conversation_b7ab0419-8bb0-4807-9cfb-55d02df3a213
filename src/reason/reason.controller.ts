import { Controller, Get, Query } from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { Roles } from "src/shared/auth/role.decorator";
import { Role } from "src/shared/auth/role.enum";
import { FindAllReasonsDto } from "./dto/find-all-reasons.dto";
import { ReasonService } from "./reason.service";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK, Role.CUSTOMER)
@ApiTags("reasons")
@Controller("reasons")
export class ReasonController {
  constructor(private readonly reasonService: ReasonService) {}

  @Get()
  findAll(@Query() query: FindAllReasonsDto) {
    return this.reasonService.findAll(query);
  }
}
