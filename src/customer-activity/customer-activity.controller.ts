import { Controller, Post, Body, Get, Param, Put, Delete, Query } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from "@nestjs/swagger";
import { Roles } from "src/shared/auth/role.decorator";
import { Role } from "src/shared/auth/role.enum";
import { CustomerActivityService } from "./customer-activity.service";
import { CreateCustomerActivityDto } from "./dto/create-customer-activity.dto";
import { UpdateCustomerActivityDto } from "./dto/update-customer-activity.dto";
import { FindAllCustomerActivityDto } from "./dto/find-all-customer-activity";
import { AuthenticatedUser, User } from "@/shared/auth/user.decorator";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK, Role.CUSTOMER)
@ApiTags("customer-activity")
@Controller("customer-activity")
export class CustomerActivityController {
  constructor(private readonly customerActivityService: CustomerActivityService) {}

  @Post()
  @ApiOperation({ summary: "Create customer activity" })
  @ApiResponse({
    status: 201,
    description: "The customer activity has been successfully created.",
  })
  @ApiBody({ type: CreateCustomerActivityDto })
  create(@Body() createCustomerActivityDto: CreateCustomerActivityDto) {
    return this.customerActivityService.create(createCustomerActivityDto);
  }

  @Get()
  @ApiOperation({ summary: "Get all customer activities" })
  @ApiResponse({ status: 200, description: "List of customer activities" })
  findAll(@Query() query?: FindAllCustomerActivityDto) {
    return this.customerActivityService.findAll(query);
  }

  @Get(":id")
  @ApiOperation({ summary: "Get customer activity by id" })
  @ApiResponse({ status: 200, description: "The customer activity details" })
  findOne(@Param("id") id: string, @User() user: AuthenticatedUser) {
    return this.customerActivityService.findOne(Number(id), user);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update customer phone by id" })
  @ApiResponse({
    status: 200,
    description: "The customer activity has been successfully updated.",
  })
  @ApiBody({ type: UpdateCustomerActivityDto })
  update(
    @Param("id") id: string,
    @Body() updateCustomerActivityDto: UpdateCustomerActivityDto,
    @User() user: AuthenticatedUser
  ) {
    return this.customerActivityService.update(Number(id), updateCustomerActivityDto, user);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete customer activity by id" })
  @ApiResponse({
    status: 200,
    description: "The customer activity has been successfully deleted.",
  })
  remove(@Param("id") id: string, @User() user: AuthenticatedUser) {
    return this.customerActivityService.remove(Number(id), user);
  }
}
