import { ApiProperty } from "@nestjs/swagger";
import { LicenseReportSetRhythm } from "@prisma/client";
import { IsString } from "class-validator";

export class PurchaseDto {
  @ApiProperty()
  @IsString()
  shopping_cart_id: string;
}

export type PurchaseEuLicense = {
  country_id: number;
  country_code: string;
  country_name: string;
  country_flag: string;
  year: number;
  authorize_representative_obligated: boolean;
  other_costs_obligated: boolean;
  representative_tiers: {
    id: number;
    name: string;
    price: number;
  }[];
  other_costs: {
    id: number;
    name: string;
    price: number;
  }[];
  required_informations: {
    id: number;
    type: string;
    name: string;
    description: string;
    question: string | null;
    file_id: string | null;
  }[];
  price_list: {
    id: number;
    name: string;
    description: string;
    condition_type: string;
    condition_type_value: string;
    start_date: Date;
    end_date: Date;
    basic_price: number;
    minimum_price: number;
    registration_fee: number;
    handling_fee: number;
    variable_handling_fee: number;
  };
  packaging_services: {
    id: number;
    name: string;
    description: string;
    report_set: {
      id: number;
      name: string;
      mode: string;
      type: string;
    };
    report_set_frequency: {
      id: number;
      rhythm: LicenseReportSetRhythm;
      frequency: Record<string, any>;
    };
    report_table: Record<string, any>;
  }[];
};

export type PaymentInfo = {
  invoice_id: number;
};

export type PurchaseDirectLicense = {
  country_id: number;
  country_code: string;
  country_name: string;
  country_flag: string;
  year: number;
  report_table: Record<string, any>;
};

export type PurchaseActionGuide = {
  country_id: number;
  country_code: string;
  country_name: string;
  country_flag: string;
  price_list: {
    id: number;
    name: string;
    description: string;
    price: number;
  };
};
