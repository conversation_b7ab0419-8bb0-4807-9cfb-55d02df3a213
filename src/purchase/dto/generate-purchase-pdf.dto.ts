import { ApiProperty } from "@nestjs/swagger";

export class GeneratePurchasePdfItemDto {
  @ApiProperty({ example: 1, description: "Item index" })
  index: number | string;

  @ApiProperty({ example: "Packaging License", description: "Service name" })
  service: string;

  @ApiProperty({ example: "License for paper packaging", description: "Service description" })
  description: string;

  @ApiProperty({ example: "C-12345", description: "Contract number" })
  contract_number: string;

  @ApiProperty({ example: "100.00", description: "Amount for the service" })
  amount: string;
}

export class GeneratePurchasePdfDto {
  @ApiProperty({ example: 1, description: "Order ID" })
  order_id: number;

  @ApiProperty({ example: "2023-10-27", description: "Invoice creation date" })
  created_at: string;

  @ApiProperty({ example: "DE123456789", required: false, description: "Customer's Tax ID" })
  tax_id?: string;

  @ApiProperty({ example: "DE987654321", required: false, description: "Customer's VAT ID" })
  vat_id?: string;

  @ApiProperty({ example: "19.00", description: "VAT amount" })
  vat_value: string;

  @ApiProperty({ example: "19", required: false, description: "VAT percentage" })
  vat_percentage?: number;

  @ApiProperty({ example: "LIZENZERO10", description: "Coupon code" })
  coupon_code: string;

  @ApiProperty({ example: "2450", description: "Coupon value" })
  coupon_value: string;

  @ApiProperty({ example: "100.00", description: "Total value before VAT or coupon discount" })
  subtotal: string;

  @ApiProperty({ example: "119.00", description: "Total value including vat or coupon discount" })
  total: string;

  @ApiProperty({ type: () => [GeneratePurchasePdfItemDto], description: "List of invoice items" })
  items: GeneratePurchasePdfItemDto[];

  @ApiProperty({ example: "Credit Card", description: "Payment method used" })
  payment_method_name: string;

  @ApiProperty({
    example: {
      id: 1,
      name: "John Doe",
      email: "<EMAIL>",
    },
    description: "Order customer",
  })
  customer: {
    id: number;
    name: string;
    email: string;
  };

  @ApiProperty({
    example: {
      full_name: "John Doe",
      street_and_number: "Main Street 123",
      zip_code: "12345",
      city: "Berlin",
      country: "Germany",
    },
    description: "Order billing",
  })
  billing: {
    full_name: string;
    street_and_number: string;
    zip_code: string;
    city: string;
    country: string;
  };
}
