import { Body, Controller, Post } from "@nestjs/common";
import { ApiOperation, ApiTags } from "@nestjs/swagger";
import { PurchaseDto } from "./dto/purchase.dto";
import { PurchaseService } from "./purchase.service";
import { AuthenticatedUser, User } from "@/shared/auth/user.decorator";

@Controller("purchases")
@ApiTags("purchases")
export class PurchaseController {
  constructor(private readonly purchaseService: PurchaseService) {}

  @Post()
  @ApiOperation({ summary: "Purchase services" })
  create(@Body() data: PurchaseDto, @User() user: AuthenticatedUser) {
    return this.purchaseService.create(data, user);
  }
}
