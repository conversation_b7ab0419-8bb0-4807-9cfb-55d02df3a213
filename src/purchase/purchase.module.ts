import { Modu<PERSON> } from "@nestjs/common";
import { PurchaseController } from "./purchase.controller";
import { PurchaseService } from "./purchase.service";
import { CustomerIoModule } from "@/customer-io/customer-io.module";
import { HttpApiModule } from "@/http/http.module";
import { CertificateModule } from "@/certificate/certificate.module";
import { FileModule } from "@/file/file.module";
import { CouponModule } from "@/coupon/coupon.module";
import { CommissionModule } from "@/commission/commission.module";
import { ContractModule } from "@/contract/contract.module";

@Module({
  imports: [
    CustomerIoModule,
    HttpApiModule,
    CertificateModule,
    FileModule,
    CouponModule,
    CommissionModule,
    ContractModule,
  ],
  controllers: [PurchaseController],
  providers: [PurchaseService],
})
export class PurchaseModule {}
