import { Test, TestingModule } from "@nestjs/testing";
import { LicenseVolumeReportItemService } from "./license-volume-report-item.service";
import { DatabaseService } from "@/database/database.service";
import { CustomerIoService } from "@/customer-io/customer-io.service";
import { BadRequestException } from "@nestjs/common";
import { CreateLicenseVolumeReportItemDto } from "./dto/create-license-volume-report-item.dto";
import { UpdateLicenseVolumeReportItemValueDto } from "./dto/update-license-volume-report-item-value.dto";
import { AuthenticatedUser } from "@/shared/auth/user.decorator";
import { Role } from "@/shared/auth/role.enum";

describe("LicenseVolumeReportItemService", () => {
  let service: LicenseVolumeReportItemService;
  let databaseService: DatabaseService;
  let customerIoService: CustomerIoService;

  const mockDatabaseService = {
    licenseVolumeReportItem: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      updateMany: jest.fn(),
    },
    licenseVolumeReport: {
      findUnique: jest.fn(),
      findFirst: jest.fn(),
    },
    $transaction: jest.fn((callback) => callback(mockDatabaseService)),
  };

  const mockCustomerIoService = {
    processNewData: jest.fn(),
  };

  beforeAll(() => {
    jest.spyOn(console, "error").mockImplementation(() => {});
    jest.spyOn(console, "log").mockImplementation(() => {});
  });

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LicenseVolumeReportItemService,
        {
          provide: DatabaseService,
          useValue: mockDatabaseService,
        },
        {
          provide: CustomerIoService,
          useValue: mockCustomerIoService,
        },
      ],
    }).compile();

    service = module.get<LicenseVolumeReportItemService>(LicenseVolumeReportItemService);
    databaseService = module.get<DatabaseService>(DatabaseService);
    customerIoService = module.get<CustomerIoService>(CustomerIoService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("create", () => {
    it("should create a new license volume report item", async () => {
      const createDto: CreateLicenseVolumeReportItemDto = {
        license_volume_report_id: 1,
        setup_fraction_id: 1,
        setup_column_id: 1,
        value: 100,
        setup_column_code: "123",
        setup_fraction_code: "123",
      };

      const expectedResult = {
        id: 1,
        ...createDto,
      };

      mockDatabaseService.licenseVolumeReportItem.create.mockResolvedValue(expectedResult);

      const result = await service.create(createDto);

      expect(result).toEqual(expectedResult);
      expect(mockDatabaseService.licenseVolumeReportItem.create).toHaveBeenCalledWith({
        data: createDto,
      });
    });
  });

  describe("findAll", () => {
    it("should return all non-deleted items for a specific report", async () => {
      const expectedItems = [
        { id: 1, value: 100 },
        { id: 2, value: 200 },
      ];

      const user: AuthenticatedUser = {
        email: "<EMAIL>",
        id: "1",
        role: Role.ADMIN,
      };

      // Mock para o licenseVolumeReport que é consultado no método findAll
      const mockLicenseVolumeReport = {
        id: 1,
        packaging_service: {
          id: 200,
          license: {
            id: 300,
            contract: {
              id: 400,
              customer_id: 1,
            },
          },
        },
      };

      // Esse é o mock correto que precisamos configurar
      mockDatabaseService.licenseVolumeReport.findFirst.mockResolvedValue(mockLicenseVolumeReport);

      // Mock para o resultado final da consulta findMany
      mockDatabaseService.licenseVolumeReportItem.findMany.mockResolvedValue(expectedItems);

      const result = await service.findAll({ license_volume_report_id: 1 }, user);

      expect(result).toEqual(expectedItems);
      expect(mockDatabaseService.licenseVolumeReportItem.findMany).toHaveBeenCalledWith({
        where: { deleted_at: null, license_volume_report_id: 1 },
      });
    });
  });

  describe("updateBulkValues", () => {
    it("should throw BadRequestException when no items provided", async () => {
      await expect(service.updateBulkValues([])).rejects.toThrow(new BadRequestException("No items to update"));
    });

    it("should throw BadRequestException when missing volume_report_item_id", async () => {
      const updateDtos: UpdateLicenseVolumeReportItemValueDto[] = [
        {
          license_volume_report_id: 1,
          volume_report_item_id: undefined as any,
          value: 100,
        },
      ];

      await expect(service.updateBulkValues(updateDtos)).rejects.toThrow(
        new BadRequestException("Missing volume_report_item_id in some items")
      );
    });

    it("should throw BadRequestException when invalid value provided", async () => {
      const updateDtos: UpdateLicenseVolumeReportItemValueDto[] = [
        {
          license_volume_report_id: 1,
          volume_report_item_id: 1,
          value: -1,
        },
      ];

      await expect(service.updateBulkValues(updateDtos)).rejects.toThrow(
        new BadRequestException("Invalid value in some items")
      );
    });

    it("should update multiple items in a transaction", async () => {
      const updateDtos: UpdateLicenseVolumeReportItemValueDto[] = [
        {
          license_volume_report_id: 1,
          volume_report_item_id: 1,
          value: 100,
        },
      ];

      await service.updateBulkValues(updateDtos);

      expect(mockDatabaseService.$transaction).toHaveBeenCalled();
      expect(mockDatabaseService.licenseVolumeReportItem.update).toHaveBeenCalled();
    });
  });
});
