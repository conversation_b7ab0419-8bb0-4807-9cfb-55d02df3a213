// customer-phone.controller.ts
import { Controller, Post, Body, Get, Param, Put, Delete } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from "@nestjs/swagger";
import { CustomerPhoneService } from "./customer-phone.service";
import { CreateCustomerPhoneDto } from "./dto/create-customer-phone.dto";
import { UpdateCustomerPhoneDto } from "./dto/update-customer-phone.dto";
import { Roles } from "src/shared/auth/role.decorator";
import { Role } from "src/shared/auth/role.enum";
import { AuthenticatedUser, User } from "@/shared/auth/user.decorator";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK, Role.CUSTOMER)
@ApiTags("customer-phone")
@Controller("customer-phone")
export class CustomerPhoneController {
  constructor(private readonly customerPhoneService: CustomerPhoneService) {}

  @Post()
  @ApiOperation({ summary: "Create customer phone" })
  @ApiResponse({
    status: 201,
    description: "The customer phone has been successfully created.",
  })
  @ApiBody({ type: CreateCustomerPhoneDto })
  create(@Body() createCustomerPhoneDto: CreateCustomerPhoneDto) {
    return this.customerPhoneService.create(createCustomerPhoneDto);
  }

  @Get()
  @ApiOperation({ summary: "Get all customer phones" })
  @ApiResponse({ status: 200, description: "List of customer phones" })
  findAll() {
    return this.customerPhoneService.findAll();
  }

  @Get(":id")
  @ApiOperation({ summary: "Get customer phone by id" })
  @ApiResponse({ status: 200, description: "The customer phone details" })
  findOne(@Param("id") id: string, @User() user: AuthenticatedUser) {
    return this.customerPhoneService.findOne(Number(id), user);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update customer phone by id" })
  @ApiResponse({
    status: 200,
    description: "The customer phone has been successfully updated.",
  })
  @ApiBody({ type: UpdateCustomerPhoneDto })
  update(
    @Param("id") id: string,
    @Body() updateCustomerPhoneDto: UpdateCustomerPhoneDto,
    @User() user: AuthenticatedUser
  ) {
    return this.customerPhoneService.update(Number(id), updateCustomerPhoneDto, user);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete customer phone by id" })
  @ApiResponse({
    status: 200,
    description: "The customer phone has been successfully deleted.",
  })
  remove(@Param("id") id: string, @User() user: AuthenticatedUser) {
    return this.customerPhoneService.remove(Number(id), user);
  }
}
