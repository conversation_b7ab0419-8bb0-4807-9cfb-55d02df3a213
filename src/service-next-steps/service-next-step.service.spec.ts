import { Test, TestingModule } from "@nestjs/testing";
import { ServiceNextStepService } from "./service-next-step.service";
import { DatabaseService } from "@/database/database.service";
import { BadRequestException, NotFoundException } from "@nestjs/common";
import { CreateServiceNextStepDto } from "./dto/create-service-next-step.dto";
import { UpdateServiceNextStepDto } from "./dto/update-service-next-step.dto";
import { AuthenticatedUser } from "@/shared/auth/user.decorator";
import { Role } from "@/shared/auth/role.enum";

describe("ServiceNextStepService", () => {
  let service: ServiceNextStepService;
  let databaseService: DatabaseService;

  const mockDatabaseService = {
    serviceNextStep: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ServiceNextStepService,
        {
          provide: DatabaseService,
          useValue: mockDatabaseService,
        },
      ],
    }).compile();

    service = module.get<ServiceNextStepService>(ServiceNextStepService);
    databaseService = module.get<DatabaseService>(DatabaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("findAll", () => {
    it("must return all next steps for a license", async () => {
      const expectedSteps = [
        {
          id: 1,
          license_id: 1,
          title: "Next Step 1",
          license: { id: 1, name: "Test License" },
        },
      ];

      mockDatabaseService.serviceNextStep.findMany.mockResolvedValue(expectedSteps);

      const result = await service.findAll({ license_id: 1 });

      expect(result).toEqual(expectedSteps);
      expect(mockDatabaseService.serviceNextStep.findMany).toHaveBeenCalledWith({
        where: {
          deleted_at: null,
          license_id: 1,
        },
        include: {
          license: true,
        },
      });
    });

    it("should return all next steps to an action guide", async () => {
      const expectedSteps = [
        {
          id: 1,
          action_guide_id: 1,
          title: "Next Step 1",
        },
      ];

      mockDatabaseService.serviceNextStep.findMany.mockResolvedValue(expectedSteps);

      const result = await service.findAll({ action_guide_id: 1 });

      expect(result).toEqual(expectedSteps);
      expect(mockDatabaseService.serviceNextStep.findMany).toHaveBeenCalledWith({
        where: {
          deleted_at: null,
          action_guide_id: 1,
        },
        include: {
          license: true,
        },
      });
    });
  });

  describe("findOne", () => {
    const user: AuthenticatedUser = {
      email: "<EMAIL>",
      id: "1",
      role: Role.ADMIN,
    };

    it("should throw BadRequestException for invalid ID", async () => {
      await expect(service.findOne(NaN, user)).rejects.toThrow(
        new BadRequestException("Service Next Step ID is invalid")
      );
    });

    it("should throw NotFoundException when next step is not found", async () => {
      mockDatabaseService.serviceNextStep.findUnique.mockResolvedValue(null);

      await expect(service.findOne(1, user)).rejects.toThrow(new NotFoundException("Service Next Step not found"));
    });

    it("must return a specific next step", async () => {
      const expectedStep = {
        id: 1,
        license_id: 1,
        title: "Next Step 1",
        license: { id: 1, name: "Test License" },
      };

      mockDatabaseService.serviceNextStep.findUnique.mockResolvedValue(expectedStep);

      jest.spyOn(service, "validatingUserPermissionServiceNextSteps").mockImplementation(async () => {
        return Promise.resolve();
      });

      const result = await service.findOne(1, user);

      expect(result).toEqual(expectedStep);
      expect(mockDatabaseService.serviceNextStep.findUnique).toHaveBeenCalledWith({
        where: { id: 1, deleted_at: null },
        include: {
          license: true,
        },
      });
    });
  });

  describe("create", () => {
    it("should throw BadRequestException when neither license_id nor action_guide_id are provided", async () => {
      const createDto: CreateServiceNextStepDto = {
        title: "Novo Passo",
        available_date: "2024-01-01",
        deadline_date: "2024-02-01",
        done_at: "2024-01-15",
      };

      await expect(service.create(createDto)).rejects.toThrow(
        new BadRequestException("License or Action Guide ID is required")
      );
    });

    it("must create a new next step", async () => {
      const createDto: CreateServiceNextStepDto = {
        license_id: 1,
        title: "New Step",
        available_date: "2024-01-01",
        deadline_date: "2024-02-01",
        done_at: "2024-01-15",
      };

      const expectedStep = {
        id: 1,
        ...createDto,
        created_at: expect.any(Date),
        updated_at: expect.any(Date),
      };

      mockDatabaseService.serviceNextStep.create.mockResolvedValue(expectedStep);

      const result = await service.create(createDto);

      expect(result).toEqual(expectedStep);
      expect(mockDatabaseService.serviceNextStep.create).toHaveBeenCalledWith({
        data: {
          license_id: createDto.license_id,
          action_guide_id: null,
          title: createDto.title,
          available_date: createDto.available_date,
          deadline_date: createDto.deadline_date,
          done_at: createDto.done_at,
          created_at: expect.any(Date),
          updated_at: expect.any(Date),
        },
      });
    });
  });

  describe("update", () => {
    const user: AuthenticatedUser = {
      email: "<EMAIL>",
      id: "1",
      role: Role.ADMIN,
    };

    it("should throw BadRequestException for invalid ID", async () => {
      const updateDto: UpdateServiceNextStepDto = {
        title: "Updated step",
      };

      await expect(service.update(NaN, updateDto, user)).rejects.toThrow(
        new BadRequestException("Service Next Step ID is invalid")
      );
    });

    it("should throw NotFoundException when next step is not found", async () => {
      mockDatabaseService.serviceNextStep.findUnique.mockResolvedValue(null);

      const updateDto: UpdateServiceNextStepDto = {
        title: "Updated step",
      };

      await expect(service.update(1, updateDto, user)).rejects.toThrow(
        new NotFoundException("Service Next Step not found")
      );
    });

    it("should update a next step", async () => {
      const existingStep = {
        id: 1,
        title: "Original step",
      };

      const updateDto: UpdateServiceNextStepDto = {
        title: "Updated step",
        available_date: "2024-02-01",
      };

      mockDatabaseService.serviceNextStep.findUnique.mockResolvedValue(existingStep);
      mockDatabaseService.serviceNextStep.update.mockResolvedValue({
        ...existingStep,
        ...updateDto,
      });

      jest.spyOn(service, "validatingUserPermissionServiceNextSteps").mockImplementation(async () => {
        return Promise.resolve();
      });

      const result = await service.update(1, updateDto, user);

      expect(result).toEqual({
        ...existingStep,
        ...updateDto,
      });
      expect(mockDatabaseService.serviceNextStep.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: {
          ...updateDto,
          updated_at: expect.any(Date),
        },
      });
    });
  });

  describe("remove", () => {
    const user: AuthenticatedUser = {
      email: "<EMAIL>",
      id: "1",
      role: Role.ADMIN,
    };

    it("must mark a next step as deleted", async () => {
      const expectedResult = {
        id: 1,
        deleted_at: expect.any(Date),
      };

      mockDatabaseService.serviceNextStep.update.mockResolvedValue(expectedResult);
      jest.spyOn(service, "validatingUserPermissionServiceNextSteps").mockImplementation(async () => {
        return Promise.resolve();
      });

      const result = await service.remove(1, user);

      expect(result).toEqual(expectedResult);
      expect(mockDatabaseService.serviceNextStep.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: {
          deleted_at: expect.any(Date),
        },
      });
    });
  });
});
