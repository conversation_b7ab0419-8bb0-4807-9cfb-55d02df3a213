import { HttpException, Injectable } from "@nestjs/common";
import { DatabaseService } from "@/database/database.service";
import { HttpModuleService } from "@/http/http.service";
import { lastValueFrom } from "rxjs";
import { Prisma } from "@prisma/client";

@Injectable()
export class CustomerInviteTokenService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly httpModuleService: HttpModuleService
  ) {}

  private createToken(customerId: number): string {
    return `LIZ-${customerId}`;
  }

  private createShareLink(customerId: number): string {
    return `/shop-invite/${customerId}`;
  }

  async create(customerId: number, tx?: Prisma.TransactionClient) {
    const date = new Date();
    const expirationDate = new Date();
    expirationDate.setFullYear(expirationDate.getFullYear() + 1);
    const code = this.createToken(customerId);

    const prismaClient = tx || this.databaseService;

    const customerInviteToken = await prismaClient.customerInviteToken.create({
      data: {
        customer_id: customerId,
        token: code,
        share_link: this.createShareLink(customerId),
        expiration_date: expirationDate,
        created_at: date,
        updated_at: date,
      },
    });

    lastValueFrom(
      this.httpModuleService.payments({
        url: "/coupon",
        params: {
          code,
          discount: 360,
          expires_at: expirationDate,
          max_uses: 100,
          discount_type: `AMOUNT`,
          customer_creator_id: customerId,
          type_creation: `CUSTOMER`,
        },
        method: "POST",
      })
    ).catch((error) => {
      console.log(error);
      throw new HttpException(
        error?.response?.message || "Error when creating customer coupon",
        error?.response?.statusCode || 400
      );
    });

    return customerInviteToken;
  }
}
