import { PrismaClient, CommissionType, CommissionServiceType, CommissionUserType } from "@prisma/client";

const prisma = new PrismaClient();

async function main() {
  // Get existing partners
  const partners = await prisma.partner.findMany({
    take: 2,
    orderBy: {
      id: "desc",
    },
  });

  if (partners.length < 2) {
    throw new Error("Need at least 2 existing partners to run this seed");
  }

  const partner1 = partners[0];
  const partner2 = partners[1];

  // Create test customers
  const customers = await Promise.all([
    prisma.customer.create({
      data: {
        first_name: "<PERSON>",
        last_name: "<PERSON>",
        email: "<EMAIL>",
        user_id: 2001,
        is_active: true,
        created_at: new Date(),
        updated_at: new Date(),
      },
    }),
    prisma.customer.create({
      data: {
        first_name: "<PERSON>",
        last_name: "<PERSON>",
        email: "<EMAIL>",
        user_id: 2002,
        is_active: true,
        created_at: new Date(),
        updated_at: new Date(),
      },
    }),
    prisma.customer.create({
      data: {
        first_name: "<PERSON>",
        last_name: "<PERSON>",
        email: "<EMAIL>",
        user_id: 2003,
        is_active: true,
        created_at: new Date(),
        updated_at: new Date(),
      },
    }),
  ]);

  // Create test coupons
  const coupons = await Promise.all([
    prisma.coupon.create({
      data: {
        code: "PARTNER1_10",
        description: "Partner 1's 10% discount",
        discount_type: "PERCENTAGE",
        value: 10,
        commission_percentage: 10,
        start_date: new Date("2023-01-01"),
        end_date: new Date("2027-12-31"),
        is_active: true,
        mode: "GENERAL",
        type: "CUSTOMER",
      },
    }),
    prisma.coupon.create({
      data: {
        code: "PARTNER2_15",
        description: "Partner 2's 15% discount",
        discount_type: "PERCENTAGE",
        value: 15,
        commission_percentage: 15,
        start_date: new Date("2023-01-01"),
        end_date: new Date("2027-12-31"),
        is_active: true,
        mode: "GENERAL",
        type: "CUSTOMER",
      },
    }),
  ]);

  // Link partners to coupons
  await Promise.all([
    prisma.couponPartners.create({
      data: {
        coupon_id: coupons[0].id,
        partner_id: partner1.id,
      },
    }),
    prisma.couponPartners.create({
      data: {
        coupon_id: coupons[1].id,
        partner_id: partner2.id,
      },
    }),
  ]);

  // Create commissions for different quarters and service types
  const commissionData = [
    // 2023 Commissions
    // Q1 2023 - Link based commission
    {
      user_id: partner1.user_id,
      user_type: CommissionUserType.PARTNER,
      commission_percentage: partner1.commission_percentage || 10,
      commission_value: 120000, // 1200 EUR
      type: CommissionType.AFFILIATE_LINK,
      affiliate_link: "https://example.com/partner1/ref123",
      service_type: CommissionServiceType.EU_LICENSE,
      order_id: 1,
      order_customer_id: customers[0].id,
      created_at: new Date("2023-03-15"),
    },
    // Q1 2023 - Coupon based commission
    {
      user_id: partner1.user_id,
      user_type: CommissionUserType.PARTNER,
      commission_percentage: partner1.commission_percentage || 10,
      commission_value: 100000, // 1000 EUR
      type: CommissionType.COUPON,
      coupon_id: coupons[0].id,
      coupon_code: coupons[0].code,
      service_type: CommissionServiceType.EU_LICENSE,
      order_id: 2,
      order_customer_id: customers[0].id,
      created_at: new Date("2023-01-15"),
    },
    {
      user_id: partner1.user_id,
      user_type: CommissionUserType.PARTNER,
      commission_percentage: partner1.commission_percentage || 10,
      commission_value: 150000, // 1500 EUR
      type: CommissionType.COUPON,
      coupon_id: coupons[0].id,
      coupon_code: coupons[0].code,
      service_type: CommissionServiceType.DIRECT_LICENSE,
      order_id: 3,
      order_customer_id: customers[1].id,
      created_at: new Date("2023-02-20"),
    },
    // Q2 2023 - Link based commission
    {
      user_id: partner2.user_id,
      user_type: CommissionUserType.PARTNER,
      commission_percentage: partner2.commission_percentage || 15,
      commission_value: 180000, // 1800 EUR
      type: CommissionType.AFFILIATE_LINK,
      affiliate_link: "https://example.com/partner2/ref456",
      service_type: CommissionServiceType.DIRECT_LICENSE,
      order_id: 4,
      order_customer_id: customers[1].id,
      created_at: new Date("2023-05-20"),
    },
    // Q2 2023 - Coupon based commission
    {
      user_id: partner1.user_id,
      user_type: CommissionUserType.PARTNER,
      commission_percentage: partner1.commission_percentage || 10,
      commission_value: 200000, // 2000 EUR
      type: CommissionType.COUPON,
      coupon_id: coupons[0].id,
      coupon_code: coupons[0].code,
      service_type: CommissionServiceType.ACTION_GUIDE,
      order_id: 5,
      order_customer_id: customers[2].id,
      created_at: new Date("2023-04-10"),
    },
    // Q3 2023 - Link based commission
    {
      user_id: partner2.user_id,
      user_type: CommissionUserType.PARTNER,
      commission_percentage: partner2.commission_percentage || 15,
      commission_value: 300000, // 3000 EUR
      type: CommissionType.AFFILIATE_LINK,
      affiliate_link: "https://example.com/partner2/ref789",
      service_type: CommissionServiceType.EU_LICENSE,
      order_id: 6,
      order_customer_id: customers[0].id,
      created_at: new Date("2023-07-05"),
    },
    // Q3 2023 - Coupon based commission
    {
      user_id: partner2.user_id,
      user_type: CommissionUserType.PARTNER,
      commission_percentage: partner2.commission_percentage || 15,
      commission_value: 250000, // 2500 EUR
      type: CommissionType.COUPON,
      coupon_id: coupons[1].id,
      coupon_code: coupons[1].code,
      service_type: CommissionServiceType.DIRECT_LICENSE,
      order_id: 7,
      order_customer_id: customers[1].id,
      created_at: new Date("2023-10-15"),
    },
    // Q1 2024 - Link based commission
    {
      user_id: partner1.user_id,
      user_type: CommissionUserType.PARTNER,
      commission_percentage: partner1.commission_percentage || 10,
      commission_value: 220000, // 2200 EUR
      type: CommissionType.AFFILIATE_LINK,
      affiliate_link: "https://example.com/partner1/ref101",
      service_type: CommissionServiceType.ACTION_GUIDE,
      order_id: 8,
      order_customer_id: customers[2].id,
      created_at: new Date("2024-01-20"),
    },
    // Q1 2024 - Coupon based commission
    {
      user_id: partner1.user_id,
      user_type: CommissionUserType.PARTNER,
      commission_percentage: partner1.commission_percentage || 10,
      commission_value: 180000, // 1800 EUR
      type: CommissionType.COUPON,
      coupon_id: coupons[0].id,
      coupon_code: coupons[0].code,
      service_type: CommissionServiceType.ACTION_GUIDE,
      order_id: 9,
      order_customer_id: customers[2].id,
      created_at: new Date("2024-01-20"),
    },

    // 2025 Commissions
    // Q1 2025 - Link based commission
    {
      user_id: partner2.user_id,
      user_type: CommissionUserType.PARTNER,
      commission_percentage: partner2.commission_percentage || 15,
      commission_value: 280000, // 2800 EUR
      type: CommissionType.AFFILIATE_LINK,
      affiliate_link: "https://example.com/partner2/ref2025-1",
      service_type: CommissionServiceType.EU_LICENSE,
      order_id: 10,
      order_customer_id: customers[0].id,
      created_at: new Date("2025-02-15"),
    },
    // Q2 2025 - Coupon based commission
    {
      user_id: partner1.user_id,
      user_type: CommissionUserType.PARTNER,
      commission_percentage: partner1.commission_percentage || 10,
      commission_value: 320000, // 3200 EUR
      type: CommissionType.COUPON,
      coupon_id: coupons[0].id,
      coupon_code: coupons[0].code,
      service_type: CommissionServiceType.DIRECT_LICENSE,
      order_id: 11,
      order_customer_id: customers[1].id,
      created_at: new Date("2025-05-10"),
    },
    // Q3 2025 - Link based commission
    {
      user_id: partner2.user_id,
      user_type: CommissionUserType.PARTNER,
      commission_percentage: partner2.commission_percentage || 15,
      commission_value: 350000, // 3500 EUR
      type: CommissionType.AFFILIATE_LINK,
      affiliate_link: "https://example.com/partner2/ref2025-2",
      service_type: CommissionServiceType.ACTION_GUIDE,
      order_id: 12,
      order_customer_id: customers[2].id,
      created_at: new Date("2025-08-20"),
    },
    // Q4 2025 - Coupon based commission
    {
      user_id: partner1.user_id,
      user_type: CommissionUserType.PARTNER,
      commission_percentage: partner1.commission_percentage || 10,
      commission_value: 290000, // 2900 EUR
      type: CommissionType.COUPON,
      coupon_id: coupons[0].id,
      coupon_code: coupons[0].code,
      service_type: CommissionServiceType.EU_LICENSE,
      order_id: 13,
      order_customer_id: customers[0].id,
      created_at: new Date("2025-11-15"),
    },

    // 2026 Commissions
    // Q1 2026 - Link based commission
    {
      user_id: partner1.user_id,
      user_type: CommissionUserType.PARTNER,
      commission_percentage: partner1.commission_percentage || 10,
      commission_value: 380000, // 3800 EUR
      type: CommissionType.AFFILIATE_LINK,
      affiliate_link: "https://example.com/partner1/ref2026-1",
      service_type: CommissionServiceType.DIRECT_LICENSE,
      order_id: 14,
      order_customer_id: customers[1].id,
      created_at: new Date("2026-01-25"),
    },
    // Q2 2026 - Coupon based commission
    {
      user_id: partner2.user_id,
      user_type: CommissionUserType.PARTNER,
      commission_percentage: partner2.commission_percentage || 15,
      commission_value: 420000, // 4200 EUR
      type: CommissionType.COUPON,
      coupon_id: coupons[1].id,
      coupon_code: coupons[1].code,
      service_type: CommissionServiceType.ACTION_GUIDE,
      order_id: 15,
      order_customer_id: customers[2].id,
      created_at: new Date("2026-04-15"),
    },
    // Q3 2026 - Link based commission
    {
      user_id: partner1.user_id,
      user_type: CommissionUserType.PARTNER,
      commission_percentage: partner1.commission_percentage || 10,
      commission_value: 450000, // 4500 EUR
      type: CommissionType.AFFILIATE_LINK,
      affiliate_link: "https://example.com/partner1/ref2026-2",
      service_type: CommissionServiceType.EU_LICENSE,
      order_id: 16,
      order_customer_id: customers[0].id,
      created_at: new Date("2026-07-10"),
    },
    // Q4 2026 - Coupon based commission
    {
      user_id: partner2.user_id,
      user_type: CommissionUserType.PARTNER,
      commission_percentage: partner2.commission_percentage || 15,
      commission_value: 480000, // 4800 EUR
      type: CommissionType.COUPON,
      coupon_id: coupons[1].id,
      coupon_code: coupons[1].code,
      service_type: CommissionServiceType.DIRECT_LICENSE,
      order_id: 17,
      order_customer_id: customers[1].id,
      created_at: new Date("2026-10-20"),
    },

    // 2027 Commissions
    // Q1 2027 - Link based commission
    {
      user_id: partner2.user_id,
      user_type: CommissionUserType.PARTNER,
      commission_percentage: partner2.commission_percentage || 15,
      commission_value: 520000, // 5200 EUR
      type: CommissionType.AFFILIATE_LINK,
      affiliate_link: "https://example.com/partner2/ref2027-1",
      service_type: CommissionServiceType.ACTION_GUIDE,
      order_id: 18,
      order_customer_id: customers[2].id,
      created_at: new Date("2027-02-15"),
    },
    // Q2 2027 - Coupon based commission
    {
      user_id: partner1.user_id,
      user_type: CommissionUserType.PARTNER,
      commission_percentage: partner1.commission_percentage || 10,
      commission_value: 550000, // 5500 EUR
      type: CommissionType.COUPON,
      coupon_id: coupons[0].id,
      coupon_code: coupons[0].code,
      service_type: CommissionServiceType.EU_LICENSE,
      order_id: 19,
      order_customer_id: customers[0].id,
      created_at: new Date("2027-05-10"),
    },
    // Q3 2027 - Link based commission
    {
      user_id: partner2.user_id,
      user_type: CommissionUserType.PARTNER,
      commission_percentage: partner2.commission_percentage || 15,
      commission_value: 580000, // 5800 EUR
      type: CommissionType.AFFILIATE_LINK,
      affiliate_link: "https://example.com/partner2/ref2027-2",
      service_type: CommissionServiceType.DIRECT_LICENSE,
      order_id: 20,
      order_customer_id: customers[1].id,
      created_at: new Date("2027-08-20"),
    },
    // Q4 2027 - Coupon based commission
    {
      user_id: partner1.user_id,
      user_type: CommissionUserType.PARTNER,
      commission_percentage: partner1.commission_percentage || 10,
      commission_value: 620000, // 6200 EUR
      type: CommissionType.COUPON,
      coupon_id: coupons[0].id,
      coupon_code: coupons[0].code,
      service_type: CommissionServiceType.ACTION_GUIDE,
      order_id: 21,
      order_customer_id: customers[2].id,
      created_at: new Date("2027-11-15"),
    },
  ];

  // Create all commissions
  await prisma.commission.createMany({
    data: commissionData,
  });

  console.log("Commission seed completed successfully");
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
