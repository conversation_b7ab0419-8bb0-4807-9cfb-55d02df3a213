/*
  Warnings:

  - You are about to drop the column `report_set__column_id` on the `report_set_column_fraction` table. All the data in the column will be lost.
  - You are about to drop the column `report_set_fraction_id` on the `report_set_column_fraction` table. All the data in the column will be lost.
  - Added the required column `column_id` to the `report_set_column_fraction` table without a default value. This is not possible if the table is not empty.
  - Added the required column `fraction_id` to the `report_set_column_fraction` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "report_set_column_fraction" DROP CONSTRAINT "report_set_column_fraction_report_set__column_id_fkey";

-- DropForeignKey
ALTER TABLE "report_set_column_fraction" DROP CONSTRAINT "report_set_column_fraction_report_set_fraction_id_fkey";

-- AlterTable
ALTER TABLE "report_set_column_fraction" DROP COLUMN "report_set__column_id",
DROP COLUMN "report_set_fraction_id",
ADD COLUMN     "column_id" INTEGER NOT NULL,
ADD COLUMN     "fraction_id" INTEGER NOT NULL;

-- AddForeignKey
ALTER TABLE "report_set_column_fraction" ADD CONSTRAINT "report_set_column_fraction_column_id_fkey" FOREIGN KEY ("column_id") REFERENCES "report_set_column"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "report_set_column_fraction" ADD CONSTRAINT "report_set_column_fraction_fraction_id_fkey" FOREIGN KEY ("fraction_id") REFERENCES "report_set_fraction"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
