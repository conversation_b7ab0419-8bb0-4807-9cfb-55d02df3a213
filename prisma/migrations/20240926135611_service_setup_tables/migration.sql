/*
  Warnings:

  - You are about to drop the column `control` on the `criteria` table. All the data in the column will be lost.
  - You are about to drop the column `criteria_type` on the `criteria` table. All the data in the column will be lost.
  - You are about to drop the column `packaging_id` on the `criteria` table. All the data in the column will be lost.
  - You are about to drop the column `service_type` on the `criteria` table. All the data in the column will be lost.
  - You are about to drop the `packaging` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `code` to the `country` table without a default value. This is not possible if the table is not empty.
  - Added the required column `flag_url` to the `country` table without a default value. This is not possible if the table is not empty.
  - Added the required column `mode` to the `criteria` table without a default value. This is not possible if the table is not empty.
  - Added the required column `type` to the `criteria` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `criteria` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "report_set_type" AS ENUM ('FRACTIONS', 'CATEGORIES');

-- CreateEnum
CREATE TYPE "report_set_mode" AS ENUM ('ON_PLATAFORM', 'BY_EXCEL');

-- CreateEnum
CREATE TYPE "report_set_rhythm" AS ENUM ('ANNUAL', 'MONTHLY', 'QUARTERLY');

-- CreateEnum
CREATE TYPE "required_information_type" AS ENUM ('TEXT', 'NUMBER', 'DOCUMENT', 'FILE', 'IMAGE');

-- CreateEnum
CREATE TYPE "price_list_type" AS ENUM ('EU_LICENSE', 'DIRECT_LICENSE', 'ACTION_GUIDE');

-- CreateEnum
CREATE TYPE "criteria_mode" AS ENUM ('COMMITMENT', 'CALCULATOR');

-- CreateEnum
CREATE TYPE "criteria_type" AS ENUM ('OBLIGATION', 'REPORT_SET', 'AUTHORIZE_REPRESENTATIVE', 'REPRESENTATIVE_TIER', 'REPORT_FREQUENCY', 'OTHER_COSTS', 'PRICE_LIST');

-- CreateEnum
CREATE TYPE "criteria_input_type" AS ENUM ('RADIO');

-- AlterTable
ALTER TABLE "country" ADD COLUMN     "authorize_representative_obligated" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "code" TEXT NOT NULL,
ADD COLUMN     "flag_url" TEXT NOT NULL,
ADD COLUMN     "other_costs_obligated" BOOLEAN NOT NULL DEFAULT false;

-- AlterTable
ALTER TABLE "criteria" DROP COLUMN "control",
DROP COLUMN "criteria_type",
DROP COLUMN "packaging_id",
DROP COLUMN "service_type",
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "deleted_at" TIMESTAMP(3),
ADD COLUMN     "help_text" TEXT,
ADD COLUMN     "input_type" "criteria_input_type",
ADD COLUMN     "mode" "criteria_mode" NOT NULL,
ADD COLUMN     "reference_id" INTEGER,
ADD COLUMN     "title" TEXT,
ADD COLUMN     "type" "criteria_type" NOT NULL,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL;

-- DropTable
DROP TABLE "packaging";

-- CreateTable
CREATE TABLE "packaging_service" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "country_id" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "packaging_service_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "report_set" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "mode" "report_set_mode" NOT NULL,
    "type" "report_set_type" NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "report_set_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "report_set_frequency" (
    "id" SERIAL NOT NULL,
    "rhythm" "report_set_rhythm" NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "report_set_id" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "report_set_frequency_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "report_set_fraction" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "report_set_id" INTEGER NOT NULL,
    "parent_id" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "report_set_fraction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "report_set_fraction_column" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "report_set_id" INTEGER NOT NULL,
    "parent_id" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "report_set_fraction_column_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "required_information" (
    "id" SERIAL NOT NULL,
    "country_id" INTEGER NOT NULL,
    "type" "required_information_type" NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),
    "question" TEXT,
    "file_id" TEXT,

    CONSTRAINT "required_information_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "other_cost" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "price" INTEGER NOT NULL,
    "country_id" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "other_cost_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "representative_tier" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "price" INTEGER NOT NULL,
    "country_id" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "representative_tier_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "price_list" (
    "id" SERIAL NOT NULL,
    "type" "price_list_type" NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "start_date" TIMESTAMP(3) NOT NULL,
    "end_date" TIMESTAMP(3) NOT NULL,
    "basic_price" INTEGER,
    "minimum_price" INTEGER,
    "registration_fee" INTEGER,
    "variable_handling_fee" DOUBLE PRECISION,
    "price" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "price_list_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "country_price_list" (
    "id" SERIAL NOT NULL,
    "country_id" INTEGER NOT NULL,
    "price_list_id" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "country_price_list_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "criteria_option" (
    "id" SERIAL NOT NULL,
    "criteria_id" INTEGER NOT NULL,
    "label" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "criteria_option_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "required_information_file_id_key" ON "required_information"("file_id");

-- AddForeignKey
ALTER TABLE "packaging_service" ADD CONSTRAINT "packaging_service_country_id_fkey" FOREIGN KEY ("country_id") REFERENCES "country"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "report_set_frequency" ADD CONSTRAINT "report_set_frequency_report_set_id_fkey" FOREIGN KEY ("report_set_id") REFERENCES "report_set"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "report_set_fraction" ADD CONSTRAINT "report_set_fraction_report_set_id_fkey" FOREIGN KEY ("report_set_id") REFERENCES "report_set"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "report_set_fraction" ADD CONSTRAINT "report_set_fraction_parent_id_fkey" FOREIGN KEY ("parent_id") REFERENCES "report_set_fraction"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "report_set_fraction_column" ADD CONSTRAINT "report_set_fraction_column_report_set_id_fkey" FOREIGN KEY ("report_set_id") REFERENCES "report_set"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "report_set_fraction_column" ADD CONSTRAINT "report_set_fraction_column_parent_id_fkey" FOREIGN KEY ("parent_id") REFERENCES "report_set_fraction_column"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "required_information" ADD CONSTRAINT "required_information_file_id_fkey" FOREIGN KEY ("file_id") REFERENCES "files"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "required_information" ADD CONSTRAINT "required_information_country_id_fkey" FOREIGN KEY ("country_id") REFERENCES "country"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "other_cost" ADD CONSTRAINT "other_cost_country_id_fkey" FOREIGN KEY ("country_id") REFERENCES "country"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "representative_tier" ADD CONSTRAINT "representative_tier_country_id_fkey" FOREIGN KEY ("country_id") REFERENCES "country"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "country_price_list" ADD CONSTRAINT "country_price_list_country_id_fkey" FOREIGN KEY ("country_id") REFERENCES "country"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "country_price_list" ADD CONSTRAINT "country_price_list_price_list_id_fkey" FOREIGN KEY ("price_list_id") REFERENCES "price_list"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
