/*
  Warnings:

  - You are about to drop the column `report_set_id` on the `report_set_frequency` table. All the data in the column will be lost.
  - Added the required column `packaging_service_id` to the `report_set_frequency` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "report_set_frequency" DROP CONSTRAINT "report_set_frequency_report_set_id_fkey";

-- AlterTable
ALTER TABLE "report_set_frequency" DROP COLUMN "report_set_id",
ADD COLUMN     "packaging_service_id" INTEGER NOT NULL;

-- AddForeignKey
ALTER TABLE "report_set_frequency" ADD CONSTRAINT "report_set_frequency_packaging_service_id_fkey" FOREIGN KEY ("packaging_service_id") REFERENCES "packaging_service"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
