/*
  Warnings:

  - You are about to drop the column `column_id` on the `report_set_column_fraction` table. All the data in the column will be lost.
  - You are about to drop the column `fraction_id` on the `report_set_column_fraction` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[sheet_file_id]` on the table `report_set` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[code]` on the table `report_set_column` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[code]` on the table `report_set_fraction` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `code` to the `report_set_column` table without a default value. This is not possible if the table is not empty.
  - Added the required column `column_code` to the `report_set_column_fraction` table without a default value. This is not possible if the table is not empty.
  - Added the required column `fraction_code` to the `report_set_column_fraction` table without a default value. This is not possible if the table is not empty.
  - Added the required column `code` to the `report_set_fraction` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "report_set_price_list_type" AS ENUM ('FIXED_PRICE', 'PRICE_PER_CATEGORY', 'PRICE_PER_VOLUME_BASE_PRICE', 'PRICE_PER_VOLUME_MINIMUM_FEE');

-- CreateEnum
CREATE TYPE "required_information_kind" AS ENUM ('COUNTRY_INFORMATION', 'GENERAL_INFORMATION');

-- DropForeignKey
ALTER TABLE "report_set_column" DROP CONSTRAINT "report_set_column_parent_id_fkey";

-- DropForeignKey
ALTER TABLE "report_set_column_fraction" DROP CONSTRAINT "report_set_column_fraction_column_id_fkey";

-- DropForeignKey
ALTER TABLE "report_set_column_fraction" DROP CONSTRAINT "report_set_column_fraction_fraction_id_fkey";

-- DropForeignKey
ALTER TABLE "report_set_fraction" DROP CONSTRAINT "report_set_fraction_parent_id_fkey";

-- DropForeignKey
ALTER TABLE "required_information" DROP CONSTRAINT "required_information_country_id_fkey";

-- AlterTable
ALTER TABLE "country" ADD COLUMN     "is_published" BOOLEAN NOT NULL DEFAULT false;

-- AlterTable
ALTER TABLE "price_list" ADD COLUMN     "thresholds" JSONB;

-- AlterTable
ALTER TABLE "report_set" ADD COLUMN     "sheet_file_description" TEXT,
ADD COLUMN     "sheet_file_id" TEXT;

-- AlterTable
ALTER TABLE "report_set_column" ADD COLUMN     "code" TEXT NOT NULL,
ADD COLUMN     "level" INTEGER NOT NULL DEFAULT 1,
ADD COLUMN     "order" INTEGER NOT NULL DEFAULT 1,
ADD COLUMN     "parent_code" TEXT;

-- AlterTable
ALTER TABLE "report_set_column_fraction" DROP COLUMN "column_id",
DROP COLUMN "fraction_id",
ADD COLUMN     "column_code" TEXT NOT NULL,
ADD COLUMN     "fraction_code" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "report_set_fraction" ADD COLUMN     "code" TEXT NOT NULL,
ADD COLUMN     "fraction_icon_id" INTEGER,
ADD COLUMN     "level" INTEGER NOT NULL DEFAULT 1,
ADD COLUMN     "order" INTEGER NOT NULL DEFAULT 1,
ADD COLUMN     "parent_code" TEXT,
ALTER COLUMN "icon" SET DEFAULT 'aluminium';

-- AlterTable
ALTER TABLE "required_information" ADD COLUMN     "kind" "required_information_kind" NOT NULL DEFAULT 'COUNTRY_INFORMATION',
ALTER COLUMN "country_id" DROP NOT NULL;

-- CreateTable
CREATE TABLE "broker" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "phone" TEXT NOT NULL,
    "enroled_at" TIMESTAMP(3) NOT NULL,
    "company_name" TEXT NOT NULL,
    "vat" TEXT,
    "tax" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3),
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "broker_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "country_follower" (
    "id" SERIAL NOT NULL,
    "country_id" INTEGER NOT NULL,
    "user_id" INTEGER NOT NULL,
    "user_email" TEXT NOT NULL,
    "user_first_name" TEXT NOT NULL,
    "user_last_name" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "country_follower_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "fraction_icon" (
    "id" SERIAL NOT NULL,
    "file_id" TEXT NOT NULL,
    "image_url" TEXT NOT NULL DEFAULT '',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "fraction_icon_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "report_set_price_list" (
    "id" SERIAL NOT NULL,
    "report_set_id" INTEGER NOT NULL,
    "title" TEXT NOT NULL,
    "license_year" INTEGER NOT NULL DEFAULT 2025,
    "start_date" TIMESTAMP(3) NOT NULL,
    "end_date" TIMESTAMP(3) NOT NULL,
    "type" "report_set_price_list_type" NOT NULL,
    "fixed_price" INTEGER,
    "base_price" INTEGER,
    "minimum_fee" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "report_set_price_list_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "report_set_price_list_item" (
    "id" SERIAL NOT NULL,
    "price_list_id" INTEGER NOT NULL,
    "fraction_code" TEXT NOT NULL,
    "price" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "report_set_price_list_item_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "broker_email_key" ON "broker"("email");

-- CreateIndex
CREATE UNIQUE INDEX "fraction_icon_file_id_key" ON "fraction_icon"("file_id");

-- CreateIndex
CREATE UNIQUE INDEX "report_set_sheet_file_id_key" ON "report_set"("sheet_file_id");

-- CreateIndex
CREATE UNIQUE INDEX "report_set_column_code_key" ON "report_set_column"("code");

-- CreateIndex
CREATE UNIQUE INDEX "report_set_fraction_code_key" ON "report_set_fraction"("code");

-- AddForeignKey
ALTER TABLE "country_follower" ADD CONSTRAINT "country_follower_country_id_fkey" FOREIGN KEY ("country_id") REFERENCES "country"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "report_set" ADD CONSTRAINT "report_set_sheet_file_id_fkey" FOREIGN KEY ("sheet_file_id") REFERENCES "files"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "report_set_fraction" ADD CONSTRAINT "report_set_fraction_parent_code_fkey" FOREIGN KEY ("parent_code") REFERENCES "report_set_fraction"("code") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "report_set_fraction" ADD CONSTRAINT "report_set_fraction_fraction_icon_id_fkey" FOREIGN KEY ("fraction_icon_id") REFERENCES "fraction_icon"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "fraction_icon" ADD CONSTRAINT "fraction_icon_file_id_fkey" FOREIGN KEY ("file_id") REFERENCES "files"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "report_set_column" ADD CONSTRAINT "report_set_column_parent_code_fkey" FOREIGN KEY ("parent_code") REFERENCES "report_set_column"("code") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "report_set_column_fraction" ADD CONSTRAINT "report_set_column_fraction_column_code_fkey" FOREIGN KEY ("column_code") REFERENCES "report_set_column"("code") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "report_set_column_fraction" ADD CONSTRAINT "report_set_column_fraction_fraction_code_fkey" FOREIGN KEY ("fraction_code") REFERENCES "report_set_fraction"("code") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "report_set_price_list" ADD CONSTRAINT "report_set_price_list_report_set_id_fkey" FOREIGN KEY ("report_set_id") REFERENCES "report_set"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "report_set_price_list_item" ADD CONSTRAINT "report_set_price_list_item_price_list_id_fkey" FOREIGN KEY ("price_list_id") REFERENCES "report_set_price_list"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "report_set_price_list_item" ADD CONSTRAINT "report_set_price_list_item_fraction_code_fkey" FOREIGN KEY ("fraction_code") REFERENCES "report_set_fraction"("code") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "required_information" ADD CONSTRAINT "required_information_country_id_fkey" FOREIGN KEY ("country_id") REFERENCES "country"("id") ON DELETE SET NULL ON UPDATE CASCADE;
