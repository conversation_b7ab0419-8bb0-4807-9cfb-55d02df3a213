-- Create<PERSON>num
CREATE TYPE "shopping_cart_status" AS ENUM ('OPEN', 'PURCHASED');

-- C<PERSON><PERSON>num
CREATE TYPE "shopping_cart_journey" AS ENUM ('LONG', 'DIRECT_LICENSE', 'QUICK_LICENSE', 'Q<PERSON>CK_ACTION_GUIDE');

-- Create<PERSON>num
CREATE TYPE "type_use_coupon" AS ENUM ('LINK', 'WRITTEN');

-- CreateEnum
CREATE TYPE "enum_marketing_material_partner_restriction" AS ENUM ('ALL', 'CLUSTER', 'SPECIFIC');

-- CreateEnum
CREATE TYPE "ClusterStatus" AS ENUM ('ACTIVE', 'INACTIVE');

-- Create<PERSON>num
CREATE TYPE "CouponType" AS ENUM ('SYSTEM', 'CUSTOMER');

-- CreateEnum
CREATE TYPE "CouponMode" AS ENUM ('GENERAL', 'INDIVIDUAL', 'GROUP_SEGMENT');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "CouponDiscountType" AS ENUM ('PERCENTAGE', 'ABSOLUTE', 'BUY_X_PRODUCTS_GET_Y_PRODUCTS', 'BUY_X_PRODUCTS_GET_Y_DISCOUNT');

-- CreateEnum
CREATE TYPE "PartnerStatus" AS ENUM ('NO_UPDATES', 'IMPROVED_CONTRACT', 'DENIED_CONTRACT', 'REQUESTED_COMMISSION', 'CHANGED_INFORMATION');

-- CreateEnum
CREATE TYPE "enum_marketing_material_category" AS ENUM ('STANDARD', 'SPECIFIC_MATERIAL');

-- CreateEnum
CREATE TYPE "enum_customer_type" AS ENUM ('REGULAR', 'PREMIUM');

-- CreateEnum
CREATE TYPE "enum_service_step_type" AS ENUM ('LICENSE', 'ACTION_GUIDE');

-- CreateEnum
CREATE TYPE "enum_consent_type" AS ENUM ('ACCOUNT', 'PURCHASE');

-- CreateEnum
CREATE TYPE "enum_license_registration_status" AS ENUM ('PENDING', 'IN_REVIEW', 'REGISTRATION', 'DONE');

-- CreateEnum
CREATE TYPE "enum_license_clerk_control_status" AS ENUM ('PENDING', 'DONE');

-- CreateEnum
CREATE TYPE "enum_license_contract_status" AS ENUM ('ACTIVE', 'TERMINATION_PROCESS', 'TERMINATED');

-- CreateEnum
CREATE TYPE "enum_license_report_set_rhythm" AS ENUM ('ANNUALLY', 'MONTHLY', 'QUARTERLY');

-- CreateEnum
CREATE TYPE "enum_license_volume_report_status" AS ENUM ('NEW', 'DONE', 'DECLINED', 'APPROVED', 'OPEN');

-- CreateEnum
CREATE TYPE "enum_license_required_information_kind" AS ENUM ('REQUIRED_INFORMATION', 'GENERAL_INFORMATION');

-- CreateEnum
CREATE TYPE "enum_license_required_information_type" AS ENUM ('TEXT', 'NUMBER', 'DOCUMENT', 'FILE', 'IMAGE');

-- CreateEnum
CREATE TYPE "enum_license_required_information_status" AS ENUM ('NEW', 'DONE', 'DECLINED', 'APPROVED', 'OPEN');

-- CreateEnum
CREATE TYPE "DeclineReasonType" AS ENUM ('LICENSE_INFORMATION', 'LICENSE_VOLUME_REPORT');

-- CreateEnum
CREATE TYPE "LicenseThirdPartyInvoiceIssuer" AS ENUM ('THIRD_PARTY_DUAL_SYSTEM', 'OTHER_THIRD_PARTY');

-- CreateEnum
CREATE TYPE "enum_license_third_party_invoice_status" AS ENUM ('OPEN', 'PAYED', 'UNPROCESSED', 'CANCELLED');

-- CreateEnum
CREATE TYPE "enum_contract_type" AS ENUM ('EU_LICENSE', 'DIRECT_LICENSE', 'ACTION_GUIDE');

-- CreateEnum
CREATE TYPE "enum_contract_status" AS ENUM ('ACTIVE', 'TERMINATION_PROCESS', 'TERMINATED');

-- CreateEnum
CREATE TYPE "enum_certificate_status" AS ENUM ('AVAILABLE', 'NOT_AVAILABLE');

-- CreateEnum
CREATE TYPE "enum_termination_status" AS ENUM ('REQUESTED', 'COMPLETED', 'PENDING');

-- CreateEnum
CREATE TYPE "enum_general_information_type" AS ENUM ('TEXT', 'NUMBER', 'DOCUMENT', 'FILE', 'IMAGE');

-- CreateEnum
CREATE TYPE "enum_general_information_status" AS ENUM ('NEW', 'DONE', 'DECLINED', 'APPROVED', 'OPEN');

-- CreateEnum
CREATE TYPE "enum_file_type" AS ENUM ('GENERAL_INFORMATION', 'REQUIRED_INFORMATION', 'CONTRACT', 'CONTRACT_TERMINATION', 'LICENSE_CONTRACT', 'CERTIFICATE', 'INVOICE', 'PAYMENT', 'LICENSE_PROOF_OF_REGISTRATION', 'PROOF_OF_TERMINATION', 'THIRD_PARTY_INVOICE', 'MARKETING_MATERIAL', 'PARTNER_CONTRACT');

-- CreateEnum
CREATE TYPE "PartnerContractStatus" AS ENUM ('DRAFT', 'ACTIVE', 'EXPIRED', 'TERMINATED', 'TO_BE_SIGNED');

-- CreateEnum
CREATE TYPE "PartnerContractChangeType" AS ENUM ('EMAIL', 'NOTE');

-- CreateTable
CREATE TABLE "customer" (
    "id" SERIAL NOT NULL,
    "type" "enum_customer_type" NOT NULL DEFAULT 'REGULAR',
    "first_name" TEXT NOT NULL,
    "last_name" TEXT NOT NULL,
    "salutation" TEXT,
    "email" TEXT NOT NULL,
    "user_id" INTEGER NOT NULL,
    "is_active" BOOLEAN,
    "document_id" INTEGER,
    "id_stripe" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,
    "company_name" TEXT,

    CONSTRAINT "customer_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "customer_phone" (
    "id" SERIAL NOT NULL,
    "phone_number" TEXT NOT NULL,
    "customer_id" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,
    "phone_type" TEXT NOT NULL DEFAULT 'PHONE',

    CONSTRAINT "customer_phone_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "customer_invite_token" (
    "id" SERIAL NOT NULL,
    "token" TEXT NOT NULL,
    "share_link" TEXT NOT NULL,
    "customer_id" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,
    "expiration_date" DATE,

    CONSTRAINT "customer_invite_token_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "customer_tutorial" (
    "id" SERIAL NOT NULL,
    "customer_id" INTEGER NOT NULL,
    "service_type" "enum_contract_type" NOT NULL,
    "is_finished" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,

    CONSTRAINT "customer_tutorial_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "customer_invitation" (
    "id" SERIAL NOT NULL,
    "comission_date" TIMESTAMP(3) NOT NULL,
    "product" TEXT NOT NULL,
    "comission" DECIMAL(65,30) NOT NULL,
    "order_number" TEXT NOT NULL,
    "lead_source" TEXT NOT NULL,
    "customer_id" INTEGER NOT NULL,
    "invited_customer_id" INTEGER,

    CONSTRAINT "customer_invitation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "customer_document" (
    "id" SERIAL NOT NULL,
    "customer_id" INTEGER NOT NULL,
    "document_url" TEXT NOT NULL,
    "status" TEXT NOT NULL,

    CONSTRAINT "customer_document_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "company" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "vat" TEXT,
    "tin" TEXT,
    "lucid" TEXT,
    "customer_id" INTEGER,
    "starting" TIMESTAMP(3),
    "website" TEXT,
    "partner_id" INTEGER,
    "address_id" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,
    "industry_sector" TEXT,
    "owner_name" TEXT,

    CONSTRAINT "company_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "company_address" (
    "id" SERIAL NOT NULL,
    "country_code" TEXT NOT NULL,
    "address_line" TEXT NOT NULL,
    "city" TEXT NOT NULL,
    "zip_code" TEXT NOT NULL,
    "street_and_number" TEXT NOT NULL,
    "additional_address" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,

    CONSTRAINT "company_address_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "company_email" (
    "id" SERIAL NOT NULL,
    "email" TEXT NOT NULL,
    "company_id" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,

    CONSTRAINT "company_email_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "company_contact" (
    "id" SERIAL NOT NULL,
    "company_id" INTEGER,
    "name" TEXT,
    "email" TEXT,
    "phone_mobile" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,

    CONSTRAINT "company_contact_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "partner" (
    "id" SERIAL NOT NULL,
    "first_name" TEXT NOT NULL,
    "last_name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "user_id" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,
    "commission_mode" TEXT,
    "no_provision_negotiated" BOOLEAN DEFAULT false,
    "payout_cycle" TEXT,
    "status" "PartnerStatus" DEFAULT 'NO_UPDATES',

    CONSTRAINT "partner_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "partner_banking" (
    "id" SERIAL NOT NULL,
    "partner_id" INTEGER,
    "business_identifier_code" TEXT,
    "international_account_number" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,

    CONSTRAINT "partner_banking_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "partner_contract" (
    "id" SERIAL NOT NULL,
    "partner_id" INTEGER NOT NULL,
    "status" "PartnerContractStatus" NOT NULL DEFAULT 'DRAFT',
    "agreed_on" TIMESTAMP(3),
    "start_date" TIMESTAMP(3) NOT NULL,
    "end_date" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,

    CONSTRAINT "partner_contract_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "partner_contract_change" (
    "id" SERIAL NOT NULL,
    "partner_contract_id" INTEGER NOT NULL,
    "change_type" "PartnerContractChangeType" NOT NULL,
    "change_description" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "partner_contract_change_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "shopping_cart" (
    "id" TEXT NOT NULL,
    "cart_json" JSONB,
    "email" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,
    "invoice_id" INTEGER,
    "is_churned" BOOLEAN NOT NULL DEFAULT false,
    "journey" "shopping_cart_journey" NOT NULL,
    "journey_step" TEXT,
    "payment" JSONB,
    "status" "shopping_cart_status" NOT NULL DEFAULT 'OPEN',
    "total" INTEGER NOT NULL DEFAULT 0,
    "vat_percentage" INTEGER NOT NULL DEFAULT 0,
    "vat_value" INTEGER NOT NULL DEFAULT 0,
    "coupon_id" INTEGER,
    "coupon_type" "type_use_coupon",

    CONSTRAINT "shopping_cart_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "shopping_cart_item" (
    "id" SERIAL NOT NULL,
    "shopping_cart_id" TEXT NOT NULL,
    "service_type" "enum_contract_type" NOT NULL,
    "country_id" INTEGER NOT NULL,
    "country_code" TEXT NOT NULL,
    "country_name" TEXT NOT NULL,
    "country_flag" TEXT NOT NULL,
    "year" INTEGER NOT NULL,
    "price_list" JSONB NOT NULL,
    "packaging_services" JSONB,
    "customer_commitment_id" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,
    "calculator" JSONB,

    CONSTRAINT "shopping_cart_item_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "recommended_country" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "customer_id" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,

    CONSTRAINT "recommended_country_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "service_step" (
    "id" SERIAL NOT NULL,
    "type" "enum_service_step_type" NOT NULL,
    "country_code" TEXT,
    "title" TEXT NOT NULL,
    "available_at" TIMESTAMP(3) NOT NULL,
    "deadline_at" TIMESTAMP(3) NOT NULL,
    "is_active" BOOLEAN NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,

    CONSTRAINT "service_step_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "customer_service_step" (
    "id" SERIAL NOT NULL,
    "service_step_id" INTEGER NOT NULL,
    "customer_id" INTEGER NOT NULL,
    "done_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,

    CONSTRAINT "customer_service_step_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "consent" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "type" "enum_consent_type" NOT NULL,
    "description" TEXT NOT NULL,
    "version" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,

    CONSTRAINT "consent_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "customer_consent" (
    "id" SERIAL NOT NULL,
    "customer_id" INTEGER NOT NULL,
    "consent_id" INTEGER NOT NULL,
    "given" BOOLEAN NOT NULL,
    "givenAt" TIMESTAMP(3),
    "revokedAt" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "customer_consent_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "action_guide" (
    "id" SERIAL NOT NULL,
    "contract_id" INTEGER NOT NULL,
    "country_id" INTEGER NOT NULL,
    "country_code" TEXT NOT NULL,
    "country_name" TEXT NOT NULL,
    "country_flag" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,
    "contract_status" "enum_contract_status" NOT NULL DEFAULT 'ACTIVE',
    "termination_id" INTEGER,

    CONSTRAINT "action_guide_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "action_guide_price_list" (
    "id" SERIAL NOT NULL,
    "setup_price_list_id" INTEGER NOT NULL,
    "action_guide_id" INTEGER NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "price" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "action_guide_price_list_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "license" (
    "id" SERIAL NOT NULL,
    "contract_id" INTEGER NOT NULL,
    "registration_number" TEXT NOT NULL,
    "registration_status" "enum_license_registration_status" NOT NULL DEFAULT 'PENDING',
    "clerk_control_status" "enum_license_clerk_control_status" NOT NULL DEFAULT 'PENDING',
    "contract_status" "enum_license_contract_status" NOT NULL DEFAULT 'ACTIVE',
    "country_id" INTEGER NOT NULL,
    "country_code" TEXT NOT NULL,
    "country_name" TEXT NOT NULL,
    "country_flag" TEXT NOT NULL,
    "year" INTEGER NOT NULL,
    "start_date" TIMESTAMP(3) NOT NULL,
    "end_date" TIMESTAMP(3),
    "termination_id" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,
    "registration_and_termination_monday_ref" INTEGER,

    CONSTRAINT "license_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "license_price_list" (
    "id" SERIAL NOT NULL,
    "setup_price_list_id" INTEGER NOT NULL,
    "license_id" INTEGER NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "condition_type" TEXT NOT NULL,
    "condition_type_value" TEXT NOT NULL,
    "start_date" TIMESTAMP(3) NOT NULL,
    "end_date" TIMESTAMP(3) NOT NULL,
    "basic_price" INTEGER,
    "minimum_price" INTEGER,
    "registration_fee" INTEGER,
    "handling_fee" INTEGER,
    "variable_handling_fee" DOUBLE PRECISION,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),

    CONSTRAINT "license_price_list_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "license_packaging_service" (
    "id" SERIAL NOT NULL,
    "setup_packaging_service_id" INTEGER NOT NULL,
    "license_id" INTEGER NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,
    "obliged" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "license_packaging_service_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "license_report_set" (
    "id" SERIAL NOT NULL,
    "setup_report_set_id" INTEGER NOT NULL,
    "license_packaging_service_id" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,

    CONSTRAINT "license_report_set_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "license_report_set_frequency" (
    "id" SERIAL NOT NULL,
    "setup_report_set_frequency_id" INTEGER NOT NULL,
    "license_packaging_service_id" INTEGER NOT NULL,
    "rhythm" "enum_license_report_set_rhythm" NOT NULL,
    "frequency" JSONB NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,

    CONSTRAINT "license_report_set_frequency_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "license_volume_report" (
    "id" SERIAL NOT NULL,
    "license_packaging_service_id" INTEGER NOT NULL,
    "status" "enum_license_volume_report_status" NOT NULL DEFAULT 'OPEN',
    "year" INTEGER NOT NULL,
    "interval" TEXT NOT NULL,
    "report_table" JSONB NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,
    "volume_report_monday_ref" INTEGER,
    "stage" TEXT,

    CONSTRAINT "license_volume_report_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "license_volume_report_item" (
    "id" SERIAL NOT NULL,
    "license_volume_report_id" INTEGER NOT NULL,
    "setup_fraction_id" INTEGER NOT NULL,
    "setup_column_id" INTEGER NOT NULL DEFAULT 0,
    "value" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,
    "setup_column_code" TEXT,
    "setup_fraction_code" TEXT,

    CONSTRAINT "license_volume_report_item_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "license_volume_report_error" (
    "id" SERIAL NOT NULL,
    "license_volume_report_id" INTEGER,
    "license_volume_report_item_id" INTEGER,
    "description" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,

    CONSTRAINT "license_volume_report_error_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "license_volume_report_decline_reason" (
    "id" SERIAL NOT NULL,
    "license_volume_report_error_id" INTEGER NOT NULL,
    "report_decline_reason_id" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,

    CONSTRAINT "license_volume_report_decline_reason_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "report_decline_reason" (
    "id" SERIAL NOT NULL,
    "title" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,

    CONSTRAINT "report_decline_reason_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "license_required_information" (
    "id" SERIAL NOT NULL,
    "setup_required_information_id" INTEGER,
    "license_id" INTEGER,
    "type" "enum_license_required_information_type" NOT NULL,
    "status" "enum_license_required_information_status" NOT NULL DEFAULT 'OPEN',
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),
    "question" TEXT,
    "file_id" TEXT,
    "answer" TEXT,
    "kind" "enum_license_required_information_kind" NOT NULL DEFAULT 'REQUIRED_INFORMATION',
    "contract_id" INTEGER,
    "setup_general_information_id" INTEGER,

    CONSTRAINT "license_required_information_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "decline" (
    "id" SERIAL NOT NULL,
    "title" TEXT NOT NULL,
    "license_required_information_id" INTEGER,
    "license_volume_report_id" INTEGER,
    "license_volume_report_item_id" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,

    CONSTRAINT "decline_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "decline_to_decline_reason" (
    "id" SERIAL NOT NULL,
    "decline_id" INTEGER NOT NULL,
    "decline_reason_id" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,

    CONSTRAINT "decline_to_decline_reason_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "decline_reason" (
    "id" SERIAL NOT NULL,
    "title" TEXT NOT NULL,
    "type" "DeclineReasonType" NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,

    CONSTRAINT "decline_reason_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "license_third_party_invoice" (
    "id" SERIAL NOT NULL,
    "title" TEXT NOT NULL,
    "price" INTEGER NOT NULL,
    "issued_at" TIMESTAMP(3) NOT NULL,
    "status" "enum_license_third_party_invoice_status" NOT NULL DEFAULT 'OPEN',
    "license_id" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,
    "due_date" TIMESTAMP(3) NOT NULL,
    "issuer" "LicenseThirdPartyInvoiceIssuer" NOT NULL,
    "third_party_invoice_monday_ref" INTEGER,

    CONSTRAINT "license_third_party_invoice_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "license_other_cost" (
    "id" SERIAL NOT NULL,
    "setup_other_cost_id" INTEGER NOT NULL,
    "license_id" INTEGER NOT NULL,
    "name" TEXT NOT NULL,
    "price" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,

    CONSTRAINT "license_other_cost_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "license_representative_tier" (
    "id" SERIAL NOT NULL,
    "setup_representative_tier_id" INTEGER NOT NULL,
    "license_id" INTEGER NOT NULL,
    "name" TEXT NOT NULL,
    "price" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,

    CONSTRAINT "license_representative_tier_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "service_next_step" (
    "id" SERIAL NOT NULL,
    "license_id" INTEGER,
    "action_guide_id" INTEGER,
    "title" TEXT NOT NULL,
    "available_date" TIMESTAMP(3) NOT NULL,
    "deadline_date" TIMESTAMP(3) NOT NULL,
    "done_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,

    CONSTRAINT "service_next_step_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "contract" (
    "id" SERIAL NOT NULL,
    "customer_id" INTEGER NOT NULL,
    "type" "enum_contract_type" NOT NULL,
    "status" "enum_contract_status" NOT NULL DEFAULT 'ACTIVE',
    "title" TEXT NOT NULL,
    "start_date" TIMESTAMP(3) NOT NULL,
    "termination_id" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,
    "end_date" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "contract_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "certificate" (
    "id" SERIAL NOT NULL,
    "license_id" INTEGER NOT NULL,
    "name" TEXT NOT NULL,
    "status" "enum_certificate_status" NOT NULL DEFAULT 'NOT_AVAILABLE',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,

    CONSTRAINT "certificate_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "termination" (
    "id" SERIAL NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,
    "completed_at" TIMESTAMP(3),
    "requested_at" TIMESTAMP(3) NOT NULL,
    "status" "enum_termination_status" NOT NULL DEFAULT 'REQUESTED',

    CONSTRAINT "termination_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "general_information" (
    "id" SERIAL NOT NULL,
    "setup_general_information_id" INTEGER NOT NULL,
    "contract_id" INTEGER NOT NULL,
    "type" "enum_general_information_type" NOT NULL,
    "status" "enum_general_information_status" NOT NULL DEFAULT 'OPEN',
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMP(3),
    "question" TEXT,
    "file_id" TEXT,
    "answer" TEXT,

    CONSTRAINT "general_information_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "file" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "original_name" TEXT NOT NULL,
    "extension" TEXT NOT NULL,
    "size" TEXT NOT NULL,
    "type" "enum_file_type" NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,
    "required_information_id" INTEGER,
    "contract_id" INTEGER,
    "certificate_id" INTEGER,
    "license_id" INTEGER,
    "termination_id" INTEGER,
    "general_information_id" INTEGER,
    "third_party_invoice_id" INTEGER,
    "marketing_material_id" INTEGER,
    "partner_contract_id" INTEGER,

    CONSTRAINT "file_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "customer_commitment" (
    "id" SERIAL NOT NULL,
    "customer_email" TEXT,
    "country_code" TEXT NOT NULL,
    "year" INTEGER NOT NULL,
    "commitment" JSONB NOT NULL,
    "service_setup" JSONB,
    "is_license_required" BOOLEAN NOT NULL DEFAULT false,
    "blame" JSONB NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,

    CONSTRAINT "customer_commitment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "marketing_material" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "start_date" DATE,
    "end_date" DATE,
    "category" "enum_marketing_material_category" NOT NULL,
    "partner_restriction" "enum_marketing_material_partner_restriction" NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,

    CONSTRAINT "marketing_material_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "marketing_material_partner" (
    "id" SERIAL NOT NULL,
    "marketing_material_id" INTEGER NOT NULL,
    "partner_id" INTEGER NOT NULL,

    CONSTRAINT "marketing_material_partner_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "coupon" (
    "id" SERIAL NOT NULL,
    "buy_x_get_y" JSONB,
    "code" TEXT NOT NULL,
    "commission_percentage" INTEGER,
    "description" TEXT,
    "discount_type" "CouponDiscountType" NOT NULL,
    "elegible_products" JSONB,
    "end_date" TIMESTAMP(3) NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "link" TEXT,
    "max_amount" INTEGER,
    "max_uses" INTEGER,
    "max_uses_per_customer" INTEGER,
    "min_amount" INTEGER,
    "min_products" INTEGER,
    "mode" "CouponMode" NOT NULL,
    "note" TEXT,
    "start_date" TIMESTAMP(3) NOT NULL,
    "type" "CouponType" NOT NULL,
    "value" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,
    "redeemable_by_new_customers" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "coupon_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "coupon_partners" (
    "id" SERIAL NOT NULL,
    "coupon_id" INTEGER NOT NULL,
    "partner_id" INTEGER NOT NULL,

    CONSTRAINT "coupon_partners_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "coupon_uses" (
    "id" SERIAL NOT NULL,
    "coupon_id" INTEGER NOT NULL,
    "shopping_cart_id" TEXT NOT NULL,
    "order_id" TEXT,
    "customer_id" INTEGER NOT NULL,
    "commission" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_first_purchase" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "coupon_uses_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "coupon_customer" (
    "id" SERIAL NOT NULL,
    "coupon_id" INTEGER NOT NULL,
    "customer_id" INTEGER NOT NULL,

    CONSTRAINT "coupon_customer_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "cluster" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "registration_start_date" TIMESTAMP(3) NOT NULL,
    "registration_end_date" TIMESTAMP(3) NOT NULL,
    "status" "ClusterStatus" NOT NULL,
    "min_household_packaging" INTEGER NOT NULL,
    "max_household_packaging" INTEGER NOT NULL,
    "type_of_services" JSONB NOT NULL,
    "participating_countries" JSONB NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" DATE,

    CONSTRAINT "cluster_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "cluster_customers" (
    "id" SERIAL NOT NULL,
    "cluster_id" INTEGER NOT NULL,
    "customer_id" INTEGER NOT NULL,

    CONSTRAINT "cluster_customers_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "customer_email_key" ON "customer"("email");

-- CreateIndex
CREATE UNIQUE INDEX "customer_unique" ON "customer"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "customer_invite_token_customer_id_key" ON "customer_invite_token"("customer_id");

-- CreateIndex
CREATE UNIQUE INDEX "customer_tutorial_customer_id_service_type_key" ON "customer_tutorial"("customer_id", "service_type");

-- CreateIndex
CREATE UNIQUE INDEX "company_contact_company_id_key" ON "company_contact"("company_id");

-- CreateIndex
CREATE UNIQUE INDEX "partner_banking_partner_id_key" ON "partner_banking"("partner_id");

-- CreateIndex
CREATE UNIQUE INDEX "partner_contract_partner_id_key" ON "partner_contract"("partner_id");

-- CreateIndex
CREATE UNIQUE INDEX "shopping_cart_item_customer_commitment_id_key" ON "shopping_cart_item"("customer_commitment_id");

-- CreateIndex
CREATE UNIQUE INDEX "customer_consent_customer_id_consent_id_key" ON "customer_consent"("customer_id", "consent_id");

-- CreateIndex
CREATE UNIQUE INDEX "license_registration_and_termination_monday_ref_unique" ON "license"("registration_and_termination_monday_ref");

-- CreateIndex
CREATE UNIQUE INDEX "license_report_set_license_packaging_service_id_key" ON "license_report_set"("license_packaging_service_id");

-- CreateIndex
CREATE UNIQUE INDEX "license_report_set_frequency_license_packaging_service_id_key" ON "license_report_set_frequency"("license_packaging_service_id");

-- CreateIndex
CREATE UNIQUE INDEX "decline_license_required_information_id_key" ON "decline"("license_required_information_id");

-- CreateIndex
CREATE UNIQUE INDEX "decline_license_volume_report_id_key" ON "decline"("license_volume_report_id");

-- CreateIndex
CREATE UNIQUE INDEX "decline_license_volume_report_item_id_key" ON "decline"("license_volume_report_item_id");

-- CreateIndex
CREATE UNIQUE INDEX "license_third_party_invoice_unique" ON "license_third_party_invoice"("third_party_invoice_monday_ref");

-- CreateIndex
CREATE UNIQUE INDEX "customer_commitment_customer_email_country_code_year_key" ON "customer_commitment"("customer_email", "country_code", "year");

-- CreateIndex
CREATE UNIQUE INDEX "coupon_code_key" ON "coupon"("code");

-- CreateIndex
CREATE UNIQUE INDEX "coupon_link_key" ON "coupon"("link");

-- AddForeignKey
ALTER TABLE "customer_phone" ADD CONSTRAINT "customer_phone_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "customer"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "customer_invite_token" ADD CONSTRAINT "customer_invite_token_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "customer"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "customer_tutorial" ADD CONSTRAINT "customer_tutorial_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "customer"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "customer_invitation" ADD CONSTRAINT "customer_invitation_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "customer"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "customer_invitation" ADD CONSTRAINT "customer_invitation_invited_customer_id_fkey" FOREIGN KEY ("invited_customer_id") REFERENCES "customer"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "customer_document" ADD CONSTRAINT "customer_document_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "customer"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company" ADD CONSTRAINT "company_address_id_fkey" FOREIGN KEY ("address_id") REFERENCES "company_address"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company" ADD CONSTRAINT "company_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "customer"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company" ADD CONSTRAINT "company_partner_id_fkey" FOREIGN KEY ("partner_id") REFERENCES "partner"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company_email" ADD CONSTRAINT "company_email_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "company"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "company_contact" ADD CONSTRAINT "company_contact_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "company"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "partner_banking" ADD CONSTRAINT "partner_banking_partner_id_fkey" FOREIGN KEY ("partner_id") REFERENCES "partner"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "partner_contract" ADD CONSTRAINT "partner_contract_partner_id_fkey" FOREIGN KEY ("partner_id") REFERENCES "partner"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "partner_contract_change" ADD CONSTRAINT "partner_contract_change_partner_contract_id_fkey" FOREIGN KEY ("partner_contract_id") REFERENCES "partner_contract"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "shopping_cart" ADD CONSTRAINT "shopping_cart_coupon_id_fkey" FOREIGN KEY ("coupon_id") REFERENCES "coupon"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "shopping_cart_item" ADD CONSTRAINT "shopping_cart_item_customer_commitment_id_fkey" FOREIGN KEY ("customer_commitment_id") REFERENCES "customer_commitment"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "shopping_cart_item" ADD CONSTRAINT "shopping_cart_item_shopping_cart_id_fkey" FOREIGN KEY ("shopping_cart_id") REFERENCES "shopping_cart"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "recommended_country" ADD CONSTRAINT "recommended_country_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "customer"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "customer_service_step" ADD CONSTRAINT "customer_service_step_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "customer"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "customer_service_step" ADD CONSTRAINT "customer_service_step_service_step_id_fkey" FOREIGN KEY ("service_step_id") REFERENCES "service_step"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "customer_consent" ADD CONSTRAINT "customer_consent_consent_id_fkey" FOREIGN KEY ("consent_id") REFERENCES "consent"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "customer_consent" ADD CONSTRAINT "customer_consent_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "customer"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "action_guide" ADD CONSTRAINT "action_guide_contract_id_fkey" FOREIGN KEY ("contract_id") REFERENCES "contract"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "action_guide" ADD CONSTRAINT "action_guide_termination_id_fkey" FOREIGN KEY ("termination_id") REFERENCES "termination"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "action_guide_price_list" ADD CONSTRAINT "action_guide_price_list_action_guide_id_fkey" FOREIGN KEY ("action_guide_id") REFERENCES "action_guide"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "license" ADD CONSTRAINT "license_contract_id_fkey" FOREIGN KEY ("contract_id") REFERENCES "contract"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "license" ADD CONSTRAINT "license_termination_id_fkey" FOREIGN KEY ("termination_id") REFERENCES "termination"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "license_price_list" ADD CONSTRAINT "license_price_list_license_id_fkey" FOREIGN KEY ("license_id") REFERENCES "license"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "license_packaging_service" ADD CONSTRAINT "license_packaging_service_license_id_fkey" FOREIGN KEY ("license_id") REFERENCES "license"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "license_report_set" ADD CONSTRAINT "license_report_set_license_packaging_service_id_fkey" FOREIGN KEY ("license_packaging_service_id") REFERENCES "license_packaging_service"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "license_report_set_frequency" ADD CONSTRAINT "license_report_set_frequency_license_packaging_service_id_fkey" FOREIGN KEY ("license_packaging_service_id") REFERENCES "license_packaging_service"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "license_volume_report" ADD CONSTRAINT "license_volume_report_license_packaging_service_id_fkey" FOREIGN KEY ("license_packaging_service_id") REFERENCES "license_packaging_service"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "license_volume_report_item" ADD CONSTRAINT "license_volume_report_item_license_volume_report_id_fkey" FOREIGN KEY ("license_volume_report_id") REFERENCES "license_volume_report"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "license_volume_report_error" ADD CONSTRAINT "license_volume_report_error_license_volume_report_id_fkey" FOREIGN KEY ("license_volume_report_id") REFERENCES "license_volume_report"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "license_volume_report_error" ADD CONSTRAINT "license_volume_report_error_license_volume_report_item_id_fkey" FOREIGN KEY ("license_volume_report_item_id") REFERENCES "license_volume_report_item"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "license_volume_report_decline_reason" ADD CONSTRAINT "license_volume_report_decline_reason_license_volume_report_fkey" FOREIGN KEY ("license_volume_report_error_id") REFERENCES "license_volume_report_error"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "license_volume_report_decline_reason" ADD CONSTRAINT "license_volume_report_decline_reason_report_decline_reason_fkey" FOREIGN KEY ("report_decline_reason_id") REFERENCES "report_decline_reason"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "license_required_information" ADD CONSTRAINT "license_required_information_contract_id_fkey" FOREIGN KEY ("contract_id") REFERENCES "contract"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "license_required_information" ADD CONSTRAINT "license_required_information_license_id_fkey" FOREIGN KEY ("license_id") REFERENCES "license"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "decline" ADD CONSTRAINT "decline_license_required_information_id_fkey" FOREIGN KEY ("license_required_information_id") REFERENCES "license_required_information"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "decline" ADD CONSTRAINT "decline_license_volume_report_id_fkey" FOREIGN KEY ("license_volume_report_id") REFERENCES "license_volume_report"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "decline" ADD CONSTRAINT "decline_license_volume_report_item_id_fkey" FOREIGN KEY ("license_volume_report_item_id") REFERENCES "license_volume_report_item"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "decline_to_decline_reason" ADD CONSTRAINT "decline_to_decline_reason_decline_id_fkey" FOREIGN KEY ("decline_id") REFERENCES "decline"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "decline_to_decline_reason" ADD CONSTRAINT "decline_to_decline_reason_decline_reason_id_fkey" FOREIGN KEY ("decline_reason_id") REFERENCES "decline_reason"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "license_third_party_invoice" ADD CONSTRAINT "license_third_party_invoice_license_id_fkey" FOREIGN KEY ("license_id") REFERENCES "license"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "license_other_cost" ADD CONSTRAINT "license_other_cost_license_id_fkey" FOREIGN KEY ("license_id") REFERENCES "license"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "license_representative_tier" ADD CONSTRAINT "license_representative_tier_license_id_fkey" FOREIGN KEY ("license_id") REFERENCES "license"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "service_next_step" ADD CONSTRAINT "service_next_step_action_guide_id_fkey" FOREIGN KEY ("action_guide_id") REFERENCES "action_guide"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "service_next_step" ADD CONSTRAINT "service_next_step_license_id_fkey" FOREIGN KEY ("license_id") REFERENCES "license"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "contract" ADD CONSTRAINT "contract_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "customer"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "contract" ADD CONSTRAINT "contract_termination_id_fkey" FOREIGN KEY ("termination_id") REFERENCES "termination"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "certificate" ADD CONSTRAINT "certificate_license_id_fkey" FOREIGN KEY ("license_id") REFERENCES "license"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "file" ADD CONSTRAINT "file_certificate_id_fkey" FOREIGN KEY ("certificate_id") REFERENCES "certificate"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "file" ADD CONSTRAINT "file_contract_id_fkey" FOREIGN KEY ("contract_id") REFERENCES "contract"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "file" ADD CONSTRAINT "file_general_information_id_fkey" FOREIGN KEY ("general_information_id") REFERENCES "general_information"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "file" ADD CONSTRAINT "file_license_id_fkey" FOREIGN KEY ("license_id") REFERENCES "license"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "file" ADD CONSTRAINT "file_marketing_material_id_fkey" FOREIGN KEY ("marketing_material_id") REFERENCES "marketing_material"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "file" ADD CONSTRAINT "file_partner_contract_id_fkey" FOREIGN KEY ("partner_contract_id") REFERENCES "partner_contract"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "file" ADD CONSTRAINT "file_required_information_id_fkey" FOREIGN KEY ("required_information_id") REFERENCES "license_required_information"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "file" ADD CONSTRAINT "file_termination_id_fkey" FOREIGN KEY ("termination_id") REFERENCES "termination"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "file" ADD CONSTRAINT "file_third_party_invoice_id_fkey" FOREIGN KEY ("third_party_invoice_id") REFERENCES "license_third_party_invoice"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "customer_commitment" ADD CONSTRAINT "customer_commitment_customer_email_fkey" FOREIGN KEY ("customer_email") REFERENCES "customer"("email") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "marketing_material_partner" ADD CONSTRAINT "marketing_material_partner_marketing_material_id_fkey" FOREIGN KEY ("marketing_material_id") REFERENCES "marketing_material"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "marketing_material_partner" ADD CONSTRAINT "marketing_material_partner_partner_id_fkey" FOREIGN KEY ("partner_id") REFERENCES "partner"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "coupon_partners" ADD CONSTRAINT "coupon_partners_coupon_id_fkey" FOREIGN KEY ("coupon_id") REFERENCES "coupon"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "coupon_partners" ADD CONSTRAINT "coupon_partners_partner_id_fkey" FOREIGN KEY ("partner_id") REFERENCES "partner"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "coupon_uses" ADD CONSTRAINT "coupon_uses_coupon_id_fkey" FOREIGN KEY ("coupon_id") REFERENCES "coupon"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "coupon_uses" ADD CONSTRAINT "coupon_uses_shopping_cart_id_fkey" FOREIGN KEY ("shopping_cart_id") REFERENCES "shopping_cart"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "coupon_customer" ADD CONSTRAINT "coupon_customer_coupon_id_fkey" FOREIGN KEY ("coupon_id") REFERENCES "coupon"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "coupon_customer" ADD CONSTRAINT "coupon_customer_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "customer"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "cluster_customers" ADD CONSTRAINT "cluster_customers_cluster_id_fkey" FOREIGN KEY ("cluster_id") REFERENCES "cluster"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "cluster_customers" ADD CONSTRAINT "cluster_customers_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "customer"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
