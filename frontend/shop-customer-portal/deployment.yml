---
apiVersion: v1
kind: Namespace
metadata:
  name: <NAMESPACE>
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: <PREFIX><PROJECT_NAME>-<COMPONENT>
  namespace: <NAMESPACE>
  labels:
    loadbalancer: <PREFIX><PROJECT_NAME>-<COMPONENT>
spec:
  replicas: 1
  selector:
    matchLabels:
      loadbalancer: <PREFIX><PROJECT_NAME>-<COMPONENT>
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        loadbalancer: <PREFIX><PROJECT_NAME>-<COMPONENT>
        app: <PREFIX><PROJECT_NAME>-<COMPONENT>
        ignore-gnp: "y"
    spec:
      automountServiceAccountToken: false
      containers:
        - envFrom:
          name: <PREFIX><PROJECT_NAME>-<COMPONENT>
          image: nexus.interzero.de:<NEXUS_PORT>/<PROJECT_NAME>-<COMPONENT>:<BRANCH>
          imagePullPolicy: Always
          resources:
            requests:
              memory: <MEMORY_REQUEST>
              cpu: <CPU_REQUEST>
            limits:
              memory: <MEMORY_LIMIT>
              cpu: <CPU_LIMIT>
          env:
            - name: "NEXT_ENV"
              value: "<NODE_ENV>"
            - name: "NEXT_PUBLIC_API_URL"
              value: "<NEXT_PUBLIC_API_URL>"
            - name: "NEXTAUTH_URL"
              value: "<NEXTAUTH_URL>"
            - name: "NEXTAUTH_SECRET"
              value: "<NEXTAUTH_SECRET>"
            - name: "SYSTEM_API_KEY"
              value: "<SYSTEM_API_KEY>"
            - name: "NEXT_PUBLIC_COOKIEBOT_ID"
              value: "<FE_SHOP_NEXT_PUBLIC_COOKIEBOT_ID>"
            - name: "NEXT_PUBLIC_ENCRYPTION_KEY"
              value: "<FE_SHOP_NEXT_PUBLIC_ENCRYPTION_KEY>"
            - name: "NEXT_PUBLIC_GOOGLE_ANALYTICS_ID"
              value: "<FE_SHOP_NEXT_PUBLIC_GOOGLE_ANALYTICS_ID>"
            - name: "NEXT_PUBLIC_LANDING_PAGE_URL"
              value: "<FE_SHOP_NEXT_PUBLIC_LANDING_PAGE_URL>"
            - name: "NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN"
              value: "<FE_SHOP_NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN>"
            - name: "NEXT_PUBLIC_MOUSE_FLOW_ID"
              value: "<FE_SHOP_NEXT_PUBLIC_MOUSE_FLOW_ID>"
            - name: "NEXT_PUBLIC_STRAPI_URL"
              value: "<FE_SHOP_NEXT_PUBLIC_STRAPI_URL>"
            - name: "NEXT_PUBLIC_STRIPE_PUBLIC_KEY"
              value: "<FE_SHOP_NEXT_PUBLIC_STRIPE_PUBLIC_KEY>"
            - name: "SECRET_STRIPE_KEY"
              value: "<FE_SHOP_SECRET_STRIPE_KEY>"
---
apiVersion: v1
kind: Service
metadata:
  name: <PREFIX><PROJECT_NAME>-<COMPONENT>
  namespace: <NAMESPACE>
spec:
  ports:
    - protocol: TCP
      port: 3000
      targetPort: 3000
  selector:
    loadbalancer: <PREFIX><PROJECT_NAME>-<COMPONENT>
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: <PREFIX><PROJECT_NAME>-<COMPONENT>
  namespace: <NAMESPACE>
  annotations:
    alb.ingress.kubernetes.io/scheme: <ALB_SCHEME>
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/group.name: <ALB_GROUP>
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 3000}, {"HTTPS":443}]'
spec:
  ingressClassName: alb
  rules:
    - host: <HOSTNAME>
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: <PREFIX><PROJECT_NAME>-<COMPONENT>
                port:
                  number: 3000
