# ----------------- G<PERSON><PERSON>BAL CONFIGURATIONS ----------------- #
default:
  tags: ["aws"]

image: gitlab.interzero.de/software-development/dependency_proxy/containers/node:${NODE_VERSION}-alpine

# Global variables
variables:
  COMPONENT_ADMIN_PORTAL: admin-portal
  COMPONENT_ADMIN_PORTAL_PATH: frontend/admin-portal
  DEPLOYMENT_FILE_ADMIN_PORTAL: ${COMPONENT_ADMIN_PORTAL_PATH}/deployment.yml

# ----------------- TEMPLATES ----------------- #
.docker_build_template: &docker_build_template
  image:
    name: gcr.io/kaniko-project/executor:v1.14.0-debug
    entrypoint: [""]
  script:
    - echo $DOCKER_AUTH_CONFIG > /kaniko/.docker/config.json
    - /kaniko/executor
      --context $CI_PROJECT_DIR/${COMPONENT_ADMIN_PORTAL_PATH}
      --dockerfile $CI_PROJECT_DIR/${COMPONENT_ADMIN_PORTAL_PATH}/Dockerfile${DOCKERFILE_EXTENSION}
      --build-arg API=$API
      --build-arg NODE_VERSION=$NODE_VERSION
      --build-arg PROJECT_NAME=$SHORT_PROJECT_NAME
      --build-arg COMPONENT=$COMPONENT_ADMIN_PORTAL
      --build-arg CI_COMMIT_REF_SLUG=$CI_COMMIT_REF_SLUG
      --insecure
      --insecure-pull
      --cache=true
      --cache-repo=nexus.interzero.de:${NEXUS_PORT}/${SHORT_PROJECT_NAME}-${COMPONENT_NAME}-cache
      --destination nexus.interzero.de:${NEXUS_PORT}/${SHORT_PROJECT_NAME}-${COMPONENT_NAME}:${CI_COMMIT_REF_SLUG}

.deployment_template: &deployment_template
  stage: deploy
  image: gitlab.interzero.de/software-development/dependency_proxy/containers/dtzar/helm-kubectl:3.12.3
  script:
    - echo "Deploy to ${DEPLOY_ENV} server"
    - echo "Delete old service"
    - kubectl delete -n ${NAMESPACE} deployment ${PREFIX}${SHORT_PROJECT_NAME}-${COMPONENT_ADMIN_PORTAL} --ignore-not-found
    - echo "Add new service"
    - kubectl config view
    - sed -i "s/<BRANCH>/${CI_COMMIT_REF_SLUG}/g" ${DEPLOYMENT_FILE_ADMIN_PORTAL}
    - sed -i "s/<NAMESPACE>/${NAMESPACE}/g" ${DEPLOYMENT_FILE_ADMIN_PORTAL}
    - sed -i "s/<COMPONENT>/${COMPONENT_ADMIN_PORTAL}/g" ${DEPLOYMENT_FILE_ADMIN_PORTAL}
    - sed -i "s/<PROJECT_NAME>/${SHORT_PROJECT_NAME}/g" ${DEPLOYMENT_FILE_ADMIN_PORTAL}
    - sed -i "s/<NEXUS_PORT>/${NEXUS_PORT}/g" ${DEPLOYMENT_FILE_ADMIN_PORTAL}
    - sed -i "s/<HOSTNAME>/${HOSTNAME}/g" ${DEPLOYMENT_FILE_ADMIN_PORTAL}
    - sed -i "s/<ALB_SCHEME>/${ALB_SCHEME}/g" ${DEPLOYMENT_FILE_ADMIN_PORTAL}
    - sed -i "s/<ALB_GROUP>/${ALB_GROUP}/g" ${DEPLOYMENT_FILE_ADMIN_PORTAL}
    - sed -i "s/<NODE_ENV>/${NODE_ENV}/g" ${DEPLOYMENT_FILE_ADMIN_PORTAL}
    - sed -i "s/<PREFIX>/${PREFIX}/g" ${DEPLOYMENT_FILE_ADMIN_PORTAL}
    - sed -i "s|<API>|${API}|g" ${DEPLOYMENT_FILE_ADMIN_PORTAL}
    - sed -i "s|<NEXT_PUBLIC_API_URL>|${API}|g" ${DEPLOYMENT_FILE_ADMIN_PORTAL}
    - sed -i "s|<NEXTAUTH_URL>|${API}|g" ${DEPLOYMENT_FILE_ADMIN_PORTAL}
    - sed -i "s|<NEXTAUTH_SECRET>|${FE_NEXTAUTH_SECRET}|g" ${DEPLOYMENT_FILE_ADMIN_PORTAL}
    - sed -i "s|<SYSTEM_API_KEY>|${FE_NEXT_SYSTEM_API_KEY}|g" ${DEPLOYMENT_FILE_ADMIN_PORTAL}
    # CPU and RAM
    - sed -i "s/<CPU_REQUEST>/${CPU_REQUEST}/g" ${DEPLOYMENT_FILE_ADMIN_PORTAL}
    - sed -i "s/<CPU_LIMIT>/${CPU_LIMIT}/g" ${DEPLOYMENT_FILE_ADMIN_PORTAL}
    - sed -i "s/<MEMORY_REQUEST>/${MEMORY_REQUEST}/g" ${DEPLOYMENT_FILE_ADMIN_PORTAL}
    - sed -i "s/<MEMORY_LIMIT>/${MEMORY_LIMIT}/g" ${DEPLOYMENT_FILE_ADMIN_PORTAL}
    - cat ${DEPLOYMENT_FILE_ADMIN_PORTAL}
    - kubectl apply -f ${DEPLOYMENT_FILE_ADMIN_PORTAL}

fe_admin_set_feature_release_env:
  stage: deploy
  only:
    - /^feature\/.*$/
    - /^release\/.*$/
  script:
    - echo $CI_COMMIT_REF_SLUG
    - MODIFIED_CI_COMMIT_REF_SLUG=$(echo "${CI_COMMIT_REF_SLUG}" | sed 's/--.*//')
    - echo "DYNAMIC_ENV_URL=https://${SHORT_PROJECT_NAME}-${COMPONENT_ADMIN_PORTAL}-${MODIFIED_CI_COMMIT_REF_SLUG}.feature.interzero.dev" >> deploy.env
  environment:
    name: review/${CI_COMMIT_REF_NAME}/frontend
    url: $DYNAMIC_ENV_URL
    auto_stop_in: 21 days
    on_stop: fe_admin_destroy_feature_release
  artifacts:
    reports:
      dotenv: deploy.env

.deployment_template_feature_release: &deployment_template_feature_release
  <<: *deployment_template

# ----------------- DEPENDENCY INSTALLATION ----------------- #
fe_admin_install_dependencies:
  <<: *docker_build_template
  stage: install_dependencies
  tags: ["eks-entw-qat-with-s3"]
  variables:
    NEXUS_PORT: "${NEXUS_ENTW_QAT}"
    COMPONENT_NAME: "${COMPONENT_ADMIN_PORTAL}-build"
    DOCKERFILE_EXTENSION: ".build"
    API: "http://${SHORT_PROJECT_NAME}-be:8080"
  cache:
    key: ${CI_PROJECT_NAME}-pnpm-store
    paths:
      - frontend/admin-portal/.pnpm-store
  before_script:
    - mkdir -p frontend/admin-portal/.pnpm-store
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
    - if: '$CI_COMMIT_BRANCH == "staging"'
    - if: '$CI_COMMIT_BRANCH == "main"'

fe_admin_install_dependencies_review_env:
  <<: *docker_build_template
  stage: install_dependencies
  tags: ["eks-entw-qat-with-s3"]
  variables:
    NEXUS_PORT: "${NEXUS_ENTW_QAT}"
    COMPONENT_NAME: "${COMPONENT_ADMIN_PORTAL}-build"
    DOCKERFILE_EXTENSION: ".build"
    API: "http://${CI_COMMIT_REF_SLUG}-${SHORT_PROJECT_NAME}-be:8080"
  cache:
    key: ${CI_PROJECT_NAME}-pnpm-store
    paths:
      - frontend/admin-portal/.pnpm-store
  before_script:
    - mkdir -p frontend/admin-portal/.pnpm-store
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    - if: '$CI_COMMIT_BRANCH =~ /^feature\/.*$/'
    - if: '$CI_COMMIT_BRANCH =~ /^release\/.*$/'

# ----------------- TESTING ----------------- #
fe_admin_unit_tests:
  stage: test
  image: nexus.interzero.de:5000/${SHORT_PROJECT_NAME}-${COMPONENT_ADMIN_PORTAL}-build:${CI_COMMIT_REF_SLUG}
  tags: ["eks-entw-qat-with-s3"]
  script:
    - node -v
    - pnpm -v
    - cd /app
    - pnpm run check-all
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    - if: '$CI_COMMIT_BRANCH == "develop"'
    - if: '$CI_COMMIT_BRANCH == "staging"'
    - if: '$CI_COMMIT_BRANCH == "main"'
    - if: '$CI_COMMIT_BRANCH =~ /^feature\/.*$/'
    - if: '$CI_COMMIT_BRANCH =~ /^release\/.*$/'
  variables:
    API: "http://${SHORT_PROJECT_NAME}-be:8080"

# ----------------- DOCKER IMAGE BUILD ----------------- #
fe_admin_docker_build:
  <<: *docker_build_template
  stage: build_docker_image
  tags: ["eks-entw-qat-with-s3"]
  variables:
    COMPONENT_NAME: "${COMPONENT_ADMIN_PORTAL}"
    DOCKERFILE_EXTENSION: ".prod"
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
      variables:
        NEXUS_PORT: "${NEXUS_ENTW_QAT}"
    - if: '$CI_COMMIT_BRANCH == "staging"'
      variables:
        NEXUS_PORT: "${NEXUS_ENTW_QAT}"
    - if: '$CI_COMMIT_BRANCH == "main"'
      variables:
        NEXUS_PORT: "${NEXUS_PROD}"
    - if: '$CI_COMMIT_BRANCH =~ /^feature\/.*$/ || $CI_COMMIT_BRANCH =~ /^release\/.*$/'
      variables:
        NEXUS_PORT: "${NEXUS_ENTW_QAT}"

# ----------------- DEPLOYMENT ----------------- #
fe_admin_deploy_develop:
  <<: *deployment_template
  stage: deploy
  tags: ["eks-entw-qat"]
  only:
    - develop
  variables:
    DEPLOY_ENV: "ENTW/QAT"
    NAMESPACE: "entw-${PROJECT_NAME}"
    NEXUS_PORT: "${NEXUS_ENTW_QAT}"
    HOSTNAME: "${SHORT_PROJECT_NAME}-${COMPONENT_ADMIN_PORTAL}.entw.interzero.dev"
    API: "http://${SHORT_PROJECT_NAME}-be:8080"
    ALB_SCHEME: "internal"
    ALB_GROUP: "eks-entw-qat-vpn"
    CPU_REQUEST: "250m"
    CPU_LIMIT: "500m"
    MEMORY_REQUEST: "2Gi"
    MEMORY_LIMIT: "2Gi"
    NODE_ENV: "development"
    PREFIX: ""
  environment:
    name: ${CI_COMMIT_REF_NAME}/frontend
    url: "https://${SHORT_PROJECT_NAME}-${COMPONENT_ADMIN_PORTAL}.entw.interzero.dev"

fe_admin_deploy_staging:
  <<: *deployment_template
  stage: deploy
  tags: ["eks-entw-qat"]
  only:
    - staging
  variables:
    DEPLOY_ENV: "ENTW/QAT"
    NAMESPACE: "qat-${PROJECT_NAME}"
    NEXUS_PORT: "${NEXUS_ENTW_QAT}"
    HOSTNAME: "${SHORT_PROJECT_NAME}-${COMPONENT_ADMIN_PORTAL}.qat.interzero.dev"
    API: "http://${SHORT_PROJECT_NAME}-be:8080"
    ALB_SCHEME: "internal"
    ALB_GROUP: "eks-entw-qat-vpn"
    CPU_REQUEST: "250m"
    CPU_LIMIT: "500m"
    MEMORY_REQUEST: "2Gi"
    MEMORY_LIMIT: "2Gi"
    NODE_ENV: "staging"
    PREFIX: ""
  environment:
    name: ${CI_COMMIT_REF_NAME}/frontend
    url: "https://${SHORT_PROJECT_NAME}-${COMPONENT_ADMIN_PORTAL}.qat.interzero.dev"

fe_admin_deploy_main:
  <<: *deployment_template
  stage: deploy
  tags: ["eks-prod"]
  only:
    - main
  when: manual
  variables:
    DEPLOY_ENV: "PROD"
    NAMESPACE: "prod-${PROJECT_NAME}"
    NEXUS_PORT: "${NEXUS_PROD}"
    HOSTNAME: "${SHORT_PROJECT_NAME}-${COMPONENT_ADMIN_PORTAL}.vpn.interzero.dev"
    API: "http://${SHORT_PROJECT_NAME}-be:8080"
    ALB_SCHEME: "internal"
    ALB_GROUP: "eks-prod-vpn"
    CPU_REQUEST: "250m"
    CPU_LIMIT: "500m"
    MEMORY_REQUEST: "2Gi"
    MEMORY_LIMIT: "2Gi"
    NODE_ENV: "production"
    PREFIX: ""
  environment:
    name: ${CI_COMMIT_REF_NAME}/frontend
    url: "https://${SHORT_PROJECT_NAME}-${COMPONENT_ADMIN_PORTAL}.vpn.interzero.dev"

fe_admin_deploy_feature_release:
  <<: *deployment_template_feature_release
  stage: deploy
  tags: ["eks-entw-qat"]
  before_script:
    - export SHORTEN_CI_COMMIT_REF_SLUG=$(echo "${CI_COMMIT_REF_SLUG}" | sed 's/--.*//')
    - export HOSTNAME="${SHORT_PROJECT_NAME}-${COMPONENT_ADMIN_PORTAL}-${SHORTEN_CI_COMMIT_REF_SLUG}.feature.interzero.dev"
    - export DB_SERVICE_NAME="check4recycling-${CI_COMMIT_REF_SLUG}"
    - export PREFIX="${CI_COMMIT_REF_SLUG}-"
  only:
    - /^feature\/.*$/
    - /^release\/.*$/
  variables:
    DEPLOY_ENV: "ENTW/QAT"
    NAMESPACE: "entw-${PROJECT_NAME}"
    NEXUS_PORT: "${NEXUS_ENTW_QAT}"
    API: "http://${CI_COMMIT_REF_SLUG}-${SHORT_PROJECT_NAME}-be:8080"
    ALB_SCHEME: "internal"
    ALB_GROUP: "eks-feature-vpn"
    CPU_REQUEST: "250m"
    CPU_LIMIT: "500m"
    MEMORY_REQUEST: "2Gi"
    MEMORY_LIMIT: "2Gi"
    NODE_ENV: "development"

# ----------------- CLEANUP ----------------- #
fe_admin_destroy_feature_release:
  image: gitlab.interzero.de/software-development/dependency_proxy/containers/dtzar/helm-kubectl:3.12.3
  stage: cleanup
  when: manual
  only:
    - /^feature\/.*$/
    - /^release\/.*$/
  tags: ["eks-entw-qat"]
  script:
    - echo "Stopping environment ${CI_COMMIT_REF_SLUG}"
    - kubectl delete -n ${NAMESPACE} deployment ${PREFIX}${SHORT_PROJECT_NAME}-${COMPONENT_ADMIN_PORTAL} --ignore-not-found
    - kubectl delete -n ${NAMESPACE} service ${PREFIX}${SHORT_PROJECT_NAME}-${COMPONENT_ADMIN_PORTAL} --ignore-not-found
    - kubectl delete -n ${NAMESPACE} ingress ${PREFIX}${SHORT_PROJECT_NAME}-${COMPONENT_ADMIN_PORTAL} --ignore-not-found
  variables:
    NAMESPACE: "entw-${PROJECT_NAME}"
  environment:
    name: review/${CI_COMMIT_REF_NAME}/frontend
    action: stop
