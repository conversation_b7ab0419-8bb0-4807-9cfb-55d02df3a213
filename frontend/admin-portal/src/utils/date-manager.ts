import dayjs, { Dayjs } from "dayjs";

export type Unit = "days" | "hours" | "minutes" | "seconds" | "years" | "months";

export abstract class DateManger {
  abstract add: (value: number, unit: Unit) => DateManger;
  abstract addDays: (days: number) => DateManger;
  abstract addHours: (hours: number) => DateManger;
  abstract addMinutes: (minutes: number) => DateManger;
  abstract addSeconds: (seconds: number) => DateManger;
  abstract addYears: (years: number) => DateManger;
  abstract addMonths: (months: number) => DateManger;
  abstract subDays: (days: number) => DateManger;
  abstract subHours: (hours: number) => DateManger;
  abstract subMinutes: (minutes: number) => DateManger;
  abstract subSeconds: (seconds: number) => DateManger;
  abstract subYears: (years: number) => DateManger;
  abstract subMonths: (months: number) => DateManger;
  abstract format: (format: string) => string;
  abstract toDate: () => Date;
  abstract toISOString: () => string;
  abstract before: (date: DateManger) => boolean;
  abstract after: (date: DateManger) => boolean;
  abstract equals: (date: DateManger) => boolean;
  abstract startOf: (unit: "year" | "month" | "day" | "week") => DateManger;
  abstract endOf: (unit: "year" | "month" | "day" | "week") => DateManger;
  abstract getDay: () => number;
  abstract getDate: () => number;
  abstract setDate: (date: number) => DateManger;
  abstract setMonth: (month: number) => DateManger;
  abstract setYear: (year: number) => DateManger;
  abstract isAfter: (date: DateManger) => boolean;
  abstract isBefore: (date: DateManger) => boolean;
  abstract isSame: (date: DateManger) => boolean;
}

class DayjsDateManger implements DateManger {
  dayjsInstance: Dayjs;

  constructor(dayjsInstance: Dayjs) {
    this.dayjsInstance = dayjsInstance;
  }

  add(value: number, unit: Unit): DateManger {
    this.dayjsInstance = this.dayjsInstance.add(value, unit);
    return this;
  }

  addDays(days: number): DateManger {
    this.dayjsInstance = this.dayjsInstance.add(days, "day");
    return this;
  }

  addHours(hours: number): DateManger {
    this.dayjsInstance = this.dayjsInstance.add(hours, "hour");
    return this;
  }

  addMinutes(minutes: number): DateManger {
    this.dayjsInstance = this.dayjsInstance.add(minutes, "minute");
    return this;
  }

  addSeconds(seconds: number): DateManger {
    this.dayjsInstance = this.dayjsInstance.add(seconds, "second");
    return this;
  }

  addYears(years: number): DateManger {
    this.dayjsInstance = this.dayjsInstance.add(years, "year");
    return this;
  }

  addMonths(months: number): DateManger {
    this.dayjsInstance = this.dayjsInstance.add(months, "month");
    return this;
  }

  subDays(days: number): DateManger {
    this.dayjsInstance = this.dayjsInstance.subtract(days, "day");
    return this;
  }

  subHours(hours: number): DateManger {
    this.dayjsInstance = this.dayjsInstance.subtract(hours, "hour");
    return this;
  }

  subMinutes(minutes: number): DateManger {
    this.dayjsInstance = this.dayjsInstance.subtract(minutes, "minute");
    return this;
  }

  subSeconds(seconds: number): DateManger {
    this.dayjsInstance = this.dayjsInstance.subtract(seconds, "second");
    return this;
  }

  subYears(years: number): DateManger {
    this.dayjsInstance = this.dayjsInstance.subtract(years, "year");
    return this;
  }

  subMonths(months: number): DateManger {
    this.dayjsInstance = this.dayjsInstance.subtract(months, "month");
    return this;
  }

  format(format: string): string {
    return this.dayjsInstance.format(format);
  }

  toDate(): Date {
    return this.dayjsInstance.toDate();
  }

  toISOString(): string {
    return this.dayjsInstance.toISOString();
  }

  before(date: DateManger): boolean {
    return this.dayjsInstance.isBefore(date.toDate());
  }

  after(date: DateManger): boolean {
    return this.dayjsInstance.isAfter(date.toDate());
  }

  equals(date: DateManger): boolean {
    return this.dayjsInstance.isSame(date.toDate());
  }

  startOf(type: "day" | "month" | "year" | "week"): DateManger {
    this.dayjsInstance = this.dayjsInstance.startOf(type);
    return this;
  }

  endOf(type: "day" | "month" | "year" | "week"): DateManger {
    this.dayjsInstance = this.dayjsInstance.endOf(type);
    return this;
  }

  getDay(): number {
    return this.dayjsInstance.day();
  }

  getDate(): number {
    return this.dayjsInstance.date();
  }

  setDate(date: number): DateManger {
    this.dayjsInstance = this.dayjsInstance.set("date", date);
    return this;
  }

  setMonth(month: number): DateManger {
    this.dayjsInstance = this.dayjsInstance.month(month);
    return this;
  }

  setYear(year: number): DateManger {
    this.dayjsInstance = this.dayjsInstance.set("year", year);
    return this;
  }

  isAfter(date: DateManger): boolean {
    return this.dayjsInstance.isAfter(date.toDate());
  }

  isBefore(date: DateManger): boolean {
    return this.dayjsInstance.isBefore(date.toDate());
  }

  isSame(date: DateManger): boolean {
    return this.dayjsInstance.isSame(date.toDate());
  }
}

export const dateManager = (date: Date | string = new Date()): DateManger => {
  return new DayjsDateManger(dayjs(date));
};
