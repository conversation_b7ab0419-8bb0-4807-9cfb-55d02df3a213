"use client";

import { useEffect, useState } from "react";
import { enqueueSnackbar } from "notistack";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { useMutation } from "@tanstack/react-query";

import { Button } from "@interzero/oneepr-react-ui/Button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { queryClient } from "@/lib/react-query";
import { deleteCoupon } from "@/lib/api/coupon";

interface DeleteDiscountCodeDialogProps {
  discountCodeId: string | number;
  children: React.ReactNode;
}

export function DeleteDiscountCodeDialog({ discountCodeId, children }: DeleteDiscountCodeDialogProps) {
  const router = useRouter();

  const [isOpen, setIsOpen] = useState(false);
  const [isDone, setIsDone] = useState(false);

  useEffect(() => {
    if (!isOpen && isDone) {
      setIsDone(false);
      router.push("/discount-codes");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, isDone]);

  const { mutate, isPending } = useMutation({
    mutationFn: () => deleteCoupon(Number(discountCodeId)),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["coupons"] });
      setIsDone(true);
    },
    onError: (error) => {
      enqueueSnackbar(error.message, { variant: "error" });
    },
  });

  async function handleDelete() {
    mutate();
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>

      {!isDone && (
        <DialogContent className="bg-surface-02 py-9 max-w-2xl">
          <DialogHeader>
            <DialogTitle className="font-bold sm:text-[1.75rem]">Delete discount</DialogTitle>
            <DialogDescription className="text-tonal-dark-cream-20 font-normal">
              By clicking on ”confirm” you are deleting this discount.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <DialogClose asChild>
              <Button type="button" variant="outlined" color="dark-blue" size="medium">
                Back
              </Button>
            </DialogClose>
            <Button variant="filled" color="red" size="medium" onClick={handleDelete} disabled={isPending}>
              {isPending ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      )}

      {isDone && (
        <DialogContent className="bg-surface-02 py-9 max-w-2xl">
          <div className="flex items-center gap-2 mb-10">
            <Image src="/assets/images/leaf-seal.svg" alt="leaf seal" width={36} height={36} className="size-9" />
            <h3 className="font-bold text-[1.75rem] text-primary">Discount deleted with success</h3>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button type="button" variant="filled" color="yellow" size="medium">
                Close
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      )}
    </Dialog>
  );
}
