import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { getAdminUserById } from "@/lib/api/admin";
import { capitalizeFirstLetter } from "@/utils/capitalize-first-letter";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { DialogTrigger } from "@radix-ui/react-dialog";
import { useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import SyntaxHighlighter from "react-syntax-highlighter";

interface AuditLogUserDialogProps {
  userId: number;
  children: React.ReactNode;
}

export function AuditLogUserDialog({ userId, children }: AuditLogUserDialogProps) {
  const {
    data: adminUser,
    refetch,
    isLoading,
  } = useQuery({
    queryKey: ["admin-user-log", userId],
    queryFn: () => getAdminUserById(userId),
    enabled: false,
  });

  const [isDialogOpen, setIsDialogOpen] = useState(false);

  async function handleDialogOpenChange(open: boolean) {
    setIsDialogOpen(open);
  }

  useEffect(() => {
    if (isDialogOpen) refetch();
    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isDialogOpen, adminUser]);

  return (
    <Dialog open={isDialogOpen} onOpenChange={handleDialogOpenChange}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="max-w-2xl z-[1001]">
        <DialogHeader>
          <DialogTitle>Audit Log Details</DialogTitle>
        </DialogHeader>
        <div className="w-full mt-12">
          <div>
            <div className="flex flex-col gap-5 items-start">
              <Info title="IP Address:" info="---" isLoading={isLoading} />
              <Info title="Account:" info={`${adminUser?.name} - ${adminUser?.email}`} isLoading={isLoading} />
              <Info title="Role:" info={capitalizeFirstLetter(adminUser?.Role?.display_name)} isLoading={isLoading} />
              <div className="h-[1px] w-full bg-tonal-dark-cream-80 rounded-full" />
              <Info title="Last Event:" info={`Registered Country`} isLoading={isLoading} />
              <Info title="Time:" info={`15.08.2022, 15h13`} isLoading={isLoading} />
            </div>

            <div className="h-[1px] w-full bg-tonal-dark-cream-80 rounded-full my-8" />

            <DetailsJson
              isLoading={isLoading}
              json={{
                authType: `PASSWORD`,
                mfaFactorType: adminUser?.type,
              }}
            />
          </div>
          <div className="flex flex-col mt-8">
            <div className="flex items-center justify-end">
              <Button
                type="button"
                variant="filled"
                color="yellow"
                size="medium"
                disabled={!adminUser}
                onClick={() => handleDialogOpenChange(false)}
              >
                Close
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

const Info = ({ title, info, isLoading }: { title: string; info: string; isLoading: boolean }) => {
  return (
    <div className="w-full flex justify-start gap-3">
      <p className="text-tonal-dark-cream-10 font-bold">{title}</p>
      {isLoading ? <Skeleton className="w-full h-5" /> : <p className="text-tonal-dark-cream-10">{info}</p>}
    </div>
  );
};

const customTheme = {
  hljs: {
    color: "#2E2E2E", // Define a cor base do texto como vermelho claro
    background: "#f0f0ef", // Fundo opcional (pode alterar ou remover)
  },
  "hljs-string": {
    color: "#E74141",
  },
};

const DetailsJson = ({ json, isLoading }: { json: { [key: string]: unknown }; isLoading: boolean }) => {
  return (
    <div>
      <p className="text-grey-blue font-bold mb-3">Details JSON:</p>
      {isLoading ? (
        <Skeleton className="w-full h-16" />
      ) : (
        <div className="bg-surface-01 rounded-lg w-full p-5">
          <SyntaxHighlighter language="json" wrapLines style={customTheme}>
            {JSON.stringify(json, null, 2)}
          </SyntaxHighlighter>
        </div>
      )}
    </div>
  );
};
