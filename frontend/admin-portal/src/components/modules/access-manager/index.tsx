"use client";

import { ModuleTitle } from "@/components/common/module-title";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Add, PeopleAlt } from "@interzero/oneepr-react-ui/Icon";
import { ModuleContent } from "@/components/common/module-content";
import { CreateUserDialog } from "./create-user-dialog";
import { useRef } from "react";
import AccountManagerTable from "./access-manager-table";

export function AccessManagerModule() {
  const accountManagerTableRef = useRef<{ refetch: () => void }>(null);

  const handleRefetch = () => {
    if (!accountManagerTableRef.current) return;

    accountManagerTableRef?.current?.refetch();
  };

  return (
    <ModuleContent>
      <div className="flex items-start justify-between">
        <ModuleTitle icon={PeopleAlt} title="Access Manager" description="Mange the access for all the clerks." />
        <CreateUserDialog onSubmitSuccessfully={handleRefetch}>
          <Button variant="filled" size="medium" color="yellow" leadingIcon={<Add />}>
            Create new
          </Button>
        </CreateUserDialog>
      </div>
      <AccountManagerTable ref={accountManagerTableRef} />
    </ModuleContent>
  );
}
