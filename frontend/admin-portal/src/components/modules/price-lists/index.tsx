"use client";
import { ModuleTitle } from "@/components/common/module-title";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Add, NotificationAdd } from "@interzero/oneepr-react-ui/Icon";
import { PriceListsTable } from "./price-lists-table";
import Link from "next/link";
import { ModuleContent } from "@/components/common/module-content";

export function PriceListsModule() {
  return (
    <ModuleContent>
      <div className="flex items-start justify-between">
        <ModuleTitle
          icon={NotificationAdd}
          title="General Price Lists"
          description="See all the price lists you've created. You can edit these list by clicking on it."
        />
        <Link href="/price-lists/create">
          <Button variant="filled" size="medium" color="yellow" leadingIcon={<Add />}>
            Create new
          </Button>
        </Link>
      </div>
      <PriceListsTable />
    </ModuleContent>
  );
}
