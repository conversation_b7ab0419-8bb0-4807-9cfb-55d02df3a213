"use client";

import { ModuleTitle } from "@/components/common/module-title";
import { Dashboard, KeyboardArrowDown, KeyboardArrowUp } from "@interzero/oneepr-react-ui/Icon";
import { AccountsTable } from "./accounts-table";
import { ModuleContent } from "@/components/common/module-content";
import { useQuery } from "@tanstack/react-query";
import { getCustomersMonthlySummary } from "@/lib/api/customer";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/utils/cn";

export function AccountsModule() {
  const { data: summary, isLoading } = useQuery({
    queryKey: ["customers-summary"],
    queryFn: getCustomersMonthlySummary,
  });

  const cards = [
    {
      title: "Create accounts",
      value: summary?.created.current,
      growthRate: summary?.created.growthRate,
      labelColor: "text-tonal-blue-40",
      bgColor: "bg-[#EBF4FF]",
      iconColor: "fill-tonal-blue-40",
    },
    {
      title: "Active accounts",
      value: summary?.active.current,
      growthRate: summary?.active.growthRate,
      labelColor: "text-tonal-dark-green-30",
      bgColor: "bg-tonal-green-90",
      iconColor: "fill-tonal-dark-green-30",
    },
    {
      title: "Terminated accounts",
      value: summary?.terminated.current,
      growthRate: summary?.terminated.growthRate,
      labelColor: "text-tonal-red-50",
      bgColor: "bg-[#FEF0EC]",
      iconColor: "fill-tonal-red-50",
    },
  ];

  const lastMonthName = (() => {
    const date = new Date();
    date.setMonth(date.getMonth() - 1);
    return date.toLocaleString("default", { month: "long" });
  })();

  return (
    <ModuleContent>
      <ModuleTitle
        icon={Dashboard}
        title="Account List"
        description="Lorem ipsum dolor sit amet consectetur. Risus nulla egestas orci non hendrerit. "
      />
      <div className="w-full grid grid-cols-1 lg:grid-cols-3 gap-6 mb-16">
        {isLoading
          ? Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className={cn("flex flex-col gap-4 p-5 rounded-xl min-h-[212px]", cards[index].bgColor)}>
                <Skeleton className="h-6 w-11/12" />
                <Skeleton className="h-16 w-28" />
                <Skeleton className="h-3 w-36" />
                <Skeleton className="h-3 w-11/12" />
              </div>
            ))
          : cards.map((card) => (
              <div key={card.title} className={cn(`flex flex-col gap-4 p-5 rounded-xl animate-fadeIn ${card.bgColor}`)}>
                <h3 className="text-xl font-bold text-primary">{card.title}</h3>
                <div className="flex flex-col gap-2">
                  <h4 className={cn("text-6xl line-height-[78px] font-bold", card.labelColor)}>{card.value}</h4>
                  <p className="text-sm text-primary">Total {card.title}</p>
                </div>
                {card.growthRate ? (
                  <span
                    className={cn(
                      "flex items-center gap-1",
                      card.growthRate > 0 ? card.labelColor : "text-tonal-red-50"
                    )}
                  >
                    {card.growthRate > 0 ? (
                      <KeyboardArrowUp width={24} className={card.iconColor} />
                    ) : (
                      <KeyboardArrowDown width={24} className="fill-tonal-red-50" />
                    )}
                    {card.growthRate}% {card.growthRate > 0 ? "more" : "less"} than {lastMonthName}
                  </span>
                ) : (
                  <span className="text-primary"></span>
                )}
              </div>
            ))}
      </div>
      <AccountsTable />
    </ModuleContent>
  );
}
