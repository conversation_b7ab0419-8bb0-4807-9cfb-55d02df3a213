"use client";

import { CustomDropdown } from "@/components/common/custom-dropdown";
import { Divider } from "@/components/common/divider";
import Status, { StatusType } from "@/components/common/license-status";
import { ModuleContent } from "@/components/common/module-content";
import { ModuleTitle } from "@/components/common/module-title";
import { CountryIcon } from "@/components/ui/country-icon";
import { Skeleton } from "@/components/ui/skeleton";
import { getCustomer } from "@/lib/api/customer";
import { Contract, LicenseClerkControlStatus, LicenseContractStatus } from "@/lib/api/customer/types";
import { dateManager } from "@/utils/date-manager";
import { formatCustomerNumber } from "@/utils/format-customer-number";
import { Elipse } from "@interzero/oneepr-react-ui/Icon";
import { useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";

interface AccountModuleProps {
  customerId: string;
}

export function AccountModule({ customerId }: AccountModuleProps) {
  const { data: customer, isLoading } = useQuery({
    queryKey: ["customer", customerId],
    queryFn: () => getCustomer(customerId),
  });

  const customerServices = (() => {
    const result = customer?.contracts.reduce((acc, contract) => acc + getServiceType(contract) + "; ", "");
    if ((customer?.contracts.length || 0) > 1) return result;
    return result?.replace(";", "");
  })();

  const countryList = (() => {
    const countries: { flag_url: string; country_name: string }[] = [];
    for (const contract of customer?.contracts ?? []) {
      for (const license of contract.licenses ?? []) {
        if (countries.find((country) => country.country_name === license.country_name)) continue;

        countries.push({ flag_url: license.country_flag, country_name: license.country_name });
      }
      for (const actionGuide of contract.action_guides ?? []) {
        if (countries.find((country) => country.country_name === actionGuide.country_name)) continue;
        countries.push({ flag_url: actionGuide.country_flag, country_name: actionGuide.country_name });
      }
    }

    return countries;
  })();

  const status = (() => {
    if (!customer?.contracts) return "ACTIVE";
    for (const contract of customer?.contracts) {
      if (contract.status !== "ACTIVE") return contract.status;
      if (contract.licenses) {
        for (const license of contract.licenses) {
          if (license.contract_status !== "ACTIVE") return license.contract_status;
        }
      }
      if (contract.action_guides) {
        for (const actionGuide of contract.action_guides) {
          if (actionGuide.contract_status !== "ACTIVE") return actionGuide.contract_status;
        }
      }
    }
    return "ACTIVE";
  })();
  const statusLabel = getStatusLabel(status);

  const usedCompany = customer?.companies?.[0];

  const availableYears = (() => {
    if (!customer?.contracts) return [];

    const years = new Set<number>();

    for (const contract of customer?.contracts) {
      for (const license of contract.licenses ?? []) {
        if (license.year) years.add(license.year);
      }
    }

    return Array.from(years).sort((a, b) => b - a);
  })();

  useEffect(() => {
    setSelectedYear(availableYears[0]);
  }, [availableYears]);

  const [selectedYear, setSelectedYear] = useState(() => {
    return availableYears[0];
  });

  const euLicenseContract = customer?.contracts.find((contract) => contract.type === "EU_LICENSE");
  const directLicenseContract = customer?.contracts.find((contract) => contract.type === "DIRECT_LICENSE");
  const actionGuideContract = customer?.contracts.find((contract) => contract.type === "ACTION_GUIDE");

  const euLicenses = (euLicenseContract?.licenses || []).filter((license) => license.year === selectedYear);
  const directLicense =
    (directLicenseContract?.licenses || []).find((license) => license.year === selectedYear) || null;
  const actionGuides = actionGuideContract?.action_guides || [];

  if (isLoading) return <AccountModuleSkeleton customerId={customerId} />;

  return (
    <ModuleContent>
      <div className="mb-16">
        <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
          <div className="flex items-start gap-6 col-span-3">
            <ModuleTitle title={`Customer #${formatCustomerNumber(customer?.id)}`} />
          </div>
          <div className="col-span-2">
            <div className="space-y-2 gap-2">
              <p className=" text-tonal-dark-cream-40">Countries</p>
              <div className="flex items-center gap-2">
                <div className="flex items-center gap-2">
                  {countryList?.map((country) => (
                    <CountryIcon
                      key={country.country_name}
                      country={{ name: country.country_name, flag_url: country.flag_url }}
                    />
                  ))}
                </div>
                <p className="text-tonal-dark-cream-40">
                  ({countryList?.length < 10 ? `0${countryList?.length}` : countryList?.length})
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-4 mt-4">
          {customer?.created_at && (
            <p className="text-tonal-dark-cream-40">
              Enrolled in: {dateManager(customer.created_at).format("DD.MM.YYYY")}
            </p>
          )}
          <div className="w-[1px] h-3 py-2 bg-support-blue-opacity rounded-full" />
          <p className="text-tonal-dark-cream-40">Service type: {customerServices}</p>
          <div className="w-[1px] h-3 py-2 bg-support-blue-opacity rounded-full" />
          <p className="text-tonal-dark-cream-40 flex items-center gap-2">
            Status:{" "}
            <div
              data-status={status}
              className="group flex items-center gap-1 text-tonal-dark-cream-30 data-[status=ACTIVE]:text-success"
            >
              <Elipse className="flex-none fill-tonal-dark-cream-30 group-data-[status=ACTIVE]:fill-success size-2" />
              <p className="font-bold text-tonal-dark-cream-30 group-data-[status=ACTIVE]:text-success">
                {statusLabel}
              </p>
            </div>
          </p>
        </div>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
        <div className="col-span-3 bg-white rounded-3xl py-9 px-8">
          <div className="flex justify-between items-center w-full">
            <h3 className="text-primary text-2xl font-bold">Countries</h3>
            {!!availableYears.length && (
              <CustomDropdown
                options={availableYears.map((year) => ({ label: year.toString(), value: year.toString() }))}
                selectedOption={(selectedYear || availableYears[0]).toString()}
                handleSelect={(year) => setSelectedYear(Number(year))}
              />
            )}
          </div>
          <Divider initialMarginDisabled className="my-4 mb-8" />
          <div className="space-y-7">
            <div className="space-y-4">
              <h3 className="text-2xl font-bold text-tonal-dark-cream-20">Licensing Services</h3>
              <div className="flex flex-col">
                {euLicenses.map((license) => (
                  <div key={license.id} className="flex items-center justify-between px-4 py-5">
                    <div className="flex items-center gap-3">
                      <CountryIcon
                        country={{ name: license.country_name, flag_url: license.country_flag }}
                        className="size-10"
                      />
                      <p className="text-large-paragraph-regular font-bold text-primary">{license.country_name}</p>
                    </div>
                    <Status status={normalizeStatus(license.clerk_control_status)} />
                  </div>
                ))}
              </div>
            </div>
            <div className="space-y-4">
              <h3 className="text-2xl font-bold text-tonal-dark-cream-20">Action Guide</h3>
              <div className="flex flex-col">
                {actionGuides.map((actionGuide) => (
                  <div key={actionGuide.id} className="flex items-center justify-between px-4 py-5">
                    <div className="flex items-center gap-3">
                      <CountryIcon
                        country={{ name: actionGuide.country_name, flag_url: actionGuide.country_flag }}
                        className="size-10"
                      />
                      <p className="text-large-paragraph-regular font-bold text-primary">{actionGuide.country_name}</p>
                    </div>
                    <Status status="Active" />
                  </div>
                ))}
              </div>
            </div>
            <div className="space-y-4">
              <h3 className="text-2xl font-bold text-tonal-dark-cream-20">Direct License</h3>
              <div className="flex flex-col">
                {directLicense && (
                  <div key={directLicense.id} className="flex items-center justify-between px-4 py-5">
                    <div className="flex items-center gap-3">
                      <CountryIcon
                        country={{ name: directLicense.country_name, flag_url: directLicense.country_flag }}
                        className="size-10"
                      />
                      <p className="text-large-paragraph-regular font-bold text-primary">
                        {directLicense.country_name}
                      </p>
                    </div>
                    <Status status={normalizeStatus(directLicense.clerk_control_status)} />
                  </div>
                )}
              </div>
            </div>
            <p className="text-tonal-dark-cream-50 text-center py-5">End of the list</p>
          </div>
        </div>
        <div className="col-span-2 space-y-6">
          <div className="w-full bg-white rounded-3xl p-6 flex flex-col gap-6">
            <h3 className="text-primary text-2xl font-bold">Contact Information</h3>
            <div>
              <div className="grid grid-cols-1 sm:grid-cols-2">
                <div className="p-4 space-y-2">
                  <label className="text-tonal-dark-cream-40 text-sm">Full Name</label>
                  <p className="text-primary">
                    {customer?.first_name} {customer?.last_name}
                  </p>
                </div>
                <div className="p-4 space-y-2">
                  <label className="text-tonal-dark-cream-40 text-sm">Phone/Mobile</label>
                  <p className="text-primary">{customer?.phones[0]?.phone_number}</p>
                </div>
              </div>
              <div className="grid grid-cols-1">
                <div className="p-4 space-y-2">
                  <label className="text-tonal-dark-cream-40 text-sm">E-mail</label>
                  <p className="text-primary">{customer?.email}</p>
                </div>
              </div>
            </div>
          </div>
          <div className="w-full bg-white rounded-3xl p-6 flex flex-col gap-6">
            <h3 className="text-primary text-2xl font-bold">Contract</h3>
            <div>
              <div className="grid grid-cols-1 sm:grid-cols-2">
                <div className="p-4 space-y-2">
                  <label className="text-tonal-dark-cream-40 text-sm">Contract Since</label>
                  <p className="text-primary">
                    {customer?.contracts?.[0]?.start_date
                      ? dateManager(customer?.contracts?.[0]?.start_date).format("DD.MM.YYYY")
                      : "-"}
                  </p>
                </div>
                <div className="p-4 space-y-2">
                  <label className="text-tonal-dark-cream-40 text-sm">Contract Until</label>
                  <p className="text-primary">
                    {customer?.contracts?.[0]?.end_date
                      ? dateManager(customer?.contracts?.[0]?.end_date).format("DD.MM.YYYY")
                      : "-"}
                  </p>
                </div>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2">
                <div className="p-4 space-y-2">
                  <label className="text-tonal-dark-cream-40 text-sm">Last date EU Termination</label>
                  <p className="text-primary">
                    {customer?.contracts?.find((contract) => contract.type === "EU_LICENSE")?.termination_date
                      ? dateManager(
                          customer?.contracts?.find((contract) => contract.type === "EU_LICENSE")
                            ?.termination_date as unknown as string
                        ).format("DD.MM.YYYY")
                      : "-"}
                  </p>
                </div>
                <div className="p-4 space-y-2">
                  <label className="text-tonal-dark-cream-40 text-sm">Last date GE Termination</label>
                  <p className="text-primary">
                    {customer?.contracts?.find((contract) => contract.type === "DIRECT_LICENSE")?.termination_date
                      ? dateManager(
                          customer?.contracts?.find((contract) => contract.type === "DIRECT_LICENSE")
                            ?.termination_date as unknown as string
                        ).format("DD.MM.YYYY")
                      : "-"}
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div className="w-full bg-white rounded-3xl p-6 flex flex-col gap-6">
            <h3 className="text-primary text-2xl font-bold">Company Data</h3>
            <div>
              <div className="grid grid-cols-1">
                <div className="p-4 space-y-2">
                  <label className="text-tonal-dark-cream-40 text-sm">Company name</label>
                  <p className="text-primary">{usedCompany?.name}</p>
                </div>
                <div className="p-4 space-y-2">
                  <label className="text-tonal-dark-cream-40 text-sm">Location</label>
                  <p className="text-primary">
                    {usedCompany?.address.country_code ?? "-"}
                    {", "}
                    {usedCompany?.address.city ?? "-"}
                  </p>
                </div>
                <div className="p-4 space-y-2">
                  <label className="text-tonal-dark-cream-40 text-sm">Address</label>
                  <p className="text-primary">{usedCompany?.address.street_and_number ?? "-"}</p>
                </div>
                <div className="flex flex-col md:flex-row gap-2">
                  <div className="p-4 space-y-2 flex-1">
                    <label className="text-tonal-dark-cream-40 text-sm">VAT</label>
                    <p className="text-primary">{usedCompany?.vat ?? "-"}</p>
                  </div>
                  <div className="p-4 space-y-2 flex-1">
                    <label className="text-tonal-dark-cream-40 text-sm">TIN</label>
                    <p className="text-primary">{usedCompany?.tin ?? "-"}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ModuleContent>
  );
}

function AccountModuleSkeleton({ customerId }: AccountModuleProps) {
  return (
    <ModuleContent>
      <div className="mb-16">
        <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
          <div className="flex items-start gap-6 col-span-3">
            <ModuleTitle title={`Customer #${customerId}`} />
          </div>
          <div className="flex flex-col gap-3">
            <Skeleton className="h-4 w-20" />
            <div className="flex gap-2">
              {Array.from({ length: 3 }).map((_, index) => (
                <Skeleton key={index} className="h-8 w-8 rounded-full" />
              ))}
            </div>
          </div>
        </div>
        <div className="flex items-center gap-4">
          <Skeleton className="h-6 w-4/5 mt-4" />
        </div>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
        <div className="col-span-3 bg-white rounded-3xl py-9 px-8">
          <div className="flex justify-between items-center w-full">
            <Skeleton className="h-8 w-32" />
            <Skeleton className="h-10 w-28" />
          </div>
          <Divider initialMarginDisabled className="my-4 mb-8" />
          <div className="space-y-8">
            <div>
              <Skeleton className="h-8 w-48 mb-4" />
              <div className="flex flex-col p-4 gap-8">
                {Array.from({ length: 2 }).map((_, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Skeleton className="h-10 w-10 rounded-full" />
                      <Skeleton className="h-6 w-32" />
                    </div>
                    <Skeleton className="h-6 w-24" />
                  </div>
                ))}
              </div>
            </div>
            <div>
              <Skeleton className="h-8 w-36 mb-4" />
              <div className="flex flex-col p-4 gap-8">
                {Array.from({ length: 2 }).map((_, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Skeleton className="h-10 w-10 rounded-full" />
                      <Skeleton className="h-6 w-32" />
                    </div>
                    <Skeleton className="h-6 w-24" />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
        <div className="col-span-2 space-y-6">
          <div className="w-full bg-white rounded-3xl p-4 flex flex-col">
            <Skeleton className="h-6 w-11/12" />
            <div className="grid grid-cols-2 p-4">
              <div className="flex flex-col gap-4">
                <Skeleton className="h-6 w-4/5" />
                <Skeleton className="h-6 w-11/12" />
              </div>
              <div className="flex flex-col gap-4">
                <Skeleton className="h-6 w-4/5" />
                <Skeleton className="h-6 w-11/12" />
              </div>
            </div>
            <div className="flex flex-col p-4 gap-4">
              <Skeleton className="h-6 w-4/5" />
              <Skeleton className="h-6 w-11/12" />
            </div>
          </div>
          <div className="w-full bg-white rounded-3xl p-6 flex flex-col gap-6">
            <Skeleton className="h-6 w-11/12" />
            <div className="grid grid-cols-2 p-4">
              <div className="flex flex-col gap-4">
                <Skeleton className="h-6 w-11/12" />
                <Skeleton className="h-6 w-11/12" />
              </div>
              <div className="flex flex-col gap-4">
                <Skeleton className="h-6 w-11/12" />
                <Skeleton className="h-6 w-11/12" />
              </div>
            </div>
          </div>
          <div className="w-full bg-white rounded-3xl p-6 flex flex-col gap-6">
            <Skeleton className="h-6 w-11/12" />
            <div className="grid grid-cols-1">
              <div className="flex flex-col gap-4">
                <Skeleton className="h-6 w-11/12" />
                <Skeleton className="h-6 w-11/12" />
                <Skeleton className="h-6 w-11/12" />
                <Skeleton className="h-6 w-11/12" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </ModuleContent>
  );
}

function getServiceType(contract: Contract) {
  if (contract.type === "EU_LICENSE") return "EU License";
  if (contract.type === "ACTION_GUIDE") return "Action Guide";
  if (contract.type === "DIRECT_LICENSE") return "Direct License";
  return "";
}

function getStatusLabel(status: LicenseContractStatus) {
  if (status === "ACTIVE") return "Active";
  if (status === "TERMINATED") return "Terminated";
  if (status === "TERMINATION_PROCESS") return "Termination Process";
  return "";
}

function normalizeStatus(status: LicenseClerkControlStatus): StatusType {
  if (status === "PENDING") return "Open to do’s";
  return "No open to do’s";
}
