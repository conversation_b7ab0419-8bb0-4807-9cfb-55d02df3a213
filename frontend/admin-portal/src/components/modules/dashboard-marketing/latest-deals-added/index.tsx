"use client";

import { Divider } from "@/components/common/divider";
import { Launch } from "@interzero/oneepr-react-ui/Icon";
import Image from "next/image";

const coupons = [
  {
    id: 1,
    name: "E-bay",
    duration: "12.02.23 - 15.05.23",
    coupon: "Licenseero25",
    image: "https://via.placeholder.com/50",
    link: "#",
  },
  {
    id: 2,
    name: "E-bay",
    duration: "12.02.23 - 15.05.23",
    coupon: "Licenseero25",
    image: "https://via.placeholder.com/50",
    link: "#",
  },
  {
    id: 3,
    name: "E-bay",
    duration: "12.02.23 - 15.05.23",
    coupon: "Licenseero25",
    image: "https://via.placeholder.com/50",
    link: "#",
  },
  {
    id: 4,
    name: "E-bay",
    duration: "12.02.23 - 15.05.23",
    coupon: "Licenseero25",
    image: "https://via.placeholder.com/50",
    link: "#",
  },
];

export function LatestDealsAdded() {
  return (
    <div id="partner-hub" className="w-full h-full bg-white rounded-3xl p-6 flex flex-col">
      <h3 className="text-primary text-2xl font-bold mb-2">Latest deals added</h3>
      <p className="text-[#808FA9] mb-2">From the partner&apos;s deals</p>

      <div className="w-full h-full flex flex-col justify-center">
        {coupons.map((coupon) => (
          <>
            <div key={coupon.id} className="flex items-center justify-between p-4 border-b">
              <div className="flex items-center gap-3">
                <Image
                  src={coupon.image}
                  alt={coupon.name}
                  className="w-10 h-10 rounded-full bg-secondary"
                  unoptimized
                />
                <div className="flex flex-col gap-2">
                  <h3 className="text-paragraph-regular text-tonal-dark-cream-10">{coupon.name}</h3>
                  <p className="text-small-paragraph-regular text-tonal-dark-cream-50">Duration: {coupon.duration}</p>
                </div>
              </div>
              <div className="flex flex-col items-end gap-2">
                <a href={coupon.link} target="_blank" rel="noopener noreferrer">
                  <Launch className="fill-support-blue size-5 cursor-pointer" />
                </a>
                <p className="text-small-paragraph-regular text-tonal-dark-cream-50">Cupom: {coupon.coupon}</p>
              </div>
            </div>
            <Divider initialMarginDisabled />
          </>
        ))}
      </div>
    </div>
  );
}
