"use client";

import { ChartData } from "@/components/common/charts/area-chart-linear";
import { Dropdown, DropdownItem } from "@/components/ui/dropdown";
import { MonthDatePickerWithRange } from "@/components/ui/month-date-picker";
import { Skeleton } from "@/components/ui/skeleton";
import { getTotalCustomers } from "@/lib/api/dashboard/total-customers";
import { KeyboardArrowDown } from "@interzero/oneepr-react-ui/Icon";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { DateRange } from "react-day-picker";
import { ChartItem } from "../../dashboard/sections/total-customers/chart-item";

const LICENSE_YEAR_FILTERS = [
  { label: "2025", value: "2025" },
  { label: "2024", value: "2024" },
  { label: "2023", value: "2023" },
];

function formatToChartData(value: number): ChartData {
  return [
    { name: "1", value: value },
    { name: "2", value: value },
  ];
}

const TotalCustomersSkeletons = () => (
  <>
    <Skeleton className="h-16 w-full rounded-xl" />
    <Skeleton className="h-16 w-full rounded-xl" />
    <Skeleton className="h-16 w-full rounded-xl" />
    <Skeleton className="h-16 w-full rounded-xl" />
    <Skeleton className="h-16 w-full rounded-xl" />
    <Skeleton className="h-16 w-full rounded-xl" />
    <Skeleton className="h-16 w-full rounded-xl" />
  </>
);

export function TotalCustomers() {
  const [selectedLicenseYear, setSelectedLicenseYear] = useState(LICENSE_YEAR_FILTERS[0]);
  const [selectedDateInterval, setSelectedDateInterval] = useState<DateRange | undefined>();

  const { data, isSuccess, isLoading } = useQuery({
    queryKey: ["total-customers", selectedLicenseYear.value, selectedDateInterval?.from, selectedDateInterval?.to],
    queryFn: () =>
      getTotalCustomers({
        year: +selectedLicenseYear.value,
        start_date: selectedDateInterval?.from,
        end_date: selectedDateInterval?.to,
      }),
  });

  function handleChangeLicenseYear(newOrder: (typeof LICENSE_YEAR_FILTERS)[number]) {
    setSelectedLicenseYear(newOrder);
  }

  return (
    <div id="total-customers" className="w-full bg-white rounded-3xl p-6 flex flex-col gap-6">
      <div className="flex flex-row justify-between items-center">
        <h3 className="text-primary text-2xl font-bold">Total customers origin</h3>

        <div className="flex flex-row gap-6 items-center">
          <Dropdown
            trigger={
              <button className="flex items-center text-support-blue font-bold text-base">
                <span className="ml-1 mr-2 mt-1 text-left">{selectedLicenseYear.label}</span>
                <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
              </button>
            }
          >
            {LICENSE_YEAR_FILTERS.map((filter, idx) => (
              <DropdownItem
                key={idx}
                onClick={() => handleChangeLicenseYear(filter)}
                className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
              >
                {filter.label}
              </DropdownItem>
            ))}
          </Dropdown>
          <MonthDatePickerWithRange onDateChange={setSelectedDateInterval} />
        </div>
      </div>

      <p className="text-[#808FA9] -mt-4">Values in thousands</p>

      <div className="grid grid-cols-2 gap-6">
        <div className="flex flex-col gap-10 rounded-2xl bg-surface-01 px-10 py-6">
          {isLoading && <TotalCustomersSkeletons />}
          {isSuccess && (
            <div className="flex flex-col items-start justify-center gap-16 h-full">
              <div className="flex flex-col gap-4">
                <p className="text-h3 text-primary font-bold">250 598 659</p>
                <p className="text-large-paragraph-regular text-primary">Customers in total</p>
              </div>
              <div className="flex flex-col gap-4">
                <p className="text-h3 text-primary font-bold">{data.net_revenues_summary.overral_products / 100}</p>
                <p className="text-large-paragraph-regular text-primary">Acquired Last Month</p>
              </div>
            </div>
          )}
        </div>

        <div className="flex flex-col gap-10 py-6">
          {isLoading && <TotalCustomersSkeletons />}
          {isSuccess && (
            <>
              <ChartItem
                chartData={formatToChartData(data.new_customers_summary.eu_licensed)}
                value={data.new_customers_summary.eu_licensed}
                label="Marketing Action Lead"
                color="blue"
              />
              <ChartItem
                chartData={formatToChartData(data.new_customers_summary.other_services)}
                value={data.new_customers_summary.other_services}
                label="Partner Lead"
                color="orange"
              />
              <ChartItem
                chartData={formatToChartData(data.new_customers_summary.direct_licensed)}
                value={data.new_customers_summary.direct_licensed}
                label="Without Cupom"
                color="red"
              />
              <ChartItem
                chartData={formatToChartData(data.new_customers_summary.brokers)}
                value={data.new_customers_summary.brokers}
                label="Invite Customers Lead"
                color="yellow"
              />
            </>
          )}
        </div>
      </div>
    </div>
  );
}
