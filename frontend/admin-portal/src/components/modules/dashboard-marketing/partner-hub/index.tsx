"use client";

import { Divider } from "@/components/common/divider";
import { Launch } from "@interzero/oneepr-react-ui/Icon";

export function PartnerHub() {
  const companies = ["E-bay", "Amazon", "Shopee", "Wish.com", "Aliexpress"];

  return (
    <div id="partner-hub" className="w-full h-full bg-white rounded-3xl p-6 flex flex-col gap-6">
      <div className="flex flex-row items-center gap-4">
        <h3 className="text-primary text-2xl font-bold">Partner Hub</h3>
        <Launch className="fill-support-blue size-6 cursor-pointer" />
      </div>
      <div>
        {companies.map((company, index) => (
          <>
            <div key={index} className="flex flex-row gap-4 items-center mb-4">
              <p className="text-title-3 text-[#808FA9] font-bold">{`${index + 1}º`}</p>
              <p key={index} className="text-paragraph-regular text-tonal-dark-cream-10">
                {company}
              </p>
            </div>
            <Divider initialMarginDisabled className="mb-4" />
          </>
        ))}
      </div>
    </div>
  );
}
