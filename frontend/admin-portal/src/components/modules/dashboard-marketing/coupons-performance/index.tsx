"use client";

import { Dropdown, DropdownItem } from "@/components/ui/dropdown";
import { MonthDatePickerWithRange } from "@/components/ui/month-date-picker";
import { Elipse, KeyboardArrowDown, Launch } from "@interzero/oneepr-react-ui/Icon";
import { useState } from "react";
import { DateRange } from "react-day-picker";
import CustomLine<PERSON>hart from "./line-chart";

const LICENSE_YEAR_FILTERS = [
  { label: "2025", value: "2025" },
  { label: "2024", value: "2024" },
  { label: "2023", value: "2023" },
];

export function CouponsPerformance() {
  const [selectedLicenseYear, setSelectedLicenseYear] = useState(LICENSE_YEAR_FILTERS[0]);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [selectedDateInterval, setSelectedDateInterval] = useState<DateRange | undefined>();

  function handleChangeLicenseYear(newOrder: (typeof LICENSE_YEAR_FILTERS)[number]) {
    setSelectedLicenseYear(newOrder);
  }

  return (
    <div id="accounts-created" className="w-full h-full bg-white rounded-3xl p-6 flex flex-col gap-6">
      <div className="flex flex-row justify-between items-center">
        <div className="flex flex-row items-center gap-4">
          <h3 className="text-primary text-2xl font-bold">Coupons Performance</h3>
          <Launch className="fill-support-blue size-6 cursor-pointer" />
        </div>

        <div className="flex flex-row gap-6 items-center">
          <Dropdown
            trigger={
              <button className="flex items-center text-support-blue font-bold text-base">
                <span className="ml-1 mr-2 mt-1 text-left">{selectedLicenseYear.label}</span>
                <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
              </button>
            }
          >
            {LICENSE_YEAR_FILTERS.map((filter, idx) => (
              <DropdownItem
                key={idx}
                onClick={() => handleChangeLicenseYear(filter)}
                className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
              >
                {filter.label}
              </DropdownItem>
            ))}
          </Dropdown>
          <MonthDatePickerWithRange onDateChange={setSelectedDateInterval} />
        </div>
      </div>
      <p className="text-[#808FA9] -mt-4">Values in thousands of euros</p>
      <div className="flex flex-col items-start w-[300px] gap-3">
        <div className="flex items-center justify-between w-full gap-1">
          <div className="flex items-center gap-1">
            <Elipse className="fill-[#F1988D] size-4" />
            <p className="text-small-paragraph-regular text-primary">LIZENZER0</p>
            <KeyboardArrowDown className="fill-primary size-5" />
          </div>
          <p className="text-large-paragraph-regular text-[#F1988D]">16 052</p>
        </div>
        <div className="flex items-center justify-between w-full gap-1">
          <div className="flex items-center gap-1">
            <Elipse className="fill-[#1B6C64] size-4" />
            <p className="text-small-paragraph-regular text-primary">WINTER10OFF</p>
            <KeyboardArrowDown className="fill-primary size-5" />
          </div>
          <p className="text-large-paragraph-regular text-[#1B6C64]">210 054</p>
        </div>
        <div className="flex items-center justify-between w-full gap-1">
          <div className="flex items-center gap-1">
            <Elipse className="fill-[#FF9E14] size-4" />
            <p className="text-small-paragraph-regular text-primary">EBAY</p>
            <KeyboardArrowDown className="fill-primary size-5" />
          </div>
          <p className="text-large-paragraph-regular text-[#FF9E14]">15 560</p>
        </div>
        <div className="flex items-center justify-between w-full gap-1">
          <div className="flex items-center gap-1">
            <Elipse className="fill-[#009DD3] size-4" />
            <p className="text-small-paragraph-regular text-primary">WELCOMEOFF</p>
            <KeyboardArrowDown className="fill-primary size-5" />
          </div>
          <p className="text-large-paragraph-regular text-[#009DD3]">82 232</p>
        </div>
      </div>
      <div className="w-full h-full">
        <CustomLineChart />
      </div>
    </div>
  );
}
