import { Datatable } from "@/components/common/datatable";
import { DatatableSearch } from "@/components/common/datatable/datatable-search";
import { Dropdown, DropdownItem } from "@/components/ui/dropdown";
import { AdsClick, Delete, FileCopy } from "@interzero/oneepr-react-ui/Icon";
import { createColumnHelper } from "@tanstack/react-table";
import { EllipsisVertical, Pencil } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";

interface InviteCustomersManagerTableData {
  id: string;
  recruitingCustomer: string;
  userId: string;
  programRegistration: string;
  customersAcc: string;
  earnings: string;
  code: string;
  link: string;
  actions: string;
}

interface InviteCustomersManagerTableProps {
  selectedYear: string;
}

const data: InviteCustomersManagerTableData[] = [
  {
    id: "1",
    recruitingCustomer: "<PERSON>",
    userId: "6598",
    programRegistration: "15.01.2024",
    customersAcc: "5",
    earnings: "30.00",
    code: "FRAZER_MIKES",
    link: "www.link.com/discount",
    actions: "Edit",
  },
  {
    id: "2",
    recruitingCustomer: "<PERSON>",
    userId: "6599",
    programRegistration: "23.12.2023",
    customersAcc: "3",
    earnings: "25.00",
    code: "SARAH_J",
    link: "www.link.com/discount",
    actions: "Edit",
  },
  {
    id: "3",
    recruitingCustomer: "John Smith",
    userId: "6600",
    programRegistration: "05.03.2023",
    customersAcc: "7",
    earnings: "45.00",
    code: "JOHN_SMITH",
    link: "www.link.com/discount",
    actions: "Edit",
  },
  {
    id: "4",
    recruitingCustomer: "Emma Davis",
    userId: "6601",
    programRegistration: "18.07.2023",
    customersAcc: "4",
    earnings: "28.00",
    code: "EMMA_D",
    link: "www.link.com/discount",
    actions: "Edit",
  },
  {
    id: "5",
    recruitingCustomer: "Robert Wilson",
    userId: "6602",
    programRegistration: "30.11.2023",
    customersAcc: "6",
    earnings: "35.00",
    code: "ROB_WILSON",
    link: "www.link.com/discount",
    actions: "Edit",
  },
  {
    id: "6",
    recruitingCustomer: "Lisa Brown",
    userId: "6603",
    programRegistration: "02.01.2024",
    customersAcc: "2",
    earnings: "15.00",
    code: "LISA_B",
    link: "www.link.com/discount",
    actions: "Edit",
  },
  {
    id: "7",
    recruitingCustomer: "David Miller",
    userId: "6604",
    programRegistration: "09.08.2023",
    customersAcc: "8",
    earnings: "50.00",
    code: "DAVID_M",
    link: "www.link.com/discount",
    actions: "Edit",
  },
];

const columnHelper = createColumnHelper<InviteCustomersManagerTableData>();

const columns = [
  columnHelper.accessor("recruitingCustomer", {
    header: "Recruiting Customer",
    cell: (info) => (
      <div className="flex flex-col gap-1">
        <p className="text-sm">{info.getValue()}</p>
        <span className="text-xs text-tonal-dark-cream-30"><EMAIL></span>
      </div>
    ),
  }),
  columnHelper.accessor("userId", {
    header: "User ID",
    cell: (info) => <p className="text-sm max-w-24">{info.getValue()}</p>,
  }),
  columnHelper.accessor("programRegistration", {
    header: "Program registration date",
    cell: (info) => <p className="text-sm max-w-24">{info.getValue()}</p>,
  }),
  columnHelper.accessor("customersAcc", {
    header: "Customers acquired",
    cell: (info) => info.getValue(),
  }),
  columnHelper.accessor("earnings", {
    header: "Earnings (€)",
    cell: (info) => info.getValue(),
  }),
  columnHelper.accessor("code", {
    header: "Code",
    cell: (info) => (
      <div className="flex items-center gap-2">
        <p className="text-sm max-w-24">{info.getValue()}</p>
        <FileCopy className="size-6 fill-support-blue" />
      </div>
    ),
  }),
  columnHelper.accessor("link", {
    header: "Link",
    cell: (info) => (
      <div className="flex items-center gap-2">
        <p className="text-sm max-w-24">{info.getValue().substring(0, 11)}</p>
        <AdsClick className="size-6 fill-support-blue" />
      </div>
    ),
  }),
  columnHelper.display({
    id: "actions",
    header: " ",
    cell: () => (
      <div className="w-4 flex items-center mr-4">
        <Dropdown
          trigger={
            <button className="rounded-full p-1 hover:bg-secondary/30">
              <EllipsisVertical className="size-6" />
            </button>
          }
        >
          <DropdownItem
            asChild
            className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
          >
            <div className="flex items-center">
              <Pencil className="size-4 mr-5 text-primary" /> Edit
            </div>
          </DropdownItem>
          <DropdownItem className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer">
            <div className="flex items-center">
              <FileCopy className="size-4 mr-5 fill-primary" /> Duplicate
            </div>
          </DropdownItem>
          <DropdownItem className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer">
            <div className="flex items-center">
              <Delete className="size-4 mr-5 fill-primary" /> Delete
            </div>
          </DropdownItem>
        </Dropdown>
      </div>
    ),
  }),
];

export function InviteCustomersManagerTable({ selectedYear }: InviteCustomersManagerTableProps) {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredData, setFilteredData] = useState(data);

  useEffect(() => {
    let filtered = [...data];

    if (selectedYear !== "ALL") {
      filtered = filtered.filter((item) => {
        const itemYear = item.programRegistration.split(".")[2];
        return itemYear === selectedYear;
      });
    }

    if (searchTerm) {
      const searchTermLower = searchTerm.toLowerCase();
      filtered = filtered.filter((item) => {
        return (
          item.recruitingCustomer.toLowerCase().includes(searchTermLower) ||
          item.userId.toLowerCase().includes(searchTermLower) ||
          item.code.toLowerCase().includes(searchTermLower) ||
          item.programRegistration.includes(searchTerm)
        );
      });
    }

    setFilteredData(filtered);
  }, [selectedYear, searchTerm]);

  function handleSearch(term: string) {
    setSearchTerm(term);
  }

  return (
    <div className="w-full">
      <div className="flex items-center justify-between mb-6">
        <DatatableSearch onSearch={handleSearch} />
      </div>
      <Datatable
        data={filteredData}
        columns={columns}
        onRowClick={(row: InviteCustomersManagerTableData) => {
          router.push(`invite-customers-manager/customer-manager/${row.id}`);
        }}
      />
    </div>
  );
}
