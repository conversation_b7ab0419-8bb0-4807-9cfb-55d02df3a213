"use client";

import { ModuleContent } from "@/components/common/module-content";
import { ModuleTitle } from "@/components/common/module-title";
import { DatePickerWithRange } from "@/components/ui/date-picker";
import { Dropdown, DropdownItem } from "@/components/ui/dropdown";
import {
  ControllerPainel,
  Elipse,
  FilterAlt,
  Handshake,
  KeyboardArrowDown,
  KeyboardArrowLeft,
  Upload,
} from "@interzero/oneepr-react-ui/Icon";
import { Input } from "@interzero/oneepr-react-ui/Input";
import Link from "next/link";
import { useState } from "react";
import { CustomersManagerTable } from "./customer-manager-table";
import { CustomerManagerChart } from "./customer-manager-graph";

interface CustomerManagerProps {
  customerId: string;
}

const LICENSE_YEAR_FILTERS = [
  { label: "Filter", value: "ALL" },
  { label: "2024", value: "2024" },
  { label: "2023", value: "2023" },
];

export default function CustomerManager({ customerId }: CustomerManagerProps) {
  const [selectedLicenseYear, setSelectedLicenseYear] = useState(LICENSE_YEAR_FILTERS[0]);

  function handleChangeLicenseYear(newOrder: (typeof LICENSE_YEAR_FILTERS)[number]) {
    setSelectedLicenseYear(newOrder);
  }
  return (
    <>
      <ModuleContent containerClassName="bg-white">
        <ModuleTitle
          icon={Handshake}
          title="Invite Customers Manager"
          description="Lorem ipsum dolor sit amet consectetur. Risus nulla egestas orci non hendrerit. "
        />
      </ModuleContent>
      <ModuleContent>
        <Link href="/invite-customers-manager">
          <div className="flex items-center gap-2">
            <KeyboardArrowLeft className="size-6 fill-support-blue" />
            <p className="text-support-blue font-bold cursor-pointer hover:underline">Back</p>
          </div>
        </Link>
        <div>
          <div className="flex items-center mt-8 justify-between">
            <div className="flex items-center gap-4">
              <p className="text-title-1 text-[#183362] font-bold">Michael Pherman</p>
              <div className="flex items-center gap-2">
                <Elipse className="w-4 h-4 fill-success" />
                <p className="text-paragraph-regular text-success">Active</p>
              </div>
            </div>
            <Link href="/invite-customers-manager/settings">
              <div className="flex items-center gap-2 cursor-pointer ">
                <ControllerPainel className="w-4 h-4 fill-support-blue" />
                <p className="text-support-blue hover:underline">Settings</p>
              </div>
            </Link>
          </div>
          <p className="text-[#183362] text-paragraph-regular mt-6">User Id: {customerId}</p>
        </div>
        {/* Missing graph */}
        <div className="w-[909px] h-[486px] bg-white rounded-xl mt-14 p-8">
          <div className="flex items-center justify-between">
            <p className="text-title-3 text-[#002652] font-bold">Active Marketing Action</p>
            <Dropdown
              trigger={
                <button className="flex items-center text-support-blue font-bold text-base">
                  <span className="ml-1 mr-2 mt-1 text-left">{selectedLicenseYear.label}</span>
                  <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
                </button>
              }
            >
              {LICENSE_YEAR_FILTERS.map((filter, idx) => (
                <DropdownItem
                  key={idx}
                  onClick={() => handleChangeLicenseYear(filter)}
                  className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
                >
                  {filter.label}
                </DropdownItem>
              ))}
            </Dropdown>
          </div>
          <div className="flex gap-6 mt-6">
            <div className="flex items-center gap-2">
              <Elipse className="w-4 h-4 fill-success" />
              <p className="text-paragraph-regular text-black">Coupon</p>
            </div>
            <div className="flex items-center gap-2">
              <Elipse className="w-4 h-4 fill-tertiary" />
              <p className="text-paragraph-regular text-black">Link</p>
            </div>
          </div>
          <div className="mt-12 h-64 flex items-center">
            <CustomerManagerChart />
          </div>
        </div>

        <div className="mt-10 flex justify-between">
          <div className="w-[496px] h-[255px] bg-tonal-dark-blue-90 rounded-2xl p-10 flex flex-col gap-6">
            <p className="text-title-3 text-on-secondary font-bold">Invites</p>
            <div className="flex flex-col gap-2">
              <div className="w-[380px]">
                <Input type="text" placeholder="www.link-sharing/loyalty-program-123" />
              </div>
              <div className="w-[240px]">
                <Input type="text" placeholder="Licenseero25" />
              </div>
            </div>
          </div>
          <div className="w-[392px] h-[255px] bg-tonal-green-90 rounded-2xl flex flex-col gap-4 justify-center px-14">
            <p className="text-lg font-bold text-tonal-dark-cream-10">Available to redeem</p>
            <p className="text-h3 font-bold text-on-secondary">€ 1 250.81</p>
            <p className="text-paragraph-regular text-on-secondary">Expires: 23.09.2023</p>
          </div>
        </div>
        <div className="w-[912px] h-auto bg-white rounded-2xl mt-10 p-8">
          <div className="w-full flex justify-between items-center mb-6">
            <p className="text-on-secondary text-title-1 font-bold">Clients recruited (2309)</p>
            <div className="flex gap-4">
              <div className="flex items-center gap-2">
                <Upload width={24} height={24} className="fill-support-blue" />
                <p className="text-large-paragraph-regular font-bold text-support-blue hover:underline cursor-pointer">
                  PDF
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Upload width={24} height={24} className="fill-[#66A73F]" />
                <p className="text-large-paragraph-regular font-bold text-[#66A73F] hover:underline cursor-pointer">
                  Excel
                </p>
              </div>
            </div>
          </div>
          <div className="flex items-center justify-between mb-8">
            <p className="text-small-paragraph-regular text-on-secondary">
              Your reward will be cashed once registration process is finished.
            </p>
            <div className="flex justify-end items-center gap-2">
              <Dropdown
                trigger={
                  <button className="flex items-center text-support-blue font-bold text-base">
                    <FilterAlt className="size-5 fill-support-blue" />
                    <span className="ml-1 mr-2 mt-1 text-left">{selectedLicenseYear.label}</span>
                    <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
                  </button>
                }
              >
                {LICENSE_YEAR_FILTERS.map((filter, idx) => (
                  <DropdownItem
                    key={idx}
                    onClick={() => handleChangeLicenseYear(filter)}
                    className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
                  >
                    {filter.label}
                  </DropdownItem>
                ))}
              </Dropdown>
              <div className="text-tonal-dark-cream-60">|</div>
              <DatePickerWithRange />
            </div>
          </div>
          <CustomersManagerTable />
        </div>
      </ModuleContent>
    </>
  );
}
