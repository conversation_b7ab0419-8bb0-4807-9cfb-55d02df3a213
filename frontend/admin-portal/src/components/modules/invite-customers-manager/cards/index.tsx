import { Upload } from "@interzero/oneepr-react-ui/Icon";
import { ArrowDown, ArrowUp } from "lucide-react";
import Image from "next/image";
import Sheet from "../../../../../public/assets/images/Sheet.svg";
import Sonstiges from "../../../../../public/assets/images/Sonstiges.svg";

const RevenueCard = () => {
  return (
    <div className="bg-tonal-dark-blue-96 w-[444px] h-[204px] rounded-lg p-4">
      <h2 className="text-large-paragraph-regular font-bold text-primary">Revenue</h2>
      <div className="flex justify-between items-center h-full px-4">
        <div className="flex flex-col gap-4">
          <p className="text-h3 text-support-blue font-bold">€ 305.00</p>
          <p className="text-small-paragraph-regular text-primary">Via discount codes</p>
          <div className="flex items-center gap-1">
            <ArrowUp size={12} className="text-support-blue" />
            <p className="text-small-paragraph-regular text-support-blue">0.5% more than august</p>
          </div>
        </div>
        <div className="flex flex-col gap-4">
          <p className="text-h3 text-error font-bold">€ 15.00</p>
          <p className="text-small-paragraph-regular text-primary">Reduction of revenue</p>
          <div className="flex items-center gap-1">
            <ArrowDown size={12} className="text-error" />
            <p className="text-small-paragraph-regular text-error">0.2% less than august</p>
          </div>
        </div>
      </div>
    </div>
  );
};

const TotalCarbonOffsetCard = () => {
  return (
    <div className="bg-tonal-dark-green-30 w-[444px] h-[204px] rounded-lg p-4">
      <div className="flex justify-between items-center">
        <h2 className="text-large-paragraph-regular font-bold text-white">Total carbon offset</h2>
        <div className="flex items-center gap-2">
          <Upload width={24} height={24} className="fill-white" />
          <p className="text-large-paragraph-regular font-bold text-white">PDF</p>
        </div>
      </div>
      <div className="flex flex-col h-full justify-center px-4 gap-4">
        <div className="flex items-center gap-2">
          <div className="w-[50px] h-[50px] rounded-full bg-tonal-green-90 flex items-center justify-center">
            <Image src={Sonstiges} alt="sheet" />
          </div>
          <div className="flex flex-col gap-1">
            <p className="text-title-1 text-tonal-green-90 font-bold">100,000,00 m2</p>
            <p className="text-small-paragraph-regular text-white">metres of renaturalised moorland</p>
          </div>
        </div>
        <div className="flex items-center gap-2 mt-1">
          <div className="w-[50px] h-[50px] rounded-full flex items-center justify-center">
            <Image src={Sheet} alt="sheet" />
          </div>
          <div className="flex gap-1">
            <p className="text-title-1 text-tonal-green-90 font-bold">234,253,095</p>
            <p className="text-small-paragraph-regular text-white mt-1">trees planted</p>
          </div>
        </div>
      </div>
    </div>
  );
};

const LeadsGeneratedCard = () => {
  return (
    <div className="bg-tonal-green-90 w-[444px] h-[204px] rounded-lg p-4">
      <h2 className="text-large-paragraph-regular font-bold text-primary">Leads generated</h2>
      <div className="flex justify-between items-center h-full px-4">
        <div className="flex flex-col gap-4">
          <p className="text-h3 text-tonal-dark-green-30 font-bold">250</p>
          <p className="text-small-paragraph-regular text-primary">Generate by coupons</p>
          <div className="flex items-center gap-1">
            <ArrowUp size={12} className="text-tonal-dark-green-30" />
            <p className="text-small-paragraph-regular text-tonal-dark-green-30">0.5% more than august</p>
          </div>
        </div>
        <div className="flex flex-col gap-4">
          <p className="text-h3 text-tonal-dark-green-30 font-bold">48</p>
          <p className="text-small-paragraph-regular text-primary">Generate by links</p>
          <div className="flex items-center gap-1">
            <ArrowUp size={12} className="text-tonal-dark-green-30" />
            <p className="text-small-paragraph-regular text-tonal-dark-green-30">0.5% more than august</p>
          </div>
        </div>
      </div>
    </div>
  );
};

const DropPurchaseCard = () => {
  return (
    <div className="bg-tonal-pink-96 w-[444px] h-[204px] rounded-lg p-4">
      <h2 className="text-large-paragraph-regular font-bold text-primary">Drop purchase</h2>
      <div className="flex justify-between items-center h-full px-4">
        <div className="flex flex-col gap-4">
          <p className="text-h3 text-error font-bold">250</p>
          <p className="text-small-paragraph-regular text-primary">Customers didn&apos;t finish the purchase</p>
          <div className="flex items-center gap-1">
            <ArrowDown size={12} className="text-error" />
            <p className="text-small-paragraph-regular text-error">0.5% more than august</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export { DropPurchaseCard, LeadsGeneratedCard, RevenueCard, TotalCarbonOffsetCard };
