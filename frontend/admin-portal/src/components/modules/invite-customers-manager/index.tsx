"use client";

import { useState } from "react";
import { ModuleContent } from "@/components/common/module-content";
import { ModuleTitle } from "@/components/common/module-title";
import { Dropdown, DropdownItem } from "@/components/ui/dropdown";
import { ControllerP<PERSON>l, Handshake, KeyboardArrowDown, Upload } from "@interzero/oneepr-react-ui/Icon";
import Link from "next/link";
import { DropPurchaseCard, LeadsGeneratedCard, RevenueCard, TotalCarbonOffsetCard } from "./cards";
import { InviteCustomersManagerTable } from "./inviteCustomersTable";
import { DatePickerWithRange } from "@/components/ui/date-picker";

const LICENSE_YEAR_FILTERS = [
  { label: "All Years", value: "ALL" },
  { label: "2024", value: "2024" },
  { label: "2023", value: "2023" },
];

export default function InviteCustomersManager() {
  const [selectedLicenseYear, setSelectedLicenseYear] = useState(LICENSE_YEAR_FILTERS[0]);

  function handleChangeLicenseYear(newOrder: (typeof LICENSE_YEAR_FILTERS)[number]) {
    setSelectedLicenseYear(newOrder);
  }
  return (
    <>
      <ModuleContent containerClassName="bg-white">
        <div className="flex items-start justify-between">
          <ModuleTitle
            icon={Handshake}
            title="Invite Customers Manager"
            description="Lorem ipsum dolor sit amet consectetur. Risus nulla egestas orci non hendrerit. "
          />

          <Link href="/invite-customers-manager/settings">
            <div className="flex items-center gap-2 cursor-pointer mt-4">
              <ControllerPainel className="w-4 h-4 fill-support-blue" />
              <p className="text-support-blue hover:underline">Settings</p>
            </div>
          </Link>
        </div>
        <div className="w-full flex justify-end items-center gap-2">
          <Dropdown
            trigger={
              <button className="flex items-center text-support-blue font-bold text-base">
                <span className="ml-1 mr-2 mt-1 text-left">{selectedLicenseYear.label}</span>
                <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
              </button>
            }
          >
            {LICENSE_YEAR_FILTERS.map((filter, idx) => (
              <DropdownItem
                key={idx}
                onClick={() => handleChangeLicenseYear(filter)}
                className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
              >
                {filter.label}
              </DropdownItem>
            ))}
          </Dropdown>
          <div className="text-tonal-dark-cream-60">|</div>
          <DatePickerWithRange />
        </div>
        <div>
          <div className="flex gap-4 mt-8">
            <RevenueCard />
            <TotalCarbonOffsetCard />
          </div>
          <div className="flex gap-4 mt-4">
            <LeadsGeneratedCard />
            <DropPurchaseCard />
          </div>
        </div>
      </ModuleContent>
      <ModuleContent containerClassName="bg-surface-02">
        <div className="w-full flex justify-between items-center mb-6">
          <p className="text-primary text-title-3 font-bold">All Customers</p>
          <div className="flex gap-4">
            <div className="flex items-center gap-2">
              <Upload width={24} height={24} className="fill-support-blue" />
              <p className="text-large-paragraph-regular font-bold text-support-blue hover:underline cursor-pointer">
                PDF
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Upload width={24} height={24} className="fill-[#66A73F]" />
              <p className="text-large-paragraph-regular font-bold text-[#66A73F] hover:underline cursor-pointer">
                Excel
              </p>
            </div>
          </div>
        </div>
        <InviteCustomersManagerTable selectedYear={selectedLicenseYear.value} />
      </ModuleContent>
    </>
  );
}
