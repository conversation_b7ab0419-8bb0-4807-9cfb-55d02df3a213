"use client";

import { useCallback } from "react";
import { useDropzone, type FileWithPath } from "react-dropzone";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Delete, File, Upload } from "@interzero/oneepr-react-ui/Icon";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { deleteMarketingMaterialFile } from "@/lib/api/marketing-materials";
import { enqueueSnackbar } from "notistack";

interface UploadMarketingMaterialsProps {
  documents: Array<File & { id?: string }>;
  setDocuments: (documents: File[]) => void;
  onRemoveDocument: (index: number) => void;
}

export function UploadMarketingMaterials({ documents, setDocuments, onRemoveDocument }: UploadMarketingMaterialsProps) {
  const queryClient = useQueryClient();

  const onDrop = useCallback((acceptedFiles: FileWithPath[]) => {
    setDocuments(acceptedFiles);

    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "application/pdf": [".pdf"],
      "application/msword": [".doc", ".docx"],
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document": [".doc", ".docx"],
    },
    // disabled: isDisabled,
    maxFiles: 1,
    multiple: false,
    // preventDropOnDocument: true,
  });

  const { mutate: handleDeleteFile, isPending: isDeletingFile } = useMutation({
    mutationFn: (fileId: string) => deleteMarketingMaterialFile(fileId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["marketing-materials"] });
      enqueueSnackbar("File deleted successfully", { variant: "success" });
    },
    onError: (error) => {
      enqueueSnackbar(error.message, { variant: "error" });
    },
  });

  if (documents.length === 0) {
    return (
      <div className="flex items-center justify-between">
        <div {...getRootProps()}>
          <input {...getInputProps()} />
          <Button
            type="button"
            size="small"
            variant="text"
            color="light-blue"
            leadingIcon={<Upload />}
            disabled={isDragActive}
          >
            Upload Document
          </Button>
        </div>
        <div className="bg-error/10 text-error rounded-xl text-sm font-bold py-2 px-4">Requested</div>
      </div>
    );
  }

  return (
    <ul className="flex flex-col">
      {documents.map((document, i) => (
        <li key={document.name + i} className="py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 flex-1">
              <File className="size-4 fill-primary" /> <p className="text-primary">{document.name}</p>
            </div>
            <Button
              type="button"
              size="iconSmall"
              color="dark-blue"
              variant="text"
              disabled={isDeletingFile}
              onClick={() => {
                if (document.id) {
                  handleDeleteFile(document.id);
                }
                onRemoveDocument(i);
              }}
            >
              <Delete className="size-4 fill-primary" />
            </Button>
          </div>
        </li>
      ))}
      <li>
        <div {...getRootProps()}>
          <input {...getInputProps()} />
          <Button
            type="button"
            size="small"
            variant="text"
            color="light-blue"
            leadingIcon={<Upload />}
            disabled={isDragActive}
          >
            Upload Document
          </Button>
        </div>
      </li>
    </ul>
  );
}
