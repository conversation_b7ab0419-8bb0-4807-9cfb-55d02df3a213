"use client";

import { useRouter } from "next/navigation";
import { ChevronLeftIcon } from "lucide-react";
import { But<PERSON> } from "@interzero/oneepr-react-ui/Button";
import { Edit } from "@interzero/oneepr-react-ui/Icon";
import { ModuleContent } from "@/components/common/module-content";
import { ModuleTitle } from "@/components/common/module-title";
import { UpdateMarketingMaterialForm } from "../update-marketing-material-form";
import { DeleteMarketingMaterialDialog } from "../delete-marketing-material-dialog";

interface EditMarketingMaterialModuleProps {
  marketingMaterialId: string | number;
}

export function EditMarketingMaterialModule({ marketingMaterialId }: EditMarketingMaterialModuleProps) {
  const router = useRouter();

  return (
    <ModuleContent>
      <Button
        variant="text"
        color="light-blue"
        size="medium"
        leadingIcon={<ChevronLeftIcon className="size-5" />}
        onClick={() => router.back()}
      >
        Back
      </Button>

      <div className="flex flex-col gap-4 items-start md:items-center md:justify-between md:flex-row mb-9">
        <ModuleTitle icon={Edit} title="Edit Marketing material" className="mb-0" />
        <DeleteMarketingMaterialDialog marketingMaterialId={marketingMaterialId}>
          <Button type="button" variant="text" color="red" size="medium">
            Delete campaign
          </Button>
        </DeleteMarketingMaterialDialog>
      </div>

      <UpdateMarketingMaterialForm title="Campaign Information" marketingMaterialId={marketingMaterialId} />
    </ModuleContent>
  );
}
