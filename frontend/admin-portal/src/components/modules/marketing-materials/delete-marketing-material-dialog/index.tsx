"use client";

import { useState } from "react";
import { enqueueSnackbar } from "notistack";
import { useRouter } from "next/navigation";
import Image from "next/image";

import { useMutation } from "@tanstack/react-query";
import { Button } from "@interzero/oneepr-react-ui/Button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { deleteMarketingMaterial } from "@/lib/api/marketing-materials";
import { queryClient } from "@/lib/react-query";

interface DeleteMarketingMaterialDialogProps {
  marketingMaterialId: string | number;
  children: React.ReactNode;
}

export function DeleteMarketingMaterialDialog({ marketingMaterialId, children }: DeleteMarketingMaterialDialogProps) {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const [isDone, setIsDone] = useState(false);

  const { mutate, isPending } = useMutation({
    mutationFn: () => deleteMarketingMaterial(marketingMaterialId),
    onSuccess: () => {
      setIsDone(true);
      enqueueSnackbar("Campaign deleted successfully", { variant: "success" });
    },
    onError: (error) => {
      enqueueSnackbar(error.message, { variant: "error" });
    },
  });

  async function handleDelete() {
    mutate();
  }

  function handleCloseAfterDelete() {
    queryClient.invalidateQueries({ queryKey: ["marketing-materials"] });
    setIsOpen(false);
    router.push("/marketing-materials");
  }

  function onOpenChange(open: boolean) {
    if (isDone && !open) {
      queryClient.invalidateQueries({ queryKey: ["marketing-materials"] });
      router.push("/marketing-materials");
    }
    setIsOpen(open);
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>{children}</DialogTrigger>

      {!isDone && (
        <DialogContent className="bg-surface-02 py-9 max-w-2xl">
          <DialogHeader>
            <DialogTitle className="font-bold sm:text-[1.75rem]">Delete campaign</DialogTitle>
            <DialogDescription className="text-tonal-dark-cream-20 font-normal">
              By clicking on ”confirm” you are deleting this campaign with all the files uploaded.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <DialogClose asChild>
              <Button type="button" variant="outlined" color="dark-blue" size="medium">
                Back
              </Button>
            </DialogClose>
            <Button variant="filled" color="red" size="medium" onClick={handleDelete} disabled={isPending}>
              {isPending ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      )}

      {isDone && (
        <DialogContent className="bg-surface-02 py-9 max-w-2xl">
          <div className="flex items-center gap-2 mb-10">
            <Image src="/assets/images/leaf-seal.svg" alt="leaf seal" width={36} height={36} className="size-9" />
            <h3 className="font-bold text-[1.75rem] text-primary">Campaign deleted with success</h3>
          </div>
          <DialogFooter>
            <Button type="button" variant="filled" color="yellow" size="medium" onClick={handleCloseAfterDelete}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      )}
    </Dialog>
  );
}
