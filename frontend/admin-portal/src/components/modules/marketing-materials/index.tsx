"use client";

import { useRouter } from "next/navigation";
import Link from "next/link";
import { ModuleTitle } from "@/components/common/module-title";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Add, Delete, Download, Edit, FileCopy, MoreVert, Pin } from "@interzero/oneepr-react-ui/Icon";
import { ModuleContent } from "@/components/common/module-content";
import AsyncPaginatedTable from "@/components/common/async-paginated-table";
import Status from "../../common/license-status";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { DeleteMarketingMaterialDialog } from "./delete-marketing-material-dialog";
import { useMutation, useQuery } from "@tanstack/react-query";
import { downloadMarketingMaterialFile, getAllMarketingMaterials } from "@/lib/api/marketing-materials";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { DatatableSearch } from "@/components/common/datatable/datatable-search";
import { CustomDropdown } from "@/components/common/custom-dropdown";
import { enqueueSnackbar } from "notistack";
import { Loader2 } from "lucide-react";
import { useState } from "react";

export function MarketingMaterialsModule() {
  const router = useRouter();
  const { paramValues, changeParam } = useQueryFilter(["page", "search", "status"]);

  const limit = 10;
  const { data, isLoading } = useQuery({
    queryKey: ["marketing-materials", paramValues],
    queryFn: () =>
      getAllMarketingMaterials({
        page: Number(paramValues.page ?? 1),
        limit,
        name: paramValues.search ?? undefined,
        is_active: paramValues.status ? paramValues.status === "active" : undefined,
      }),
  });

  function handleCreate() {
    router.push("/marketing-materials/create");
  }

  function handleEdit(id: number) {
    router.push(`/marketing-materials/${id}/edit`);
  }

  function handleDuplicate(id: number) {
    const marketingMaterial = data?.marketingMaterials.find((m) => m.id === id);
    if (!marketingMaterial) return;
    sessionStorage.setItem("marketingMaterial", JSON.stringify(marketingMaterial));
    router.push("/marketing-materials/create");
  }

  function handleSearchName(search: string) {
    changeParam("search", search);
  }

  function dateToDDMMYYYY(date: Date | string) {
    const toDate = new Date(date);
    return toDate.toISOString().split("T")[0].replaceAll("-", ".").split(".").reverse().join(".");
  }

  const [downloadingFileId, setDownloadingFileId] = useState<string>();
  const { mutate: handleDownloadFile } = useMutation({
    mutationFn: (fileId: string) => downloadMarketingMaterialFile(fileId),
    onSuccess: () => {
      setDownloadingFileId(undefined);
      enqueueSnackbar("File downloaded successfully", { variant: "success" });
    },
    onError: () => {
      setDownloadingFileId(undefined);
      enqueueSnackbar("Error downloading file", { variant: "error" });
    },
  });

  return (
    <ModuleContent>
      <div className="flex items-start justify-between">
        <ModuleTitle
          icon={Pin}
          title="Marketing Materials"
          description="Lorem ipsum dolor sit amet consectetur. Risus nulla egestas orci non hendrerit."
        />
        <Link href="/marketing-materials/create">
          <Button variant="filled" size="medium" color="yellow" leadingIcon={<Add />} onClick={handleCreate}>
            Add new material
          </Button>
        </Link>
      </div>
      <div className="flex items-center justify-between mb-4">
        <DatatableSearch defaultValue={paramValues.search ?? ""} onSearch={handleSearchName} />
        <CustomDropdown
          options={[
            { label: "All", value: "" },
            { label: "Active", value: "active" },
            { label: "Inactive", value: "inactive" },
          ]}
          selectedOption={paramValues.status ?? ""}
          handleSelect={(status) => changeParam("status", status)}
        />
      </div>
      <AsyncPaginatedTable
        isLoading={isLoading}
        data={data?.marketingMaterials ?? []}
        columns={[
          {
            accessorKey: "files",
            header: "File",
            cell: ({ row }) => {
              const file = row.original.files?.[0];

              if (!file) return <span className="max-w-[150px] truncate">No file</span>;

              return (
                <Button
                  variant="text"
                  size="small"
                  color="dark-blue"
                  className="text-sm flex items-center truncate text-start -ml-2"
                  disabled={downloadingFileId === file.id}
                  title={file?.name ?? "No file"}
                  onClick={() => {
                    setDownloadingFileId(file.id);
                    handleDownloadFile(file.id);
                  }}
                  trailingIcon={
                    downloadingFileId === file.id ? (
                      <Loader2 className="size-4 animate-spin mt-1" />
                    ) : (
                      <Download className="size-5 fill-support-blue" />
                    )
                  }
                >
                  <span className="max-w-[150px] truncate">{file?.name}</span>
                </Button>
              );
            },
          },
          {
            accessorKey: "name",
            header: "Campaign",
            cell: ({ row }) => <p className="text-sm">{row.original.name}</p>,
          },
          {
            accessorKey: "startDate",
            header: "Start Date",
            cell: ({ row }) => (
              <p className="text-sm ml-4">{row.original.start_date ? dateToDDMMYYYY(row.original.start_date) : "-"}</p>
            ),
          },
          {
            accessorKey: "endDate",
            header: "End Date",
            cell: ({ row }) => (
              <p className="text-sm ml-4">{row.original.end_date ? dateToDDMMYYYY(row.original.end_date) : "-"}</p>
            ),
          },
          {
            accessorKey: "downloads",
            header: "Downloads",
            cell: ({ row }) => <p className="text-sm ml-4">{row.original.id}</p>,
          },
          {
            accessorKey: "status",
            header: "Status",
            cell: ({ row }) => {
              const isActive = row.original.end_date
                ? compareToTodayWithoutTimeAndTimezone(row.original.end_date)
                : true;
              return <Status status={isActive ? "Active" : "Inactive"} />;
            },
          },
          {
            id: "actions",
            header: "",
            cell: ({ row }) => (
              <Popover>
                <PopoverTrigger>
                  <MoreVert className="w-6 h-6 fill-primary mt-2" />
                </PopoverTrigger>
                <PopoverContent side="bottom" className="w-40 overflow-hidden shadow-elevation-04-1 border-none p-0">
                  <div className="flex flex-col gap-2">
                    <Button
                      variant="text"
                      size="small"
                      color="dark-blue"
                      className="w-full flex justify-start p-3 py-3 rounded-none"
                      leadingIcon={<Edit />}
                      onClick={() => handleEdit(row.original.id)}
                    >
                      Edit
                    </Button>
                    <Button
                      variant="text"
                      size="small"
                      color="dark-blue"
                      className="w-full flex justify-start p-3 py-3 rounded-none"
                      leadingIcon={<FileCopy />}
                      onClick={() => handleDuplicate(row.original.id)}
                    >
                      Duplicate
                    </Button>
                    <DeleteMarketingMaterialDialog marketingMaterialId={row.original.id}>
                      <Button
                        variant="text"
                        size="small"
                        color="dark-blue"
                        className="w-full flex justify-start p-3 py-3 rounded-none"
                        leadingIcon={<Delete className="fill-primary" />}
                      >
                        Delete
                      </Button>
                    </DeleteMarketingMaterialDialog>
                  </div>
                </PopoverContent>
              </Popover>
            ),
          },
        ]}
        currentPage={data?.current_page ?? 1}
        onPageChange={(page) => changeParam("page", page.toString())}
        pageSize={limit}
        pages={data?.pages ?? 1}
        noResultsMessage="No marketing materials found"
        showHeaderOnNoResults
      />
    </ModuleContent>
  );
}

function compareToTodayWithoutTimeAndTimezone(date: string) {
  const today = new Date();
  const d1 = `${today.getFullYear()}-${today.getMonth() + 1 > 9 ? today.getMonth() + 1 : `0${today.getMonth() + 1}`}-${
    today.getDate() > 9 ? today.getDate() : `0${today.getDate()}`
  }`;
  const d2 = `${date.split("T")[0]}`;
  return d2 >= d1;
}
