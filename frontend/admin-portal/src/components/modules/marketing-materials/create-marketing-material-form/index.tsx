"use client";

import { useEffect, useState } from "react";
import { Controller, FormProvider, useFieldArray, useForm, useFormContext } from "react-hook-form";
import { useRouter } from "next/navigation";
import { useMutation, useQuery } from "@tanstack/react-query";
import { zodResolver } from "@hookform/resolvers/zod";
import { enqueueSnackbar } from "notistack";
import { z } from "zod";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { Help, KeyboardArrowRight, Delete } from "@interzero/oneepr-react-ui/Icon";
import { queryClient } from "@/lib/react-query";
import { createMarketingMaterial } from "@/lib/api/marketing-materials";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { dateManager } from "@/utils/date-manager";
import { UploadMarketingMaterials } from "../upload-marketing-materials";
import { MarketingMaterial } from "@/lib/api/marketing-materials/types";
import { AutoComplete } from "@/components/common/autocomplete";
import { getPartners } from "@/lib/api/partner";

const createMarketingMaterialFormSchema = z
  .object({
    name: z.string().trim().min(1, { message: "Name is required" }),
    start_date: z
      .string()
      .optional() // Torna o campo opcional
      .refine((value) => !value || !isNaN(new Date(value).getTime()), {
        message: "Invalid date format",
      })
      .refine((value) => !value || new Date(value) > new Date(), {
        message: "Start date must be in the future",
      }),
    end_date: z
      .string()
      .optional() // Torna o campo opcional
      .refine((value) => !value || !isNaN(new Date(value).getTime()), {
        message: "Invalid date format",
      }),
    category: z.enum(["STANDARD", "SPECIFIC_MATERIAL"]).default("STANDARD"),
    partner_restriction: z.enum(["ALL", "CLUSTER", "SPECIFIC"]).default("ALL"),
    partners: z
      .array(z.object({ name: z.string().trim().min(1, "Name is required"), id: z.number() }))
      .optional()
      .default([]),
  })
  .refine(
    (data) => {
      if (data.start_date && data.end_date) {
        return new Date(data.end_date) > new Date(data.start_date);
      }
      return true;
    },
    {
      message: "End date cannot be earlier than start date.",
      path: ["end_date"],
    }
  )
  .refine(
    (data) => {
      if (data.partner_restriction === "SPECIFIC") {
        return data.partners.length > 0;
      }
      return true;
    },
    {
      message: "Add at least one partner",
      path: ["partner_restriction"],
    }
  );

type CreateMarketingMaterialFormData = z.infer<typeof createMarketingMaterialFormSchema>;

interface CreateMarketingMaterialFormProps {
  title: string;
}

export function CreateMarketingMaterialForm({ title }: CreateMarketingMaterialFormProps) {
  const router = useRouter();

  const [documents, setDocuments] = useState<Array<File>>([]);

  const form = useForm<CreateMarketingMaterialFormData>({
    resolver: zodResolver(createMarketingMaterialFormSchema),
    defaultValues: {
      category: "STANDARD",
      partner_restriction: "ALL",
      partners: [],
    },
  });

  const storedMarketingMaterial = typeof window !== "undefined" ? sessionStorage.getItem("marketingMaterial") : null;

  useEffect(() => {
    if (!storedMarketingMaterial) return;
    const marketingMaterial = JSON.parse(storedMarketingMaterial) as MarketingMaterial;
    const startDate = dateManager(marketingMaterial.start_date).format("YYYY-MM-DD");
    const endDate = dateManager(marketingMaterial.end_date).format("YYYY-MM-DD");
    form.setValue("name", marketingMaterial.name);
    form.setValue("start_date", startDate);
    form.setValue("end_date", endDate);
    form.setValue("category", marketingMaterial.category);
    form.setValue("partner_restriction", marketingMaterial.partner_restriction);
    sessionStorage.removeItem("marketingMaterial");
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [storedMarketingMaterial]);

  const { fields, append, remove } = useFieldArray({ control: form.control, name: "partners" });

  const { mutate, isPending: isCreatingMarketingMaterial } = useMutation({
    mutationFn: (data: CreateMarketingMaterialFormData) => {
      return createMarketingMaterial({
        ...data,
        partners: data.partners.map((partner) => partner.id),
        start_date: data.start_date ? dateManager(data.start_date).toISOString() : undefined,
        end_date: data.end_date ? dateManager(data.end_date).toISOString() : undefined,
        files: documents,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["marketing-materials"] });
      queryClient.refetchQueries({ queryKey: ["marketing-materials"] });
      enqueueSnackbar("Marketing material created successfully", { variant: "success" });
      router.push("/marketing-materials");
    },
    onError: (error) => {
      enqueueSnackbar(error.message, { variant: "error" });
    },
  });

  function handleFormSubmit(data: CreateMarketingMaterialFormData) {
    if (data.partners.some((partner) => partner.id === 0)) {
      const index = data.partners.findIndex((partner) => partner.id === 0);
      form.setError(`partners.${index}.name`, { message: "Please select a partner", type: "manual" });
      return;
    }

    const hasDuplicatePartners = data.partners.some((partner, index) =>
      data.partners.some((p, i) => i !== index && p.id === partner.id)
    );

    if (hasDuplicatePartners) {
      const duplicatedPartnerIndex = data.partners.findIndex((partner) =>
        data.partners.some((p) => p.id === partner.id && p.id !== 0)
      );
      form.setError(`partners.${duplicatedPartnerIndex}.name`, {
        message: "Duplicated partner",
        type: "manual",
      });
      return;
    }

    if (documents.length > 0) {
      mutate(data);
    }
  }

  function handleUploadDocument(files: File[]) {
    const document = files?.[0] || null;
    if (document) {
      setDocuments((prevDocuments) => [...prevDocuments, document]);
    }
  }

  function handleRemoveDocument(index: number) {
    setDocuments((prevDocuments) => prevDocuments.filter((_, i) => i !== index));
  }

  const selectedPartnerRestriction = form.watch("partner_restriction");
  const isLoading = form.formState.isSubmitting || isCreatingMarketingMaterial;

  return (
    <FormProvider {...form}>
      <form className="w-full space-y-10" onSubmit={form.handleSubmit(handleFormSubmit)}>
        <div className="col-span-3 bg-white rounded-3xl py-9 px-8">
          <h3 className="text-primary text-2xl font-bold mb-1">{title}</h3>
          <p className="text-[#808FA9] text-sm mb-9">*Mandatory Fields</p>
          <div className="w-full space-y-6">
            <div className="w-full grid grid-cols-1">
              <Input
                {...form.register("name")}
                label="Name of campaign *"
                placeholder="Name of campaign"
                variant={form.formState.errors.name ? "error" : "default"}
                errorMessage={form.formState.errors.name?.message}
              />
            </div>
            <div className="w-full grid grid-cols-1 md:grid-cols-5 gap-6">
              <div className="col-span-2">
                <Input
                  label="Start Date"
                  placeholder="00/00/0000"
                  type="date"
                  {...form.register("start_date")}
                  variant={form.formState.errors.start_date ? "error" : "default"}
                  errorMessage={form.formState.errors.start_date?.message}
                />
              </div>
              <div className="col-span-2">
                <Input
                  label="End Date"
                  placeholder="00/00/0000"
                  type="date"
                  {...form.register("end_date")}
                  variant={form.formState.errors.end_date ? "error" : "default"}
                  errorMessage={form.formState.errors.end_date?.message}
                />
              </div>
            </div>
            <div className="flex flex-col gap-6">
              <p className="text-primary">Category *</p>
              <div className="flex flex-col gap-3">
                <Controller
                  control={form.control}
                  name="category"
                  render={({ field: { onChange, value } }) => (
                    <RadioGroup value={value} onValueChange={(newValue) => onChange(newValue)}>
                      <div className="flex flex-col items-start gap-3">
                        <label className="flex items-center gap-2 text-tonal-dark-cream-10 cursor-pointer">
                          <RadioGroupItem value="STANDARD" className="block" />
                          Standard materials
                        </label>
                        <label className="flex items-center gap-2 text-tonal-dark-cream-10 cursor-pointer">
                          <RadioGroupItem value="SPECIFIC_MATERIAL" className="block" />
                          Campaign specific materials
                        </label>
                      </div>
                    </RadioGroup>
                  )}
                />
              </div>
            </div>
            <div className="h-[1px] w-full bg-tonal-dark-cream-80 rounded-full" />
            <div className="flex flex-col gap-6">
              <div className="flex items-center gap-3">
                <p className="text-primary">Add partners *</p>
                <Help className="size-5 fill-primary" />
              </div>
              <div className="flex flex-col gap-3">
                <Controller
                  control={form.control}
                  name="partner_restriction"
                  render={({ field: { onChange, value }, formState: { errors } }) => (
                    <RadioGroup value={value} onValueChange={(newValue) => onChange(newValue)}>
                      <div className="flex flex-col items-start gap-3">
                        <label className="flex items-center gap-2 text-tonal-dark-cream-10 cursor-pointer">
                          <RadioGroupItem value="ALL" className="block" />
                          All partners
                        </label>
                        <label className="flex items-center gap-2 text-tonal-dark-cream-10 cursor-pointer">
                          <RadioGroupItem value="CLUSTER" className="block" />
                          Cluster
                        </label>
                        <label className="flex items-center gap-2 text-tonal-dark-cream-10 cursor-pointer">
                          <RadioGroupItem value="SPECIFIC" className="block" />
                          Specific partner
                        </label>
                        {!!errors.partner_restriction && (
                          <p className="text-sm text-error">{errors.partner_restriction.message}</p>
                        )}
                      </div>
                    </RadioGroup>
                  )}
                />
              </div>
              {(selectedPartnerRestriction === "CLUSTER" || selectedPartnerRestriction === "SPECIFIC") && (
                <div className="w-full flex flex-col items-start gap-6 px-6">
                  {fields.map((field, index) => (
                    <div key={field.id} className="relative w-full grid grid-cols-[1fr,0.1fr] items-center gap-2">
                      <PartnerInput index={index} />
                      <Button
                        type="button"
                        size="iconSmall"
                        variant="text"
                        color="dark-blue"
                        onClick={() => remove(index)}
                      >
                        <Delete className="size-5 fill-primary" />
                      </Button>
                    </div>
                  ))}
                  <Button
                    type="button"
                    size="small"
                    variant="text"
                    color="light-blue"
                    trailingIcon={<KeyboardArrowRight className="size-5 fill-support-blue" />}
                    onClick={() => append({ name: "", id: 0 })}
                  >
                    Add partner
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
        <div className="flex flex-col gap-4 py-9 px-8">
          <h3 className="text-primary text-2xl font-bold mb-6">Marketing Materials</h3>
          {form.formState.isSubmitted && documents.length === 0 && (
            <p className="text-sm text-error">You must upload at least one document</p>
          )}
          <UploadMarketingMaterials
            documents={documents}
            setDocuments={handleUploadDocument}
            onRemoveDocument={handleRemoveDocument}
          />
        </div>
        <div className="flex items-center justify-end gap-6">
          <Button type="button" onClick={() => router.back()} variant="outlined" color="dark-blue" size="medium">
            Cancel
          </Button>
          <Button type="submit" variant="filled" color="yellow" size="medium" disabled={isLoading}>
            {isLoading ? "Saving..." : "Save"}
          </Button>
        </div>
      </form>
    </FormProvider>
  );
}

function PartnerInput({ index }: { index: number }) {
  const {
    formState: { errors },
    setValue,
    watch,
  } = useFormContext<CreateMarketingMaterialFormData>();

  const [search, setSearch] = useState("");
  const { data: partners, isLoading } = useQuery({
    queryKey: ["partners", search],
    queryFn: async () => {
      const response = await getPartners({ name: search });
      return response.map((partner) => ({
        value: partner.id.toString(),
        label: `${partner.first_name} ${partner.last_name}`,
      }));
    },
  });

  return (
    <div className="flex flex-col gap-2">
      <AutoComplete
        isLoading={isLoading}
        items={partners ?? []}
        onChange={(value) => setValue(`partners.${index}.id`, Number(value))}
        onSelect={({ label }) => setValue(`partners.${index}.name`, label)}
        onSearchValueChange={setSearch}
        searchValue={search}
        value={watch(`partners.${index}.name`) as string}
        placeholder="Name of partner"
        hasError={!!errors["partners"]?.[index]}
      />
      {!!errors["partners"] && <p className="text-sm text-error">{errors["partners"][index]?.name?.message}</p>}
    </div>
  );
}
