"use client";

import { useState } from "react";
import { Datatable } from "../../../common/datatable";
import { DatatableSearch } from "../../../common/datatable/datatable-search";
import { createColumnHelper } from "@tanstack/react-table";

type Order = {
  id: string;
  customerNumber: string;
  companyName: string;
  transferDate: string;
  orderNumber: string;
  registerNumber: string;
  year: number;
};

const mockData: Order[] = Array.from({ length: 15 }, (_, index) => ({
  id: `ORD-${(100000 + index).toString()}`,
  customerNumber: `C-${(100020 + Math.floor(Math.random() * 10)).toString().padStart(6, "0")}`,
  companyName: "Lautner & Co.",
  transferDate: `25.02.202${Math.floor(Math.random() * 3)}`, // Random year from 2020 to 2023
  orderNumber: `O-${(100030 + Math.floor(Math.random() * 10)).toString()}`,
  registerNumber: `DE${Math.floor(100000000000 + Math.random() * 999999999999)}`, // Random 12-digit register number
  year: 2023,
}));

const columnHelper = createColumnHelper<Order>();

const columns = [
  columnHelper.accessor("customerNumber", {
    header: "Customer. no.",
    cell: (info) => info.getValue(),
  }),
  columnHelper.accessor("companyName", {
    header: "Company name",
    cell: (info) => info.getValue(),
  }),
  columnHelper.accessor("transferDate", {
    header: "Transfer date",
    cell: (info) => info.getValue(),
  }),
  columnHelper.accessor("orderNumber", {
    header: "Order no.",
    cell: (info) => info.getValue(),
  }),
  columnHelper.accessor("registerNumber", {
    header: "Register no.",
    cell: (info) => info.getValue(),
  }),
  columnHelper.accessor("year", {
    header: "Year",
    cell: (info) => info.getValue(),
  }),
];

export function OrdersTable() {
  const [searchTerm, setSearchTerm] = useState("");

  return (
    <div className="flex flex-col gap-10 mt-14">
      <span className="text-title-3 text-[#183362]">Orders</span>
      <div className="flex text-center justify-between">
        <div className="max-w-[320px]">
          <DatatableSearch onSearch={setSearchTerm} defaultValue={searchTerm} placeholder="Search" />
        </div>
        <span className="text-tonal-dark-cream-40 mt-4">{mockData.length} Results</span>
      </div>

      <div className="bg-gray-50 rounded-xl overflow-hidden">
        <Datatable columns={columns} data={mockData} />
      </div>
    </div>
  );
}
