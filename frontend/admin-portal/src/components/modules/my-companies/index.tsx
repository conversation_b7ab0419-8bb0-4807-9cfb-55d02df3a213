"use client";

import { ModuleContent } from "@/components/common/module-content";
import { ModuleTitle } from "@/components/common/module-title";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Add, MapsHomeWork, Upload } from "@interzero/oneepr-react-ui/Icon";
import { useRouter } from "next/navigation";
import { MyCompaniesTable } from "./my-companies-table";

export default function MyCompaniesModule() {
  const router = useRouter();

  return (
    <ModuleContent>
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center sm:gap-10 mb-10">
        <ModuleTitle
          icon={MapsHomeWork}
          title="My Companies"
          description="Lorem ipsum dolor sit amet consectetur. Risus nulla egestas orci non hendrerit. "
        />
        <Button
          variant="filled"
          color="yellow"
          size="medium"
          leadingIcon={<Add />}
          onClick={() => router.push("/my-companies/create")}
        >
          Add new companny
        </Button>
      </div>

      <MyCompaniesTable />

      <div className="flex items-center justify-end gap-2">
        <Upload className="fill-[#66A73F] size-6" />
        <p className="text-[#66A73F] text-large-paragraph-regular font-bold">Excel</p>
      </div>
    </ModuleContent>
  );
}
