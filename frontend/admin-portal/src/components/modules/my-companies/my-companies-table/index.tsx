"use client";

import { createColumnHelper } from "@tanstack/react-table";
import { MoreVerticalIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import { Datatable } from "@/components/common/datatable";
import { DatatableSearch } from "@/components/common/datatable/datatable-search";
import { Dropdown, DropdownItem } from "@/components/ui/dropdown";
import { Edit, SortDown } from "@interzero/oneepr-react-ui/Icon";

interface MyCompaniesData {
  id: string;
  companyName: string;
  regNr: string;
  vat: string;
  tax: string;
  taxNrCountryCD: string;
  adress: string;
  city: string;
  contact: { name: string; email: string };
  phone: string;
  error: string;
}

const mockData: MyCompaniesData[] = [
  {
    id: "1",
    companyName: "Lauter & Co.",
    regNr: "DE1714028249764",
    vat: "---",
    tax: "12656",
    taxNrCountryCD: "CN",
    adress: "1363, Bunts Rd Lakewood",
    city: "CN - Cologne",
    contact: {
      name: "Mr. <PERSON>",
      email: "<EMAIL>",
    },
    phone: "656565 65655",
    error: "---",
  },
  {
    id: "2",
    companyName: "Lauter & Co.",
    regNr: "DE1714028249764",
    vat: "---",
    tax: "12656",
    taxNrCountryCD: "CN",
    adress: "1363, Bunts Rd Lakewood",
    city: "CN - Cologne",
    contact: {
      name: "Mr. Taylor Lautner",
      email: "<EMAIL>",
    },
    phone: "656565 65655",
    error: "---",
  },
  {
    id: "3",
    companyName: "Lauter & Co.",
    regNr: "DE1714028249764",
    vat: "---",
    tax: "12656",
    taxNrCountryCD: "CN",
    adress: "1363, Bunts Rd Lakewood",
    city: "CN - Cologne",
    contact: {
      name: "Mr. Taylor Lautner",
      email: "<EMAIL>",
    },
    phone: "656565 65655",
    error: "---",
  },
  {
    id: "4",
    companyName: "Lauter & Co.",
    regNr: "DE1714028249764",
    vat: "---",
    tax: "12656",
    taxNrCountryCD: "CN",
    adress: "1363, Bunts Rd Lakewood",
    city: "CN - Cologne",
    contact: {
      name: "Mr. Taylor Lautner",
      email: "<EMAIL>",
    },
    phone: "656565 65655",
    error: "---",
  },
  {
    id: "5",
    companyName: "Lauter & Co.",
    regNr: "DE1714028249764",
    vat: "---",
    tax: "12656",
    taxNrCountryCD: "CN",
    adress: "1363, Bunts Rd Lakewood",
    city: "CN - Cologne",
    contact: {
      name: "Mr. Taylor Lautner",
      email: "<EMAIL>",
    },
    phone: "656565 65655",
    error: "---",
  },
];

const columnHelper = createColumnHelper<MyCompaniesData>();

export function MyCompaniesTable() {
  const router = useRouter();

  const [data] = useState<MyCompaniesData[]>(mockData);
  const [filteredData, setFilteredData] = useState<MyCompaniesData[]>(data);
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    let filtered = [...data];

    // Filter by search term
    if (searchTerm.trim()) {
      const term = searchTerm.toLowerCase().trim();
      filtered = filtered.filter((item) => item.companyName.toLowerCase().includes(term));
    }

    setFilteredData(filtered);
  }, [data, searchTerm]);

  const columns = [
    columnHelper.accessor("companyName", {
      header: ({ column }) => {
        return (
          <div className="flex items-center cursor-pointer w-[200px]">
            <p className="text-small-paragraph-regular text-primary">Company Name</p>
            <SortDown
              className="ml-2 h-4 w-4 fill-tonal-dark-cream-40"
              onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            />
          </div>
        );
      },
    }),
    columnHelper.accessor("regNr", {
      header: "Reg. Nr.",
      cell: (info) => {
        return (
          <div className="w-[140px]">
            <span className="text-primary">{info.getValue()}</span>
          </div>
        );
      },
    }),
    columnHelper.accessor("vat", {
      header: "VAT ID",
      cell: (info) => {
        return (
          <div className="w-[80px]">
            <span className="text-primary">{info.getValue()}</span>
          </div>
        );
      },
    }),
    columnHelper.accessor("tax", {
      header: "TAX",
      cell: (info) => {
        return (
          <div className="w-[80px]">
            <span className="text-primary">{info.getValue()}</span>
          </div>
        );
      },
    }),
    columnHelper.accessor("taxNrCountryCD", {
      header: "Tax Nr. Country CD",
      cell: (info) => {
        return (
          <div className="w-[140px]">
            <span className="text-primary">{info.getValue()}</span>
          </div>
        );
      },
    }),
    columnHelper.accessor("adress", {
      header: "Adress",
      cell: (info) => {
        return (
          <div className="w-[200px]">
            <span className="text-primary">{info.getValue()}</span>
          </div>
        );
      },
    }),
    columnHelper.accessor("city", {
      header: "City",
      cell: (info) => {
        return (
          <div className="w-[120px]">
            <span className="text-primary">{info.getValue()}</span>
          </div>
        );
      },
    }),
    columnHelper.accessor("contact", {
      header: "Contact",
      cell: (info) => {
        return (
          <div className="w-[170px] flex flex-col gap-1">
            <span className="text-primary">{info.getValue().name}</span>
            <span className="text-on-surface-01">{info.getValue().email}</span>
          </div>
        );
      },
    }),
    columnHelper.accessor("phone", {
      header: "Phone Number",
      cell: (info) => {
        return (
          <div className="w-[140px]">
            <span className="text-primary">{info.getValue()}</span>
          </div>
        );
      },
    }),
    columnHelper.accessor("error", {
      header: "Error",
      cell: (info) => {
        return (
          <div className="w-[50px]">
            <span className="text-primary">{info.getValue()}</span>
          </div>
        );
      },
    }),

    columnHelper.accessor("id", {
      header: " ",
      cell: (info) => {
        const customerClusterId = info.getValue();
        return (
          <Dropdown
            trigger={
              <button className="flex items-center justify-center rounded-full size-6 text-primary hover:bg-tonal-dark-cream-70 transition-colors">
                <MoreVerticalIcon className="size-4" />
              </button>
            }
          >
            <DropdownItem
              onClick={() => router.push(`/customer-clusters/${customerClusterId}/edit`)}
              className="flex flex-shrink-0 items-center gap-4 text-primary hover:bg-support-blue/10 py-5 px-4 outline-none text-base hover:cursor-pointer"
            >
              <Edit className="size-4 fill-primary" /> Edit
            </DropdownItem>
          </Dropdown>
        );
      },
    }),
  ];

  function handleViewCompanyProfile(rowData: MyCompaniesData) {
    const companyId = rowData.id;
    router.push(`/my-companies/${companyId}`);
  }

  return (
    <div className="flex flex-col gap-6 bg-cream rounded-3xl">
      <div className="flex items-center justify-between gap-4">
        <div className="relative flex-1 max-w-[320px]">
          <DatatableSearch onSearch={setSearchTerm} defaultValue={searchTerm} placeholder="Search by name" />
        </div>
      </div>
      <div className="pb-8">
        <div className="bg-gray-50 rounded-xl overflow-hidden">
          <Datatable columns={columns} data={filteredData} onRowClick={handleViewCompanyProfile} />
        </div>
      </div>
    </div>
  );
}
