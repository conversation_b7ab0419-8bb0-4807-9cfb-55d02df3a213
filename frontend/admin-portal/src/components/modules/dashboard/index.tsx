import { ModuleContent } from "@/components/common/module-content";
import { ModuleTitle } from "@/components/common/module-title";
import { Dashboard } from "@interzero/oneepr-react-ui/Icon";

import {
  AccountsCreated,
  AverageRevenue,
  OpenBalance,
  PaymentMethod,
  RevenueAndContracts,
  // ServicesCustomers,
  // ServicesRevenueOvertime,
  TerminationData,
  // TopServices,
  TotalCustomers,
} from "./sections";

export function DashboardModule() {
  return (
    <ModuleContent>
      <ModuleTitle icon={Dashboard} title="Dashboard" description="See all the information about the countries" />

      <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
        <div className="col-span-5 space-y-6">
          <TotalCustomers />
        </div>

        <div className="col-span-5 space-y-6">
          <OpenBalance />
        </div>

        <div className="col-span-5 space-y-6">
          <AverageRevenue />
        </div>

        <div className="col-span-5 space-y-6">
          <RevenueAndContracts />
        </div>

        {/* <div className="col-span-5 space-y-6">
          <ServicesCustomers />
        </div> */}

        {/* <div className="col-span-2 space-y-6">
          <TopServices />
        </div>

        <div className="col-span-3 space-y-6">
          <ServicesRevenueOvertime />
        </div> */}

        <div className="col-span-2 space-y-6">
          <AccountsCreated />
        </div>

        <div className="col-span-3 space-y-6">
          <PaymentMethod />
        </div>

        <div className="col-span-5 space-y-6">
          <TerminationData />
        </div>
      </div>
    </ModuleContent>
  );
}
