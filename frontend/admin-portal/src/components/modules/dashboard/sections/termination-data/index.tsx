"use client";
import { Dropdown, DropdownItem } from "@/components/ui/dropdown";
import { Skeleton } from "@/components/ui/skeleton";
import { getTerminationData } from "@/lib/api/dashboard/termination-data";
import { KeyboardArrowDown, Sort } from "@interzero/oneepr-react-ui/Icon";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";

const LICENSE_YEAR_FILTERS = [
  { label: "2025", value: "2025" },
  { label: "2024", value: "2024" },
  { label: "2023", value: "2023" },
];

type CONTRACT_TYPE = "EU_LICENSE" | "DIRECT_LICENSE" | "ACTION_GUIDE";

const CONTRACT_TYPE_FILTERS: { label: string; value: CONTRACT_TYPE }[] = [
  { label: "EU License", value: "EU_LICENSE" },
  { label: "Direct License", value: "DIRECT_LICENSE" },
  { label: "Action Guide", value: "ACTION_GUIDE" },
];

function formatValue(value: number): string {
  return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, " ");
}

export function TerminationData() {
  const [selectedLicenseYear, setSelectedLicenseYear] = useState(LICENSE_YEAR_FILTERS[0]);
  const [selectedContractType, setSelectedContractType] = useState(CONTRACT_TYPE_FILTERS[0]);
  const { data, isSuccess, isLoading } = useQuery({
    queryKey: ["terminations-data", selectedLicenseYear.value, selectedContractType.value],
    queryFn: () => getTerminationData({ year: +selectedLicenseYear.value, contract_type: selectedContractType.value }),
  });

  function handleChangeLicenseYear(newOrder: (typeof LICENSE_YEAR_FILTERS)[number]) {
    setSelectedLicenseYear(newOrder);
  }

  function handleChangeContractType(newOrder: (typeof CONTRACT_TYPE_FILTERS)[number]) {
    setSelectedContractType(newOrder);
  }

  return (
    <div id="termination-data" className="w-full bg-white rounded-3xl p-6 flex flex-col gap-6">
      <div className="flex flex-row justify-between items-center">
        <h3 className="text-primary text-2xl font-bold">Termination data</h3>

        <div className="flex flex-row gap-6 items-center">
          <Dropdown
            trigger={
              <button className="flex items-center text-support-blue font-bold text-base">
                <Sort width={20} height={20} className="fill-support-blue" />
                <span className="ml-1 mr-2 mt-1 text-left">{selectedLicenseYear.label}</span>
                <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
              </button>
            }
          >
            {LICENSE_YEAR_FILTERS.map((filter, idx) => (
              <DropdownItem
                key={idx}
                onClick={() => handleChangeLicenseYear(filter)}
                className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
              >
                {filter.label}
              </DropdownItem>
            ))}
          </Dropdown>

          <Dropdown
            trigger={
              <button className="flex items-center text-support-blue font-bold text-base">
                <Sort width={20} height={20} className="fill-support-blue" />
                <span className="ml-1 mr-2 mt-1 text-left">{selectedContractType.label}</span>
                <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
              </button>
            }
          >
            {CONTRACT_TYPE_FILTERS.map((filter, idx) => (
              <DropdownItem
                key={idx}
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                onClick={() => handleChangeContractType(filter as any)}
                className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
              >
                {filter.label}
              </DropdownItem>
            ))}
          </Dropdown>
        </div>
      </div>

      <div className="bg-surface-01 p-8 grid grid-cols-2 rounded-3xl">
        {isLoading && (
          <>
            <div className="flex flex-col gap-4 mr-4">
              <Skeleton className="h-16" />
              <Skeleton className="h-16" />
            </div>
            <div className="flex flex-col gap-4 ml-4">
              <Skeleton className="h-16" />
              <Skeleton className="h-16" />
            </div>
          </>
        )}
        {isSuccess && (
          <>
            <div className="flex flex-col gap-4">
              <span className="text-primary">Terminations for the upcoming year</span>
              <div className="flex flex-col">
                <span className="text-[32px] font-bold text-alert">
                  {formatValue(data.upcoming_year.total_contracts_cancelled / 100)}
                </span>
                <span className="text-primary font-2xl font-bold">Amount of contracts cancellation</span>
              </div>
              <div className="flex flex-col">
                <span className="text-[32px] font-bold text-alert">
                  € {data.upcoming_year.cancellations_revenue / 100}
                </span>
                <span className="text-primary font-2xl font-bold">Amount of contracts cancellation</span>
              </div>
            </div>

            <div className="flex flex-col gap-4">
              <span className="text-primary">Cancellations for the current year</span>
              <div className="flex flex-col">
                <span className="text-[32px] font-bold text-error">
                  {formatValue(data.current_year.total_contracts_cancelled)}
                </span>
                <span className="text-primary font-2xl font-bold">Amount of contracts cancellation</span>
              </div>
              <div className="flex flex-col">
                <span className="text-[32px] font-bold text-error">
                  € {data.current_year.cancellations_revenue / 100}
                </span>
                <span className="text-primary font-2xl font-bold">Amount of contracts cancellation</span>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
