export function ListItem({ label, value, color }: { label: string; value: number; color: string }) {
  return (
    <div className="flex flex-row w-full items-center justify-between">
      <div className="flex flex-row gap-2">
        <div style={{ backgroundColor: color }} className="size-4 rounded-full" />
        <span className="text-sm text-tonal-dark-cream-10">{label}</span>
      </div>

      <span style={{ color: color }} className="font-bold">
        € {value}
      </span>
    </div>
  );
}
