"use client";
import { DashboardPlaceHolder } from "@/components/modules/dashboard/placeholder/placeholder";
import { Dropdown, DropdownItem } from "@/components/ui/dropdown";
import { Skeleton } from "@/components/ui/skeleton";
import { getPaymentMethods } from "@/lib/api/dashboard/payment-methods";
import { GraphColors } from "@/utils/graph-colors";
import { KeyboardArrowDown, Sort } from "@interzero/oneepr-react-ui/Icon";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { PaymentMethodChart } from "./payment-method-chart";
import { PaymentMethodList } from "./payment-method-list";

const LICENSE_YEAR_FILTERS = [
  { label: "2025", value: "2025" },
  { label: "2024", value: "2024" },
  { label: "2023", value: "2023" },
];

export function PaymentMethod() {
  const [selectedLicenseYear, setSelectedLicenseYear] = useState(LICENSE_YEAR_FILTERS[0]);
  const { data, isSuccess, isLoading } = useQuery({
    queryKey: ["payment-methods", selectedLicenseYear.value],
    queryFn: () => getPaymentMethods({ year: +selectedLicenseYear.value }),
  });
  const graphColors = isSuccess
    ? new GraphColors(
        data.payment_methods.map((method) => method.label),
        true
      )
    : null;

  function handleChangeLicenseYear(newOrder: (typeof LICENSE_YEAR_FILTERS)[number]) {
    setSelectedLicenseYear(newOrder);
  }

  return (
    <div id="payment-method" className="w-full h-full bg-white rounded-3xl p-6 flex flex-col gap-6">
      <div className="flex flex-row justify-between items-center">
        <h3 className="text-primary text-2xl font-bold">Payment method</h3>

        <div className="flex flex-row gap-6 items-center">
          <Dropdown
            trigger={
              <button className="flex items-center text-support-blue font-bold text-base">
                <Sort width={20} height={20} className="fill-support-blue" />
                <span className="ml-1 mr-2 mt-1 text-left">{selectedLicenseYear.label}</span>
                <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
              </button>
            }
          >
            {LICENSE_YEAR_FILTERS.map((filter, idx) => (
              <DropdownItem
                key={idx}
                onClick={() => handleChangeLicenseYear(filter)}
                className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
              >
                {filter.label}
              </DropdownItem>
            ))}
          </Dropdown>
        </div>
      </div>

      <div className="flex flex-col gap-6 h-full">
        {isLoading && (
          <>
            <Skeleton className="h-64" />
            <Skeleton className="h-64" />
          </>
        )}
        {isSuccess && data.payment_methods.length === 0 && <DashboardPlaceHolder itemName="Payment Methods" />}
        {isSuccess && data.payment_methods.length > 0 && graphColors && (
          <>
            <PaymentMethodChart data={data} graphColors={graphColors} />
            <PaymentMethodList data={data} graphColors={graphColors} />
          </>
        )}
      </div>
    </div>
  );
}
