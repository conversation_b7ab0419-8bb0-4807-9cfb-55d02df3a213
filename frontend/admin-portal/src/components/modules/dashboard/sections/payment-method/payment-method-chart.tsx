import { PaymentMethods } from "@/types/dashboard/payment-methods";
import { GraphColors } from "@/utils/graph-colors";
import { formatPaymentMethodLabel } from "@/utils/payment-method-labels";
import { <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer, Toolt<PERSON>, <PERSON>A<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const CustomYAxisTick = ({ x, y, payload }: any) => {
  const [method, percentage] = payload.value.split("; ");
  return (
    <g transform={`translate(${x},${y - 10})`}>
      <foreignObject width={200} height={20}>
        <div className="flex items-center gap-2">
          <span className="font-bold text-[#002652]">{method}</span>
          <span className="text-[#808FA9]">{percentage}</span>
        </div>
      </foreignObject>
    </g>
  );
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const CustomTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    const methodName = data.label.split("; ")[0];
    return (
      <div className="bg-white p-3 shadow-lg rounded border">
        <p className="text-sm text-[#808FA9]">{methodName}</p>
        <div className="flex gap-1">
          <p className="font-bold text-[#002652]">{data.value}</p>
          <span className="text-[#808FA9]">({data.percentage}%)</span>
        </div>
      </div>
    );
  }

  return null;
};

export function PaymentMethodChart({ data, graphColors }: { data: PaymentMethods; graphColors: GraphColors }) {
  const chartData = data.payment_methods.map((method) => ({
    label: formatPaymentMethodLabel(method),
    value: method.total_transactions,
    percentage: method.percentage,
    fill: graphColors.getColor(method.label),
  }));

  return (
    <ResponsiveContainer width="100%" height={250}>
      <BarChart data={chartData} layout="vertical" barSize={18} margin={{ right: 50 }}>
        <Tooltip content={<CustomTooltip />} />
        <XAxis type="number" hide />
        <YAxis
          dataKey="label"
          type="category"
          orientation="right"
          axisLine={false}
          tickLine={false}
          width={100}
          tick={<CustomYAxisTick />}
        />
        <Bar dataKey="value" radius={[0, 20, 20, 0]} />
      </BarChart>
    </ResponsiveContainer>
  );
}
