"use client";

import { useEffect, useState } from "react";
import { createColumnHelper } from "@tanstack/react-table";
import { MoreVerticalIcon } from "lucide-react";

import { Button } from "@interzero/oneepr-react-ui/Button";
import { Cancel, Edit, SortDown } from "@interzero/oneepr-react-ui/Icon";
import { Datatable } from "@/components/common/datatable";
import { DatatableSearch } from "@/components/common/datatable/datatable-search";
import { Dropdown } from "@/components/ui/dropdown";
import { DropdownItem } from "@/components/common/dropdown";
import { formatCurrency } from "@/utils/format-currency";
import { dateManager } from "@/utils/date-manager";

import { EditOrderDialog } from "../edit-order-dialog";
import { CancelOrderDialog } from "../cancel-order-dialog";

interface OrderData {
  id: string | number;
  status: "OPEN" | "PAYED" | "CANCELLED";
  total_price: number;
  certificate: boolean | null;
  broker_number: string;
  broker_name: string;
  customer_number: string;
  company_name: string;
  transfer_date: string;
  order_number: string;
  register_number: string;
  year: number;
  fractions: Record<string, { weight: string }>;
  created_at: string;
}

const mockData: OrderData[] = Array.from({ length: 15 }, (_, index) => ({
  id: (1000 + index).toString(),
  status: index % 2 === 0 ? "OPEN" : "CANCELLED",
  total_price: 150659 * 100, // in cents
  certificate: index % 2 === 0 ? true : false,
  broker_number: "B-1000000",
  broker_name: "Techcent",
  customer_number: "C-100021",
  company_name: "Lauter & Co.",
  transfer_date: "2023-02-25",
  order_number: "O-1000365",
  register_number: "DE1714028249764",
  year: 2023,
  fractions: {
    glass: { weight: "56 kg" },
    ppk: { weight: "56 kg" },
    fe: { weight: "56 kg" },
    alu: { weight: "56 kg" },
    cb: { weight: "56 kg" },
    sv: { weight: "56 kg" },
    plstc: { weight: "56 kg" },
    sm: { weight: "56 kg" },
  },
  created_at: "2023-01-01",
}));

const columnHelper = createColumnHelper<OrderData>();

const columns = [
  columnHelper.accessor("id", {
    header: " ",
    cell: (info) => {
      const orderId = info.getValue();
      const status = info.row.original.status;
      return (
        <Dropdown
          trigger={
            <button className="flex items-center justify-center rounded-full size-6 text-primary hover:bg-tonal-dark-cream-70 transition-colors">
              <MoreVerticalIcon className="size-4" />
            </button>
          }
        >
          <DropdownItem asChild>
            <EditOrderDialog orderId={orderId}>
              <button className="flex flex-shrink-0 items-center gap-4 w-full text-primary hover:bg-support-blue/10 py-5 px-4 outline-none text-base hover:cursor-pointer">
                <Edit className="size-4 fill-primary" /> Edit Order
              </button>
            </EditOrderDialog>
          </DropdownItem>
          {status !== "CANCELLED" && (
            <DropdownItem asChild>
              <CancelOrderDialog orderId={orderId}>
                <button className="flex flex-shrink-0 items-center text-left gap-4 w-full text-primary hover:bg-support-blue/10 py-5 px-4 outline-none text-base hover:cursor-pointer">
                  <Cancel className="size-4 fill-primary" /> Cancel <br /> Order
                </button>
              </CancelOrderDialog>
            </DropdownItem>
          )}
        </Dropdown>
      );
    },
  }),
  columnHelper.accessor("status", {
    header: ({ column }) => {
      return (
        <Button
          type="button"
          variant="text"
          size="small"
          color="gray"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Status Code <SortDown className="ml-2 h-4 w-4 fill-tonal-dark-cream-40" />
        </Button>
      );
    },
    cell: (info) => {
      const status = info.getValue();
      const createdAt = info.row.original.created_at;
      return (
        <div
          data-status={status}
          className="pl-2 flex flex-col gap-1 text-primary data-[status=OPEN]:text-success data-[status=CANCELLED]:text-error"
        >
          <div className="flex items-center gap-1">
            <div className="bg-[currentColor] size-2 rounded-full" />
            <span className="text-sm font-bold text-[inherit] capitalize">{status.toLowerCase()}</span>
          </div>
          {status === "CANCELLED" && (
            <span className="text-tonal-dark-cream-40 text-sm">on {dateManager(createdAt).format("DD.MM.YY")}</span>
          )}
        </div>
      );
    },
  }),
  columnHelper.accessor("total_price", {
    header: ({ column }) => {
      return (
        <Button
          type="button"
          variant="text"
          size="small"
          color="gray"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Total Price <SortDown className="ml-2 h-4 w-4 fill-tonal-dark-cream-40" />
        </Button>
      );
    },
    cell: (info) => {
      const amount = info.getValue() / 100;
      return <span className="pl-2">{formatCurrency(amount)}</span>;
    },
  }),
  columnHelper.accessor("certificate", {
    header: ({ column }) => {
      return (
        <Button
          type="button"
          variant="text"
          size="small"
          color="gray"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Certificate <SortDown className="ml-2 h-4 w-4 fill-tonal-dark-cream-40" />
        </Button>
      );
    },
    cell: (info) => {
      const hasCertificate = info.getValue();
      return (
        <span data-valid={hasCertificate} className="pl-2 data-[valid=true]:text-success font-bold text-sm">
          {hasCertificate ? "Valid" : "-----"}
        </span>
      );
    },
  }),
  columnHelper.accessor("broker_number", {
    header: ({ column }) => {
      return (
        <Button
          type="button"
          variant="text"
          size="small"
          color="gray"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Broker no. <SortDown className="ml-2 h-4 w-4 fill-tonal-dark-cream-40" />
        </Button>
      );
    },
    cell: (info) => {
      return <span className="pl-2">{info.getValue()}</span>;
    },
  }),
  columnHelper.accessor("broker_name", {
    header: ({ column }) => {
      return (
        <Button
          type="button"
          variant="text"
          size="small"
          color="gray"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Broker Name <SortDown className="ml-2 h-4 w-4 fill-tonal-dark-cream-40" />
        </Button>
      );
    },
    cell: (info) => {
      return <span className="pl-2">{info.getValue()}</span>;
    },
  }),
  columnHelper.accessor("customer_number", {
    header: ({ column }) => {
      return (
        <Button
          type="button"
          variant="text"
          size="small"
          color="gray"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Customer. no. <SortDown className="ml-2 h-4 w-4 fill-tonal-dark-cream-40" />
        </Button>
      );
    },
    cell: (info) => {
      return <span className="pl-2">{info.getValue()}</span>;
    },
  }),
  columnHelper.accessor("company_name", {
    header: ({ column }) => {
      return (
        <Button
          type="button"
          variant="text"
          size="small"
          color="gray"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Company name <SortDown className="ml-2 h-4 w-4 fill-tonal-dark-cream-40" />
        </Button>
      );
    },
    cell: (info) => {
      return <span className="pl-2">{info.getValue()}</span>;
    },
  }),
  columnHelper.accessor("transfer_date", {
    header: ({ column }) => {
      return (
        <Button
          type="button"
          variant="text"
          size="small"
          color="gray"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Transfer date <SortDown className="ml-2 h-4 w-4 fill-tonal-dark-cream-40" />
        </Button>
      );
    },
    cell: (info) => {
      const date = info.getValue();
      return <span className="pl-2">{dateManager(date).format("DD.MM.YYYY")}</span>;
    },
  }),
  columnHelper.accessor("order_number", {
    header: ({ column }) => {
      return (
        <Button
          type="button"
          variant="text"
          size="small"
          color="gray"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Order no. <SortDown className="ml-2 h-4 w-4 fill-tonal-dark-cream-40" />
        </Button>
      );
    },
    cell: (info) => {
      return <span className="pl-2">{info.getValue()}</span>;
    },
  }),
  columnHelper.accessor("register_number", {
    header: ({ column }) => {
      return (
        <Button
          type="button"
          variant="text"
          size="small"
          color="gray"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Register no. <SortDown className="ml-2 h-4 w-4 fill-tonal-dark-cream-40" />
        </Button>
      );
    },
    cell: (info) => {
      return <span className="pl-2">{info.getValue()}</span>;
    },
  }),
  columnHelper.accessor("year", {
    header: ({ column }) => {
      return (
        <Button
          type="button"
          variant="text"
          size="small"
          color="gray"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Year <SortDown className="ml-2 h-4 w-4 fill-tonal-dark-cream-40" />
        </Button>
      );
    },
    cell: (info) => {
      return <span className="pl-2">{info.getValue()}</span>;
    },
  }),
];

export function OrdersTable() {
  const [data] = useState<OrderData[]>(mockData);
  const [filteredData, setFilteredData] = useState<OrderData[]>(data);
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    let filtered = [...data];

    // Filter by search term
    if (searchTerm.trim()) {
      const term = searchTerm.toLowerCase().trim();
      filtered = filtered.filter(
        (item) =>
          item.broker_number.toLowerCase().includes(term) ||
          item.broker_name.toLowerCase().includes(term) ||
          item.customer_number.includes(term)
      );
    }

    setFilteredData(filtered);
  }, [data, searchTerm]);

  return (
    <div className="flex flex-col items-center gap-9 w-max">
      <div className="flex justify-between items-center w-full">
        <div className="max-w-[320px]">
          <DatatableSearch onSearch={setSearchTerm} defaultValue={searchTerm} placeholder="Search" />
        </div>
        <p className="text-tonal-dark-cream-40">{mockData.length} Results</p>
      </div>
      <div className="w-max max-w-[912px]">
        <Datatable columns={columns} data={filteredData} />
      </div>
    </div>
  );
}
