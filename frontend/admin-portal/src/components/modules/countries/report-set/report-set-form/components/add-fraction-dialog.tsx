import { CheckboxInput } from "@/components/ui/checkbox";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { FractionIcon } from "@/components/ui/fraction-icon";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Add } from "@interzero/oneepr-react-ui/Icon";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { zodResolver } from "@hookform/resolvers/zod";
import { ArrowRight, RefreshCcw } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm, useFormContext, useWatch } from "react-hook-form";
import { z } from "zod";
import { ReportSetFormData } from "./report-set-form-provider";
import { AddFractionIconSchema, ReportSetFractionIcons } from "./report-set-fraction-icons";
import { SPECIAL_CHARS_REGEX } from "@/utils/regex";

interface AddFractionDialogProps {
  type: "FRACTION" | "CATEGORY";
  onAdd: (data: AddFractionFormData) => void;
}

const addFractionFormSchema = z.object({
  id: z.number().optional(),
  name: z
    .string({ message: "Fraction name is required" })
    .min(1, { message: "Fraction name is required" })
    .regex(SPECIAL_CHARS_REGEX, "Invalid name"),
  description: z
    .string()
    .refine((val) => val === "" || SPECIAL_CHARS_REGEX.test(val), "Invalid description")
    .optional()
    .default(""),
  fraction_icon_id: z.number({ message: "Fraction icon is required" }),
  fraction_icon_image_url: z.string().optional().default(""),
  is_active: z.boolean().default(true),
  level: z.number().default(1),
  parent_id: z.number().nullable().default(null),
  has_second_level: z.boolean().default(false),
  has_third_level: z.boolean().default(false),
});

export type AddFractionFormData = z.infer<typeof addFractionFormSchema>;

export function AddFractionDialog({ onAdd }: AddFractionDialogProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isFractionIconsOpen, setIsFractionIconsOpen] = useState(false);

  const reportSetForm = useFormContext<ReportSetFormData>();

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
    control,
    setValue,
  } = useForm<AddFractionFormData>({
    resolver: zodResolver(addFractionFormSchema),
    defaultValues: {
      is_active: true,
    },
  });

  useEffect(() => {
    reset();

    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isDialogOpen]);

  function handleFormSubmit(data: AddFractionFormData) {
    onAdd(data);
    setIsDialogOpen(false);
  }

  async function handleDialogOpenChange(open: boolean) {
    if (!open) {
      reset({
        id: undefined,
        name: undefined,
        description: undefined,
        is_active: true,
        has_second_level: false,
        has_third_level: false,
      });
      setIsFractionIconsOpen(false);
    }

    setIsDialogOpen(open);
  }

  function handleAddFractionIcon({ fraction_icon_id, fraction_icon_image_url }: AddFractionIconSchema) {
    if (!fraction_icon_id) return;

    setValue("fraction_icon_id", fraction_icon_id);
    setValue("fraction_icon_image_url", fraction_icon_image_url);
    setIsFractionIconsOpen(false);
  }

  const hasSecondLevel = useWatch({ control, name: "has_second_level" });

  const reportSetMode = reportSetForm.getValues("mode");

  const fractionIconId = useWatch({ control, name: "fraction_icon_id" });
  const fractionIconImageUrl = useWatch({ control, name: "fraction_icon_image_url" });

  return (
    <Dialog open={isDialogOpen} onOpenChange={handleDialogOpenChange}>
      <DialogTrigger asChild>
        <div>
          <Button type="button" variant="text" color="light-blue" size="small" leadingIcon={<Add />}>
            Add Fraction level 01
          </Button>
        </div>
      </DialogTrigger>
      <DialogContent className="px-8 max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Add fraction (level 01)</DialogTitle>
          <DialogDescription>
            {!isFractionIconsOpen ? "Fill fraction (level 01) informations" : "Select or upload image"}
          </DialogDescription>
        </DialogHeader>
        {isFractionIconsOpen && (
          <ReportSetFractionIcons
            key={fractionIconId}
            onClose={() => setIsFractionIconsOpen(false)}
            onAddFractionIcon={handleAddFractionIcon}
            defaultFractionIconId={fractionIconId}
          />
        )}
        {!isFractionIconsOpen && (
          <div id="add-new-information-form" className="w-full space-y-10">
            <div className="w-full space-y-6">
              <div className="flex items-center gap-3">
                <CheckboxInput {...register("is_active")} />
                <div
                  data-error={!!errors.fraction_icon_id}
                  onClick={() => setIsFractionIconsOpen(true)}
                  className="rounded-lg cursor-pointer hover:opacity-80 border-[1.5px] border-transparent data-[error=true]:border-error"
                >
                  {fractionIconId && (
                    <div className="relative size-16 overflow-hidden flex-none cursor-pointer border-[1.5px] border-transparent hover:border-primary data-[selected=true]:border-primary rounded-md transition-all duration-100">
                      <FractionIcon iconUrl={fractionIconImageUrl} size="large" />
                      <div className="z-10 absolute bottom-1 right-1 rounded-full flex items-center justify-center">
                        <RefreshCcw className="size-3 stroke-support-blue" />
                      </div>
                    </div>
                  )}
                  {!fractionIconId && (
                    <div className="bg-tonal-dark-blue-90 text-white flex-none size-16 rounded-lg flex items-center justify-center">
                      <Add className="size-6 fill-primary" />
                    </div>
                  )}
                </div>
                <Input placeholder="Fraction name" {...register("name")} variant={errors.name ? "error" : "default"} />
              </div>
              <Textarea
                label="Description"
                placeholder="Description"
                {...register("description")}
                errorMessage={errors.description?.message}
                rows={3}
              />
              {reportSetMode === "ON_PLATAFORM" && (
                <div className="flex items-center gap-8">
                  <CheckboxInput
                    label="Subfraction Level 02"
                    {...register("has_second_level", {
                      onChange: (e) => {
                        if (!e.target.checked) setValue("has_third_level", false);
                      },
                    })}
                  />
                  <CheckboxInput
                    disabled={!hasSecondLevel}
                    label="Subfraction Level 03"
                    {...register("has_third_level")}
                  />
                </div>
              )}
            </div>
            <div className="flex flex-col mt-8">
              <div className="flex items-center justify-end">
                <Button
                  form="add-new-information-form"
                  type="submit"
                  variant="filled"
                  color="yellow"
                  size="medium"
                  trailingIcon={<ArrowRight />}
                  onClick={() => handleSubmit(handleFormSubmit)()}
                >
                  Save
                </Button>
              </div>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
