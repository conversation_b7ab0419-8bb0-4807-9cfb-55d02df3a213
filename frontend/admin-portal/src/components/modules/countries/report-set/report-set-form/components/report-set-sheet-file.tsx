"use client";

import { useFormContext, useWatch } from "react-hook-form";
import { ReportSetFormData } from "./report-set-form-provider";
import { Delete, Download, Upload } from "@interzero/oneepr-react-ui/Icon";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { formatFileSize } from "@/utils/format-file-size";
import { downloadFile } from "@/utils/download-file";
import { Textarea } from "@/components/ui/textarea";
import { uploadFile } from "@/lib/api/upload-files";
import { Role } from "@/utils/user";
import { useMutation, useQuery } from "@tanstack/react-query";
import { enqueueSnackbar } from "notistack";
import { FullReportSet } from "@/types/service-setup";
import { Loader } from "lucide-react";
import { useState } from "react";

export function ReportSetSheetFile({ sheetFile }: { sheetFile: FullReportSet["sheet_file"] }) {
  const {
    register,
    formState: { errors },
    control,
    setValue,
  } = useFormContext<ReportSetFormData>();

  const sheetFileId = useWatch({ control, name: "sheet_file_id" });
  const [memoryFile, setMemoryFile] = useState<File | null>(null);

  const { mutate: uploadSheetFile, isPending: isUploadingSheetFile } = useMutation({
    mutationFn: (file: File) =>
      uploadFile({
        user_id: 0,
        user_role: Role.ADMIN,
        file,
        document_type: "REPORT_SET_SHEET",
      }),
  });

  const { refetch: downloadSheetFile, isLoading: isDownloadingSheetFile } = useQuery({
    queryKey: ["report-set-sheet-file", sheetFileId],
    queryFn: () => {
      const name = sheetFileId === sheetFile?.id ? sheetFile?.original_name : memoryFile?.name;

      return downloadFile({
        fileId: sheetFileId!,
        fileName: name!,
      });
    },
    enabled: false,
  });

  function handleOnDragOver(e: React.DragEvent<HTMLDivElement>) {
    e.preventDefault();
    e.stopPropagation();
  }

  async function handleOnDrop(e: React.DragEvent<HTMLDivElement>) {
    e.preventDefault();
    e.stopPropagation();

    const file = e.dataTransfer.files[0];

    setMemoryFile(file);
    handleUploadSheetFile(file);
  }

  async function handleOnInputChange(e: React.ChangeEvent<HTMLInputElement>) {
    const file = e.target.files?.[0];
    if (!file) return;

    setMemoryFile(file);
    handleUploadSheetFile(file);
  }

  async function handleUploadSheetFile(file: File) {
    if (!file) return;

    if (!file.name.endsWith(".xls") && !file.name.endsWith(".xlsx")) return;

    uploadSheetFile(file, {
      onSuccess: (uploadedFile) => {
        enqueueSnackbar("File uploaded successfully", { variant: "success" });
        setValue("sheet_file_id", uploadedFile.id);
      },
      onError: () => {
        setMemoryFile(null);
        enqueueSnackbar("Error uploading file", { variant: "error" });
      },
    });
  }

  function handleDeleteSheetFile() {
    setValue("sheet_file_id", null);
  }

  if (isUploadingSheetFile)
    return (
      <div className="flex items-center justify-center flex-col w-full p-8 gap-4 rounded-2xl border-2 bg-tonal-dark-blue-90 border-dashed border-support-blue cursor-pointer">
        <Upload className="size-12 fill-support-blue animate-pulse" />
        <p className="text-primary">Uploading file...</p>
      </div>
    );

  if (!sheetFileId)
    return (
      <>
        <input
          type="file"
          className="hidden"
          accept=".xls,.xlsx"
          onChange={handleOnInputChange}
          id="sheet-file-input"
        />
        <div
          data-error={!!errors.sheet_file_id}
          className="group flex items-center justify-center flex-col w-full p-8 gap-4 rounded-2xl border-2 border-dashed cursor-pointer bg-tonal-dark-blue-90  border-support-blue data-[error=true]:border-error data-[error=true]:bg-on-error"
          onClick={() => document.getElementById("sheet-file-input")?.click()}
          onDragOver={handleOnDragOver}
          onDrop={handleOnDrop}
        >
          <Upload className="size-12 fill-support-blue group-data-[error=true]:fill-error" />
          <p className="text-primary group-data-[error=true]:text-error">
            Drag and drop Excel file or{" "}
            <span className="underline underline-offset-4 text-support-blue group-data-[error=true]:text-error">
              click here
            </span>
            .
          </p>
        </div>
      </>
    );

  return (
    <div className="bg-background rounded-[20px] p-8 w-full">
      <div className="flex items-center gap-2">
        <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M38.82 14.8229L29.16 5.16293C28.42 4.42293 27.4 4.00293 26.34 4.00293H12C9.8 4.00293 8.02 5.80293 8.02 8.00293L8 40.0029C8 42.2029 9.78 44.0029 11.98 44.0029H36C38.2 44.0029 40 42.2029 40 40.0029V17.6629C40 16.6029 39.58 15.5829 38.82 14.8229ZM28 18.0029C26.9 18.0029 26 17.1029 26 16.0029V7.00293L37 18.0029H28Z"
            fill="#1B6C64"
          />
          <path
            d="M17.545 31.6033H19.621L21.289 34.2553L22.969 31.6033H25.009L22.333 35.6713L25.201 40.0033H23.113L21.265 37.0993L19.393 40.0033H17.353L20.209 35.6833L17.545 31.6033Z"
            fill="#F2F2F2"
          />
          <path d="M26.1497 31.6033H27.9497V38.3113H30.8777V40.0033H26.1497V31.6033Z" fill="#F2F2F2" />
          <path
            d="M31.516 38.0953L33.064 37.2673C33.352 37.9873 33.952 38.4673 34.684 38.4673C35.272 38.4673 35.764 38.1673 35.764 37.6273C35.764 37.2193 35.488 36.9433 34.888 36.7273L33.88 36.3673C32.656 35.9233 31.876 35.1793 31.876 33.9673C31.876 32.4433 33.04 31.4473 34.624 31.4473C35.872 31.4473 36.964 32.1313 37.48 33.2593L35.98 34.1233C35.728 33.5593 35.284 33.1153 34.66 33.1153C34.108 33.1153 33.724 33.4153 33.724 33.8593C33.724 34.2553 34.024 34.4953 34.54 34.6873L35.632 35.1073C36.976 35.6113 37.624 36.3793 37.624 37.5073C37.624 39.1873 36.22 40.1593 34.66 40.1593C33.268 40.1593 32.044 39.4273 31.516 38.0953Z"
            fill="#F2F2F2"
          />
        </svg>
        <div className="text-primary font-bold">{memoryFile?.name || sheetFile?.original_name}</div>
      </div>
      <div className="px-4">
        <div className="py-3 w-full flex items-center justify-between text-sm text-tonal-dark-cream-20 gap-4">
          <div className="flex-1 overflow-hidden text-ellipsis whitespace-nowrap">
            {memoryFile?.name || sheetFile?.original_name}
          </div>
          <div className="flex-none flex items-center justify-end gap-2">
            <div className="w-20 flex items-center justify-end">
              {formatFileSize(Number(memoryFile?.size || sheetFile?.size))}
            </div>

            <div>
              {isDownloadingSheetFile ? (
                <Loader className="size-4 animate-spin" />
              ) : (
                <Button
                  type="button"
                  variant="text"
                  color="dark-blue"
                  size="iconSmall"
                  trailingIcon={<Download />}
                  onClick={() => downloadSheetFile()}
                />
              )}
            </div>
            <Button
              type="button"
              variant="text"
              color="dark-blue"
              size="iconSmall"
              trailingIcon={<Delete className="fill-primary" />}
              onClick={() => handleDeleteSheetFile()}
            />
          </div>
        </div>
        <div className="h-[1px] w-full bg-tonal-dark-cream-80 rounded-full my-4" />
        <Textarea
          label="Description"
          placeholder="Description"
          {...register("sheet_file_description")}
          errorMessage={errors.sheet_file_description?.message}
          rows={3}
        />
      </div>
    </div>
  );
}
