"use client";

import { FractionIcon } from "@/components/ui/fraction-icon";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Delete, Error, Visibility, VisibilityOff } from "@interzero/oneepr-react-ui/Icon";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { CornerDownRight } from "lucide-react";
import { useFormContext, useWatch } from "react-hook-form";
import { AddFractionDialog } from "./add-fraction-dialog";
import { FormFraction, ReportSetFormData } from "./report-set-form-provider";
import { UpdateFractionDialog } from "./update-fraction-dialog";
import {
  AlertDialog,
  AlertDialogTitle,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogCancel,
  AlertDialogAction,
  AlertDialogFooter,
  AlertDialogTrigger,
  AlertDialogDescription,
} from "@/components/ui/alert-dialog";
import { generateCode } from "@/utils/generate-code";
import { enqueueSnackbar } from "notistack";

export function ReportSetFractions() {
  const reportSetForm = useFormContext<ReportSetFormData>();
  const {
    register,
    formState: { errors },
    control,
    setValue,
    getValues,
  } = useFormContext<ReportSetFormData>();

  function handleAddFraction(data: {
    name: string;
    description: string;
    fraction_icon_id: number;
    fraction_icon_image_url: string;
    is_active: boolean;
    parent_code?: string;
    children_visible?: boolean;
    has_second_level?: boolean;
    has_third_level?: boolean;
  }) {
    const currentFractions = getValues("fractions");

    if (!data.parent_code) {
      const firstLevelFractionCode = generateCode();

      const newFraction: FormFraction = {
        parent_id: null,
        code: firstLevelFractionCode,
        parent_code: null,
        name: data.name,
        description: data.description,
        icon: "aluminium",
        fraction_icon_id: data.fraction_icon_id,
        fraction_icon: {
          id: data.fraction_icon_id,
          image_url: data.fraction_icon_image_url,
        },
        is_active: data.is_active,
        level: 1,
        order: currentFractions.length + 1,
        children: [],
        children_visible: true,
        has_second_level: data.has_second_level || false,
        has_third_level: data.has_third_level || false,
      };

      if (data.has_second_level) {
        const secondLevelFractionCode = generateCode();

        newFraction.children = [
          {
            parent_id: null,
            code: secondLevelFractionCode,
            parent_code: firstLevelFractionCode,
            name: "",
            description: "",
            icon: "aluminium",
            fraction_icon_id: data.fraction_icon_id,
            fraction_icon: {
              id: data.fraction_icon_id,
              image_url: data.fraction_icon_image_url,
            },
            is_active: true,
            level: 2,
            order: 1,
            children: [],
            children_visible: true,
            has_second_level: false,
            has_third_level: false,
          },
        ];

        if (data.has_third_level) {
          newFraction.children[0].children = [
            {
              parent_id: null,
              code: generateCode(),
              parent_code: secondLevelFractionCode,
              name: "",
              description: "",
              icon: "aluminium",
              fraction_icon_id: data.fraction_icon_id,
              fraction_icon: {
                id: data.fraction_icon_id,
                image_url: data.fraction_icon_image_url,
              },
              is_active: true,
              level: 3,
              order: 1,
              children_visible: true,
              has_second_level: false,
              has_third_level: false,
            },
          ];
        }
      }

      setValue("fractions", [...currentFractions, newFraction]);
      return;
    }

    const foundParentFraction = findFractionByCode(data.parent_code);

    if (!foundParentFraction) return;

    // Parent level 1
    if (!foundParentFraction.firstLevelParent && !foundParentFraction.secondLevelParent) {
      setValue(`fractions.${foundParentFraction.fraction.order - 1}`, {
        ...foundParentFraction.fraction,
        children_visible: true,
        children: [
          ...foundParentFraction.fraction.children,
          {
            code: generateCode(),
            parent_code: foundParentFraction.fraction.code,
            name: "",
            description: "",
            is_active: true,
            parent_id: foundParentFraction.fraction.id || null,
            children: [],
            children_visible: false,
            icon: "aluminium",
            fraction_icon_id: foundParentFraction.fraction.fraction_icon_id,
            fraction_icon: foundParentFraction.fraction.fraction_icon,
            level: foundParentFraction.fraction.level + 1,
            order: foundParentFraction.fraction.children.length + 1,
            has_second_level: false,
            has_third_level: false,
          },
        ],
      });
      return;
    }

    // Parent level 2
    if (foundParentFraction.firstLevelParent && !foundParentFraction.secondLevelParent) {
      setValue(
        `fractions.${foundParentFraction.firstLevelParent.order - 1}.children.${
          foundParentFraction.fraction.order - 1
        }`,
        {
          ...foundParentFraction.fraction,
          children_visible: true,
          children: [
            ...foundParentFraction.fraction.children,
            {
              id: undefined,
              code: generateCode(),
              parent_code: foundParentFraction.fraction.code,
              name: "",
              description: "",
              is_active: true,
              parent_id: foundParentFraction.fraction.id || null,
              children_visible: false,
              icon: "aluminium",
              fraction_icon_id: foundParentFraction.fraction.fraction_icon_id,
              fraction_icon: foundParentFraction.fraction.fraction_icon,
              level: foundParentFraction.fraction.level + 1,
              order: foundParentFraction.fraction.children.length + 1,
              has_second_level: false,
              has_third_level: false,
            },
          ],
        }
      );
      return;
    }
  }

  function handleUpdateFraction(
    code: string,
    data: {
      name?: string;
      description?: string;
      fraction_icon_id?: number;
      fraction_icon_image_url?: string;
      is_active?: boolean;
      children_visible?: boolean;
    }
  ) {
    const foundFraction = findFractionByCode(code);

    if (!foundFraction) return;

    if (!foundFraction.firstLevelParent && !foundFraction.secondLevelParent) {
      setValue(`fractions.${foundFraction.fraction.order - 1}`, {
        ...foundFraction.fraction,
        ...data,
        name: data.name || foundFraction.fraction.name || "",
        description: data.description || foundFraction.fraction.description || "",
        fraction_icon: {
          id: data.fraction_icon_id! || foundFraction.fraction.fraction_icon_id,
          image_url: data.fraction_icon_image_url! || foundFraction.fraction.fraction_icon.image_url,
        },
      });
    }

    if (foundFraction.firstLevelParent && !foundFraction.secondLevelParent) {
      setValue(`fractions.${foundFraction.firstLevelParent.order - 1}.children.${foundFraction.fraction.order - 1}`, {
        ...foundFraction.fraction,
        ...data,
        name: data.name || foundFraction.fraction.name || "",
        description: data.description || foundFraction.fraction.description || "",
        fraction_icon: {
          id: data.fraction_icon_id! || foundFraction.fraction.fraction_icon_id,
          image_url: data.fraction_icon_image_url! || foundFraction.fraction.fraction_icon.image_url,
        },
      });
    }

    if (foundFraction.firstLevelParent && foundFraction.secondLevelParent) {
      setValue(
        `fractions.${foundFraction.firstLevelParent.order - 1}.children.${
          foundFraction.secondLevelParent.order - 1
        }.children.${foundFraction.fraction.order - 1}`,
        {
          ...foundFraction.fraction,
          ...data,
          name: data.name || foundFraction.fraction.name || "",
          description: data.description || foundFraction.fraction.description || "",
          fraction_icon: {
            id: data.fraction_icon_id! || foundFraction.fraction.fraction_icon_id,
            image_url: data.fraction_icon_image_url! || foundFraction.fraction.fraction_icon.image_url,
          },
        }
      );
    }
  }

  function handleRemoveFraction(code: string) {
    const foundFraction = findFractionByCode(code);

    if (!foundFraction) return;

    if (!foundFraction.firstLevelParent && !foundFraction.secondLevelParent) {
      const currentFractions = getValues("fractions") || [];

      const formattedFractions = currentFractions
        .filter((fraction) => fraction.code !== code)
        .map((fraction, index) => ({
          ...fraction,
          order: index + 1,
        }));

      setValue("fractions", formattedFractions);
      enqueueSnackbar("Fraction removed successfully", { variant: "success" });
      return;
    }

    if (foundFraction.firstLevelParent && !foundFraction.secondLevelParent) {
      const currentFractions = getValues(`fractions.${foundFraction.firstLevelParent.order - 1}.children`) || [];

      const formattedFractions = currentFractions
        .filter((fraction) => fraction.code !== code)
        .map((fraction, index) => ({
          ...fraction,
          order: index + 1,
        }));

      setValue(`fractions.${foundFraction.firstLevelParent.order - 1}.children`, formattedFractions);
      enqueueSnackbar("Fraction removed successfully", { variant: "success" });
      return;
    }

    if (foundFraction.firstLevelParent && foundFraction.secondLevelParent) {
      const currentFractions =
        getValues(
          `fractions.${foundFraction.firstLevelParent.order - 1}.children.${
            foundFraction.secondLevelParent.order - 1
          }.children`
        ) || [];

      const formattedFractions = currentFractions
        .filter((fraction) => fraction.code !== code)
        .map((fraction, index) => ({
          ...fraction,
          order: index + 1,
        }));

      setValue(
        `fractions.${foundFraction.firstLevelParent.order - 1}.children.${
          foundFraction.secondLevelParent.order - 1
        }.children`,
        formattedFractions
      );
      enqueueSnackbar("Fraction removed successfully", { variant: "success" });
      return;
    }
  }

  function findFractionByCode(code: string) {
    const firstLevelFractions = getValues("fractions");

    const firstLevelFraction = firstLevelFractions.find((firstLevelFraction) => firstLevelFraction.code === code);

    if (firstLevelFraction) {
      return {
        fraction: firstLevelFraction,
        firstLevelParent: null,
        secondLevelParent: null,
      };
    }

    for (const firstLevelFraction of firstLevelFractions) {
      const secondLevelFraction = firstLevelFraction.children.find((child) => child.code === code);

      if (secondLevelFraction) {
        return {
          fraction: secondLevelFraction,
          firstLevelParent: firstLevelFraction,
          secondLevelParent: null,
        };
      }
    }

    for (const firstLevelFraction of firstLevelFractions) {
      const secondLevelFractions = firstLevelFraction.children;

      for (const secondLevelFraction of secondLevelFractions) {
        const thirdLevelFraction = secondLevelFraction.children.find((child) => child.code === code);

        if (thirdLevelFraction) {
          return {
            fraction: thirdLevelFraction,
            firstLevelParent: firstLevelFraction,
            secondLevelParent: secondLevelFraction,
          };
        }
      }
    }

    return null;
  }

  const fractions = useWatch({ control, name: "fractions" });
  const reportSetMode = reportSetForm.getValues("mode");

  return (
    <div className="w-full space-y-8">
      <div className="flex items-center justify-between">
        <p className="text-primary text-lg font-bold">Fractions for calculator (level 01)</p>
        {!!errors.fractions && (
          <div className="flex items-center gap-2">
            <Error className="size-5 fill-error" />
            <p className="text-error text-sm">{errors.fractions.root?.message || errors.fractions.message}</p>
          </div>
        )}
      </div>
      <div className="space-y-8">
        {fractions.map((firstLevelFraction, firstLevelFractionIndex) => (
          <div key={firstLevelFraction.id} className="space-y-1">
            <div className="flex items-center gap-3">
              <FractionIcon size="medium" iconUrl={firstLevelFraction.fraction_icon.image_url} />
              <div className="flex-1">
                <p className="text-primary text-lg font-bold flex-1">{firstLevelFraction.name}</p>
                {errors.fractions?.[firstLevelFractionIndex]?.root && (
                  <p className="text-error text-sm flex-1">
                    {errors.fractions?.[firstLevelFractionIndex]?.root?.message}
                  </p>
                )}
              </div>
              <UpdateFractionDialog
                fraction={firstLevelFraction}
                onUpdate={(data) =>
                  handleUpdateFraction(firstLevelFraction.code, {
                    name: data.name!,
                    description: data.description!,
                    fraction_icon_id: data.fraction_icon_id!,
                    fraction_icon_image_url: data.fraction_icon_image_url!,
                    is_active: data.is_active!,
                  })
                }
              />
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button
                    type="button"
                    variant="text"
                    color="dark-blue"
                    size="iconXSmall"
                    leadingIcon={<Delete className="fill-primary" />}
                  />
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Delete fraction?</AlertDialogTitle>
                    <AlertDialogDescription>
                      By clicking on ”confirm” you are deleting this fraction and all content within it.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Back</AlertDialogCancel>
                    <AlertDialogAction onClick={() => handleRemoveFraction(firstLevelFraction.code)}>
                      Delete
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
            {reportSetMode === "ON_PLATAFORM" && firstLevelFraction.has_second_level && (
              <div className="px-4 py-6 bg-surface-02 rounded-sm">
                <div className="space-y-6">
                  <div className="flex items-center gap-4 justify-between">
                    <div className="flex items-center gap-4">
                      <CornerDownRight className="stroke-tonal-dark-cream-80" />
                      <p className="text-tonal-dark-cream-40">Sub-fractions (level 02)</p>
                      <Button
                        type="button"
                        variant="text"
                        color="gray"
                        size="iconSmall"
                        onClick={() =>
                          handleUpdateFraction(firstLevelFraction.code, {
                            children_visible: !firstLevelFraction.children_visible,
                          })
                        }
                        leadingIcon={
                          firstLevelFraction.children_visible ? (
                            <Visibility className=" fill-tonal-dark-cream-50" />
                          ) : (
                            <VisibilityOff className=" fill-tonal-dark-cream-50" />
                          )
                        }
                      />
                      <Button
                        type="button"
                        variant="text"
                        color="light-blue"
                        size="small"
                        onClick={() =>
                          handleAddFraction({
                            name: "",
                            description: "",
                            fraction_icon_id: firstLevelFraction.fraction_icon_id,
                            fraction_icon_image_url: firstLevelFraction.fraction_icon.image_url,
                            is_active: true,
                            parent_code: firstLevelFraction.code,
                          })
                        }
                      >
                        Add level 02
                      </Button>
                    </div>
                    <div className="flex items-center gap-2">
                      {!!errors.fractions?.[firstLevelFractionIndex]?.children?.length && (
                        <Error className="w-6 h-6 fill-error" />
                      )}
                      {!!firstLevelFraction.children.length && (
                        <p className="flex items-center gap-1 text-tonal-dark-cream-40 text-sm">
                          <span>{firstLevelFraction.children.length}</span>
                          {firstLevelFraction.children.length > 1 ? "items" : "item"}
                        </p>
                      )}
                    </div>
                  </div>
                  {firstLevelFraction.children_visible &&
                    firstLevelFraction.children.map((secondLevelFraction, secondLevelFractionIndex) => (
                      <div key={secondLevelFractionIndex} className="space-y-6">
                        <div className="pl-3 lg:pl-8 space-y-6">
                          <div className="grid grid-cols-1 lg:grid-cols-5 gap-3 lg:gap-6">
                            <div className="col-span-2">
                              <div className="flex items-end gap-2 text-primary text-sm">
                                <Button
                                  type="button"
                                  variant="text"
                                  color="dark-blue"
                                  size="iconXSmall"
                                  leadingIcon={<Delete className="fill-primary" />}
                                  onClick={() => handleRemoveFraction(secondLevelFraction.code)}
                                />
                                Name
                                <span className="text-tonal-dark-cream-40 italic">Sub-fractions (02)</span>
                              </div>
                              <Input
                                placeholder="Name"
                                {...register(
                                  `fractions.${firstLevelFractionIndex}.children.${secondLevelFractionIndex}.name`
                                )}
                                variant={
                                  errors.fractions?.[firstLevelFractionIndex]?.children?.[secondLevelFractionIndex]
                                    ?.name
                                    ? "error"
                                    : "default"
                                }
                                errorMessage={
                                  errors.fractions?.[firstLevelFractionIndex]?.children?.[secondLevelFractionIndex]
                                    ?.name?.message
                                }
                              />
                            </div>
                            <div className="col-span-3">
                              <Textarea
                                label="Description"
                                placeholder="Description"
                                rows={3}
                                {...register(
                                  `fractions.${firstLevelFractionIndex}.children.${secondLevelFractionIndex}.description`
                                )}
                                errorMessage={
                                  errors.fractions?.[firstLevelFractionIndex]?.children?.[secondLevelFractionIndex]
                                    ?.description?.message
                                }
                              />
                            </div>
                          </div>
                          {firstLevelFraction.has_third_level && (
                            <div className="space-y-5">
                              <div className="flex items-center justify-between gap-4">
                                <div className="flex items-center gap-4">
                                  <CornerDownRight className="stroke-tonal-dark-cream-80" />
                                  <p className="text-tonal-dark-cream-40">Sub-fractions (level 03)</p>
                                  <Button
                                    type="button"
                                    variant="text"
                                    color="gray"
                                    size="iconSmall"
                                    onClick={() =>
                                      handleUpdateFraction(secondLevelFraction.code, {
                                        children_visible: !secondLevelFraction.children_visible,
                                      })
                                    }
                                    leadingIcon={
                                      secondLevelFraction.children_visible ? (
                                        <Visibility className=" fill-tonal-dark-cream-50" />
                                      ) : (
                                        <VisibilityOff className=" fill-tonal-dark-cream-50" />
                                      )
                                    }
                                  />
                                  <Button
                                    type="button"
                                    variant="text"
                                    color="light-blue"
                                    size="small"
                                    onClick={() =>
                                      handleAddFraction({
                                        name: "",
                                        description: "",
                                        fraction_icon_id: secondLevelFraction.fraction_icon_id,
                                        fraction_icon_image_url: secondLevelFraction.fraction_icon.image_url,
                                        is_active: true,
                                        parent_code: secondLevelFraction.code,
                                      })
                                    }
                                  >
                                    Add level 03
                                  </Button>
                                </div>
                                <div className="flex items-center gap-2">
                                  {!!errors.fractions?.[firstLevelFractionIndex]?.children?.[secondLevelFractionIndex]
                                    ?.children?.length && <Error className="w-6 h-6 fill-error" />}
                                  {!!secondLevelFraction.children.length && (
                                    <p className="flex items-center gap-1 text-tonal-dark-cream-40 text-sm">
                                      <span>{secondLevelFraction.children.length}</span>
                                      {secondLevelFraction.children.length > 1 ? "items" : "item"}
                                    </p>
                                  )}
                                </div>
                              </div>
                              {secondLevelFraction.children_visible &&
                                secondLevelFraction.children.map((thirdLevelFraction, thirdLevelFractionIndex) => (
                                  <div key={thirdLevelFractionIndex} className="space-y-6">
                                    <div className="pl-3 lg:pl-8">
                                      <div className="grid grid-cols-1 lg:grid-cols-5 gap-3 lg:gap-6">
                                        <div className="col-span-2">
                                          <div className="flex items-end text-sm gap-2 text-primary">
                                            <Button
                                              type="button"
                                              variant="text"
                                              color="dark-blue"
                                              size="iconXSmall"
                                              leadingIcon={<Delete className="fill-primary" />}
                                              onClick={() => handleRemoveFraction(thirdLevelFraction.code)}
                                            />
                                            Name
                                            <span className="text-tonal-dark-cream-40 italic">
                                              Sub-fractions (level 03)
                                            </span>
                                          </div>
                                          <Input
                                            placeholder="Name"
                                            {...register(
                                              `fractions.${firstLevelFractionIndex}.children.${secondLevelFractionIndex}.children.${thirdLevelFractionIndex}.name`
                                            )}
                                            variant={
                                              errors.fractions?.[firstLevelFractionIndex]?.children?.[
                                                secondLevelFractionIndex
                                              ]?.children?.[thirdLevelFractionIndex]?.name
                                                ? "error"
                                                : "default"
                                            }
                                            errorMessage={
                                              errors.fractions?.[firstLevelFractionIndex]?.children?.[
                                                secondLevelFractionIndex
                                              ]?.children?.[thirdLevelFractionIndex]?.name?.message
                                            }
                                          />
                                        </div>
                                        <div className="col-span-3">
                                          <Textarea
                                            label="Description"
                                            placeholder="Description"
                                            rows={3}
                                            {...register(
                                              `fractions.${firstLevelFractionIndex}.children.${secondLevelFractionIndex}.children.${thirdLevelFractionIndex}.description`
                                            )}
                                            errorMessage={
                                              errors.fractions?.[firstLevelFractionIndex]?.children?.[
                                                secondLevelFractionIndex
                                              ]?.children?.[thirdLevelFractionIndex]?.description?.message
                                            }
                                          />
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                ))}
                            </div>
                          )}
                        </div>
                        <div className="h-[1px] w-full bg-tonal-dark-cream-80" />
                      </div>
                    ))}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
      <AddFractionDialog type="FRACTION" onAdd={(data) => handleAddFraction(data)} />
    </div>
  );
}
