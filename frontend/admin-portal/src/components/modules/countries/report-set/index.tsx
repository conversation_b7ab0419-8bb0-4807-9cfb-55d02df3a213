"use client";

import { ModuleContent } from "@/components/common/module-content";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { getServiceSetupReportSet } from "@/lib/api/service-setups";
import { useQuery } from "@tanstack/react-query";
import { Skeleton } from "@/components/ui/skeleton";
import { useServiceSetup, withServiceSetupProvider } from "@/hooks/use-service-setup";
import { ChevronLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { CountryContent } from "../service-setup/country-content";
import { PreviewCalculatorDialog } from "@/components/common/preview/preview-calculator-dialog";
import { PreviewReportTableDialog } from "@/components/common/preview/preview-report-table-dialog";
import { ReportSetExcelForm } from "./report-set-form/excel";
import { ReportSetFormProvider } from "./report-set-form/components/report-set-form-provider";
import { ReportSetPlataformForm } from "./report-set-form/plataform";

interface CountryServiceSetupReportSetProps {
  countryCode: string;
  reportSetId: number;
}

function CountryServiceSetupReportSet({ countryCode, reportSetId }: CountryServiceSetupReportSetProps) {
  const router = useRouter();
  const { country } = useServiceSetup();

  const { data: reportSet, isLoading } = useQuery({
    queryKey: ["service-setup-report-set", countryCode, reportSetId],
    queryFn: () => getServiceSetupReportSet(countryCode, reportSetId),
  });

  return (
    <>
      <CountryContent country={country} description="Edit this country's services" />
      <ModuleContent containerClassName="flex-1 bg-surface-03 pt-6">
        <div className="space-y-10">
          <Button
            variant="text"
            color="light-blue"
            size="medium"
            leadingIcon={<ChevronLeft className="size-5" />}
            onClick={() => router.back()}
          >
            Back
          </Button>
          <div className="flex flex-col lg:flex-row lg:items-end justify-between gap-4">
            <div className="space-y-2">
              <h3 className="text-primary text-xl">2. Fractions/Category Set</h3>
              <h2 className="text-primary text-3xl font-bold">
                Add set
                {!!reportSet && (
                  <span className="text-tonal-dark-cream-50 italic ml-2">({reportSet?.packaging_service.name})</span>
                )}
              </h2>
            </div>
            {!!reportSet && (
              <div className="flex items-center gap-1 text-primary">
                Preview:
                {reportSet.mode === "ON_PLATAFORM" && (
                  <>
                    <PreviewReportTableDialog countryCode={countryCode} reportSetId={reportSet.id}>
                      <Button variant="text" color="light-blue" size="small">
                        Report Table
                      </Button>
                    </PreviewReportTableDialog>
                    <span>|</span>
                  </>
                )}
                <PreviewCalculatorDialog countryCode={countryCode} reportSetId={reportSet.id}>
                  <Button variant="text" color="light-blue" size="small">
                    Calculator
                  </Button>
                </PreviewCalculatorDialog>
              </div>
            )}
          </div>
          {isLoading && (
            <div className="space-y-6">
              <div className="bg-background rounded-[20px] p-8 space-y-4">
                <div className="flex items-center justify-between gap-4">
                  <Skeleton className="h-10 flex-1" />
                  <Skeleton className="h-10 w-20" />
                </div>
                <div className="flex items-center justify-between gap-4">
                  <Skeleton className="h-10 flex-1" />
                  <Skeleton className="h-10 w-60" />
                </div>
              </div>
              <div className="bg-background rounded-[20px] p-8 space-y-4">
                <div className="flex items-center justify-between gap-4">
                  <Skeleton className="h-10 flex-1" />
                  <Skeleton className="h-10 w-20" />
                </div>
                <div className="flex items-center justify-between gap-4">
                  <Skeleton className="h-10 flex-1" />
                  <Skeleton className="h-10 w-60" />
                </div>
              </div>
              <div className="bg-background rounded-[20px] p-8 space-y-4">
                <div className="flex items-center justify-between gap-4">
                  <Skeleton className="h-10 flex-1" />
                  <Skeleton className="h-10 w-20" />
                </div>
                <div className="flex items-center justify-between gap-4">
                  <Skeleton className="h-10 flex-1" />
                  <Skeleton className="h-10 w-60" />
                </div>
              </div>
            </div>
          )}
          {!!reportSet && (
            <ReportSetFormProvider reportSet={reportSet}>
              {reportSet.mode === "ON_PLATAFORM" && (
                <ReportSetPlataformForm countryCode={countryCode} reportSet={reportSet} />
              )}
              {reportSet.mode === "BY_EXCEL" && <ReportSetExcelForm countryCode={countryCode} reportSet={reportSet} />}
            </ReportSetFormProvider>
          )}
        </div>
      </ModuleContent>
    </>
  );
}

export const CountryServiceSetupReportSetModule = withServiceSetupProvider(CountryServiceSetupReportSet);
