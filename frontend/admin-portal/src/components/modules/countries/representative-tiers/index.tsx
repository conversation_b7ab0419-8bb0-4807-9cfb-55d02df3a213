"use client";

import { ModuleContent } from "@/components/common/module-content";
import { Button } from "@interzero/oneepr-react-ui/Button";

import { getServiceSetupRepresentativeTiers } from "@/lib/api/service-setups";
import { useQuery } from "@tanstack/react-query";

import { useRouter } from "next/navigation";
import { ChevronLeft } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { RepresentativeTiersForm } from "./representative-tiers-form";
import { useServiceSetup, withServiceSetupProvider } from "@/hooks/use-service-setup";
import { CountryContent } from "../service-setup/country-content";

interface CountryServiceSetupRepresentativeTiersProps {
  countryCode: string;
}

export function CountryServiceSetupRepresentativeTiers({ countryCode }: CountryServiceSetupRepresentativeTiersProps) {
  const router = useRouter();

  const { country } = useServiceSetup();

  const { data: representativeTiers, isFetching } = useQuery({
    queryKey: ["service-setup-representative-tiers", countryCode],
    queryFn: () => getServiceSetupRepresentativeTiers(countryCode),
  });

  return (
    <>
      <CountryContent country={country} description="Edit this country's services" />
      <ModuleContent containerClassName="flex-1 bg-surface-03 pt-6">
        <div className="space-y-10">
          <Button
            variant="text"
            color="light-blue"
            size="medium"
            leadingIcon={<ChevronLeft className="size-5" />}
            onClick={() => router.back()}
          >
            Back
          </Button>
          <div className="space-y-2">
            <h3 className="text-primary text-xl">5. Third party costs</h3>
            <h2 className="text-primary text-3xl font-bold">Representative tiers</h2>
          </div>
          {isFetching && (
            <div className="space-y-10">
              <div className="bg-background rounded-[20px] p-8 space-y-4">
                <div className="flex items-center justify-between gap-4">
                  <Skeleton className="h-10 flex-1" />
                  <Skeleton className="h-10 w-20" />
                </div>
                <div className="flex items-center justify-between gap-4">
                  <Skeleton className="h-10 flex-1" />
                  <Skeleton className="h-10 w-60" />
                </div>
              </div>
              <div className="bg-background rounded-[20px] p-8 space-y-4">
                <div className="flex items-center justify-between gap-4">
                  <Skeleton className="h-10 flex-1" />
                  <Skeleton className="h-10 w-20" />
                </div>
                <div className="flex items-center justify-between gap-4">
                  <Skeleton className="h-10 flex-1" />
                  <Skeleton className="h-10 w-60" />
                </div>
              </div>
              <div className="bg-background rounded-[20px] p-8 space-y-4">
                <div className="flex items-center justify-between gap-4">
                  <Skeleton className="h-10 flex-1" />
                  <Skeleton className="h-10 w-20" />
                </div>
                <div className="flex items-center justify-between gap-4">
                  <Skeleton className="h-10 flex-1" />
                  <Skeleton className="h-10 w-60" />
                </div>
              </div>
            </div>
          )}
          {!isFetching && representativeTiers && (
            <RepresentativeTiersForm representativeTiers={representativeTiers || []} />
          )}
        </div>
      </ModuleContent>
    </>
  );
}

export const CountryServiceSetupRepresentativeTiersModule = withServiceSetupProvider(
  CountryServiceSetupRepresentativeTiers
);
