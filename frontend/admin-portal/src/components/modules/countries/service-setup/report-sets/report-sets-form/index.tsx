"use client";

import { PackagingService } from "@/types/service-setup/packaging-service";
import { ReportSet } from "@/types/service-setup/report-set";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { EditCircle, FileCopy } from "@interzero/oneepr-react-ui/Icon";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useEffect, useMemo } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { CriteriasButton } from "../../criterias-drawer/criterias-button";
import { AddReportSetDialog } from "./add-report-set-dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogTitle,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogTrigger,
  AlertDialogDescription,
  AlertDialogFooter,
} from "@/components/ui/alert-dialog";
import { useMutation } from "@tanstack/react-query";
import { enqueueSnackbar } from "notistack";
import { queryClient } from "@/lib/react-query";
import { duplicateReportSet } from "@/lib/api/report-sets";
import { useServiceSetup } from "@/hooks/use-service-setup";

interface ReportSetsFormProps {
  packagingServices: PackagingService[];
  reportSets: ReportSet[];
  onCloseStep: () => void;
}

const reportSetsFormSchema = z.object({
  packagingServices: z.array(
    z
      .object({
        id: z.number(),
        hasReportSetCriteria: z.boolean(),
        reportSets: z.array(
          z.object({
            id: z.number(),
          })
        ),
      })
      .superRefine((data, ctx) => {
        if (!data.reportSets.length) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "At least one report set is required",
            path: ["reportSets"],
          });
        }

        if (data.reportSets.length > 1 && !data.hasReportSetCriteria) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "When multiple report sets exist, at least one commitment criteria is required",
            path: ["reportSets"],
          });
        }
      })
  ),
});

export function ReportSetsForm({ packagingServices, reportSets, onCloseStep }: ReportSetsFormProps) {
  const { country } = useServiceSetup();

  const pathname = usePathname();
  const {
    handleSubmit,
    formState: { isSubmitting, errors },
    trigger,
  } = useForm({
    resolver: zodResolver(reportSetsFormSchema),
    defaultValues: {
      packagingServices: packagingServices.map((packagingService) => ({
        id: packagingService.id,
        hasReportSetCriteria: packagingService.has_report_set_criteria,
        reportSets: reportSets
          .filter((reportSet) => reportSet.packaging_service_id === packagingService.id)
          .map((reportSet) => ({
            id: reportSet.id,
          })),
      })),
    },
  });

  async function handleFormSubmit() {
    onCloseStep();
    queryClient.invalidateQueries({ queryKey: ["service-setup-status", country.code] });
  }

  const { mutate: duplicateReportSetById, isPending: isDuplicatingReportSet } = useMutation({
    mutationFn: (reportSetId: number) => duplicateReportSet(reportSetId),
  });

  async function handleDuplicateReportSet(reportSetId: number) {
    duplicateReportSetById(reportSetId, {
      onSuccess: () => {
        enqueueSnackbar("Report set duplicated successfully", { variant: "success" });
        queryClient.invalidateQueries({ queryKey: ["service-setup-report-sets", country.code] });
      },
      onError: () => {
        enqueueSnackbar("Failed to duplicate report set", { variant: "error" });
      },
    });
  }

  useEffect(() => {
    trigger();

    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const renderData = useMemo(() => {
    return packagingServices.map((packagingService) => {
      return {
        packagingService,
        reportSets: reportSets.filter((reportSet) => reportSet.packaging_service_id === packagingService.id),
      };
    });
  }, [packagingServices, reportSets]);

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="mt-6 space-y-6">
      {renderData.map(({ packagingService, reportSets }, packagingServiceIndex) => (
        <div key={packagingService.id} className="space-y-6">
          <div className="flex items-center justify-between">
            <p className="text-primary font-bold">EPR Compliance Packaging - {packagingService.name}</p>
          </div>
          <div className="pl-10 space-y-4">
            <p className="text-primary">Sets</p>
            <div className="space-y-1">
              {reportSets.map((reportSet) => (
                <div key={reportSet.id} className="flex items-center">
                  <div className="flex-1 font-bold text-tonal-dark-cream-10">{reportSet.name}</div>
                  <Link href={`${pathname}/report-set/${reportSet.id}`}>
                    <Button
                      type="button"
                      variant="text"
                      color="light-blue"
                      size="small"
                      leadingIcon={<EditCircle className="size-5" />}
                    >
                      Edit
                    </Button>
                  </Link>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <div>
                        <Button type="button" variant="text" color="light-blue" size="small" leadingIcon={<FileCopy />}>
                          Duplicate
                        </Button>
                      </div>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                        <AlertDialogDescription>This action cannot be undone.</AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          className="bg-primary text-white hover:bg-primary/90"
                          onClick={(e) => {
                            e.preventDefault();
                            handleDuplicateReportSet(reportSet.id);
                          }}
                          disabled={isDuplicatingReportSet}
                        >
                          {isDuplicatingReportSet ? "Duplicating..." : "Duplicate"}
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              ))}
            </div>
            <AddReportSetDialog packagingService={packagingService} />
            <CriteriasButton
              criteriaType="REPORT_SET"
              packagingServiceId={packagingService.id}
              hasCriteria={!!packagingService.has_report_set_criteria}
              criteriaText="Set Criteria"
              shouldOpenCriteriasDrawer={() => {
                if (reportSets.length < 2) {
                  enqueueSnackbar("Please add at least two report sets to add a criteria", { variant: "warning" });
                  return false;
                }

                return true;
              }}
            />
            {errors.packagingServices?.[packagingServiceIndex]?.reportSets && (
              <p className="text-error text-sm">
                {errors.packagingServices?.[packagingServiceIndex]?.reportSets?.message}
              </p>
            )}
          </div>
        </div>
      ))}
      <div className="space-y-6">
        <div className="flex items-center justify-end gap-10">
          <Button type="button" onClick={() => onCloseStep()} variant="text" color="dark-blue" size="medium">
            Cancel
          </Button>
          <Button
            type="submit"
            variant="filled"
            color={!!Object.keys(errors).length ? "red" : "yellow"}
            size="medium"
            className="w-60"
            disabled={isSubmitting}
          >
            {isSubmitting ? "Saving..." : "Save fraction sets"}
          </Button>
        </div>
      </div>
    </form>
  );
}
