import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useServiceSetup } from "@/hooks/use-service-setup";
import { createReportSet } from "@/lib/api/report-sets";
import { queryClient } from "@/lib/react-query";
import { PackagingService } from "@/types/service-setup/packaging-service";
import { CreateReportSet } from "@/types/service-setup/report-set";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Add } from "@interzero/oneepr-react-ui/Icon";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { enqueueSnackbar } from "notistack";
import { MouseEvent, useEffect, useState } from "react";
import { Controller, useForm, useWatch } from "react-hook-form";
import { z } from "zod";

interface AddReportSetDialogProps {
  packagingService: PackagingService;
}

const addReportSetFormSchema = z.object({
  mode: z.enum(["ON_PLATAFORM", "BY_EXCEL"]).default("ON_PLATAFORM"),
  type: z.enum(["FRACTIONS", "CATEGORIES"]).default("FRACTIONS"),
});

export type AddReportSetFormData = z.infer<typeof addReportSetFormSchema>;

export function AddReportSetDialog({ packagingService }: AddReportSetDialogProps) {
  const router = useRouter();
  const { country } = useServiceSetup();

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const { handleSubmit, reset, control } = useForm<AddReportSetFormData>({
    resolver: zodResolver(addReportSetFormSchema),
    defaultValues: {
      mode: "ON_PLATAFORM",
      type: "FRACTIONS",
    },
  });

  const { mutate, isPending: isCreatingReportSet } = useMutation({
    mutationFn: (data: CreateReportSet) => createReportSet(data),
  });

  useEffect(() => {
    reset({
      mode: "ON_PLATAFORM",
      type: "FRACTIONS",
    });

    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isDialogOpen]);

  async function handleFormSubmit(data: AddReportSetFormData) {
    const createData: CreateReportSet = {
      name: packagingService.name,
      packaging_service_id: packagingService.id,
      mode: data.mode,
      type: data.type,
      sheet_file_id: null,
      sheet_file_description: null,
    };

    mutate(createData, {
      onSuccess: (response) => {
        queryClient.invalidateQueries({ queryKey: ["service-setup-report-sets", country.code] });
        queryClient.invalidateQueries({ queryKey: ["service-setup-status", country.code] });

        enqueueSnackbar("Report set created successfully", { variant: "success" });

        router.push(`/en/countries/${country.code}/service-setup/report-set/${response.id}`);
      },
      onError: () => {
        enqueueSnackbar("Failed to create report set", { variant: "error" });
      },
    });
  }

  function handleClickSubmit(e: MouseEvent<HTMLButtonElement>) {
    e.preventDefault();
    handleSubmit(handleFormSubmit)();
  }

  function handleDialogOpenChange(open: boolean) {
    if (!open) reset();

    setIsDialogOpen(open);
  }

  const mode = useWatch({ control, name: "mode" });

  return (
    <Dialog open={isDialogOpen} onOpenChange={handleDialogOpenChange}>
      <DialogTrigger asChild>
        <Button type="button" variant="text" color="light-blue" size="small" leadingIcon={<Add />}>
          Add report set
        </Button>
      </DialogTrigger>
      <DialogContent className="bg-surface-03 py-8">
        <DialogHeader>
          <DialogTitle>Add a fraction/category set</DialogTitle>
          <DialogDescription>Select an option to create a set.</DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="mt-6 space-y-10">
          <div className="space-y-4">
            <div className="w-full space-y-4">
              <label className="text-primary text-base font-centra mb-2">Set mode</label>
              <Controller
                control={control}
                name="mode"
                render={({ field: { onChange, value } }) => (
                  <RadioGroup value={value} onValueChange={(newValue) => onChange(newValue)}>
                    <div className="flex items-center gap-6">
                      <label className="flex items-center gap-2 text-primary cursor-pointer">
                        <RadioGroupItem value="ON_PLATAFORM" />
                        On Plataform
                      </label>
                      <label className="flex items-center gap-2 text-primary cursor-pointer">
                        <RadioGroupItem value="BY_EXCEL" />
                        By Excel
                      </label>
                    </div>
                  </RadioGroup>
                )}
              />
            </div>
            <div className="h-[1px] w-full bg-tonal-dark-cream-80 rounded-full" />
            <div className="w-full space-y-4">
              <label className="text-primary text-base font-centra mb-2">Set type</label>
              <Controller
                control={control}
                name="type"
                disabled={mode === "BY_EXCEL"}
                render={({ field: { onChange, value } }) => (
                  <RadioGroup
                    value={value}
                    onValueChange={(newValue) => onChange(newValue)}
                    disabled={mode === "BY_EXCEL"}
                  >
                    <div className="flex items-center gap-6">
                      <label className="flex items-center gap-2 text-primary cursor-pointer">
                        <RadioGroupItem value="FRACTIONS" className="block" disabled={mode === "BY_EXCEL"} />
                        Per fractions
                      </label>
                      <label className="flex items-center gap-2 text-primary cursor-pointer">
                        <RadioGroupItem value="CATEGORIES" className="block" disabled={mode === "BY_EXCEL"} />
                        Per category
                      </label>
                    </div>
                  </RadioGroup>
                )}
              />
            </div>
          </div>
          <div className="flex items-center justify-end">
            <Button
              type="button"
              variant="filled"
              color="yellow"
              size="medium"
              disabled={isCreatingReportSet}
              onClick={handleClickSubmit}
            >
              {isCreatingReportSet ? "Saving..." : "Save Fractions"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
