import { Button } from "@interzero/oneepr-react-ui/Button";
import { CornerDownRight } from "lucide-react";

import { CriteriaType } from "@/types/service-setup/criteria";
import { MoveRight } from "lucide-react";
import { useServiceSetup } from "@/hooks/use-service-setup";
import { CheckCircle } from "@interzero/oneepr-react-ui/Icon";

interface CriteriasButtonProps {
  criteriaType: CriteriaType;
  packagingServiceId?: number;
  requiredInformationId?: number;
  hasCriteria: boolean;
  criteriaText: string;
  shouldOpenCriteriasDrawer?: () => boolean;
}

export function CriteriasButton({
  criteriaType,
  packagingServiceId,
  requiredInformationId,
  hasCriteria,
  criteriaText,
  shouldOpenCriteriasDrawer,
}: CriteriasButtonProps) {
  const { openCriteriasDrawer } = useServiceSetup();

  if (!hasCriteria) {
    return (
      <div className="flex items-center gap-4 text-primary">
        <CornerDownRight className="text-tonal-dark-cream-50" />
        Criteria:
        <Button
          type="button"
          variant="text"
          color="light-blue"
          size="small"
          trailingIcon={<MoveRight />}
          onClick={handleOpenCriteriasDrawer}
        >
          Add {criteriaText}
        </Button>
      </div>
    );
  }

  function handleOpenCriteriasDrawer() {
    if (shouldOpenCriteriasDrawer) {
      if (!shouldOpenCriteriasDrawer()) return;
    }

    openCriteriasDrawer({
      type: criteriaType,
      packagingServiceId,
      requiredInformationId,
    });
  }

  return (
    <div className="flex items-center gap-4 text-primary">
      <CornerDownRight className="text-tonal-dark-cream-50" />
      Criteria (Added):
      <CheckCircle className="size-5 fill-success transition-all duration-300" />
      <Button
        type="button"
        variant="text"
        color="dark-blue"
        size="small"
        trailingIcon={<MoveRight />}
        onClick={handleOpenCriteriasDrawer}
      >
        {criteriaText}
      </Button>
    </div>
  );
}
