"use client";

import { ModuleContent } from "@/components/common/module-content";
import { Button } from "@interzero/oneepr-react-ui/Button";

import { ChevronLeft } from "lucide-react";

import { useQueryFilter } from "@/hooks/use-query-filter";
import { useEffect } from "react";
import { CriteriasForm } from "./criterias-form";

import { CRITERIA_TYPES } from "./criterias-form";
import { CriteriaType } from "@/types/service-setup/criteria";
import { useServiceSetup } from "@/hooks/use-service-setup";

// TODO: ON SUBMIT CHECK IF ALL OPTION RESPONSES ARE DIFFERENT

export function CriteriasDrawer() {
  const { closeCriteriasDrawer } = useServiceSetup();

  const { paramValues } = useQueryFilter(["criterias", "type", "packaging_service_id", "required_information_id"]);

  const isCriteriasDrawerOpen = paramValues.criterias === "true";
  const currentCriteriaType = CRITERIA_TYPES.find((type) => type.value === paramValues.type) || null;

  useEffect(() => {
    if (currentCriteriaType?.value === "PACKAGING_SERVICE" && !paramValues.packaging_service_id) {
      return closeCriteriasDrawer();
    }

    if (currentCriteriaType?.value === "REQUIRED_INFORMATION" && !paramValues.required_information_id) {
      return closeCriteriasDrawer();
    }

    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const type = paramValues.type as CriteriaType | null;
  const packagingServiceId = Number(paramValues.packaging_service_id) || undefined;
  const requiredInformationId = Number(paramValues.required_information_id) || undefined;

  if (!isCriteriasDrawerOpen) return null;

  return (
    <div className="absolute top-0 left-0 w-full h-full bg-surface-03">
      <ModuleContent containerClassName="flex-1 bg-surface-03 pt-6">
        <div className="space-y-10">
          <Button
            variant="text"
            color="light-blue"
            size="medium"
            leadingIcon={<ChevronLeft className="size-5" />}
            onClick={() => closeCriteriasDrawer()}
          >
            Back
          </Button>
          {type && (
            <CriteriasForm
              type={type}
              packagingServiceId={packagingServiceId}
              requiredInformationId={requiredInformationId}
            />
          )}
        </div>
      </ModuleContent>
    </div>
  );
}
