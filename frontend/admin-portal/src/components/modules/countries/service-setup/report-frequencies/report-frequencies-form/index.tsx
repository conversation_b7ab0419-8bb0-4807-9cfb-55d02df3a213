"use client";

import { useServiceSetup } from "@/hooks/use-service-setup";
import {
  createReportSetFrequency,
  deleteReportSetFrequency,
  updateReportSetFrequency,
} from "@/lib/api/report-set-frequencies";
import { queryClient } from "@/lib/react-query";
import { PackagingService } from "@/types/service-setup/packaging-service";
import {
  AnnuallyFrequency,
  Frequency,
  MonthlyFrequency,
  QuarterlyFrequency,
  ReportSetFrequency,
} from "@/types/service-setup/report-set-frequency";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { zodResolver } from "@hookform/resolvers/zod";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";
import { ReportSetFrequenciesFormItem } from "./report-frequencies-form-item";

const reportSetFrequenciesFormSchema = z.object({
  packagingServices: z.array(
    z
      .object({
        id: z.number(),
        name: z.string(),
        hasReportFrequencyCriteria: z.boolean(),
        frequencies: z.object({
          ANNUALLY: z
            .object({
              id: z.coerce
                .number()
                .transform((value) => Number(value) || undefined)
                .optional(),
              enabled: z.boolean(),
              frequency: z
                .object({
                  deadline: z.object({
                    day: z.coerce.number().min(1).max(31).optional(),
                    month: z.string().default("JANUARY").optional(),
                  }),
                  open: z.object({
                    day: z.coerce.number().min(1).max(31).optional(),
                    month: z.string().default("JANUARY").optional(),
                  }),
                })
                .optional(),
            })
            .refine((data) => !data.enabled || (data.enabled && data.frequency), {
              message: "Frequency is required when enabled",
            })
            .superRefine((data, ctx) => {
              if (!data.enabled || !data.frequency) return;

              if (data.frequency.deadline.day === undefined) {
                ctx.addIssue({
                  code: z.ZodIssueCode.custom,
                  message: "Day is required for deadline when enabled",
                  path: ["frequency", "deadline", "day"],
                });
              }

              if (data.frequency.deadline.month === undefined) {
                ctx.addIssue({
                  code: z.ZodIssueCode.custom,
                  message: "Day is required for deadline when enabled",
                  path: ["frequency", "deadline", "day"],
                });
              }

              if (data.frequency.open.day === undefined) {
                ctx.addIssue({
                  code: z.ZodIssueCode.custom,
                  message: "Day is required for open when enabled",
                  path: ["frequency", "open", "day"],
                });
              }

              if (data.frequency.open.month === undefined) {
                ctx.addIssue({
                  code: z.ZodIssueCode.custom,
                  message: "Day is required for deadline when enabled",
                  path: ["frequency", "deadline", "day"],
                });
              }
            }),
          QUARTERLY: z
            .object({
              id: z.coerce
                .number()
                .transform((value) => Number(value) || undefined)
                .optional(),
              enabled: z.boolean(),
              frequency: z
                .object({
                  deadline: z.object({
                    option: z.string().default("FIRST").optional(),
                    weekDay: z.string().default("MONDAY").optional(),
                  }),
                  open: z.object({
                    option: z.string().default("FIRST").optional(),
                    weekDay: z.string().default("MONDAY").optional(),
                  }),
                })
                .optional(),
            })
            .refine((data) => !data.enabled || (data.enabled && data.frequency), {
              message: "Frequency is required when enabled",
            })
            .superRefine((data, ctx) => {
              if (!data.enabled || !data.frequency) return;

              if (data.frequency.deadline.option === undefined) {
                ctx.addIssue({
                  code: z.ZodIssueCode.custom,
                  message: "Option is required for deadline when enabled",
                  path: ["frequency", "deadline", "option"],
                });
              }

              if (data.frequency.deadline.weekDay === undefined) {
                ctx.addIssue({
                  code: z.ZodIssueCode.custom,
                  message: "Week day is required for deadline when enabled",
                  path: ["frequency", "deadline", "weekDay"],
                });
              }

              if (data.frequency.open.option === undefined) {
                ctx.addIssue({
                  code: z.ZodIssueCode.custom,
                  message: "Option is required for open when enabled",
                  path: ["frequency", "open", "option"],
                });
              }

              if (data.frequency.open.weekDay === undefined) {
                ctx.addIssue({
                  code: z.ZodIssueCode.custom,
                  message: "Week day is required for open when enabled",
                  path: ["frequency", "open", "weekDay"],
                });
              }
            }),
          MONTHLY: z
            .object({
              id: z.coerce
                .number()
                .transform((value) => Number(value) || undefined)
                .optional(),
              enabled: z.boolean(),
              frequency: z
                .object({
                  deadline: z.object({
                    day: z.coerce.number().min(1).max(31).optional(),
                  }),
                  open: z.object({
                    day: z.coerce.number().min(1).max(31).optional(),
                  }),
                })
                .optional(),
            })
            .refine((data) => !data.enabled || (data.enabled && data.frequency), {
              message: "Frequency is required when enabled",
            })
            .superRefine((data, ctx) => {
              if (!data.enabled || !data.frequency) return;

              if (data.frequency.deadline.day === undefined) {
                ctx.addIssue({
                  code: z.ZodIssueCode.custom,
                  message: "Day is required for deadline when enabled",
                  path: ["frequency", "deadline", "day"],
                });
              }
              if (data.frequency.open.day === undefined) {
                ctx.addIssue({
                  code: z.ZodIssueCode.custom,
                  message: "Day is required for open when enabled",
                  path: ["frequency", "open", "day"],
                });
              }
            }),
        }),
      })
      .superRefine((data, ctx) => {
        const enabledFrequencies = Object.values(data.frequencies).filter((frequency) => frequency.enabled);

        if (!enabledFrequencies.length) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "At least one frequency is required",
            path: ["frequencies"],
          });
        }
      })
  ),
});

export type ReportSetFrequenciesFormData = z.infer<typeof reportSetFrequenciesFormSchema>;

type FrequencyByRhythm<T extends "ANNUALLY" | "QUARTERLY" | "MONTHLY"> = T extends "ANNUALLY"
  ? AnnuallyFrequency
  : T extends "QUARTERLY"
    ? QuarterlyFrequency
    : T extends "MONTHLY"
      ? MonthlyFrequency
      : never;

function getReportSetFrequency<R extends "ANNUALLY" | "QUARTERLY" | "MONTHLY">(
  rhythm: R,
  packagingServiceId: number,
  frequencies: ReportSetFrequency[]
): {
  enabled: boolean;
  id: number | undefined;
  frequency: FrequencyByRhythm<R>;
} {
  const reportSetFrequency = frequencies.find(
    (reportSetFrequency) =>
      reportSetFrequency.packaging_service_id === packagingServiceId && reportSetFrequency.rhythm === rhythm
  );

  if (!reportSetFrequency) {
    const defaultFrequency = (() => {
      switch (rhythm) {
        case "ANNUALLY":
          return {
            deadline: {
              day: undefined,
              month: "JANUARY",
            },
            open: {
              day: undefined,
              month: "JANUARY",
            },
          };
        case "QUARTERLY":
          return {
            deadline: {
              option: "FIRST",
              weekDay: "MONDAY",
            },
            open: {
              option: "FIRST",
              weekDay: "MONDAY",
            },
          };
        case "MONTHLY":
          return {
            deadline: {
              day: undefined,
            },
            open: {
              day: undefined,
            },
          };
      }
    })() as FrequencyByRhythm<R>;

    return {
      enabled: false,
      id: undefined,
      frequency: defaultFrequency,
    };
  }

  return {
    enabled: true,
    id: reportSetFrequency.id,
    frequency: reportSetFrequency.frequency as FrequencyByRhythm<R>,
  };
}

interface ReportSetFrequenciesFormProps {
  packagingServices: PackagingService[];
  reportFrequencies: ReportSetFrequency[];
  onCloseStep: () => void;
}

export function ReportSetFrequenciesForm({
  packagingServices,
  reportFrequencies,
  onCloseStep,
}: ReportSetFrequenciesFormProps) {
  const { country } = useServiceSetup();

  const methods = useForm<ReportSetFrequenciesFormData>({
    resolver: zodResolver(reportSetFrequenciesFormSchema),
    defaultValues: {
      packagingServices: packagingServices.map((packagingService) => ({
        id: packagingService.id,
        name: packagingService.name,
        hasReportFrequencyCriteria: packagingService.has_report_frequency_criteria,
        frequencies: {
          ANNUALLY: getReportSetFrequency("ANNUALLY", packagingService.id, reportFrequencies),
          QUARTERLY: getReportSetFrequency("QUARTERLY", packagingService.id, reportFrequencies),
          MONTHLY: getReportSetFrequency("MONTHLY", packagingService.id, reportFrequencies),
        },
      })),
    },
  });

  const {
    handleSubmit,
    setValue,
    formState: { isSubmitting, errors },
  } = methods;

  async function handleFormSubmit(data: ReportSetFrequenciesFormData) {
    try {
      if (!data.packagingServices.length) return;

      const promises = data.packagingServices.reduce(
        (acc, packagingService) => {
          for (const [rhythm, reportSetFrequency] of Object.entries(packagingService.frequencies)) {
            if (reportSetFrequency.enabled) {
              if (!reportSetFrequency.id) {
                acc.push(
                  createReportSetFrequency({
                    rhythm: rhythm as "ANNUALLY" | "QUARTERLY" | "MONTHLY",
                    packaging_service_id: packagingService.id,
                    frequency: (reportSetFrequency.frequency as Frequency) || null,
                  })
                );
                continue;
              }

              acc.push(
                updateReportSetFrequency(reportSetFrequency.id, {
                  frequency: (reportSetFrequency.frequency as Frequency) || null,
                })
              );
              continue;
            }

            if (reportSetFrequency.id) {
              acc.push(deleteReportSetFrequency(reportSetFrequency.id));
              continue;
            }
          }

          return acc;
        },
        [] as Promise<ReportSetFrequency | void>[]
      );

      const responses = await Promise.allSettled(promises);

      responses.forEach((response, index) => {
        if (response.status !== "fulfilled") return;

        if (!response.value) return;

        setValue(`packagingServices.${index}.frequencies.${response.value.rhythm}.id`, response.value.id);
      });

      queryClient.invalidateQueries({ queryKey: ["service-setup-report-frequencies", country.code] });
      queryClient.invalidateQueries({ queryKey: ["service-setup-status", country.code] });

      onCloseStep();
    } catch {}
  }

  return (
    <FormProvider {...methods}>
      <form onSubmit={handleSubmit(handleFormSubmit)} className="mt-6 space-y-6">
        {packagingServices.map((packagingService, packagingServiceIndex) => (
          <ReportSetFrequenciesFormItem
            key={packagingService.id}
            packagingService={packagingService}
            packagingServiceIndex={packagingServiceIndex}
          />
        ))}
        <div className="space-y-6">
          <div className="flex items-center justify-end gap-10">
            <Button type="button" onClick={() => onCloseStep()} variant="text" color="dark-blue" size="medium">
              Cancel
            </Button>
            <Button
              type="submit"
              variant="filled"
              color={!!Object.keys(errors).length ? "red" : "dark-blue"}
              size="medium"
              className="w-60"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Saving..." : "Save"}
            </Button>
          </div>
        </div>
      </form>
    </FormProvider>
  );
}
