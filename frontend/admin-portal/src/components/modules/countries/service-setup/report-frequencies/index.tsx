"use client";

import { CheckCircle } from "@interzero/oneepr-react-ui/Icon";

import { ReportSetFrequenciesForm } from "./report-frequencies-form";
import { useQuery } from "@tanstack/react-query";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { useServiceSetup } from "@/hooks/use-service-setup";
import { getServiceSetupPackagingServices, getServiceSetupReportFrequencies } from "@/lib/api/service-setups";
import { Skeleton } from "@/components/ui/skeleton";
import { PackagingService } from "@/types/service-setup/packaging-service";
import { ReportSetFrequency } from "@/types/service-setup/report-set-frequency";
import { AlertCircle } from "lucide-react";

interface ServiceSetupReportFrequenciesProps {}

export function isReportFrequenciesComplete(
  packagingServices: PackagingService[],
  reportFrequencies: ReportSetFrequency[]
) {
  try {
    if (!packagingServices?.length) throw new Error();

    if (!reportFrequencies?.length) throw new Error();

    packagingServices.forEach((packagingService) => {
      const packagingServiceReportFrequencies = reportFrequencies?.filter(
        (reportFrequency) => reportFrequency.packaging_service_id === packagingService.id
      );

      if (!packagingServiceReportFrequencies?.length) throw new Error();

      if (packagingServiceReportFrequencies?.length === 1) return;

      if (!packagingService.has_report_frequency_criteria) throw new Error();

      return;
    });

    return true;
  } catch {
    return false;
  }
}

export function ServiceSetupReportFrequencies({}: ServiceSetupReportFrequenciesProps) {
  const { paramValues, changeParam, deleteAllParams } = useQueryFilter(["step"]);

  const isSelected = paramValues.step === "report-frequencies";

  function handleOpenStep() {
    changeParam("step", "report-frequencies");
  }

  function handleCloseStep() {
    deleteAllParams();
  }

  const { country } = useServiceSetup();

  const { data: reportFrequencies, isFetching } = useQuery({
    queryKey: ["service-setup-report-frequencies", country.code],
    queryFn: () => getServiceSetupReportFrequencies(country.code),
  });

  const { data: packagingServices } = useQuery({
    queryKey: ["service-setup-packaging-services", country.code],
    queryFn: () => getServiceSetupPackagingServices(country.code),
  });

  if (isFetching) {
    return (
      <div className="bg-background rounded-[20px] p-8 space-y-2" id="report-frequencies-step">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-full" />
      </div>
    );
  }

  const isComplete = isReportFrequenciesComplete(packagingServices || [], reportFrequencies || []);
  return (
    <div
      data-selected={isSelected}
      data-complete={isComplete}
      className="group bg-background rounded-[20px] p-8 cursor-pointer data-[selected=true]:cursor-default"
      onClick={() => !isSelected && handleOpenStep()}
      id="report-frequencies-step"
    >
      <div className="flex items-center justify-between">
        <h3 className="text-primary text-2xl font-bold">4. Reporting Frequency</h3>
        {!isSelected && isComplete && <CheckCircle className="size-6 fill-success transition-all duration-300" />}
        {!isComplete && <AlertCircle className="size-6 fill-error transition-all duration-300" />}
      </div>
      {isSelected && !isComplete && (
        <p className="text-error text-sm">
          There are packaging services without at least one report frequency or with more than one but without a
          commitment criteria
        </p>
      )}
      {isSelected && (
        <ReportSetFrequenciesForm
          packagingServices={packagingServices || []}
          reportFrequencies={reportFrequencies || []}
          onCloseStep={handleCloseStep}
        />
      )}
    </div>
  );
}
