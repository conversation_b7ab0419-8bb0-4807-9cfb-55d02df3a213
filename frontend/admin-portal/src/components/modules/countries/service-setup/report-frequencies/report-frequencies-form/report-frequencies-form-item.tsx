"use client";

import { CheckboxInput } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { PackagingService } from "@/types/service-setup/packaging-service";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { Error } from "@interzero/oneepr-react-ui/Icon";
import { Controller, useFormContext, useWatch, UseFormRegister, Control, FieldErrors } from "react-hook-form";
import { ReportSetFrequenciesFormData } from ".";
import { CriteriasButton } from "../../criterias-drawer/criterias-button";
import { enqueueSnackbar } from "notistack";
import { memo, useMemo } from "react";

const MONTHS = [
  { label: "January", value: "JANUARY" },
  { label: "February", value: "FEBRUARY" },
  { label: "March", value: "MARCH" },
  { label: "April", value: "APRIL" },
  { label: "May", value: "MAY" },
  { label: "June", value: "JUNE" },
  { label: "July", value: "JULY" },
  { label: "August", value: "AUGUST" },
  { label: "September", value: "SEPTEMBER" },
  { label: "October", value: "OCTOBER" },
  { label: "November", value: "NOVEMBER" },
  { label: "December", value: "DECEMBER" },
];

const WEEKDAYS = [
  { label: "Monday", value: "MONDAY" },
  { label: "Tuesday", value: "TUESDAY" },
  { label: "Wednesday", value: "WEDNESDAY" },
  { label: "Thursday", value: "THURSDAY" },
  { label: "Friday", value: "FRIDAY" },
  { label: "Saturday", value: "SATURDAY" },
  { label: "Sunday", value: "SUNDAY" },
];

const QUARTERLY_OPTIONS = [
  { label: "First", value: "FIRST" },
  { label: "Last", value: "LAST" },
];

interface ReportSetFrequenciesFormItemProps {
  packagingServiceIndex: number;
  packagingService: PackagingService;
}

export function ReportSetFrequenciesFormItem({
  packagingServiceIndex,
  packagingService,
}: ReportSetFrequenciesFormItemProps) {
  const {
    register,
    control,
    formState: { errors },
  } = useFormContext<ReportSetFrequenciesFormData>();

  const frequencies = useWatch({
    control,
    name: `packagingServices.${packagingServiceIndex}.frequencies`,
  });
  const activeFrequenciesList = useMemo(() => {
    return !frequencies ? [] : Object.values(frequencies).filter((frequency) => frequency.enabled);
  }, [frequencies]);

  if (!frequencies) {
    return null;
  }

  return (
    <div className="space-y-6">
      <p className="text-primary font-bold">EPR Compliance PACK - {packagingService.name}</p>
      <div className="pl-10 space-y-4">
        <p className="text-primary">Report Rhythm</p>
        <div className="space-y-4">
          <ReportRhythmAnnually
            packagingServiceIndex={packagingServiceIndex}
            register={register}
            control={control}
            errors={errors}
            data={frequencies.ANNUALLY}
          />
          <ReportRhythmQuarterly
            packagingServiceIndex={packagingServiceIndex}
            register={register}
            control={control}
            errors={errors}
            data={frequencies.QUARTERLY}
          />
          <ReportRhythmMonthly
            packagingServiceIndex={packagingServiceIndex}
            register={register}
            control={control}
            errors={errors}
            data={frequencies.MONTHLY}
          />
        </div>
      </div>

      <CriteriasButton
        criteriaType="REPORT_FREQUENCY"
        packagingServiceId={packagingService.id}
        hasCriteria={!!packagingService.has_report_frequency_criteria}
        criteriaText="Report Rhythm Criteria"
        shouldOpenCriteriasDrawer={() => {
          const isMissingId = activeFrequenciesList.some((frequency) => !frequency.id);

          if (isMissingId) {
            enqueueSnackbar("Please save the report frequencies first and add a criteria later", {
              variant: "warning",
            });
            return false;
          }

          if (activeFrequenciesList.length < 2) {
            enqueueSnackbar("Please enable at least two frequencies to add a criteria", { variant: "warning" });
            return false;
          }

          return true;
        }}
      />
      {activeFrequenciesList.length > 1 && !packagingService.has_report_frequency_criteria ? (
        <p className="text-error text-sm flex flex-row items-center gap-2" role="alert" aria-live="polite">
          <Error className="fill-error size-4" />
          {"When multiple reporting rhythms exist, at least one criteria is required"}
        </p>
      ) : null}
      {activeFrequenciesList.length === 1 && !packagingService.has_report_frequency_criteria ? (
        <p
          className="text-tonal-dark-cream-40 italic text-sm flex flex-row items-center gap-2"
          role="alert"
          aria-live="polite"
        >
          <Error className="fill-tonal-dark-cream-40 size-4" />
          {"If a criteria is not added then the setup will be applied to all customers."}
        </p>
      ) : null}
      {errors.packagingServices?.[packagingServiceIndex]?.frequencies?.root && (
        <p className="text-error text-sm" role="alert" aria-live="polite">
          {errors.packagingServices?.[packagingServiceIndex]?.frequencies?.root?.message}
        </p>
      )}
    </div>
  );
}

interface ReportRhythmProps<T extends keyof ReportSetFrequenciesFormData["packagingServices"][number]["frequencies"]> {
  packagingServiceIndex: number;
  register: UseFormRegister<ReportSetFrequenciesFormData>;
  control: Control<ReportSetFrequenciesFormData>;
  errors: FieldErrors<ReportSetFrequenciesFormData>;
  data: ReportSetFrequenciesFormData["packagingServices"][number]["frequencies"][T];
}

const ReportRhythmAnnually = memo(function ReportRhythmAnnually(props: ReportRhythmProps<"ANNUALLY">) {
  const { control, register, errors, data, packagingServiceIndex } = props;
  return (
    <div className="space-y-3">
      <CheckboxInput
        label="Annually"
        {...register(`packagingServices.${packagingServiceIndex}.frequencies.ANNUALLY.enabled`)}
      />
      {data.enabled && (
        <div className="pl-8 space-y-3">
          <div className="text-tonal-dark-cream-40">Reporting available from</div>
          <div className="flex items-center gap-2 text-primary text-normal">
            Day
            <div className="w-24">
              <Input
                type="number"
                placeholder="Day"
                min={1}
                max={31}
                {...register(`packagingServices.${packagingServiceIndex}.frequencies.ANNUALLY.frequency.open.day`)}
                variant={
                  errors.packagingServices?.[packagingServiceIndex]?.frequencies?.ANNUALLY?.frequency?.open?.day
                    ? "error"
                    : "default"
                }
              />
            </div>
            of every
            <div className="w-40">
              <div className="space-y-2">
                <label />
                <Controller
                  control={control}
                  name={`packagingServices.${packagingServiceIndex}.frequencies.ANNUALLY.frequency.open.month`}
                  render={({ field: { value, onChange } }) => (
                    <Select defaultValue={MONTHS[0].value} value={value} onValueChange={onChange}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {MONTHS.map((month) => (
                          <SelectItem key={month.value} value={month.value}>
                            {month.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
              </div>
            </div>
          </div>
          <div className="text-tonal-dark-cream-40">Reporting ends on</div>
          <div className="flex items-center gap-2 text-primary text-normal">
            Day
            <div className="w-24">
              <Input
                type="number"
                placeholder="Day"
                min={1}
                max={31}
                {...register(`packagingServices.${packagingServiceIndex}.frequencies.ANNUALLY.frequency.deadline.day`)}
                variant={
                  errors.packagingServices?.[packagingServiceIndex]?.frequencies?.ANNUALLY?.frequency?.deadline?.day
                    ? "error"
                    : "default"
                }
              />
            </div>
            of every
            <div className="w-40">
              <div className="space-y-2">
                <label />
                <Controller
                  control={control}
                  name={`packagingServices.${packagingServiceIndex}.frequencies.ANNUALLY.frequency.deadline.month`}
                  render={({ field: { value, onChange } }) => (
                    <Select defaultValue={MONTHS[0].value} value={value} onValueChange={onChange}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {MONTHS.map((month) => (
                          <SelectItem key={month.value} value={month.value}>
                            {month.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
});

const ReportRhythmQuarterly = memo(function ReportRhythmQuarterly(props: ReportRhythmProps<"QUARTERLY">) {
  const { control, register, data, packagingServiceIndex } = props;
  return (
    <div className="space-y-3">
      <CheckboxInput
        label="Quarterly"
        {...register(`packagingServices.${packagingServiceIndex}.frequencies.QUARTERLY.enabled`)}
      />
      {data.enabled && (
        <div className="pl-8 space-y-3">
          <div className="text-tonal-dark-cream-40">Reporting available from</div>
          <div className="flex items-center gap-2 text-primary text-normal">
            <div className="w-40">
              <div className="space-y-2">
                <label htmlFor=""></label>
                <Controller
                  control={control}
                  name={`packagingServices.${packagingServiceIndex}.frequencies.QUARTERLY.frequency.open.option`}
                  render={({ field: { value, onChange } }) => (
                    <Select defaultValue={QUARTERLY_OPTIONS[0].value} value={value} onValueChange={onChange}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {QUARTERLY_OPTIONS.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
              </div>
            </div>
            <div className="w-40">
              <div className="space-y-2">
                <label htmlFor=""></label>
                <Controller
                  control={control}
                  name={`packagingServices.${packagingServiceIndex}.frequencies.QUARTERLY.frequency.open.weekDay`}
                  render={({ field: { value, onChange } }) => (
                    <Select defaultValue={WEEKDAYS[0].value} value={value} onValueChange={onChange}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {WEEKDAYS.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
              </div>
            </div>
            of every quarter
          </div>
          <div className="text-tonal-dark-cream-40">Reporting ends on</div>
          <div className="flex items-center gap-2 text-primary text-normal">
            <div className="w-40">
              <div className="space-y-2">
                <label htmlFor=""></label>
                <Controller
                  control={control}
                  name={`packagingServices.${packagingServiceIndex}.frequencies.QUARTERLY.frequency.deadline.option`}
                  render={({ field: { value, onChange } }) => (
                    <Select defaultValue={QUARTERLY_OPTIONS[0].value} value={value} onValueChange={onChange}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {QUARTERLY_OPTIONS.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
              </div>
            </div>
            <div className="w-40">
              <div className="space-y-2">
                <label htmlFor=""></label>
                <Controller
                  control={control}
                  name={`packagingServices.${packagingServiceIndex}.frequencies.QUARTERLY.frequency.deadline.weekDay`}
                  render={({ field: { value, onChange } }) => (
                    <Select defaultValue={WEEKDAYS[0].value} value={value} onValueChange={onChange}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {WEEKDAYS.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
              </div>
            </div>
            of every quarter
          </div>
        </div>
      )}
    </div>
  );
});

const ReportRhythmMonthly = memo(function ReportRhythmMonthly(props: ReportRhythmProps<"MONTHLY">) {
  const { register, data, errors, packagingServiceIndex } = props;
  return (
    <div className="space-y-3">
      <CheckboxInput
        label="Monthly"
        {...register(`packagingServices.${packagingServiceIndex}.frequencies.MONTHLY.enabled`)}
      />
      {data.enabled && (
        <div className="pl-8 space-y-3">
          <div className="text-tonal-dark-cream-40">Reporting available from</div>
          <div className="flex items-center gap-2 text-primary text-normal">
            Day
            <div className="w-24">
              <Input
                type="number"
                placeholder="Day"
                min={1}
                max={31}
                {...register(`packagingServices.${packagingServiceIndex}.frequencies.MONTHLY.frequency.open.day`)}
                variant={
                  errors.packagingServices?.[packagingServiceIndex]?.frequencies?.MONTHLY?.frequency?.open?.day
                    ? "error"
                    : "default"
                }
              />
            </div>
            of every month
          </div>
          <div className="text-tonal-dark-cream-40">Reporting ends on</div>
          <div className="flex items-center gap-2 text-primary text-normal">
            Day
            <div className="w-24">
              <Input
                type="number"
                placeholder="Day"
                min={1}
                max={31}
                {...register(`packagingServices.${packagingServiceIndex}.frequencies.MONTHLY.frequency.deadline.day`)}
                variant={
                  errors.packagingServices?.[packagingServiceIndex]?.frequencies?.MONTHLY?.frequency?.deadline?.day
                    ? "error"
                    : "default"
                }
              />
            </div>
            of every month
          </div>
        </div>
      )}
    </div>
  );
});
