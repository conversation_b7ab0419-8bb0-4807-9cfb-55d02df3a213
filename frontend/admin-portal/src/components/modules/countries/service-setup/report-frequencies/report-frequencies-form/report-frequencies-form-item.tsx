"use client";

import { CheckboxInput } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { PackagingService } from "@/types/service-setup/packaging-service";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { Controller, useFormContext, useWatch } from "react-hook-form";
import { ReportSetFrequenciesFormData } from ".";
import { CriteriasButton } from "../../criterias-drawer/criterias-button";
import { enqueueSnackbar } from "notistack";

const MONTHS = [
  { label: "January", value: "JANUARY" },
  { label: "February", value: "FEBRUARY" },
  { label: "March", value: "MARCH" },
  { label: "April", value: "APRIL" },
  { label: "May", value: "MAY" },
  { label: "June", value: "JUNE" },
  { label: "July", value: "JULY" },
  { label: "August", value: "AUGUST" },
  { label: "September", value: "SEPTEMBER" },
  { label: "October", value: "OCTOBER" },
  { label: "November", value: "NOVEMBER" },
  { label: "December", value: "DECEMBER" },
];

const WEEKDAYS = [
  { label: "Monday", value: "MONDAY" },
  { label: "Tuesday", value: "TUESDAY" },
  { label: "Wednesday", value: "WEDNESDAY" },
  { label: "Thursday", value: "THURSDAY" },
  { label: "Friday", value: "FRIDAY" },
  { label: "Saturday", value: "SATURDAY" },
  { label: "Sunday", value: "SUNDAY" },
];

const QUARTERLY_OPTIONS = [
  { label: "First", value: "FIRST" },
  { label: "Last", value: "LAST" },
];

interface ReportSetFrequenciesFormItemProps {
  packagingServiceIndex: number;
  packagingService: PackagingService;
}

export function ReportSetFrequenciesFormItem({
  packagingServiceIndex,
  packagingService,
}: ReportSetFrequenciesFormItemProps) {
  const {
    register,
    control,
    formState: { errors },
  } = useFormContext<ReportSetFrequenciesFormData>();

  const frequencies = useWatch({
    control,
    name: `packagingServices.${packagingServiceIndex}.frequencies`,
  });

  return (
    <div className="space-y-6">
      <p className="text-primary font-bold">
        Package {packagingServiceIndex + 1}
        <span className="ml-2 text-tonal-dark-cream-30 font-normal italic">({packagingService.name})</span>
      </p>
      <div className="pl-10 space-y-4">
        <p className="text-primary">Report Rhythm</p>
        <div className="space-y-4">
          <div className="space-y-3">
            <CheckboxInput
              label="Annually"
              {...register(`packagingServices.${packagingServiceIndex}.frequencies.ANNUALLY.enabled`)}
            />
            {frequencies.ANNUALLY.enabled && (
              <div className="pl-8 space-y-3">
                <div className="text-tonal-dark-cream-40">Reporting Deadline</div>
                <div className="flex items-center gap-2 text-primary text-normal">
                  Day
                  <div className="w-24">
                    <Input
                      type="number"
                      placeholder="Day"
                      min={1}
                      max={31}
                      {...register(
                        `packagingServices.${packagingServiceIndex}.frequencies.ANNUALLY.frequency.deadline.day`
                      )}
                      variant={
                        errors.packagingServices?.[packagingServiceIndex]?.frequencies?.ANNUALLY?.frequency?.deadline
                          ?.day
                          ? "error"
                          : "default"
                      }
                    />
                  </div>
                  of every
                  <div className="w-40">
                    <div className="space-y-2">
                      <label />
                      <Controller
                        control={control}
                        name={`packagingServices.${packagingServiceIndex}.frequencies.ANNUALLY.frequency.deadline.month`}
                        render={({ field: { value, onChange } }) => (
                          <Select defaultValue={MONTHS[0].value} value={value} onValueChange={onChange}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {MONTHS.map((month) => (
                                <SelectItem key={month.value} value={month.value}>
                                  {month.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                      />
                    </div>
                  </div>
                </div>
                <div className="text-tonal-dark-cream-40">Reporting Open</div>
                <div className="flex items-center gap-2 text-primary text-normal">
                  Day
                  <div className="w-24">
                    <Input
                      type="number"
                      placeholder="Day"
                      min={1}
                      max={31}
                      {...register(
                        `packagingServices.${packagingServiceIndex}.frequencies.ANNUALLY.frequency.open.day`
                      )}
                      variant={
                        errors.packagingServices?.[packagingServiceIndex]?.frequencies?.ANNUALLY?.frequency?.open?.day
                          ? "error"
                          : "default"
                      }
                    />
                  </div>
                  of every
                  <div className="w-40">
                    <div className="space-y-2">
                      <label />
                      <Controller
                        control={control}
                        name={`packagingServices.${packagingServiceIndex}.frequencies.ANNUALLY.frequency.open.month`}
                        render={({ field: { value, onChange } }) => (
                          <Select defaultValue={MONTHS[0].value} value={value} onValueChange={onChange}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {MONTHS.map((month) => (
                                <SelectItem key={month.value} value={month.value}>
                                  {month.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
          <div className="space-y-3">
            <CheckboxInput
              label="Quarterly"
              {...register(`packagingServices.${packagingServiceIndex}.frequencies.QUARTERLY.enabled`)}
            />
            {frequencies.QUARTERLY.enabled && (
              <div className="pl-8 space-y-3">
                <div className="text-tonal-dark-cream-40">Reporting Deadline</div>
                <div className="flex items-center gap-2 text-primary text-normal">
                  <div className="w-40">
                    <div className="space-y-2">
                      <label htmlFor=""></label>
                      <Controller
                        control={control}
                        name={`packagingServices.${packagingServiceIndex}.frequencies.QUARTERLY.frequency.deadline.option`}
                        render={({ field: { value, onChange } }) => (
                          <Select defaultValue={QUARTERLY_OPTIONS[0].value} value={value} onValueChange={onChange}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {QUARTERLY_OPTIONS.map((option) => (
                                <SelectItem key={option.value} value={option.value}>
                                  {option.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                      />
                    </div>
                  </div>
                  <div className="w-40">
                    <div className="space-y-2">
                      <label htmlFor=""></label>
                      <Controller
                        control={control}
                        name={`packagingServices.${packagingServiceIndex}.frequencies.QUARTERLY.frequency.deadline.weekDay`}
                        render={({ field: { value, onChange } }) => (
                          <Select defaultValue={WEEKDAYS[0].value} value={value} onValueChange={onChange}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {WEEKDAYS.map((option) => (
                                <SelectItem key={option.value} value={option.value}>
                                  {option.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                      />
                    </div>
                  </div>
                  of every quarter
                </div>
                <div className="text-tonal-dark-cream-40">Reporting Open</div>
                <div className="flex items-center gap-2 text-primary text-normal">
                  <div className="w-40">
                    <div className="space-y-2">
                      <label htmlFor=""></label>
                      <Controller
                        control={control}
                        name={`packagingServices.${packagingServiceIndex}.frequencies.QUARTERLY.frequency.open.option`}
                        render={({ field: { value, onChange } }) => (
                          <Select defaultValue={QUARTERLY_OPTIONS[0].value} value={value} onValueChange={onChange}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {QUARTERLY_OPTIONS.map((option) => (
                                <SelectItem key={option.value} value={option.value}>
                                  {option.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                      />
                    </div>
                  </div>
                  <div className="w-40">
                    <div className="space-y-2">
                      <label htmlFor=""></label>
                      <Controller
                        control={control}
                        name={`packagingServices.${packagingServiceIndex}.frequencies.QUARTERLY.frequency.open.weekDay`}
                        render={({ field: { value, onChange } }) => (
                          <Select defaultValue={WEEKDAYS[0].value} value={value} onValueChange={onChange}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {WEEKDAYS.map((option) => (
                                <SelectItem key={option.value} value={option.value}>
                                  {option.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                      />
                    </div>
                  </div>
                  of every quarter
                </div>
              </div>
            )}
          </div>
          <div className="space-y-3">
            <CheckboxInput
              label="Monthly"
              {...register(`packagingServices.${packagingServiceIndex}.frequencies.MONTHLY.enabled`)}
            />
            {frequencies.MONTHLY.enabled && (
              <div className="pl-8 space-y-3">
                <div className="text-tonal-dark-cream-40">Reporting Deadline</div>
                <div className="flex items-center gap-2 text-primary text-normal">
                  Day
                  <div className="w-24">
                    <Input
                      type="number"
                      placeholder="Day"
                      min={1}
                      max={31}
                      {...register(
                        `packagingServices.${packagingServiceIndex}.frequencies.MONTHLY.frequency.deadline.day`
                      )}
                      variant={
                        errors.packagingServices?.[packagingServiceIndex]?.frequencies?.MONTHLY?.frequency?.deadline
                          ?.day
                          ? "error"
                          : "default"
                      }
                    />
                  </div>
                  of every month
                </div>
                <div className="text-tonal-dark-cream-40">Reporting Open</div>
                <div className="flex items-center gap-2 text-primary text-normal">
                  Day
                  <div className="w-24">
                    <Input
                      type="number"
                      placeholder="Day"
                      min={1}
                      max={31}
                      {...register(`packagingServices.${packagingServiceIndex}.frequencies.MONTHLY.frequency.open.day`)}
                      variant={
                        errors.packagingServices?.[packagingServiceIndex]?.frequencies?.MONTHLY?.frequency?.open?.day
                          ? "error"
                          : "default"
                      }
                    />
                  </div>
                  of every month
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <CriteriasButton
        criteriaType="REPORT_FREQUENCY"
        packagingServiceId={packagingService.id}
        hasCriteria={!!packagingService.has_report_frequency_criteria}
        criteriaText="Report Rhythm Criteria"
        shouldOpenCriteriasDrawer={() => {
          const frequencyList = Object.values(frequencies).filter((frequency) => frequency.enabled);

          const isMissingId = frequencyList.some((frequency) => !frequency.id);

          if (isMissingId) {
            enqueueSnackbar("Please save the report frequencies first and add a criteria later", {
              variant: "warning",
            });
            return false;
          }

          if (frequencyList.length < 2) {
            enqueueSnackbar("Please enable at least two frequencies to add a criteria", { variant: "warning" });
            return false;
          }

          return true;
        }}
      />
      {errors.packagingServices?.[packagingServiceIndex]?.frequencies?.root && (
        <p className="text-error text-sm">
          {errors.packagingServices?.[packagingServiceIndex]?.frequencies?.root?.message}
        </p>
      )}
    </div>
  );
}
