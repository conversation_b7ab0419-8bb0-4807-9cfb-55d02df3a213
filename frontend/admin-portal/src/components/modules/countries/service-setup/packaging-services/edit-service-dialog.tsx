"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Trigger } from "@/components/ui/dialog";
import { useServiceSetup } from "@/hooks/use-service-setup";
import { updatePackagingService } from "@/lib/api/packaging-services";
import { queryClient } from "@/lib/react-query";
import { SPECIAL_CHARS_REGEX } from "@/utils/regex";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { Textarea } from "@/components/ui/textarea";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { enqueueSnackbar } from "notistack";
import { ReactNode, useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const editServiceFormSchema = z.object({
  name: z
    .string()
    .min(1, "Name is required")
    .regex(SPECIAL_CHARS_REGEX, "Special characters are not allowed for this field"),
  description: z
    .string()
    .regex(SPECIAL_CHARS_REGEX, "Special characters are not allowed for this field")
    .optional()
    .or(z.literal("")),
});

export type EditServiceFormData = z.infer<typeof editServiceFormSchema>;

interface EditServiceDialogProps {
  children: ReactNode;
  service: {
    id: number;
    name: string;
    description: string;
  };
}

export function EditServiceDialog({ children, service }: EditServiceDialogProps) {
  const { country } = useServiceSetup();
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const {
    handleSubmit,
    register,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<EditServiceFormData>({
    resolver: zodResolver(editServiceFormSchema),
    defaultValues: {
      name: service.name,
      description: service.description || "",
    },
  });

  const { mutate: updateService, isPending } = useMutation({
    mutationFn: (data: { id: number; name: string; description: string; country_id: number }) =>
      updatePackagingService(data.id, data),
  });

  useEffect(() => {
    if (isDialogOpen) {
      reset({
        name: service.name,
        description: service.description || "",
      });
    }
  }, [isDialogOpen, service, reset]);

  async function handleFormSubmit(data: EditServiceFormData) {
    try {
      updateService(
        {
          id: service.id,
          name: data.name,
          description: data.description || "",
          country_id: country.id,
        },
        {
          onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["service-setup-packaging-services", country.code] });
            queryClient.invalidateQueries({ queryKey: ["service-setup-report-sets", country.code] });
            queryClient.invalidateQueries({ queryKey: ["service-setup-report-frequencies", country.code] });
            queryClient.invalidateQueries({ queryKey: ["service-setup-status", country.code] });
            enqueueSnackbar("Service updated successfully", { variant: "success" });
            setIsDialogOpen(false);
          },
          onError: () => {
            enqueueSnackbar("Failed to update service", { variant: "error" });
          },
        }
      );
    } catch {
      enqueueSnackbar("Failed to update service", { variant: "error" });
    }
  }

  function handleDialogOpenChange(open: boolean) {
    setIsDialogOpen(open);
  }

  return (
    <Dialog open={isDialogOpen} onOpenChange={handleDialogOpenChange}>
      <DialogTrigger asChild>
        <div onClick={(e) => e.stopPropagation()}>{children}</div>
      </DialogTrigger>
      <DialogContent className="py-8">
        <DialogHeader>
          <DialogTitle>Edit service type</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="mt-6 space-y-6">
          <div className="[&_input]:text-primary [&_input]:opacity-80">
            <Input
              label="Service type name *"
              placeholder="Enter service type name"
              {...register("name")}
              variant={errors.name ? "error" : "default"}
              errorMessage={errors.name?.message}
            />
          </div>
          <Textarea
            label="Description"
            placeholder="Enter description"
            rows={4}
            className="text-base text-primary/80 resize-none"
            {...register("description")}
            errorMessage={errors.description?.message}
          />
          <div className="flex items-center justify-end gap-4">
            <Button
              type="button"
              variant="outlined"
              color="dark-blue"
              size="medium"
              className="rounded-full"
              onClick={() => setIsDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="filled"
              color="yellow"
              size="medium"
              className="rounded-full"
              disabled={isSubmitting || isPending}
            >
              {isSubmitting || isPending ? "Saving..." : "Save"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
