"use client";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { useServiceSetup } from "@/hooks/use-service-setup";
import { createPackagingService, deletePackagingService, updatePackagingService } from "@/lib/api/packaging-services";
import { getServiceSetupPackagingServices } from "@/lib/api/service-setups";
import { queryClient } from "@/lib/react-query";
import { PackagingService } from "@/types/service-setup/packaging-service";
import { SPECIAL_CHARS_REGEX } from "@/utils/regex";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Add, CheckCircle } from "@interzero/oneepr-react-ui/Icon";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery } from "@tanstack/react-query";
import { enqueueSnackbar } from "notistack";
import { useEffect } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { z } from "zod";
import { CriteriasButton } from "../criterias-drawer/criterias-button";

interface ServiceSetupPackagingServicesProps {}

export function ServiceSetupPackagingServices({}: ServiceSetupPackagingServicesProps) {
  const { paramValues, changeParam, deleteAllParams } = useQueryFilter(["step"]);

  const isSelected = paramValues.step === "packaging-services";

  function handleOpenStep() {
    changeParam("step", "packaging-services");
  }

  function handleCloseStep() {
    deleteAllParams();
  }

  const { country } = useServiceSetup();

  const { data: packagingServices, isFetching } = useQuery({
    queryKey: ["service-setup-packaging-services", country.code],
    queryFn: () => getServiceSetupPackagingServices(country.code),
  });

  const isComplete = packagingServices && !!packagingServices.length;

  if (isFetching) {
    return (
      <div className="bg-background rounded-[20px] p-8 space-y-2" id="packaging-services-step">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-full" />
      </div>
    );
  }

  return (
    <div
      data-selected={isSelected}
      data-complete={isComplete}
      className="group bg-background rounded-[20px] p-8 cursor-pointer data-[selected=true]:cursor-default"
      onClick={() => !isSelected && handleOpenStep()}
      id="packaging-services-step"
    >
      <div className="flex items-center justify-between">
        <h3 className="text-primary text-2xl font-bold">1. Service Details and Obligation</h3>
        {!isSelected && isComplete && <CheckCircle className="size-6 fill-success transition-all duration-300" />}
      </div>
      {isSelected && (
        <PackagingServicesForm packagingServices={packagingServices || []} onCloseStep={handleCloseStep} />
      )}
    </div>
  );
}

const packagingServiceSchema = z.object({
  id: z.coerce
    .number()
    .transform((value) => Number(value) || undefined)
    .optional(),
  name: z
    .string()
    .min(1, "Name is required")
    .regex(SPECIAL_CHARS_REGEX, "Special characters are not allowed for this field"),
  description: z
    .string()
    .min(1, "Name is required")
    .regex(SPECIAL_CHARS_REGEX, "Special characters are not allowed for this field"),
  has_criteria: z.boolean().optional(),
});

const packagingServicesFormSchema = z.object({
  packagingServices: z.array(packagingServiceSchema),
});

type PackagingServicesFormData = z.infer<typeof packagingServicesFormSchema>;

interface PackagingServicesFormProps {
  packagingServices: PackagingService[];
  onCloseStep: () => void;
}

function PackagingServicesForm({ packagingServices, onCloseStep }: PackagingServicesFormProps) {
  const { country } = useServiceSetup();

  const {
    control,
    handleSubmit,
    register,
    formState: { errors, isSubmitting },
    setValue,
    reset,
    setError,
    clearErrors,
  } = useForm<PackagingServicesFormData>({
    resolver: zodResolver(packagingServicesFormSchema),
    defaultValues: {
      packagingServices,
    },
  });

  const { mutate: deleteService, isPending: isDeletingPackagingService } = useMutation({
    mutationFn: (packagingServiceId: number) => deletePackagingService(packagingServiceId),
  });

  useEffect(() => {
    reset({ packagingServices });

    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [packagingServices]);

  const { fields, append, remove } = useFieldArray({
    control,
    name: "packagingServices",
    keyName: "key",
  });

  async function handleFormSubmit(data: PackagingServicesFormData) {
    try {
      if (!data.packagingServices.length) {
        setError("packagingServices", { message: "At least one packaging service is required" });
        return;
      }

      const promises = data.packagingServices.map(async (packagingService) => {
        if (!packagingService.id) {
          return createPackagingService({ ...packagingService, country_id: country.id });
        }

        return updatePackagingService(packagingService.id, packagingService);
      });

      const responses = await Promise.allSettled(promises);

      responses.forEach((response, index) => {
        if (response.status !== "fulfilled") return;

        setValue(`packagingServices.${index}.id`, response.value.id);
      });

      queryClient.invalidateQueries({ queryKey: ["service-setup-packaging-services", country.code] });
      queryClient.invalidateQueries({ queryKey: ["service-setup-report-sets", country.code] });
      queryClient.invalidateQueries({ queryKey: ["service-setup-report-frequencies", country.code] });
      queryClient.invalidateQueries({ queryKey: ["service-setup-status", country.code] });

      onCloseStep();
    } catch {}
  }

  function handleAddPackagingService() {
    if (!packagingServices.length && errors.packagingServices) {
      clearErrors("packagingServices");
    }

    append({ name: "", description: "" });
  }

  async function handleDeletePackagingService(index: number) {
    if (fields.length === 1) {
      setError("packagingServices", { message: "At least one packaging service is required" });
      return;
    }

    const packagingService = fields[index];

    if (!packagingService.id) {
      remove(index);
      return;
    }

    deleteService(packagingService.id, {
      onSuccess: () => {
        remove(index);
        queryClient.invalidateQueries({ queryKey: ["service-setup-packaging-services", country.code] });
        queryClient.invalidateQueries({ queryKey: ["service-setup-status", country.code] });
        enqueueSnackbar("Packaging service deleted successfully", { variant: "success" });
      },
      onError: () => {
        enqueueSnackbar("Failed to delete packaging service", { variant: "error" });
      },
    });
  }

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="mt-6 space-y-6">
      {fields.map((field, index) => (
        <div key={field.key} className="space-y-6">
          <input type="hidden" className="hidden" {...register(`packagingServices.${index}.id`)} />
          <div className="flex items-center justify-between">
            <p className="text-primary">Package {index + 1}</p>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <button type="button" className="text-sm font-bold text-error hover:bg-error/30 rounded-full py-1 px-3">
                  Delete
                </button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Delete service?</AlertDialogTitle>
                  <AlertDialogDescription>
                    By clicking on ”confirm” you are deleting this package and all content within it.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel disabled={isDeletingPackagingService}>Back</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={(e) => {
                      e.preventDefault();
                      handleDeletePackagingService(index);
                    }}
                    disabled={isDeletingPackagingService}
                  >
                    {isDeletingPackagingService ? "Deleting..." : "Confirm"}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
          <div className="pl-10 w-full space-y-6">
            <Input
              label="Name of the Service *"
              placeholder="Name for the service"
              {...register(`packagingServices.${index}.name`)}
              variant={errors.packagingServices?.[index]?.name ? "error" : "default"}
              errorMessage={errors.packagingServices?.[index]?.name?.message}
            />
            <Input
              label="Description *"
              placeholder="What is this service?"
              {...register(`packagingServices.${index}.description`)}
              variant={errors.packagingServices?.[index]?.description ? "error" : "default"}
              errorMessage={errors.packagingServices?.[index]?.description?.message}
            />
            <CriteriasButton
              criteriaType="PACKAGING_SERVICE"
              packagingServiceId={field.id}
              hasCriteria={!!field.has_criteria}
              criteriaText="Obligation Criteria"
              shouldOpenCriteriasDrawer={() => {
                if (!field.id) {
                  enqueueSnackbar("Please save the packaging service first and add a criteria later", {
                    variant: "warning",
                  });
                  return false;
                }
                return true;
              }}
            />
          </div>
          <div className="h-[1px] w-full bg-tonal-dark-cream-80 rounded-full" />
        </div>
      ))}
      <div className="space-y-6">
        <Button
          type="button"
          variant="text"
          color="light-blue"
          size="small"
          leadingIcon={<Add />}
          onClick={handleAddPackagingService}
        >
          Add Package
        </Button>
        {errors.packagingServices && (
          <p className="text-error text-sm text-right">{errors.packagingServices.message}</p>
        )}
        <div className="flex items-center justify-end gap-10">
          <Button type="button" onClick={() => onCloseStep()} variant="text" color="dark-blue" size="medium">
            Cancel
          </Button>
          <Button
            type="submit"
            variant="filled"
            color={"dark-blue"}
            size="medium"
            className="w-60"
            disabled={isSubmitting}
          >
            {isSubmitting ? "Saving..." : "Save"}
          </Button>
        </div>
      </div>
    </form>
  );
}
