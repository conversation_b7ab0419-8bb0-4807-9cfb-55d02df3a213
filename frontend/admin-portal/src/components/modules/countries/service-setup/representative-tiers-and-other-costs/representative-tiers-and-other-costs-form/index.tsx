"use client";

import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { useServiceSetup } from "@/hooks/use-service-setup";
import { updateCountry } from "@/lib/api/countries";
import { queryClient } from "@/lib/react-query";
import { OtherCost } from "@/types/service-setup/other-cost";
import { RepresentativeTier } from "@/types/service-setup/representative-tier";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Add } from "@interzero/oneepr-react-ui/Icon";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { useEffect } from "react";
import { Controller, useForm, useWatch } from "react-hook-form";
import { z } from "zod";
import { CriteriasButton } from "../../criterias-drawer/criterias-button";
import { enqueueSnackbar } from "notistack";

const representativeTiersAndOtherCostsFormSchema = z.object({
  authorize_representative_obligated: z.boolean(),
  other_costs_obligated: z.boolean(),
});

type RepresentativeTiersAndOtherCostsFormData = z.infer<typeof representativeTiersAndOtherCostsFormSchema>;

function stringBooleanToBoolean(value?: string | null) {
  return value === "true";
}

interface RepresentativeTiersAndOtherCostsFormProps {
  representativeTiers: RepresentativeTier[];
  otherCosts: OtherCost[];
  onCloseStep: () => void;
}

export function RepresentativeTiersAndOtherCostsForm({
  representativeTiers,
  otherCosts,
  onCloseStep,
}: RepresentativeTiersAndOtherCostsFormProps) {
  const { country } = useServiceSetup();

  const { paramValues, changeParam, deleteAllParams } = useQueryFilter([
    "authorize_representative_obligated",
    "other_costs_obligated",
  ]);

  const authorizeRepresentativeObligatedParam = paramValues.authorize_representative_obligated;
  const otherCostsObligatedParam = paramValues.other_costs_obligated;

  const {
    handleSubmit,
    reset,
    control,
    formState: { isSubmitting },
  } = useForm<RepresentativeTiersAndOtherCostsFormData>({
    resolver: zodResolver(representativeTiersAndOtherCostsFormSchema),
    defaultValues: {
      authorize_representative_obligated:
        stringBooleanToBoolean(authorizeRepresentativeObligatedParam) ||
        country.authorize_representative_obligated ||
        false,
      other_costs_obligated: stringBooleanToBoolean(otherCostsObligatedParam) || country.other_costs_obligated || false,
    },
  });

  useEffect(() => {
    reset({
      authorize_representative_obligated:
        stringBooleanToBoolean(authorizeRepresentativeObligatedParam) || country.authorize_representative_obligated,
      other_costs_obligated: stringBooleanToBoolean(otherCostsObligatedParam) || country.other_costs_obligated,
    });

    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [country, representativeTiers, otherCosts]);

  async function handleFormSubmit(data: RepresentativeTiersAndOtherCostsFormData) {
    try {
      await updateCountry(country.id, data);

      deleteAllParams();

      queryClient.invalidateQueries({ queryKey: ["country", country.code] });
      queryClient.invalidateQueries({ queryKey: ["service-setup-status", country.code] });

      onCloseStep();
    } catch {}
  }

  const authorizeRepresentativeObligated = useWatch({
    control,
    name: "authorize_representative_obligated",
  });

  const otherCostsObligated = useWatch({
    control,
    name: "other_costs_obligated",
  });

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="mt-6 space-y-6">
      <div className="w-full mt-6 space-y-6">
        <p className="text-primary font-bold">Authorize Representative</p>
        <div className="w-full pl-10 space-y-6">
          <div className="w-full space-y-4">
            <label className="text-primary text-base font-centra mb-2">
              Is this service obligated to have Authorize representative?
            </label>
            <Controller
              control={control}
              name="authorize_representative_obligated"
              render={({ field: { onChange, value } }) => (
                <RadioGroup
                  value={value ? "true" : "false"}
                  onValueChange={(newValue) => {
                    onChange(newValue === "true");
                    changeParam("authorize_representative_obligated", newValue);
                  }}
                >
                  <div className="flex items-center gap-6">
                    <label className="flex items-center gap-2 text-primary cursor-pointer">
                      <RadioGroupItem value="true" className="block" />
                      Yes
                    </label>
                    <label className="flex items-center gap-2 text-primary cursor-pointer">
                      <RadioGroupItem value="false" className="block" />
                      No
                    </label>
                  </div>
                </RadioGroup>
              )}
            />
          </div>
          <CriteriasButton
            criteriaType="AUTHORIZE_REPRESENTATIVE"
            hasCriteria={!!country.has_authorize_representative_criteria}
            criteriaText="Authorize representative Criteria"
          />
          {authorizeRepresentativeObligated && (
            <div className="w-full space-y-4">
              <p className="text-primary">Authorize representatives tier</p>
              {representativeTiers.map((representativeTier) => (
                <div className="flex items-center justify-between py-1" key={representativeTier.id}>
                  <p className="text-tonal-dark-cream-10 font-bold">{representativeTier.name}</p>
                  <div className="flex items-center gap-2">
                    <Link href={`/countries/${country.code}/service-setup/representative-tiers`} className="block">
                      <Button variant="text" color="light-blue" size="small">
                        Edit
                      </Button>
                    </Link>
                  </div>
                </div>
              ))}

              <Link href={`/countries/${country.code}/service-setup/representative-tiers`} className="block">
                <Button type="button" variant="text" color="light-blue" size="small" leadingIcon={<Add />}>
                  Add Authorize representatives tier
                </Button>
              </Link>
              <CriteriasButton
                criteriaType="REPRESENTATIVE_TIER"
                hasCriteria={!!country.has_representative_tier_criteria}
                criteriaText="Representative tier Criteria"
                shouldOpenCriteriasDrawer={() => {
                  if (representativeTiers.length < 2) {
                    enqueueSnackbar("Please add at least two representative tiers to add a criteria", {
                      variant: "warning",
                    });
                    return false;
                  }

                  return true;
                }}
              />
              <div className="h-[1px] w-full bg-tonal-dark-cream-80 rounded-full my-8" />
            </div>
          )}
        </div>
      </div>
      <div className="w-full mt-6 space-y-6">
        <p className="text-primary font-bold">Other costs</p>
        <div className="w-full pl-10 space-y-6">
          <div className="w-full space-y-4">
            <label className="text-primary text-base font-centra mb-2">
              Is this service obligated to have other costs?
            </label>
            <Controller
              control={control}
              name="other_costs_obligated"
              render={({ field: { onChange, value } }) => (
                <RadioGroup
                  value={value ? "true" : "false"}
                  onValueChange={(newValue) => {
                    onChange(newValue === "true");
                    changeParam("other_costs_obligated", newValue);
                  }}
                >
                  <div className="flex items-center gap-6">
                    <label className="flex items-center gap-2 text-primary cursor-pointer">
                      <RadioGroupItem value="true" className="block" />
                      Yes
                    </label>
                    <label className="flex items-center gap-2 text-primary cursor-pointer">
                      <RadioGroupItem value="false" className="block" />
                      No
                    </label>
                  </div>
                </RadioGroup>
              )}
            />
          </div>
          {otherCostsObligated && (
            <div className="w-full space-y-4">
              <p className="text-primary">Other costs</p>
              {otherCosts.map((otherCost) => (
                <div className="flex items-center justify-between py-1" key={otherCost.id}>
                  <p className="text-tonal-dark-cream-10 font-bold">{otherCost.name}</p>
                  <div className="flex items-center gap-2">
                    <Link href={`/countries/${country.code}/service-setup/other-costs`} className="block">
                      <Button variant="text" color="light-blue" size="small">
                        Edit
                      </Button>
                    </Link>
                  </div>
                </div>
              ))}
              <Link href={`/countries/${country.code}/service-setup/other-costs`} className="block">
                <Button type="button" variant="text" color="light-blue" size="small" leadingIcon={<Add />}>
                  Add other costs
                </Button>
              </Link>
              <CriteriasButton
                criteriaType="OTHER_COST"
                hasCriteria={!!country.has_other_cost_criteria}
                criteriaText="Other Costs Criteria"
                shouldOpenCriteriasDrawer={() => {
                  if (otherCosts.length < 2) {
                    enqueueSnackbar("Please add at least two costs to add a criteria", { variant: "warning" });
                    return false;
                  }

                  return true;
                }}
              />
              <div className="h-[1px] w-full bg-tonal-dark-cream-80 rounded-full my-8" />
            </div>
          )}
        </div>
      </div>
      <div className="space-y-6">
        <div className="flex items-center justify-end gap-10">
          <Button type="button" onClick={() => onCloseStep()} variant="text" color="dark-blue" size="medium">
            Cancel
          </Button>
          <Button type="submit" variant="filled" color="dark-blue" size="medium" className="w-60">
            {isSubmitting ? "Saving..." : "Save"}
          </Button>
        </div>
      </div>
    </form>
  );
}
