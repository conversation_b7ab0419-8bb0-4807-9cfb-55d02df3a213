"use client";

import { Skeleton } from "@/components/ui/skeleton";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { useServiceSetup } from "@/hooks/use-service-setup";
import { getServiceSetupOtherCosts, getServiceSetupRepresentativeTiers } from "@/lib/api/service-setups";
import { CheckCircle } from "@interzero/oneepr-react-ui/Icon";
import { useQuery } from "@tanstack/react-query";
import { RepresentativeTiersAndOtherCostsForm } from "./representative-tiers-and-other-costs-form";

interface ServiceSetupRepresentativeTiersAndOtherCostsProps {}

export function ServiceSetupRepresentativeTiersAndOtherCosts({}: ServiceSetupRepresentativeTiersAndOtherCostsProps) {
  const { paramValues, changeParam, deleteAllParams } = useQueryFilter(["step"]);

  const isSelected = paramValues.step === "representative-tier-and-other-costs";

  function handleOpenStep() {
    changeParam("step", "representative-tier-and-other-costs");
  }

  function handleCloseStep() {
    deleteAllParams();
  }

  const { country } = useServiceSetup();

  const { data: representativeTiers, isFetching: isFetchingRepresentativeTiers } = useQuery({
    queryKey: ["service-setup-representative-tiers", country.code],
    queryFn: () => getServiceSetupRepresentativeTiers(country.code),
  });

  const { data: otherCosts, isFetching: isFetchingOtherCosts } = useQuery({
    queryKey: ["service-setup-other-costs", country.code],
    queryFn: () => getServiceSetupOtherCosts(country.code),
  });

  const isComplete = representativeTiers && !!representativeTiers.length && otherCosts && !!otherCosts.length;

  if (isFetchingRepresentativeTiers || isFetchingOtherCosts) {
    return (
      <div className="bg-background rounded-[20px] p-8 space-y-2" id="representative-tiers-and-other-costs-step">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-full" />
      </div>
    );
  }

  return (
    <div
      data-selected={isSelected}
      data-complete={isComplete}
      className="group bg-background rounded-[20px] p-8 cursor-pointer data-[selected=true]:cursor-default"
      onClick={() => !isSelected && handleOpenStep()}
      id="representative-tiers-and-other-costs-step"
    >
      <div className="flex items-center justify-between">
        <h3 className="text-primary text-2xl font-bold">4. Authorize representative & Other costs</h3>
        {!isSelected && isComplete && <CheckCircle className="size-6 fill-success transition-all duration-300" />}
      </div>
      {isSelected && (
        <RepresentativeTiersAndOtherCostsForm
          representativeTiers={representativeTiers || []}
          otherCosts={otherCosts || []}
          onCloseStep={handleCloseStep}
        />
      )}
    </div>
  );
}
