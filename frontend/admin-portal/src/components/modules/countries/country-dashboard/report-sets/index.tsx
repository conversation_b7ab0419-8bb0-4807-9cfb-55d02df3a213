import { useServiceSetup } from "@/hooks/use-service-setup";
import { getReportSets } from "@/lib/api/report-sets";
import { Edit, Search } from "@interzero/oneepr-react-ui/Icon";
import { useQuery } from "@tanstack/react-query";
import { Dropdown, DropdownItem } from "@/components/ui/dropdown";
import { EllipsisVertical } from "lucide-react";
import Link from "next/link";
import { PreviewCalculatorDialog } from "@/components/common/preview/preview-calculator-dialog";
import { PreviewReportTableDialog } from "@/components/common/preview/preview-report-table-dialog";

interface CountryReportSetsCardProps {
  packagingServiceId: number;
}

export function CountryReportSetsCard({ packagingServiceId }: CountryReportSetsCardProps) {
  const { country } = useServiceSetup();

  const { data: reportSets, isLoading } = useQuery({
    queryKey: ["report-sets", packagingServiceId],
    queryFn: () => getReportSets({ packagingServiceId }),
    enabled: !!packagingServiceId,
  });

  const filteredReportSets = reportSets?.filter((reportSet) => reportSet.packaging_service_id === packagingServiceId);

  return (
    <div className="bg-background rounded-xl p-8 space-y-6 col-span-1 lg:col-span-7 min-h-[500px]">
      {isLoading && <div className="bg-background w-full h-full"></div>}
      {!isLoading && (
        <>
          <div className="flex items-center">
            <h3 className="text-primary text-2xl font-bold">Report Set</h3>
          </div>
          <div className="flex flex-col gap-2">
            {filteredReportSets?.map((reportSet) => (
              <div key={reportSet.id} className="flex items-center justify-between">
                <h4 className="text-primary font-bold">{reportSet.name}</h4>
                <div className="w-4">
                  <Dropdown
                    trigger={
                      <button className="rounded-full p-1 hover:bg-secondary/30">
                        <EllipsisVertical className="size-4 text-primary" />
                      </button>
                    }
                  >
                    <DropdownItem asChild className="w-full">
                      <Link
                        href={`/countries/${country.code}/service-setup/report-set/${reportSet.id}`}
                        className="cursor-pointer text-tonal-dark-cream-10 hover:bg-surface-01 flex items-center  w-full py-3 px-4 outline-none text-base hover:cursor-pointer"
                      >
                        <Edit className="size-4 mr-5 stroke-primary" /> Edit
                      </Link>
                    </DropdownItem>
                    <DropdownItem asChild className="w-full">
                      <PreviewReportTableDialog reportSetId={reportSet.id} countryCode={country.code}>
                        <div className="cursor-pointer text-tonal-dark-cream-10 hover:bg-surface-01 flex items-center  w-full py-3 px-4 outline-none text-base hover:cursor-pointer">
                          <Search className="size-4 mr-5 fill-primary" /> Preview Report Table
                        </div>
                      </PreviewReportTableDialog>
                    </DropdownItem>
                    <DropdownItem asChild className="w-full">
                      <PreviewCalculatorDialog reportSetId={reportSet.id} countryCode={country.code}>
                        <div className="cursor-pointer text-tonal-dark-cream-10 hover:bg-surface-01 flex items-center  w-full py-3 px-4 outline-none text-base hover:cursor-pointer">
                          <Search className="size-4 mr-5 fill-primary" /> Preview Calculator
                        </div>
                      </PreviewCalculatorDialog>
                    </DropdownItem>
                  </Dropdown>
                </div>
              </div>
            ))}
          </div>
        </>
      )}
    </div>
  );
}
