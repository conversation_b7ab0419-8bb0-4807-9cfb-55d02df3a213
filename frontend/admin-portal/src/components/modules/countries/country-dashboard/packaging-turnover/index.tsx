"use client";

import { Select, SelectItem, SelectContent, SelectTrigger } from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { customerApi } from "@/lib/api";
import { PackagingService } from "@/types/service-setup/packaging-service";
import { formatCurrency } from "@/utils/format-currency";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { Area, AreaChart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts";

interface PackagingTurnoverProps {
  packagingService: PackagingService;
}

const GROUP_BY_OPTIONS = [
  { label: "Month", value: "MONTH" },
  { label: "Quarter", value: "QUARTER" },
  { label: "Year", value: "YEAR" },
];

export function PackagingTurnover({ packagingService }: PackagingTurnoverProps) {
  const [groupBy, setGroupBy] = useState<"MONTH" | "QUARTER" | "YEAR">("QUARTER");

  const packagingServiceTurnoverQuery = useQuery({
    queryKey: ["packaging-turnover", packagingService.id, groupBy],
    queryFn: async () => {
      const data = await customerApi.get<{ period_label: string; revenue: number }[]>(
        `/customer/packaging-services/${packagingService.id}/turnover?group_by=${groupBy}`
      );
      return data.data;
    },
  });

  const periods = packagingServiceTurnoverQuery.data?.map((period) => ({
    period_label: period.period_label,
    revenue: period.revenue / 1000,
  }));

  return (
    <div className="bg-background rounded-xl p-8 space-y-5 col-span-1 lg:col-span-7">
      <div className="flex flex-col lg:flex-row items-start justify-between gap-1">
        <div className="space-y-5 flex-1">
          <div className="flex items-center">
            <h3 className="text-primary text-xl lg:text-2xl font-bold">{packagingService.name} turnover</h3>
          </div>
          <span className="text-sm text-grey-blue/60">Values in thousands of euros</span>
        </div>
        <div>
          <Select onValueChange={(value) => setGroupBy(value as "MONTH" | "QUARTER" | "YEAR")}>
            <SelectTrigger className="border-0 py-1 px-2 w-[120px]! mt-2">
              {GROUP_BY_OPTIONS.find((option) => option.value === groupBy)?.label}
            </SelectTrigger>
            <SelectContent>
              {GROUP_BY_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      {packagingServiceTurnoverQuery.isLoading && <Skeleton className="h-[400px]" />}
      {!packagingServiceTurnoverQuery.isLoading && packagingServiceTurnoverQuery.data && (
        <div className="w-full h-[400px]">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart accessibilityLayer data={periods} margin={{ left: 10, right: 15 }}>
              <CartesianGrid vertical={false} />
              <Tooltip formatter={(value) => formatCurrency(Number(value))} />
              <YAxis tickLine={false} axisLine={false} tickFormatter={(value) => formatCurrency(Number(value))} />
              <XAxis dataKey="period_label" tickLine={false} axisLine={false} interval={0} fontSize={12} />
              <Area type="monotone" dataKey="revenue" fill="#F1988D" fillOpacity={0.4} stroke="#F1988D" />
            </AreaChart>
          </ResponsiveContainer>
        </div>
      )}
    </div>
  );
}
