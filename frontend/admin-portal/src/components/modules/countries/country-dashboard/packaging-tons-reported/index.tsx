"use client";

import { Select, SelectContent, SelectItem, SelectTrigger } from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { customerApi } from "@/lib/api";
import { getReportSets } from "@/lib/api/report-sets";
import { PackagingService } from "@/types/service-setup/packaging-service";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { Bar, BarChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts";

interface PackagingTonsReportedProps {
  packagingService: PackagingService;
}

export function PackagingTonsReported({ packagingService }: PackagingTonsReportedProps) {
  const packagingServiceTurnoverQuery = useQuery({
    queryKey: ["packaging-weight-reported", packagingService.id],
    queryFn: async () => {
      const data = await customerApi.get<{ setup_fraction_code: string; total_weight: number }[]>(
        `/customer/packaging-services/${packagingService.id}/weight-reported`
      );
      return data.data;
    },
  });

  const [selectedReportSetId, setSelectedReportSetId] = useState<number | null>(null);

  const { data: reportSets, isLoading } = useQuery({
    queryKey: ["report-sets", packagingService.id],
    queryFn: async () => {
      const reportSets = await getReportSets({ packagingServiceId: packagingService.id });

      if (!!reportSets.length) setSelectedReportSetId(reportSets[0].id);

      return reportSets;
    },
  });

  const selectedReportSet = reportSets?.find((reportSet) => reportSet.id === selectedReportSetId) || null;

  const chartData =
    packagingServiceTurnoverQuery.data
      ?.map((f) => ({
        fraction: selectedReportSet?.fractions.find((fraction) => fraction.code === f.setup_fraction_code),
        setup_fraction_code: f.setup_fraction_code,
        total_weight: Math.round(f.total_weight / 1000),
      }))
      .filter((f) => !!f.fraction) || [];

  return (
    <div className="bg-background rounded-xl p-8 space-y-5 col-span-1 lg:col-span-5">
      <div className="flex flex-col lg:flex-row items-start justify-between gap-1">
        <div className="space-y-5 flex-1">
          <div className="flex items-center">
            <h3 className="text-primary text-xl lg:text-2xl font-bold text-nowrap">Tons reported</h3>
          </div>
          <span className="text-sm text-grey-blue/60">Value in tons</span>
        </div>
        <div>
          {isLoading && <Skeleton className="h-[40px] w-[120px]" />}
          {!isLoading && (
            <Select onValueChange={(value) => setSelectedReportSetId(Number(value))}>
              <SelectTrigger className="border-0 py-1 px-2 mt-2">
                <span className="text-ellipsis text-nowrap whitespace-nowrap overflow-hidden w-[80px] block">
                  {reportSets?.find((reportSet) => reportSet.id === selectedReportSetId)?.name}
                </span>
              </SelectTrigger>
              <SelectContent>
                {reportSets?.map((reportSet) => (
                  <SelectItem key={reportSet.id} value={reportSet.id.toString()}>
                    {reportSet.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div>
      </div>
      {packagingServiceTurnoverQuery.isLoading && <Skeleton className="h-[400px]" />}
      {!packagingServiceTurnoverQuery.isLoading && !!chartData.length && (
        <div className="w-full h-[400px]">
          <ResponsiveContainer width="100%" height={250}>
            <BarChart data={chartData} layout="vertical" barSize={18} margin={{ right: 50 }}>
              <Tooltip label="Tons reported:" formatter={(value) => `${Number(value).toFixed(2)} tons`} />
              <XAxis type="number" hide />
              <YAxis
                dataKey="fraction.name"
                type="category"
                orientation="right"
                axisLine={false}
                tickLine={false}
                width={100}
                tickFormatter={(value) => value.replace("_", " ")}
              />
              <Bar dataKey="total_weight" fill="#F1988D" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      )}
      {!packagingServiceTurnoverQuery.isLoading && !chartData.length && (
        <div className="flex items-center justify-center">
          <span className="text-sm text-grey-blue/60">No data found for this report set</span>
        </div>
      )}
    </div>
  );
}
