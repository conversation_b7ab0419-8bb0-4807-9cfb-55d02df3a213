import { useServiceSetup } from "@/hooks/use-service-setup";
import { getServiceSetupRequiredInformations } from "@/lib/api/service-setups";
import { useQuery } from "@tanstack/react-query";
import { REQUIRED_INFORMATION_TYPES } from "../../service-setup/required-information";

interface CountryRequiredInformationsCardProps {}

export function CountryRequiredInformationsCard({}: CountryRequiredInformationsCardProps) {
  const { country } = useServiceSetup();

  const { data: requiredInformations, isLoading } = useQuery({
    queryKey: ["service-setup-required-informations", country.code],
    queryFn: () => getServiceSetupRequiredInformations(country.code),
  });

  return (
    <div className="bg-background rounded-xl p-8 space-y-6 col-span-1 lg:col-span-5 min-h-[500px]">
      {isLoading && <div className="bg-background w-full h-full"></div>}
      {!isLoading && (
        <>
          <div className="flex items-center">
            <h3 className="text-primary text-2xl font-bold">Required information</h3>
          </div>
          <div className="flex flex-col gap-2">
            {requiredInformations?.map((requiredInformation) => (
              <div key={requiredInformation.id} className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="h-10 w-10 flex items-center justify-center">
                      {REQUIRED_INFORMATION_TYPES[requiredInformation.type].icon()}
                    </div>
                    <p className="text-primary text-sm font-bold">{requiredInformation.name}</p>
                  </div>
                </div>
                <div className="h-[1px] w-full bg-tonal-dark-cream-80 rounded-full" />
              </div>
            ))}
          </div>
        </>
      )}
    </div>
  );
}
