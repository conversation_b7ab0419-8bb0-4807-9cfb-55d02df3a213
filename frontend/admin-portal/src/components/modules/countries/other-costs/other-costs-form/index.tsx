"use client";

import { Button } from "@interzero/oneepr-react-ui/Button";
import { zodResolver } from "@hookform/resolvers/zod";
import { Controller, useFieldArray, useForm } from "react-hook-form";
import { useEffect } from "react";
import { queryClient } from "@/lib/react-query";
import { z } from "zod";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Add } from "@interzero/oneepr-react-ui/Icon";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { createOtherCost, deleteOtherCost, updateOtherCost } from "@/lib/api/other-costs";
import { FractionInput } from "@/components/ui/fraction-input";
import { OtherCost } from "@/types/service-setup/other-cost";
import { useServiceSetup } from "@/hooks/use-service-setup";
import { enqueueSnackbar } from "notistack";
import { SPECIAL_CHARS_REGEX } from "@/utils/regex";

const otherCostSchema = z.object({
  id: z.coerce
    .number()
    .transform((value) => Number(value) || undefined)
    .optional(),
  name: z.string().min(1, "Name is required").regex(SPECIAL_CHARS_REGEX, "Special characters are not allowed"),
  price: z.coerce.number().min(1, "Price is required"),
});

const otherCostsFormSchema = z.object({
  otherCosts: z.array(otherCostSchema),
});

type OtherCostsFormData = z.infer<typeof otherCostsFormSchema>;

interface OtherCostsFormProps {
  otherCosts: OtherCost[];
}

export function OtherCostsForm({ otherCosts }: OtherCostsFormProps) {
  const { country } = useServiceSetup();

  const {
    control,
    handleSubmit,
    register,
    formState: { errors, isSubmitting },
    setValue,
    reset,
  } = useForm<OtherCostsFormData>({
    resolver: zodResolver(otherCostsFormSchema),
    defaultValues: {
      otherCosts,
    },
  });

  useEffect(() => {
    reset({ otherCosts });

    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [otherCosts]);

  const { fields, append, remove } = useFieldArray({
    control,
    name: "otherCosts",
    keyName: "key",
  });

  async function handleFormSubmit(data: OtherCostsFormData) {
    try {
      if (!data.otherCosts.length) return;

      const promises = data.otherCosts.map(async (otherCost) => {
        if (!otherCost.id) {
          return createOtherCost({ ...otherCost, country_id: country.id });
        }

        return updateOtherCost(otherCost.id, otherCost);
      });

      const responses = await Promise.allSettled(promises);

      responses.forEach((response, index) => {
        if (response.status !== "fulfilled") return;

        setValue(`otherCosts.${index}.id`, response.value.id);
      });

      queryClient.invalidateQueries({ queryKey: ["service-setup-other-costs", country.code] });
      queryClient.invalidateQueries({ queryKey: ["service-setup-status", country.code] });

      enqueueSnackbar("Other costs saved successfully", { variant: "success" });
    } catch {
      enqueueSnackbar("Failed to save other costs", { variant: "error" });
    }
  }

  async function handleDeleteOtherCost(index: number) {
    const otherCost = fields[index];

    if (!otherCost.id) {
      remove(index);
      enqueueSnackbar("Other cost removed successfully", { variant: "success" });
      return;
    }

    try {
      await deleteOtherCost(otherCost.id);

      remove(index);

      queryClient.invalidateQueries({ queryKey: ["service-setup-other-costs", country.code] });
      enqueueSnackbar("Other cost removed successfully", { variant: "success" });
    } catch {
      enqueueSnackbar("Failed to remove other cost. Please try again.", { variant: "error" });
    }
  }

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-10">
      {fields.map((field, index) => (
        <div key={field.key} className="flex flex-col gap-6 bg-background py-6 px-5 rounded-3xl">
          <input type="hidden" className="hidden" {...register(`otherCosts.${index}.id`)} />
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <p className="text-primary">Other cost {index + 1}</p>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <button
                    type="button"
                    className="text-sm font-bold text-error hover:bg-error/30 rounded-full py-1 px-3"
                  >
                    Remove cost
                  </button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Delete representative tier?</AlertDialogTitle>
                    <AlertDialogDescription>
                      By clicking on ”confirm” you are deleting this representative tier.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Back</AlertDialogCancel>
                    <AlertDialogAction onClick={() => handleDeleteOtherCost(index)}>Confirm</AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </div>
          <div className="w-full grid grid-cols-1 lg:grid-cols-4 gap-4">
            <div className="col-span-3">
              <Input
                label="Name *"
                placeholder="Name for the service"
                {...register(`otherCosts.${index}.name`)}
                variant={errors.otherCosts?.[index]?.name ? "error" : "default"}
                errorMessage={errors.otherCosts?.[index]?.name?.message}
              />
            </div>
            <div className="col-span-1">
              <Controller
                name={`otherCosts.${index}.price`}
                control={control}
                render={({ field }) => (
                  <FractionInput
                    label="Price *"
                    {...field}
                    type="currency"
                    error={errors.otherCosts?.[index]?.price?.message}
                  />
                )}
              />
            </div>
          </div>
        </div>
      ))}
      <div className="flex items-center justify-between">
        <Button
          type="button"
          variant="text"
          color="light-blue"
          size="small"
          leadingIcon={<Add />}
          onClick={() => append({ name: "", price: 0 })}
        >
          Add cost
        </Button>
        <div className="flex items-center justify-end gap-10">
          <Button type="submit" variant="filled" color="dark-blue" size="medium" className="w-60">
            {isSubmitting ? "Saving..." : "Save"}
          </Button>
        </div>
      </div>
    </form>
  );
}
