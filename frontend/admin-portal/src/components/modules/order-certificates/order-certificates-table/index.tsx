"use client";
import { createColumnHelper } from "@tanstack/react-table";
import { Button as Default<PERSON><PERSON><PERSON> } from "@interzero/oneepr-react-ui/Button";
import { Checkbox } from "../../../ui/checkbox/checkbox";
import { But<PERSON> } from "../../../ui/button";
import { ArrowDown, ArrowUp } from "lucide-react";
import { DatatableSearch } from "../../../common/datatable/datatable-search";
import { useState } from "react";
import { Download, Elipse } from "@interzero/oneepr-react-ui/Icon";
import { Datatable } from "../../../common/datatable";

type ParticipationCertificate = {
  id: string;
  companyId: string;
  companyName: string;
  registerNumber: string;
  certificateYear: string;
  certificateFileName: string;
  status: "VALID" | "INVALID";
};

const mockData: ParticipationCertificate[] = Array.from({ length: 15 }, (_, index) => ({
  id: (1000 + index).toString(),
  companyId: "1411",
  companyName: "Lauter & Co.",
  registerNumber: "DE174002289764",
  certificateYear: "2023",
  certificateFileName: "DE174002289764.pdf",
  status: "VALID",
}));

const columnHelper = createColumnHelper<ParticipationCertificate>();

const columns = [
  columnHelper.accessor("companyId", {
    header: ({ table }) => (
      <div className="flex items-center gap-1">
        <Checkbox
          checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")}
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        />
        <span>Comp. ID</span>
      </div>
    ),
    cell: ({ row }) => (
      <div className="flex items-center gap-4">
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
        <span>{row.original.companyId}</span>
      </div>
    ),
  }),
  columnHelper.accessor("companyName", {
    header: ({ column }) => (
      <Button
        variant="ghost"
        className="font-normal"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Company Name
        {column.getCanSort() ? column.getNextSortingOrder() === "desc" ? <ArrowDown /> : <ArrowUp /> : undefined}
      </Button>
    ),
    cell: (info) => (
      <div className="flex items-center justify-center">
        <span>{info.getValue()}</span>
      </div>
    ),
  }),
  columnHelper.accessor("registerNumber", {
    header: "Reg. Nr.",
    cell: (info) => info.getValue(),
  }),
  columnHelper.accessor("certificateYear", {
    header: ({ column }) => (
      <Button
        variant="ghost"
        className="font-normal"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Certificate Year
        {column.getCanSort() ? column.getNextSortingOrder() === "desc" ? <ArrowDown /> : <ArrowUp /> : undefined}
      </Button>
    ),
    cell: (info) => (
      <div className="flex items-center justify-center">
        <span>{info.getValue()}</span>
      </div>
    ),
  }),
  columnHelper.accessor("status", {
    header: "Status",
    cell: (info) => {
      const status = info.getValue();
      if (status === "VALID")
        return (
          <div className="flex items-center gap-2">
            <Elipse className="fill-success size-2" />
            <span className="text-success">Valid</span>
          </div>
        );
      else return <span>Invalid</span>;
    },
  }),
  columnHelper.display({
    id: "action",
    cell: () => <DefaultButton variant="text" size="iconSmall" color="light-blue" leadingIcon={<Download />} />,
  }),
];

export function ParticipationCertificatesTable() {
  const [searchTerm, setSearchTerm] = useState("");
  const [rowSelection, setRowSelection] = useState({});

  return (
    <div className="flex flex-col items-center gap-9 w-max">
      <div className="flex justify-between items-center w-full">
        <div className="max-w-[320px]">
          <DatatableSearch onSearch={setSearchTerm} defaultValue={searchTerm} placeholder="Search" />
        </div>
        <DefaultButton
          variant="filled"
          color="dark-blue"
          size="iconSmall"
          disabled={!(Object.keys(rowSelection).length > 0)}
          leadingIcon={<Download />}
        >
          Download all
        </DefaultButton>
      </div>
      <Datatable columns={columns} data={mockData} selection={{ rowSelection, setRowSelection }} />
    </div>
  );
}
