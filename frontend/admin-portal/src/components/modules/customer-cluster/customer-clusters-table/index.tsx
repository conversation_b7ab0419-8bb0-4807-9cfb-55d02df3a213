"use client";

import { createColumnHelper } from "@tanstack/react-table";
import { MoreVerticalIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { useMemo, useState } from "react";

import { Datatable } from "@/components/common/datatable";
import { DatatableSearch } from "@/components/common/datatable/datatable-search";
import { Dropdown, DropdownItem } from "@/components/ui/dropdown";
import { dateManager } from "@/utils/date-manager";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Delete, Edit, FileCopy, SortDown } from "@interzero/oneepr-react-ui/Icon";

import { MonthDatePickerWithRange } from "@/components/ui/month-date-picker";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { Cluster, getAllCustomersClusters } from "@/lib/api/customer-cluster";
import { useQuery } from "@tanstack/react-query";
import { DateRange } from "react-day-picker";
import { ConfirmDuplicateClusterDialog } from "../confirm-duplicate-cluster";
import { DeleteCustomerClusterDialog } from "../delete-customer-cluster-dialog";

const columnHelper = createColumnHelper<Cluster>();

export function CustomerClustersTable() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState("");
  const { paramValues, changeParams } = useQueryFilter(["page", "search", "status", "start_date", "end_date"]);

  const { data, refetch } = useQuery({
    queryKey: ["partners", paramValues],
    queryFn: () =>
      getAllCustomersClusters({
        page: Number(paramValues.page ?? 1),
        limit: 10,
        search: searchTerm,
        start_date: paramValues.start_date ? new Date(paramValues.start_date).toISOString() : undefined,
        end_date: paramValues.end_date ? new Date(paramValues.end_date).toISOString() : undefined,
      }),
  });

  const filteredClusters = useMemo(() => {
    if (!data?.clusters) return [];
    if (!searchTerm) return data.clusters;
    return data.clusters.filter((cluster) => cluster.name.toLowerCase().includes(searchTerm.toLowerCase()));
  }, [data?.clusters, searchTerm]);

  function handleDateChange(dateRange: DateRange | undefined) {
    if (!dateRange?.from || !dateRange?.to) {
      changeParams({
        start_date: undefined,
        end_date: undefined,
      });
      return;
    }
    changeParams({
      start_date: dateManager(dateRange.from).format("YYYY-MM-DD"),
      end_date: dateManager(dateRange.to).format("YYYY-MM-DD"),
    });
  }

  const columns = [
    columnHelper.accessor("name", {
      header: ({ column }) => {
        return (
          <Button
            type="button"
            variant="text"
            size="small"
            color="gray"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Name <SortDown className="ml-2 h-4 w-4 fill-tonal-dark-cream-40" />
          </Button>
        );
      },
      cell: (info) => {
        const createdAt = info.row.original.created_at;
        return (
          <div className="flex flex-col gap-1">
            <p className="text-sm text-[#002652]">{info.getValue()}</p>
            <span className="text-xs text-[#656773] font-medium">
              Created in: {dateManager(createdAt).format("DD.MM.YYYY")}
            </span>
          </div>
        );
      },
    }),
    columnHelper.accessor("registration_start_date", {
      header: "Start Date",
      cell: (info) => <span className="text-primary">{dateManager(info.getValue()).format("DD.MM.YYYY")}</span>,
    }),
    columnHelper.accessor("registration_end_date", {
      header: "End Date",
      cell: (info) => <span className="text-primary">{dateManager(info.getValue()).format("DD.MM.YYYY")}</span>,
    }),
    columnHelper.accessor("clients_count", {
      header: "Clients",
      cell: (info) => {
        const amount = info.getValue() ?? 2000;
        // const amount = 2000;
        const formatted = new Intl.NumberFormat("en-US", { notation: "standard" }).format(amount);
        return <span className="text-primary text-sm">{formatted}</span>;
      },
    }),
    columnHelper.accessor("id", {
      header: " ",
      cell: (info) => {
        const customerClusterId = info.getValue();
        return (
          <Dropdown
            trigger={
              <button className="flex items-center justify-center rounded-full size-6 text-primary hover:bg-tonal-dark-cream-70 transition-colors">
                <MoreVerticalIcon className="size-4" />
              </button>
            }
          >
            <DropdownItem
              onClick={() => router.push(`/customer-clusters/${customerClusterId}/edit`)}
              className="flex flex-shrink-0 items-center gap-4 text-primary hover:bg-support-blue/10 py-5 px-4 outline-none text-base hover:cursor-pointer"
            >
              <Edit className="size-4 fill-primary" /> Edit
            </DropdownItem>
            <DropdownItem
              asChild
              className="flex flex-shrink-0 items-center gap-4 text-primary hover:bg-support-blue/10 py-5 px-4 outline-none text-base hover:cursor-pointer"
            >
              <ConfirmDuplicateClusterDialog clusterId={customerClusterId}>
                <button className="flex flex-shrink-0 items-center gap-4 w-full text-primary hover:bg-support-blue/10 py-5 px-4 outline-none text-base hover:cursor-pointer">
                  <FileCopy className="size-4 fill-primary" /> Duplicate
                </button>
              </ConfirmDuplicateClusterDialog>
            </DropdownItem>
            <DropdownItem asChild>
              <DeleteCustomerClusterDialog customerClusterId={customerClusterId} refetch={refetch}>
                <button className="flex flex-shrink-0 items-center gap-4 w-full text-primary hover:bg-support-blue/10 py-5 px-4 outline-none text-base hover:cursor-pointer">
                  <Delete className="size-4 fill-primary" /> Delete
                </button>
              </DeleteCustomerClusterDialog>
            </DropdownItem>
          </Dropdown>
        );
      },
    }),
  ];

  return (
    <div className="flex flex-col gap-6 bg-cream rounded-3xl">
      <div className="px-8 flex items-center justify-between gap-4">
        <div className="relative flex-1 max-w-[320px]">
          <DatatableSearch onSearch={setSearchTerm} defaultValue={searchTerm} placeholder="Search by name" />
        </div>
        <div className="flex items-center gap-6">
          <div className="flex items-center gap-2">
            <MonthDatePickerWithRange onDateChange={handleDateChange} />
          </div>
        </div>
      </div>
      <div className="px-8 pb-8">
        <div className="bg-gray-50 rounded-xl overflow-hidden">
          <Datatable columns={columns} data={filteredClusters ?? []} totalPages={data?.pages} />
        </div>
      </div>
    </div>
  );
}
