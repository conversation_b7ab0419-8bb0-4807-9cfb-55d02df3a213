"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";

import { Button } from "@interzero/oneepr-react-ui/Button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface ConfirmDuplicateClusterDialogProps {
  clusterId: string | number;
  children: React.ReactNode;
}

export function ConfirmDuplicateClusterDialog({ clusterId, children }: ConfirmDuplicateClusterDialogProps) {
  const router = useRouter();

  const [isOpen, setIsOpen] = useState(false);

  async function handleConfirm() {
    router.push(`/customer-clusters/${clusterId}/duplicate`);
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="bg-surface-02 py-9 max-w-2xl">
        <DialogHeader>
          <DialogTitle className="font-bold sm:text-[1.75rem]">Duplicate cluster</DialogTitle>
          <DialogDescription className="text-tonal-dark-cream-20 font-normal">
            By clicking on ”confirm” you are duplicating this cluster. You are also duplicating the settings and
            customer list from this cluster.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <DialogClose asChild>
            <Button type="button" variant="outlined" color="dark-blue" size="medium">
              Back
            </Button>
          </DialogClose>
          <Button variant="filled" color="yellow" size="medium" onClick={handleConfirm}>
            Confirm
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
