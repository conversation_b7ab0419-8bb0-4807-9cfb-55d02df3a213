"use client";

import { useQuery } from "@tanstack/react-query";

import { Button } from "@interzero/oneepr-react-ui/Button";
import { CheckboxInput } from "@/components/ui/checkbox";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

import { CancelInvoiceDialog } from "../cancel-invoice-dialog";
import { TransferFundsDialog } from "../transfer-funds-dialog";

interface InvoiceOrderData {
  id: string | number;
  order_number: string;
  year: string | number;
  fractions: {
    glass: string;
    pppk: string;
    fe: string;
    alu: string;
    cb: string;
    sv: string;
    plstc: string;
    sm: string;
  };
}

const mockData: InvoiceOrderData[] = Array.from({ length: 4 }).map((_, idx) => ({
  id: idx + 1,
  order_number: "O-1000365",
  year: 2023,
  fractions: {
    glass: "56 kg",
    pppk: "56 kg",
    fe: "56 kg",
    alu: "56 kg",
    cb: "56 kg",
    sv: "56 kg",
    plstc: "56 kg",
    sm: "56 kg",
  },
}));

interface InvoiceOrdersTableProps {
  invoiceId: string | number;
}

export function InvoiceOrdersTable({ invoiceId }: InvoiceOrdersTableProps) {
  const { data: orders } = useQuery({
    queryKey: ["invoice-orders", invoiceId],
    queryFn: async () => {
      // return getInvoiceOrdersData(invoiceId)
      new Promise((resolve) => setTimeout(resolve, 2000));
      return mockData;
    },
  });
  return (
    <>
      <Table>
        <TableHeader>
          <TableRow className="!border-b-0">
            <TableHead role="checkbox" className="w-[20px]">
              {" "}
            </TableHead>
            <TableHead className="text-primary font-normal">Orders No.</TableHead>
            <TableHead className="text-primary font-normal">Year</TableHead>
            <TableHead className="text-primary font-normal">Glass</TableHead>
            <TableHead className="text-primary font-normal">PPK</TableHead>
            <TableHead className="text-primary font-normal">Fe</TableHead>
            <TableHead className="text-primary font-normal">Alu</TableHead>
            <TableHead className="text-primary font-normal">Cb</TableHead>
            <TableHead className="text-primary font-normal">Sv</TableHead>
            <TableHead className="text-primary font-normal">Plstc</TableHead>
            <TableHead className="text-primary font-normal">Sm</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {orders?.map((order) => (
            <TableRow key={order.id} className="!border-b-0">
              <TableCell className="text-primary">
                <CheckboxInput />
              </TableCell>
              <TableCell className="text-primary">O-1000365</TableCell>
              <TableCell className="text-primary">2023</TableCell>
              <TableCell className="text-primary">56 kg</TableCell>
              <TableCell className="text-primary">56 kg</TableCell>
              <TableCell className="text-primary">56 kg</TableCell>
              <TableCell className="text-primary">56 kg</TableCell>
              <TableCell className="text-primary">56 kg</TableCell>
              <TableCell className="text-primary">56 kg</TableCell>
              <TableCell className="text-primary">56 kg</TableCell>
              <TableCell className="text-primary">56 kg</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      <div className="flex items-center justify-end gap-8 py-2 w-full bg-support-blue/10 border-b border-b-on-surface-02">
        <TransferFundsDialog invoiceId={invoiceId}>
          <Button variant="text" size="small" color="light-blue">
            Transfer payment
          </Button>
        </TransferFundsDialog>
        <CancelInvoiceDialog invoiceId={invoiceId}>
          <Button variant="text" size="small" color="red">
            Cancel order
          </Button>
        </CancelInvoiceDialog>
      </div>
    </>
  );
}
