"use client";

import { useEffect, useState } from "react";
import { createColumnHelper } from "@tanstack/react-table";
import { ChevronDownIcon, MoreVerticalIcon } from "lucide-react";

import { FilterAlt, KeyboardArrowDown, Sort, SortDown } from "@interzero/oneepr-react-ui/Icon";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { DatatableSearch } from "@/components/common/datatable/datatable-search";
import { Dropdown, DropdownItem } from "@/components/ui/dropdown";
import { Datatable } from "@/components/common/datatable";
import { YEARS } from "@/utils/get-years";
import { useDateRangeFilter } from "@/hooks/use-date-range-filter";
import { dateManager } from "@/utils/date-manager";
import { DateRangeFilter } from "@/components/common/date-range-filter";
import { formatCurrency } from "@/utils/format-currency";

import { ReIssueInvoiceDialog } from "../re-issue-invoice-dialog";
import { CancelInvoiceDialog } from "../cancel-invoice-dialog";
import { InvoiceOrdersTable } from "../invoice-orders-table";

const STATUS_FILTERS = [
  { label: "Open", value: "OPEN" },
  { label: "Payed", value: "PAYED" },
  { label: "Cancelled", value: "CANCELLED " },
  { label: "All Status", value: "ALL" },
];

interface InvoiceData {
  id: string;
  invoice_code: string;
  broker_name: string;
  broker_id: string | number;
  amount: number;
  claim: number;
  balance: number;
  status_code: "OPEN" | "PAYED" | "CANCELLED";
  created_at: string;
}

const mockData: InvoiceData[] = Array.from({ length: 40 }).map((_, idx) => ({
  id: (idx + 1).toString(),
  invoice_code: "O-1000365",
  broker_name: "Techcent",
  broker_id: 1411,
  amount: 250_598_00 * 100, // in cents
  claim: 250_598_00 * 100, // in cents
  balance: idx === 5 ? -59 * 100 : 250_598_00 * 100, // in cents
  status_code: idx === 1 || idx === 4 || idx === 5 ? "CANCELLED" : idx % 2 === 0 ? "PAYED" : "OPEN",
  created_at: "2025.01.01",
}));

const columnHelper = createColumnHelper<InvoiceData>();

export function InvoicesTable() {
  const { selectedMonth, selectedYear, endDate, setSelectedYear, setSelectedMonth } = useDateRangeFilter();

  const [data] = useState<InvoiceData[]>(mockData);
  const [filteredData, setFilteredData] = useState<InvoiceData[]>(data);
  const [selectedStatus, setSelectedStatus] = useState(STATUS_FILTERS[3]);
  const [searchTerm, setSearchTerm] = useState("");
  const [expandedRowId, setExpandedRowId] = useState<string | null>(null);

  useEffect(() => {
    let filtered = [...data];

    // Filter by search term
    if (searchTerm.trim()) {
      const term = searchTerm.toLowerCase().trim();
      filtered = filtered.filter(
        (item) => item.broker_name.toLowerCase().includes(term) || item.invoice_code.toLowerCase().includes(term)
      );
    }

    // Filter by status
    if (selectedStatus.value !== "ALL") {
      filtered = filtered.filter((item) => item.status_code === selectedStatus.value);
    }

    // Filter by date range
    filtered = filtered.filter((item) => {
      // Convert DD.MM.YYYY to Date object
      const [day, month, year] = item.created_at.split(".");
      const itemDate = dateManager(`${year}-${month}-${day}`);

      const startFilterDate = dateManager()
        .setYear(parseInt(selectedYear.value))
        .setMonth(parseInt(selectedMonth.value) - 1)
        .startOf("month");
      const endFilterDate = dateManager()
        .setYear(parseInt(endDate.year.value))
        .setMonth(parseInt(endDate.month.value) - 1)
        .endOf("month");

      return itemDate.toDate() >= startFilterDate.toDate() && itemDate.toDate() <= endFilterDate.toDate();
    });

    setFilteredData(filtered);

    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, selectedStatus, selectedMonth, selectedYear, searchTerm]);

  const toggleRow = (rowId: string) => {
    setExpandedRowId(expandedRowId === rowId ? null : rowId);
  };

  const columns = [
    columnHelper.accessor("id", {
      header: "",
      enableSorting: false,
      enableHiding: false,
      cell: ({ row }) => {
        return (
          <Button type="button" variant="text" color="light-blue" size="iconSmall" onClick={() => toggleRow(row.id)}>
            <ChevronDownIcon
              data-expanded={expandedRowId === row.id}
              className="size-5 data-[expanded=true]:rotate-180"
            />
          </Button>
        );
      },
    }),
    columnHelper.accessor("invoice_code", {
      header: ({ column }) => {
        return (
          <button
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="inline-flex items-center gap-2"
          >
            Invoice No. <SortDown className="ml-2 h-4 w-4 fill-tonal-dark-cream-40" />
          </button>
        );
      },
      cell: (info) => <span className="pl-2">{info.getValue()}</span>,
    }),
    columnHelper.accessor("broker_name", {
      header: ({ column }) => {
        return (
          <button
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="inline-flex items-center gap-2"
          >
            Broker Info <SortDown className="ml-2 h-4 w-4 fill-tonal-dark-cream-40" />
          </button>
        );
      },
      cell: (info) => (
        <div className="pl-2 flex flex-col gap-1">
          <p className="text-primary">{info.getValue()}</p>
          <span className="text-[#656773] text-xs font-medium">#{info.row.original.broker_id}</span>
        </div>
      ),
    }),
    columnHelper.accessor("created_at", {
      header: ({ column }) => {
        return (
          <button
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="inline-flex items-center gap-2"
          >
            Date <SortDown className="ml-2 h-4 w-4 fill-tonal-dark-cream-40" />
          </button>
        );
      },
      cell: (info) => <span className="pl-2">{dateManager(info.getValue()).format("DD.MM.YY")}</span>,
    }),
    columnHelper.accessor("amount", {
      header: ({ column }) => {
        return (
          <button
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="inline-flex items-center gap-2"
          >
            Paid amount <SortDown className="ml-2 h-4 w-4 fill-tonal-dark-cream-40" />
          </button>
        );
      },
      cell: (info) => {
        const value = info.getValue() / 100;
        return (
          <span data-negative={value < 0} className="pl-2 data-[negative=true]:text-tonal-red-50">
            {formatCurrency(value)}
          </span>
        );
      },
    }),
    columnHelper.accessor("claim", {
      header: ({ column }) => {
        return (
          <button
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="inline-flex items-center gap-2"
          >
            Claim <SortDown className="ml-2 h-4 w-4 fill-tonal-dark-cream-40" />
          </button>
        );
      },
      cell: (info) => {
        const value = info.getValue() / 100;
        return (
          <span data-negative={value < 0} className="pl-2 data-[negative=true]:text-tonal-red-50">
            {formatCurrency(value)}
          </span>
        );
      },
    }),
    columnHelper.accessor("balance", {
      header: ({ column }) => {
        return (
          <button
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="inline-flex items-center gap-2"
          >
            Balance <SortDown className="ml-2 h-4 w-4 fill-tonal-dark-cream-40" />
          </button>
        );
      },
      cell: (info) => {
        const value = info.getValue() / 100;
        return (
          <span data-negative={value < 0} className="pl-2 data-[negative=true]:text-tonal-red-50">
            {formatCurrency(value)}
          </span>
        );
      },
    }),
    columnHelper.accessor("status_code", {
      header: ({ column }) => {
        return (
          <button
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="inline-flex items-center gap-2"
          >
            Status Code <SortDown className="ml-2 h-4 w-4 fill-tonal-dark-cream-40" />
          </button>
        );
      },
      cell: (info) => {
        const status = info.getValue();
        const invoiceId = info.row.original.id;
        return (
          <div className="flex items-center justify-between gap-4">
            <div
              className="group flex items-center gap-1 text-tonal-dark-cream-30 data-[status=PAYED]:text-success data-[status=CANCELLED]:text-error"
              data-status={status}
            >
              <div className="bg-tonal-dark-cream-30 group-data-[status=PAYED]:bg-success group-data-[status=CANCELLED]:bg-error w-2 h-2 rounded-full" />
              <strong className="capitalize font-bold text-sm">{status.toLowerCase()}</strong>
            </div>

            <Dropdown
              trigger={
                <button className="flex items-center justify-center rounded-full size-6 text-primary hover:bg-tonal-dark-cream-70 transition-colors">
                  <MoreVerticalIcon className="size-4" />
                </button>
              }
            >
              <DropdownItem asChild>
                <ReIssueInvoiceDialog invoiceId={invoiceId}>
                  <button className="text-left w-full text-tonal-dark-cream-10 hover:bg-support-blue/10 py-5 px-4 outline-none text-base hover:cursor-pointer">
                    Re-issue Invoice
                  </button>
                </ReIssueInvoiceDialog>
              </DropdownItem>
              <DropdownItem asChild>
                <CancelInvoiceDialog invoiceId={invoiceId}>
                  <button className="text-left w-full text-tonal-dark-cream-10 hover:bg-support-blue/10 py-5 px-4 outline-none text-base hover:cursor-pointer">
                    Cancel
                  </button>
                </CancelInvoiceDialog>
              </DropdownItem>
            </Dropdown>
          </div>
        );
      },
    }),
  ];

  const yearsFilter = YEARS.filter((y) => Number(y.value) <= new Date().getFullYear()).reverse();

  return (
    <div className="flex flex-col gap-6 rounded-3xl">
      <div className="flex items-center justify-between gap-8">
        <div className="relative flex-1 max-w-[320px]">
          <DatatableSearch onSearch={setSearchTerm} defaultValue={searchTerm} placeholder="Search by name" />
        </div>
        <div className="flex items-center gap-6">
          <Dropdown
            trigger={
              <button className="flex items-center text-support-blue font-bold text-base">
                <Sort width={20} height={20} className="fill-support-blue" />
                <span className="ml-1 mr-2 mt-1 text-left">{selectedStatus.label}</span>
                <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
              </button>
            }
          >
            {STATUS_FILTERS.map((filter, idx) => (
              <DropdownItem
                key={idx}
                onClick={() => setSelectedStatus(filter)}
                data-status={filter.value}
                className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
                style={{
                  ...(selectedStatus.value === filter.value && {
                    fontWeight: "bold",
                  }),
                }}
              >
                {filter.label}
              </DropdownItem>
            ))}
          </Dropdown>
          <div className="h-4 border w-px border-tonal-dark-cream-60" />
          <Dropdown
            trigger={
              <button className="flex items-center text-support-blue font-bold text-base">
                <FilterAlt width={20} height={20} className="fill-support-blue" />
                <span className="ml-1 mr-2 mt-1 text-left">{selectedYear.label}</span>
                <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
              </button>
            }
          >
            {yearsFilter.map((filter, idx) => (
              <DropdownItem
                key={idx}
                onClick={() => setSelectedYear(filter)}
                className="flex items-center gap-2 text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
              >
                {filter.label}
              </DropdownItem>
            ))}
          </Dropdown>
          <div className="h-4 border w-px border-tonal-dark-cream-60" />
          <DateRangeFilter
            endDate={endDate}
            selectedMonth={selectedMonth}
            selectedYear={selectedYear}
            onMonthChange={setSelectedMonth}
            onYearChange={setSelectedYear}
          />
        </div>
      </div>
      <div className="rounded-xl overflow-hidden text-surface-01">
        <Datatable
          columns={columns}
          data={filteredData}
          isRowExpanded={(rowId) => rowId === expandedRowId}
          renderExpandedRow={(rowData) => <InvoiceOrdersTable invoiceId={rowData.id} />}
        />
      </div>
    </div>
  );
}
