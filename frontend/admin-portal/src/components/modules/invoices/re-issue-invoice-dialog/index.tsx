"use client";

import { useEffect, useState } from "react";
import { enqueueSnackbar } from "notistack";
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import Image from "next/image";
import { z } from "zod";

import { useMutation, useQuery } from "@tanstack/react-query";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Question } from "@interzero/oneepr-react-ui/Icon";
import { Input } from "@interzero/oneepr-react-ui/Input";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { queryClient } from "@/lib/react-query";
import { CountrySelect } from "@/components/common/country-select";
import { COUNTRIES } from "@/utils/countries";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

interface InvoiceData {
  id: string | number;
  invoice_code: string;
  company_name: string;
  street: string;
  additional_address_line: string;
  zip_code: string;
  city: string;
  country: string;
  federal_state: string;
  vat: string;
  tin: string | null;
}

const mockData: InvoiceData = {
  id: "1",
  invoice_code: "029231",
  company_name: "Techcent",
  street: "1363 Bunts Rd Lakewood",
  additional_address_line: "Main street",
  zip_code: "25894",
  city: "Berlin",
  country: "DE",
  federal_state: "Berlin",
  vat: "1269",
  tin: null,
};

const reIssueInvoiceFormSchema = z.object({
  company_name: z.string(),
  street: z.string(),
  additional_address_line: z.string(),
  zip_code: z.string(),
  city: z.string(),
  country: z.string().optional(),
  federal_state: z.string(),
  vat: z.string(),
  tin: z.string().optional(),
  type: z.enum(["VAT", "TIN"]),
});

type ReIssueInvoiceFormData = z.infer<typeof reIssueInvoiceFormSchema>;

interface ReIssueInvoiceDialogProps {
  invoiceId: string | number;
  children: React.ReactNode;
}

export function ReIssueInvoiceDialog({ invoiceId, children }: ReIssueInvoiceDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [step, setStep] = useState<"confirm" | "submit" | "done">("confirm");

  const { data: invoice, refetch } = useQuery({
    queryKey: ["invoice", invoiceId],
    queryFn: async () => {
      // return getInvoice(invoiceId)
      await new Promise((resolve) => setTimeout(resolve, 2000));
      return mockData;
    },
    enabled: false,
  });

  const { mutate, isPending } = useMutation({
    mutationFn: (data: ReIssueInvoiceFormData) => {
      // reIssueInvoice(invoiceId, data)

      // eslint-disable-next-line no-console
      console.log("data", data);
      return new Promise((resolve) => setTimeout(resolve, 2000));
    },
    onSuccess: () => {
      queryClient.refetchQueries({ queryKey: ["invoice", invoiceId] });
      setStep("done");
    },
    onError: (error) => {
      enqueueSnackbar(error.message, { variant: "error" });
    },
  });

  const defaultValues: Partial<ReIssueInvoiceFormData> = {
    company_name: invoice?.company_name || undefined,
    street: invoice?.street || undefined,
    additional_address_line: invoice?.additional_address_line || undefined,
    zip_code: invoice?.zip_code || undefined,
    city: invoice?.city || undefined,
    country: COUNTRIES.find((country) => country.code === invoice?.country)?.code || undefined,
    federal_state: invoice?.federal_state || undefined,
    vat: invoice?.vat || undefined,
    tin: invoice?.tin || undefined,
    type: "VAT",
  };

  const { formState, ...form } = useForm<ReIssueInvoiceFormData>({
    resolver: zodResolver(reIssueInvoiceFormSchema),
    defaultValues,
  });

  useEffect(() => {
    if (invoiceId) refetch();
    if (!invoice) return;

    form.reset(defaultValues);

    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [invoiceId, invoice]);

  function handleConfirm() {
    setStep("submit");
  }

  async function handleSubmit() {
    const data = form.getValues();
    mutate(data);
  }

  function handleClose() {
    setStep("confirm");
    setIsOpen(false);
    form.reset();
  }

  return (
    <Dialog modal open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="bg-surface-02 py-9 max-w-3xl z-[1001]">
        {step === "confirm" && (
          <DialogHeader>
            <DialogTitle className="flex items-center gap-4 font-bold sm:text-[1.75rem] text-primary">
              Re-issue invoice <Question className="mt-1 fill-grey-blue opacity-70 size-5" />
            </DialogTitle>
            <DialogDescription className="text-sm text-grey-blue opacity-70">*Mandatory Fields</DialogDescription>
          </DialogHeader>
        )}
        {step === "submit" && (
          <DialogHeader>
            <DialogTitle className="font-bold sm:text-[1.75rem] text-primary">Are you sure?</DialogTitle>
            <DialogDescription className="text-tonal-dark-cream-20 text-base">
              By changing this information the invoice{" "}
              <span className="font-bold text-primary">#{invoice?.invoice_code}</span> will be automatic cancelled and a
              new one will be created.
            </DialogDescription>
          </DialogHeader>
        )}
        {step === "done" && (
          <DialogHeader className="mb-0">
            <div className="flex items-center gap-2 mb-10">
              <Image src="/assets/images/leaf-seal.svg" alt="leaf seal" width={36} height={36} className="size-9" />
              <h3 className="font-bold text-[1.75rem] text-primary">Invoice generated successfully!</h3>
            </div>
          </DialogHeader>
        )}

        {step === "confirm" && (
          <form
            onSubmit={form.handleSubmit(handleConfirm)}
            className="mt-9 px-1 pt-0.5 w-full grid md:grid-cols-2 gap-6"
          >
            <Controller
              name="company_name"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <div className="col-span-full">
                  <Input
                    label="Company Name"
                    placeholder="Name"
                    variant={error ? "error" : "default"}
                    errorMessage={error?.message}
                    {...field}
                  />
                </div>
              )}
            />
            <Controller
              name="street"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <div className="md:col-span-1">
                  <Input
                    label="Street and number"
                    placeholder=""
                    variant={error ? "error" : "default"}
                    errorMessage={error?.message}
                    {...field}
                  />
                </div>
              )}
            />
            <Controller
              name="additional_address_line"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <div className="md:col-span-1">
                  <Input
                    label="Additional address line"
                    placeholder=""
                    variant={error ? "error" : "default"}
                    errorMessage={error?.message}
                    {...field}
                  />
                </div>
              )}
            />
            <Controller
              name="zip_code"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <div className="md:col-span-1">
                  <Input
                    label="ZIP Code"
                    placeholder=""
                    variant={error ? "error" : "default"}
                    errorMessage={error?.message}
                    {...field}
                  />
                </div>
              )}
            />
            <Controller
              name="city"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <div className="md:col-span-1">
                  <Input
                    label="City"
                    placeholder=""
                    variant={error ? "error" : "default"}
                    errorMessage={error?.message}
                    {...field}
                  />
                </div>
              )}
            />
            <Controller
              name="country"
              control={form.control}
              render={({ field }) => (
                <div className="md:col-span-1">
                  <CountrySelect
                    // label="Country"
                    // isInvalid={!!error}
                    // errorMessage={error?.message}
                    value={field.value}
                    onValueChange={field.onChange}
                    modal
                    placeholder="Country"
                    contentClassName="z-[1002]"
                  />
                </div>
              )}
            />
            <Controller
              name="federal_state"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <div className="md:col-span-1 group flex flex-col gap-2">
                  <label htmlFor="federal_state" className="text-primary">
                    Federal State
                  </label>
                  <Select value={field.value} onValueChange={(value) => value && field.onChange(value)}>
                    <SelectTrigger
                      id="federal_state"
                      className="group-data-[invalid=true]:border-error group-data-[invalid=true]:bg-error/10"
                    >
                      <SelectValue placeholder="Federal State" data-invalid={!!error} />
                    </SelectTrigger>
                    <SelectContent className="z-[1002]">
                      {["Berlin"].map((opt, i) => (
                        <SelectItem key={i} value={opt}>
                          {opt}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {error?.message && <span className="font-centra text-sm text-error">{error?.message}</span>}
                </div>
              )}
            />
            <Controller
              name="type"
              control={form.control}
              render={({ field: { onChange, value } }) => (
                <RadioGroup value={value} onValueChange={(newValue) => onChange(newValue)} className="col-span-full">
                  <div className="w-full grid md:grid-cols-2 gap-6">
                    <div data-value={value} className="group flex flex-col">
                      <label className="flex items-center gap-2 text-primary group-data-[value=TIN]:text-[#8C8A87] cursor-pointer text-nowrap">
                        <RadioGroupItem value="VAT" className="block group-data-[value=TIN]:border-[#8C8A87]" /> VAT
                      </label>
                      {value === "VAT" ? (
                        <Input
                          placeholder="VAT"
                          variant={formState.errors.vat ? "error" : "default"}
                          errorMessage={formState.errors.vat?.message}
                          {...form.register("vat")}
                        />
                      ) : (
                        <input
                          disabled
                          placeholder="VAT"
                          className="mt-2 bg-[#BEBDBB] opacity-35 placeholder-[#8C8A87] border-tonal-dark-cream-80 cursor-not-allowed block w-full border rounded-2xl p-4"
                        />
                      )}
                    </div>
                    <div data-value={value} className="group flex flex-col">
                      <label className="flex items-center gap-2 text-primary group-data-[value=VAT]:text-[#8C8A87] cursor-pointer text-nowrap">
                        <RadioGroupItem value="TIN" className="block group-data-[value=VAT]:border-[#8C8A87]" /> TIN
                      </label>
                      {value === "TIN" ? (
                        <Input
                          placeholder="TIN"
                          variant={formState.errors.tin ? "error" : "default"}
                          errorMessage={formState.errors.tin?.message}
                          {...form.register("tin")}
                        />
                      ) : (
                        <input
                          disabled
                          placeholder="TIN"
                          className="mt-2 bg-[#BEBDBB] opacity-35 placeholder-[#8C8A87] border-tonal-dark-cream-80 cursor-not-allowed block w-full border rounded-2xl p-4"
                        />
                      )}
                    </div>
                  </div>
                </RadioGroup>
              )}
            />
            <div className="col-span-full py-2 px-6 bg-surface-03 text-primary rounded-2xl w-full sm:max-w-[300px]">
              19% VAT is being charged
            </div>
            <DialogFooter className="col-span-full mt-6">
              <Button type="submit" variant="filled" color="dark-blue" size="medium" className="md:min-w-[240px]">
                Confirm
              </Button>
            </DialogFooter>
          </form>
        )}
        {step === "submit" && (
          <DialogFooter className="mt-10 w-full !space-x-0 flex items-center justify-end gap-6">
            <Button
              type="button"
              variant="outlined"
              color="dark-blue"
              size="medium"
              className="sm:min-w-[200px]"
              onClick={() => setStep("confirm")}
            >
              Back
            </Button>
            <Button
              variant="filled"
              color="yellow"
              size="medium"
              onClick={handleSubmit}
              className="sm:min-w-[200px]"
              disabled={isPending}
            >
              {isPending ? "Generating..." : "Generate invoice"}
            </Button>
          </DialogFooter>
        )}
        {step === "done" && (
          <DialogFooter>
            <DialogClose asChild>
              <Button
                variant="filled"
                color="dark-blue"
                size="medium"
                onClick={handleClose}
                className="sm:min-w-[200px]"
              >
                Close
              </Button>
            </DialogClose>
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
}
