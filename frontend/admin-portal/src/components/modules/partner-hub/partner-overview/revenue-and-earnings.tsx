"use client";

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>A<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { KeyboardArrowDown } from "@interzero/oneepr-react-ui/Icon";
import { Popover, PopoverClose, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { useState } from "react";
import { cn } from "@/utils/cn";
import { useQuery } from "@tanstack/react-query";
import { getRevenuePlusEarningsOfTopFivePartners } from "@/lib/api/partner";
import { CustomDropdown } from "@/components/common/custom-dropdown";
import { Skeleton } from "@/components/ui/skeleton";

export function RevenueAndEarnings() {
  const years = Array.from({ length: 9 }, (_, i) => new Date().getFullYear() - 4 + i);
  const [filter, setFilter] = useState<"revenue" | "earnings">("revenue");
  const [selectedYear, setSelectedYear] = useState<string>(new Date().getFullYear().toString());

  const { data: revenuePlusEarnings, isLoading: isRevenuePlusEarningsLoading } = useQuery({
    queryKey: ["revenue-plus-earnings", selectedYear],
    queryFn: () => getRevenuePlusEarningsOfTopFivePartners(selectedYear.toString()),
  });

  return (
    <div className="w-full py-5 px-4 gap-6 rounded-xl bg-white">
      <div className="flex items-center justify-between">
        <p className="text-2xl text-tonal-dark-green-30 font-bold">Revenue + Earnings</p>
        <CustomDropdown
          selectedOption={selectedYear}
          options={years.map((year) => ({
            label: year.toString(),
            value: year.toString(),
          }))}
          handleSelect={setSelectedYear}
        />
      </div>
      <p className="text-tonal-dark-cream-60">TOP 5 Partners</p>
      <Popover>
        <PopoverTrigger className="flex items-center gap-2 mt-6 w-36">
          <div
            className={cn(
              "h-4 w-4 rounded-full",
              filter === "revenue" ? "bg-tonal-dark-green-30" : "bg-tonal-green-90"
            )}
          />
          <p className="font-bold text-tonal-dark-cream-10">{filter === "revenue" ? "Revenue" : "Earnings"}</p>
          <KeyboardArrowDown className="fill-primary h-5 w-5" />
        </PopoverTrigger>
        <PopoverContent
          side="bottom"
          className="w-60 overflow-hidden shadow-elevation-04-1 border-none p-0 rounded-2xl"
        >
          <PopoverClose asChild>
            <Button
              variant="text"
              size="medium"
              color="dark-blue"
              className="w-full flex justify-start p-4 py-6 rounded-none"
              leadingIcon={<div className="h-4 w-4 rounded-full bg-tonal-dark-green-30 mt-[2px]" />}
              onClick={() => setFilter("revenue")}
            >
              Revenue
            </Button>
          </PopoverClose>
          <PopoverClose asChild>
            <Button
              variant="text"
              size="medium"
              color="dark-blue"
              className="w-full flex justify-start p-4 py-6 rounded-none"
              leadingIcon={<div className="h-4 w-4 rounded-full bg-tonal-green-90 mt-[2px]" />}
              onClick={() => setFilter("earnings")}
            >
              Earnings
            </Button>
          </PopoverClose>
        </PopoverContent>
      </Popover>
      {isRevenuePlusEarningsLoading ? (
        <div className="grid grid-cols-5 gap-4 w-full h-[300px] mt-6 ml-4">
          {[...Array(5)].map((_, index) => (
            <Skeleton key={index} className="h-full w-10" />
          ))}
        </div>
      ) : (
        <div className="w-full mt-6 -ml-8">
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={revenuePlusEarnings?.partners ?? []} barSize={40}>
              <CartesianGrid strokeDasharray="0" vertical={false} />
              <XAxis dataKey="partner_name" tick={{ fontSize: 12 }} />
              <YAxis domain={[0, 100]} tick={{ fontSize: 12 }} />
              <Tooltip />
              {filter === "revenue" && <Bar dataKey="revenue" fill="#185C5C" radius={[6, 6, 0, 0]} />}
              {filter === "earnings" && <Bar dataKey="earnings" fill="#D8F2D8" radius={[6, 6, 0, 0]} />}
            </BarChart>
          </ResponsiveContainer>
        </div>
      )}
    </div>
  );
}
