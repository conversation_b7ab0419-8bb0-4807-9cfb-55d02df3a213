"use client";

import { useRouter } from "next/navigation";
import Link from "next/link";
import { ModuleTitle } from "@/components/common/module-title";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Add, Delete, Download, Edit, FileCopy, LocalOffer, MoreVert } from "@interzero/oneepr-react-ui/Icon";
import { ModuleContent } from "@/components/common/module-content";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/utils/cn";
import AsyncPaginatedTable from "@/components/common/async-paginated-table";
import { useQuery } from "@tanstack/react-query";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { getPartnersPaginated } from "@/lib/api/partner";
import { CouponPartners, Partner, PartnerStatus } from "@/lib/api/partner/types";
import { dateManager } from "@/utils/date-manager";
import { DatatableSearch } from "@/components/common/datatable/datatable-search";
import { CustomDropdown } from "@/components/common/custom-dropdown";
import { MonthDatePickerWithRange } from "@/components/ui/month-date-picker";
import { DateRange } from "react-day-picker";
import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";
import { convertPartnersTableToCSV } from "./partner-overview/convert-partners-table-CSV";
import { RevenueAndEarnings } from "./partner-overview/revenue-and-earnings";
import { DiscountsAndLinks } from "./partner-overview/discounts-and-links";

export function PartnerHubModule() {
  const router = useRouter();
  const { paramValues, changeParam, changeParams } = useQueryFilter([
    "page",
    "search",
    "status",
    "start_date",
    "end_date",
    "status",
  ]);

  const { data, isLoading } = useQuery({
    queryKey: ["partners", paramValues],
    queryFn: () =>
      getPartnersPaginated({
        page: Number(paramValues.page ?? 1),
        limit: 10,
        name: paramValues.search ?? undefined,
        start_date: paramValues.start_date ?? undefined,
        end_date: paramValues.end_date ?? undefined,
        status: paramValues.status === "all" ? undefined : (paramValues.status as PartnerStatus),
      }),
  });

  function handleCreate() {
    router.push("/partner-hub/create");
  }

  function handleEdit(id: number) {
    router.push(`/partner-hub/${id}/edit`);
  }

  function handleDuplicate(id: number) {
    const partner = data?.partners?.find((m) => m.id === id);
    if (!partner) return;
    sessionStorage.setItem("partner", JSON.stringify(partner));
    router.push("/partner-hub/create");
  }

  function handleViewProfile(partner: Partner) {
    router.push(`/partner-hub/${partner.id}/profile`);
  }

  function handleDateChange(dateRange: DateRange | undefined) {
    if (!dateRange?.from || !dateRange?.to) {
      changeParams({
        start_date: "",
        end_date: "",
      });
      return;
    }
    changeParams({
      start_date: dateManager(dateRange.from).format("YYYY-MM-DD"),
      end_date: dateManager(dateRange.to).format("YYYY-MM-DD"),
    });
  }

  function handleStatusChange(selectedOptionValue: string) {
    changeParam("status", selectedOptionValue);
  }

  function downloadPDF() {
    const input = document.getElementById("registered-partners-table");

    if (input) {
      const pdf = new jsPDF({
        orientation: "landscape",
        unit: "mm",
        format: [200, 250],
      });

      const headers = Array.from(input.querySelectorAll("th")).map((th) => th.innerText);

      const rows = Array.from(input.querySelectorAll("tr")).map((tr) =>
        Array.from(tr.querySelectorAll("td")).map((td) => td.innerText)
      );

      autoTable(pdf, {
        head: [headers],
        body: rows,
        startY: 5,
        theme: "grid",
        styles: { fontSize: 8 },
        headStyles: { fillColor: [24, 51, 98] },
        alternateRowStyles: { fillColor: [240, 240, 240] },
      });

      pdf.save("registered-partners.pdf");
    }
  }

  function downloadCSV(partners: Partner[]) {
    const csvContent = convertPartnersTableToCSV(partners);
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", "registered-partners.csv");
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  return (
    <ModuleContent>
      <div className="flex items-start justify-between">
        <ModuleTitle
          icon={LocalOffer}
          title="Partner Hub"
          description="Lorem ipsum dolor sit amet consectetur. Risus nulla egestas orci non hendrerit."
        />
        <Link href="/partner-hub/create">
          <Button variant="filled" size="medium" color="yellow" leadingIcon={<Add />} onClick={handleCreate}>
            Add new partner
          </Button>
        </Link>
      </div>
      <div className="grid grid-cols-2 gap-6">
        <RevenueAndEarnings />
        <DiscountsAndLinks />
      </div>
      <div className="pt-10 flex flex-col gap-6">
        <div className="flex items-start justify-between w-full">
          <p className="text-2xl text-primary font-bold">Registered Partners</p>
          <div className="flex items-center gap-2">
            <Button onClick={downloadPDF} color="light-blue" size="medium" variant="text" leadingIcon={<Download />}>
              PDF
            </Button>
            <Button
              color="gray"
              size="medium"
              variant="text"
              leadingIcon={<Download />}
              className="text-[#66A73F] fill-[#66A73F]"
              onClick={() => downloadCSV(data?.partners ?? [])}
            >
              Excel
            </Button>
          </div>
        </div>
        <div className="flex w-full items-center">
          <div className="w-full max-w-64 col-span-2">
            <DatatableSearch
              defaultValue={paramValues.search ?? ""}
              onSearch={(search) => changeParam("search", search)}
            />
          </div>
          <div className="flex items-center gap-8 ml-auto mr-2">
            <CustomDropdown
              options={[
                {
                  label: "All Status",
                  value: "all",
                },
                {
                  label: "No Updates",
                  value: "NO_UPDATES",
                },
                {
                  label: "Improved Contract",
                  value: "IMPROVED_CONTRACT",
                },
                {
                  label: "Denied Contract",
                  value: "DENIED_CONTRACT",
                },
                {
                  label: "Requested Commission",
                  value: "REQUESTED_COMMISSION",
                },
                {
                  label: "Changed Information",
                  value: "CHANGED_INFORMATION",
                },
              ]}
              selectedOption={paramValues.status ?? "all"}
              handleSelect={handleStatusChange}
            />
            <MonthDatePickerWithRange onDateChange={handleDateChange} />
          </div>
        </div>
        <AsyncPaginatedTable
          isLoading={isLoading}
          data={data?.partners ?? []}
          currentPage={data?.current_page ?? 1}
          onPageChange={(page) => changeParam("page", page.toString())}
          pageSize={data?.limit ?? 10}
          pages={data?.pages ?? 1}
          noResultsMessage="No partners found"
          showHeaderOnNoResults
          maxWidth={913}
          onRowClick={handleViewProfile}
          columns={[
            {
              accessorKey: "name",
              header: "Name",
              enableSorting: true,
              cell: ({ row }) => (
                <p className="block min-w-32 text-sm text-primary">
                  {row.original.first_name} {row.original.last_name}
                </p>
              ),
            },
            {
              accessorKey: "status",
              header: "Status",
              enableSorting: true,
              cell: ({ row }) => (
                <div className="min-w-48">
                  <div
                    className={cn(
                      "flex items-center justify-center py-2 px-4 gap-2 h-8 rounded-xl font-bold text-sm w-fit max-w-fit",
                      "bg-success-container text-on-success-container",
                      row.original.status === "NO_UPDATES" && "bg-success-container text-on-success-container",
                      row.original.status === "REQUESTED_COMMISSION" && "bg-tonal-red-90 text-on-error-container",
                      row.original.status === "CHANGED_INFORMATION" && "bg-tonal-beige-90 text-on-surface-04",
                      row.original.status === "IMPROVED_CONTRACT" && "bg-alert-container text-on-alert-container",
                      row.original.status === "DENIED_CONTRACT" && "bg-alert-container text-on-alert-container"
                    )}
                  >
                    {row.original.status === "NO_UPDATES" && "No updates"}
                    {row.original.status === "REQUESTED_COMMISSION" && "Requested Comission"}
                    {row.original.status === "CHANGED_INFORMATION" && "Changed Information"}
                    {row.original.status === "IMPROVED_CONTRACT" && "Improved Contract"}
                    {row.original.status === "DENIED_CONTRACT" && "Denied Contract"}
                  </div>
                </div>
              ),
            },
            {
              accessorKey: "discountCodeUsed",
              header: "Discount Code Used",
              enableSorting: true,
              cell: ({ row }) => {
                const couponUses = getCouponUses(row.original.coupons ?? []);
                return <p className="text-sm text-primary ml-8">{couponUses}</p>;
              },
            },
            {
              accessorKey: "linksUsed",
              header: "Links Used",
              enableSorting: true,
              cell: ({ row }) => {
                const linksUses = getLinksUses(row.original.coupons ?? []);
                return <p className="text-sm text-primary ml-8">{linksUses}</p>;
              },
            },
            {
              accessorKey: "totalEarnings",
              header: "Total Earnings",
              enableSorting: true,
              cell: ({ row }) => {
                const totalEarnings = getTotalEarnings(row.original);
                return <p className="text-sm text-primary ml-6">€ {(totalEarnings ?? 0) / 100}</p>;
              },
            },
            {
              accessorKey: "earnedRevenue",
              header: "Earned Revenue",
              enableSorting: true,
              cell: ({ row }) => {
                const earnedRevenue = getEarnedRevenue(row.original);
                return <p className="text-sm text-primary ml-6">€ {(earnedRevenue ?? 0) / 100}</p>;
              },
            },
            {
              accessorKey: "reductionOfRevenue",
              header: "Reduction of Revenue",
              enableSorting: true,
              cell: ({ row }) => {
                const reductionOfRevenue = getReductionOfRevenue(row.original);
                return <p className="text-sm text-primary ml-6">€ {(reductionOfRevenue ?? 0) / 100}</p>;
              },
            },
            {
              accessorKey: "enroled",
              header: "Enroled",
              enableSorting: true,
              cell: ({ row }) => (
                <p className="text-sm text-primary ml-4 mr-4">
                  {dateManager(row.original.created_at).format("DD.MM.YYYY")}
                </p>
              ),
            },
            {
              id: "actions",
              header: "",
              cell: ({ row }) => (
                <Popover>
                  <PopoverTrigger>
                    <MoreVert className="w-6 h-6 fill-primary mt-2" />
                  </PopoverTrigger>
                  <PopoverContent side="bottom" className="w-40 overflow-hidden shadow-elevation-04-1 border-none p-0">
                    <div className="flex flex-col gap-2">
                      <Button
                        variant="text"
                        size="small"
                        color="dark-blue"
                        className="w-full flex justify-start p-3 py-3 rounded-none"
                        leadingIcon={<Edit />}
                        onClick={() => handleEdit(row.original.id)}
                      >
                        Edit
                      </Button>
                      <Button
                        variant="text"
                        size="small"
                        color="dark-blue"
                        className="w-full flex justify-start p-3 py-3 rounded-none"
                        leadingIcon={<FileCopy />}
                        onClick={() => handleDuplicate(row.original.id)}
                      >
                        Duplicate
                      </Button>
                      <Button
                        variant="text"
                        size="small"
                        color="dark-blue"
                        className="w-full flex justify-start p-3 py-3 rounded-none"
                        leadingIcon={<Delete className="fill-primary" />}
                      >
                        Delete
                      </Button>
                    </div>
                  </PopoverContent>
                </Popover>
              ),
            },
          ]}
          useBuiltInSort
        />
      </div>
    </ModuleContent>
  );
}

export const getCouponUses = (coupons: CouponPartners[]) => {
  const couponUses =
    coupons
      ?.filter((coupon) => !coupon.coupon.link)
      ?.map((coupon) => coupon.coupon.coupon_uses)
      .filter(Boolean).length ?? 0;

  return couponUses;
};

export const getLinksUses = (coupons: CouponPartners[]) => {
  const couponUses =
    coupons
      ?.filter((coupon) => !coupon.coupon.link)
      ?.map((coupon) => coupon.coupon.coupon_uses)
      .filter(Boolean).length ?? 0;

  return couponUses;
};

export const getTotalEarnings = (partner: Partner) => {
  const commissions = partner.coupons
    ?.map((coupon) => coupon.coupon.commissions)
    .filter(Boolean)
    .map((commission) =>
      commission?.filter((item) => item.user_id === partner.user_id)?.map((item) => item.commission_value)
    )
    .flat()
    .filter(Boolean);

  const totalEarnings = commissions?.reduce((acc, curr) => (acc ?? 0) + (curr ?? 0), 0);

  return totalEarnings ?? 0;
};

export const getEarnedRevenue = (partner: Partner) => {
  const commissions = partner.coupons
    ?.map((coupon) => coupon.coupon.commissions)
    .filter(Boolean)
    .map((commission) =>
      commission?.filter((item) => item.user_id === partner.user_id)?.map((item) => item.net_turnover)
    )
    .flat()
    .filter(Boolean);

  const earnedRevenue = commissions?.reduce((acc, curr) => (acc ?? 0) + (curr ?? 0), 0);

  return earnedRevenue ?? 0;
};

export const getReductionOfRevenue = (partner: Partner) => {
  const commissions = partner.coupons
    ?.map((coupon) => coupon.coupon.commissions)
    .filter(Boolean)
    .map((commission) =>
      commission?.filter((item) => item.user_id === partner.user_id)?.map((item) => item.net_turnover)
    )
    .flat()
    .filter(Boolean);

  const reductionOfRevenue = commissions?.reduce((acc, curr) => (acc ?? 0) + (curr ?? 0), 0);

  return reductionOfRevenue ?? 0;
};
