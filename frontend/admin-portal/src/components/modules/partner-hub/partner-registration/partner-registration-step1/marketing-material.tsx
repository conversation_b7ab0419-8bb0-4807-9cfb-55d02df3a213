"use client";

import { AutoComplete } from "@/components/common/autocomplete";
import { Divider } from "@/components/common/divider";
import { CheckboxInput } from "@/components/ui/checkbox";
import { getAllMarketingMaterials } from "@/lib/api/marketing-materials";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Delete, File, Upload } from "@interzero/oneepr-react-ui/Icon";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { useQuery } from "@tanstack/react-query";
import { useRef, useState } from "react";
import { Controller, useFormContext } from "react-hook-form";
import { CreatePartnerParams } from "..";

export function MarketingMaterial() {
  const form = useFormContext<CreatePartnerParams>();

  const [searchCampaign, setSearchCampaign] = useState<string>("");
  const { data: marketingMaterials, isLoading: isLoadingMarketingMaterials } = useQuery({
    queryKey: ["marketingMaterials"],
    queryFn: async () => {
      const response = await getAllMarketingMaterials({
        name: searchCampaign,
      });
      return response.marketingMaterials.map((material) => ({
        label: material.name,
        value: material.id.toString(),
      }));
    },
  });

  const marketingMaterialsFileInputRef = useRef<HTMLInputElement>(null);

  return (
    <div className="flex flex-col gap-6 rounded-[40px] bg-surface-02 p-10">
      <p className="text-2xl font-bold text-primary">
        Marketing Material
        <span className="ml-4 text-base italic font-normal text-tonal-dark-cream-30">Optional</span>
      </p>
      {form.watch("step_1.marketingMaterial.isNewMarketingMaterial") ? (
        <Controller
          control={form.control}
          name="step_1.marketingMaterial.name"
          render={({ field, fieldState: { error } }) => (
            <Input
              {...field}
              label="Campaign Name"
              placeholder="Enter campaign name"
              variant={error ? "error" : "default"}
              errorMessage={error?.message}
              onBlur={() => {
                field.onChange(field.value?.trim());
              }}
            />
          )}
        />
      ) : (
        <AutoComplete
          items={marketingMaterials ?? []}
          onChange={(value) => {
            form.setValue("step_1.marketingMaterial.id", Number(value));
          }}
          onSearchValueChange={setSearchCampaign}
          searchValue={searchCampaign}
          value={form.watch("step_1.marketingMaterial.id")?.toString() ?? ""}
          isLoading={isLoadingMarketingMaterials}
          placeholder="Search"
          label="Search for campaign"
        />
      )}
      <CheckboxInput
        label="New Campaign"
        checked={
          form.watch("step_1.marketingMaterial.isNewMarketingMaterial") ||
          !!form.watch("step_1.marketingMaterial.files")?.length
        }
        onChange={(e) => {
          form.setValue("step_1.marketingMaterial.isNewMarketingMaterial", e.target.checked);
          form.setValue("step_1.marketingMaterial.files", []);

          if (e.target.checked) {
            form.setValue("step_1.marketingMaterial.name", undefined);
            form.setValue("step_1.marketingMaterial.id", undefined);
            setSearchCampaign("");
          }
        }}
      />
      <Divider className="my-2" initialMarginDisabled />
      <p className="text-2xl font-bold text-primary">Files</p>
      {form.watch("step_1.marketingMaterial.files")?.map((file) => (
        <>
          <div key={file.id} className="flex items-end gap-2 justify-between w-full">
            <div className="flex items-center gap-2 w-full">
              <File className="size-6 fill-primary" />
              <p className="text-primary">{file.name}</p>
            </div>
            <Button
              type="button"
              color="light-blue"
              size="iconMedium"
              variant="text"
              leadingIcon={<Delete className="fill-primary" />}
              onClick={() => {
                const formValue = form.getValues("step_1.marketingMaterial.files") ?? [];
                form.setValue(
                  "step_1.marketingMaterial.files",
                  formValue.filter((f) => f.name !== file.name)
                );
              }}
            />
          </div>
          <Divider className="-my-2" initialMarginDisabled />
        </>
      ))}
      <input
        type="file"
        className="hidden"
        onChange={(e) => {
          if (e.target.files) {
            const files = Array.from(e.target.files);
            const formValue = form.getValues("step_1.marketingMaterial.files") ?? [];
            form.setValue("step_1.marketingMaterial.files", [...formValue, ...files]);
            if (marketingMaterialsFileInputRef.current) {
              marketingMaterialsFileInputRef.current.value = "";
            }
          }
        }}
        multiple
        ref={marketingMaterialsFileInputRef}
      />
      <Button
        type="button"
        color="light-blue"
        size="medium"
        variant="text"
        leadingIcon={<Upload />}
        className="max-w-fit -ml-4"
        onClick={() => marketingMaterialsFileInputRef.current?.click()}
      >
        Upload File
      </Button>
      <Divider initialMarginDisabled className="-my-4" />
    </div>
  );
}
