"use client";

import { Divider } from "@/components/common/divider";
import { PasswordInput } from "@/components/ui/password-input";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { Controller, useFormContext } from "react-hook-form";
import { CreatePartnerParams } from "..";

export function LoginInformation() {
  const form = useFormContext<CreatePartnerParams>();

  const password = form.watch("step_1.loginInformation.password");
  const confirmPassword = form.watch("step_1.loginInformation.passwordConfirmation");
  const email = form.watch("step_1.loginInformation.email");
  const confirmEmail = form.watch("step_1.loginInformation.emailConfirmation");

  const isNotValidConfirmPassword = password && confirmPassword && password !== confirmPassword;
  const isNotValidConfirmEmail = email && confirmEmail && email !== confirmEmail;

  return (
    <div className="flex flex-col gap-6 rounded-[40px] bg-surface-02 p-10 my-12">
      <p className="text-2xl font-bold text-primary">Login Information</p>
      <Controller
        control={form.control}
        name="step_1.loginInformation.email"
        render={({ field, fieldState: { error } }) => (
          <Input
            {...field}
            label="Email *"
            placeholder="Enter email"
            variant={error ? "error" : "default"}
            errorMessage={error?.message}
          />
        )}
      />
      <Controller
        control={form.control}
        name="step_1.loginInformation.emailConfirmation"
        render={({ field, fieldState: { error } }) => (
          <Input
            {...field}
            label="Email Confirmation *"
            placeholder="Enter email confirmation"
            variant={(error || isNotValidConfirmEmail) && "error"}
            errorMessage={error?.message || (isNotValidConfirmEmail && "Emails do not match")}
          />
        )}
      />
      <Divider className="my-4" initialMarginDisabled />
      <Controller
        control={form.control}
        name="step_1.loginInformation.password"
        render={({ field, fieldState: { error } }) => (
          <PasswordInput
            {...field}
            label="Password *"
            placeholder="Enter password"
            variant={error ? "error" : "default"}
            errorMessage={error?.message}
          />
        )}
      />
      <Controller
        control={form.control}
        name="step_1.loginInformation.passwordConfirmation"
        render={({ field, fieldState: { error } }) => (
          <PasswordInput
            {...field}
            label="Password Confirmation *"
            placeholder="Enter password confirmation"
            variant={(error || isNotValidConfirmPassword) && "error"}
            errorMessage={error?.message || (isNotValidConfirmPassword && "Passwords do not match")}
          />
        )}
      />
    </div>
  );
}
