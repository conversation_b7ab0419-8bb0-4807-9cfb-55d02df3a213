"use client";

import { But<PERSON> } from "@interzero/oneepr-react-ui/Button";
import { East } from "@interzero/oneepr-react-ui/Icon";
import { Loader2Icon } from "lucide-react";
import { useFormContext } from "react-hook-form";
import { DraftCooperation } from "./draft-cooperation";
import { CompanyInformation } from "./company-information";
import { LoginInformation } from "./login-information";
import { MarketingMaterial } from "./marketing-material";
import { PartnershipCommission } from "./partnership-commission";
import { useRouter } from "next/navigation";

export function PartnerRegistrationStep1({ onNextStep }: { onNextStep: () => void }) {
  const router = useRouter();
  const form = useFormContext();

  return (
    <div>
      <DraftCooperation />
      <CompanyInformation />
      <LoginInformation />
      <MarketingMaterial />
      <PartnershipCommission />

      <div className="flex items-center w-full justify-end gap-6">
        <Button
          type="button"
          color="dark-blue"
          size="medium"
          variant="outlined"
          onClick={() => {
            router.back();
          }}
        >
          Cancel
        </Button>
        <Button
          type="button"
          color="yellow"
          size="medium"
          variant="filled"
          trailingIcon={form.formState.isSubmitting ? <Loader2Icon className="animate-spin" /> : <East />}
          disabled={form.formState.isSubmitting}
          onClick={onNextStep}
        >
          Next Step
        </Button>
      </div>
    </div>
  );
}
