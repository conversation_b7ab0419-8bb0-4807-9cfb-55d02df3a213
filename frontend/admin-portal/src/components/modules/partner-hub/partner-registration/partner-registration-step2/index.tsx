"use client";

import { But<PERSON> } from "@interzero/oneepr-react-ui/Button";
import { KeyboardArrowLeft } from "@interzero/oneepr-react-ui/Icon";
import { useFormContext } from "react-hook-form";
import { AtributeDiscount } from "./atribute-discount";
import { CommissionTerms } from "./commission-terms";

export function PartnerRegistrationStep2({ onBack }: { onBack: () => void }) {
  const form = useFormContext();

  return (
    <div>
      <Button
        type="button"
        color="light-blue"
        size="medium"
        variant="text"
        onClick={onBack}
        className="mb-4"
        leadingIcon={<KeyboardArrowLeft />}
      >
        Back
      </Button>

      {!form.watch("noProvisionNegotiated") && <CommissionTerms />}
      <AtributeDiscount />
    </div>
  );
}
