"use client";

import { AdsClick, LocalOffer } from "@interzero/oneepr-react-ui/Icon";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { CustomDropdown } from "@/components/common/custom-dropdown";
import { DatatableSearch } from "@/components/common/datatable/datatable-search";
import { dateManager } from "@/utils/date-manager";
import { DateRange as DateRangeType } from "react-day-picker";
import { Download } from "@interzero/oneepr-react-ui/Icon";
import { getPartnerCommissions } from "@/lib/api/partner";
import { GetPartnerCommissionsParams } from "@/lib/api/partner/types";
import { MonthDatePickerWithRange } from "@/components/ui/month-date-picker";
import { useParams } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { useState } from "react";
import AsyncPaginatedTable from "@/components/common/async-paginated-table";

function formatAmount(amount: number): string {
  return (amount / 100).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, " ");
}

export function PartnerTable() {
  const { id } = useParams();
  const [selectedProduct, setSelectedProduct] = useState<"all" | "eu_license" | "direct_license" | "action_guide">(
    "all"
  );
  const [selectedLeadType, setSelectedLeadType] = useState<"all" | "code" | "link">("all");
  const [dateRange, setDateRange] = useState<DateRangeType | undefined>(undefined);
  const { paramValues, changeParam } = useQueryFilter(["page", "query"]);

  const filters: GetPartnerCommissionsParams = {
    query: paramValues.query ?? undefined,
    product: selectedProduct === "all" ? undefined : selectedProduct,
    lead_type: selectedLeadType === "all" ? undefined : selectedLeadType,
    ...(dateRange?.from && {
      start_date: dateRange.from.toISOString(),
    }),
    ...(dateRange?.to && {
      end_date: dateRange.to.toISOString(),
    }),
    page: paramValues.page ? Number(paramValues.page) : 1,
  };

  const { data, isLoading: isCommissionsLoading } = useQuery({
    queryKey: ["partner-commissions", id, filters],
    queryFn: async () => {
      if (!id) return;
      const commissions = await getPartnerCommissions(Number(id), filters);
      return commissions;
    },
  });

  return (
    <div className="flex flex-col gap-4">
      <CustomDropdown
        options={[
          { label: "All Products", value: "all" },
          { label: "Licensing Service (Germany)", value: "direct_license" },
          { label: "Licensing Service (EU)", value: "eu_license" },
          { label: "Action Guide", value: "action_guide" },
        ]}
        handleSelect={(value) => {
          setSelectedProduct(value as "all" | "eu_license" | "direct_license" | "action_guide");
        }}
        selectedOption={selectedProduct}
        showFilterIcon={false}
        textClassName="text-3xl"
        triggerClassName="-ml-1 mb-4 w-fit"
      />

      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-semibold text-primary">List of specific leads</h2>
        <div className="flex items-center gap-2">
          <Button color="light-blue" size="medium" variant="text" leadingIcon={<Download />}>
            PDF
          </Button>
          <Button
            color="gray"
            size="medium"
            variant="text"
            leadingIcon={<Download />}
            className="text-[#66A73F] fill-[#66A73F]"
            onClick={() => {}}
          >
            Excel
          </Button>
        </div>
      </div>
      <div className="flex items-center justify-between gap-4 mb-4">
        <div className="mt-2">
          <DatatableSearch
            onSearch={(value) => {
              changeParam("query", value);
            }}
          />
        </div>
        <div className="flex items-center gap-4">
          <CustomDropdown
            options={[
              { label: "All Leads", value: "all" },
              { label: "Code", value: "code" },
              { label: "Link", value: "link" },
            ]}
            handleSelect={(value) => {
              setSelectedLeadType(value as "all" | "code" | "link");
            }}
            selectedOption={selectedLeadType}
          />
          <MonthDatePickerWithRange onDateChange={setDateRange} />
        </div>
      </div>
      <div className="w-full mt-4">
        <AsyncPaginatedTable
          isLoading={isCommissionsLoading}
          data={data?.items ?? []}
          currentPage={data?.current_page ?? 1}
          onPageChange={(page) => changeParam("page", page.toString())}
          pageSize={data?.limit ?? 10}
          pages={data?.pages ?? 1}
          noResultsMessage="No commissions found"
          showHeaderOnNoResults
          maxWidth={913}
          columns={[
            {
              accessorKey: "created_at",
              header: "Commission Date",
              enableSorting: true,
              cell: ({ row }) => (
                <p className="block min-w-32 text-sm text-primary">
                  {row.original.created_at ? dateManager(row.original.created_at).format("DD.MM.YYYY") : "-"}
                </p>
              ),
            },
            {
              accessorKey: "id",
              header: "Net Turnover",
              enableSorting: true,
              cell: ({ row }) => (
                <p className="text-sm text-primary ml-8">€ {formatAmount(row.original.net_turnover ?? 0)}</p>
              ),
            },
            {
              accessorKey: "coupon",
              header: "Commission Percentage",
              enableSorting: true,
              cell: ({ row }) => (
                <p className="text-sm text-primary ml-6">{row.original.commission_percentage ?? 0} %</p>
              ),
            },
            {
              accessorKey: "commission",
              header: "Commission in Euros",
              enableSorting: true,
              cell: ({ row }) => (
                <p className="text-sm text-primary ml-6">€ {formatAmount(row.original.commission_value ?? 0)}</p>
              ),
            },
            {
              accessorKey: "coupon_id",
              header: "Order Number",
              enableSorting: true,
              cell: ({ row }) => <p className="text-sm text-primary ml-6">{row.original.order_id}</p>,
            },
            {
              accessorKey: "coupon.link",
              header: "Lead Origin",
              enableSorting: true,
              cell: ({ row }) => (
                <div className="bg-tonal-dark-blue-80 py-2 px-3 rounded-xl flex items-center gap-2 w-fit">
                  {row.original.type === "AFFILIATE_LINK" ? (
                    <AdsClick className="size-4 fill-primary" />
                  ) : (
                    <LocalOffer className="size-4 fill-primary" />
                  )}
                  <p className="text-sm text-primary">{row.original.type === "AFFILIATE_LINK" ? "Link" : "Coupon"}</p>
                </div>
              ),
            },
            {
              accessorKey: "coupon.code",
              header: "Link or Code",
              enableSorting: true,
              cell: ({ row }) => (
                <p className="text-sm text-primary ml-4 mr-4">
                  {row.original.type === "AFFILIATE_LINK" ? row.original.affiliate_link : row.original.coupon_code}
                </p>
              ),
            },
            {
              accessorKey: "is_first_purchase",
              header: "First Time Purchase",
              enableSorting: true,
              cell: ({ row }) => (
                <p className="text-sm text-primary ml-4 mr-4">
                  {row.original.coupon?.coupon_uses?.find((use) => use.order_id === row.original.order_id)
                    ?.is_first_purchase
                    ? "Yes"
                    : "No"}
                </p>
              ),
            },
          ]}
          useBuiltInSort
          beforePaginationContent={
            <div className="w-full bg-surface-03 rounded-b-xl p-6 flex items-center justify-between">
              <div className="flex items-center">
                <p className="text-xl font-bold text-tonal-dark-cream-10">Results for:</p>
                {selectedLeadType === "all" && (
                  <>
                    <Button
                      color="dark-blue"
                      size="small"
                      variant="text"
                      leadingIcon={<LocalOffer className="mt-[2px]" />}
                    >
                      Coupon
                    </Button>
                    ;
                    <Button
                      color="dark-blue"
                      size="small"
                      variant="text"
                      leadingIcon={<AdsClick className="fill-primary" />}
                    >
                      Link
                    </Button>
                  </>
                )}
                {selectedLeadType === "code" && (
                  <Button
                    color="dark-blue"
                    size="small"
                    variant="text"
                    leadingIcon={<LocalOffer className="mt-[2px]" />}
                  >
                    Coupon
                  </Button>
                )}
                {selectedLeadType === "link" && (
                  <Button
                    color="dark-blue"
                    size="small"
                    variant="text"
                    leadingIcon={<AdsClick className="fill-primary" />}
                  >
                    Link
                  </Button>
                )}
              </div>
              <div className="flex items-center gap-20">
                <p className="text-2xl font-bold text-tonal-dark-cream-10 text-right">
                  € {formatAmount(data?.items?.reduce((acc, curr) => acc + (curr.net_turnover ?? 0), 0) ?? 0)} <br />{" "}
                  <span className="text-sm">Total of net turnover</span>
                </p>
                <p className="text-2xl font-bold text-tonal-dark-cream-10 text-right">
                  € {formatAmount(data?.items?.reduce((acc, curr) => acc + (curr.commission_value ?? 0), 0) ?? 0)}
                  <br /> <span className="text-sm">Total commission for</span>
                </p>
              </div>
            </div>
          }
        />
      </div>
    </div>
  );
}
