import { Divider } from "@/components/common/divider";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { DateRange, Download, Edit } from "@interzero/oneepr-react-ui/Icon";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { useState } from "react";

import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useQuery } from "@tanstack/react-query";
import { getPartnerGlobalStatistics } from "@/lib/api/partner";
import { Partner } from "@/lib/api/partner/types";
import { Skeleton } from "@/components/ui/skeleton";

function formatCommissionAmount(amount: number): string {
  return (amount / 100).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, " ");
}

export function PartnerGlobalStatisticsAndCommission({ partner }: { partner?: Partner }) {
  const { data: globalStatistics, isLoading: isGlobalStatisticsLoading } = useQuery({
    queryKey: ["global-statistics", partner?.id],
    queryFn: async () => {
      if (!partner?.id) return;
      const statistics = await getPartnerGlobalStatistics(Number(partner.id));
      return statistics;
    },
    enabled: !!partner?.id,
  });

  const segmentSize = 251.2 / 3;

  if (isGlobalStatisticsLoading || !partner) {
    return (
      <div className="grid grid-cols-[1fr,0.8fr] gap-4">
        <div className="bg-tonal-pink-80 p-10 pb-7 rounded-[40px]">
          <Skeleton className="h-8 w-48 mb-8" />
          <div className="grid grid-cols-[1fr,125px]">
            <div className="relative w-64 h-64">
              <Skeleton className="w-full h-full rounded-full" />
            </div>
            <div className="flex flex-col gap-4">
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
            </div>
          </div>
          <Skeleton className="h-12 w-48 mt-4" />
          <Skeleton className="h-6 w-32 mt-2" />
          <Skeleton className="h-8 w-64 mt-16" />
        </div>
        <div className="bg-white p-10 pb-7 rounded-[40px] flex flex-col gap-4">
          <Skeleton className="h-8 w-64 mb-2" />
          <div className="flex items-center gap-2">
            <Skeleton className="h-8 w-24" />
            <Skeleton className="h-8 w-24" />
          </div>
          <Skeleton className="h-12 w-48" />
          <Skeleton className="h-10 w-44" />
          <Divider initialMarginDisabled className="mt-2" />
          <div className="grid grid-cols-[0.4fr,1fr,1fr,1fr] gap-4">
            {[...Array(7)].map(() => (
              <>
                <Skeleton className="h-8 w-8" />
                <Skeleton className="h-8 w-full" />
                <Skeleton className="h-8 w-full" />
                <Skeleton className="h-8 w-24" />
              </>
            ))}
          </div>
          <Divider initialMarginDisabled className="mt-2" />
          <div className="flex items-center gap-4">
            <Skeleton className="h-8 w-20" />
            <Skeleton className="h-8 w-20" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-[1fr,0.8fr] gap-4">
      <div className="bg-tonal-pink-80 p-10 pb-7 rounded-[40px]">
        <h2 className="text-[28px] font-semibold text-primary">Global statistics</h2>
        <div className="grid grid-cols-[1fr,125px]">
          <div className="relative w-64 h-64">
            <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
              <circle
                cx="50"
                cy="50"
                r="40"
                stroke="#009DD3"
                strokeWidth="8"
                fill="none"
                strokeDasharray={`${segmentSize} ${251.2}`}
                strokeDashoffset="0"
                className="transition-all duration-1000 ease-out"
              />
              <circle
                cx="50"
                cy="50"
                r="40"
                stroke="#FFE6D5"
                strokeWidth="8"
                fill="none"
                strokeDasharray={`${segmentSize} ${251.2}`}
                strokeDashoffset={-segmentSize}
                className="transition-all duration-1000 ease-out"
              />
              <circle
                cx="50"
                cy="50"
                r="40"
                stroke="#1B6C64"
                strokeWidth="8"
                fill="none"
                strokeDasharray={`${segmentSize} ${251.2}`}
                strokeDashoffset={-segmentSize * 2}
                className="transition-all duration-1000 ease-out"
              />
            </svg>
            <div className="absolute inset-0 flex flex-col items-center justify-center text-center">
              <span className="text-primary text-[46px] font-bold">
                {globalStatistics?.total_acquired_clients ?? 0}
              </span>
              <span className="text-primary text-base mt-1 font-bold">
                Total <br /> acquired clients
              </span>
            </div>
          </div>
          <div className="flex flex-col gap-2">
            <p className="text-[40px] font-bold">
              {globalStatistics?.direct_license ?? 0}
              <div className="flex items-center min-w-fit">
                <span className="w-3 h-3 rounded-full mr-1" style={{ backgroundColor: "#0088FE" }}></span>
                <p className="text-primary font-bold text-xs truncate">Direct License - Germany</p>
              </div>
            </p>
            <p className="text-[40px] font-bold">
              {globalStatistics?.eu_license ?? 0}
              <div className="flex items-center min-w-fit">
                <span className="w-3 h-3 rounded-full mr-1" style={{ backgroundColor: "#2E665A" }}></span>
                <p className="text-primary font-bold text-xs truncate">EU License</p>
              </div>
            </p>
            <p className="text-[40px] font-bold">
              {globalStatistics?.action_guide ?? 0}
              <div className="flex items-center min-w-fit">
                <span className="w-3 h-3 rounded-full mr-1" style={{ backgroundColor: "#FFE6D5" }}></span>
                <p className="text-primary font-bold text-xs truncate">Action Guide</p>
              </div>
            </p>
          </div>
        </div>
        <p className="text-5xl font-bold mt-4 text-primary">
          <span className="text-3xl mr-2">€</span>
          {formatCommissionAmount(globalStatistics?.total_earnings ?? 0)}
        </p>
        <p className="text-primary font-bold">Commission total</p>
        <div className="text-2xl mt-16 text-primary font-bold">
          Payout cycle: <span className="font-normal">{partner?.payout_cycle ?? "-"}</span>
        </div>
      </div>
      <div className="bg-white p-10 pb-7 rounded-[40px] flex flex-col gap-4">
        <h2 className="text-2xl font-semibold text-primary">Commission amount open</h2>
        <div className="flex items-center gap-2">
          <Button color="light-blue" size="small" variant="text" leadingIcon={<DateRange />}>
            08/2023
          </Button>
          to
          <Button color="light-blue" size="small" variant="text" leadingIcon={<DateRange />}>
            09/2023
          </Button>
        </div>
        <p className="text-5xl font-bold text-primary">€ 5 250.81</p>
        <PayCommissionModal>
          <Button color="yellow" variant="filled" size="small" className="w-44">
            Pay commission
          </Button>
        </PayCommissionModal>
        <Divider initialMarginDisabled className="mt-2" />
        <div className="grid grid-cols-[0.4fr,1fr,1fr,1fr] items-center gap-2">
          <div className="block" />
          <p className="text-tonal-dark-cream-30 text-sm">Amount</p>
          <p className="text-tonal-dark-cream-30 text-sm">Request in</p>
          <p className="text-tonal-dark-cream-30 text-sm">Status</p>
          <ChangeCommisionAmountModal>
            <Button
              color="gray"
              size="iconXSmall"
              variant="text"
              leadingIcon={<Edit className="fill-tonal-dark-cream-50" />}
            />
          </ChangeCommisionAmountModal>
          <p className="text-tonal-dark-cream-50 text-sm">€ 250</p>
          <p className="text-tonal-dark-cream-50 text-sm">2023.01.01</p>
          <p className="text-success text-sm">Approved</p>
          <ChangeCommisionAmountModal>
            <Button
              color="gray"
              size="iconXSmall"
              variant="text"
              leadingIcon={<Edit className="fill-tonal-dark-cream-50" />}
            />
          </ChangeCommisionAmountModal>
          <p className="text-tonal-dark-cream-50 text-sm">€ 250</p>
          <p className="text-tonal-dark-cream-50 text-sm">2023.01.01</p>
          <p className="text-success text-sm">Approved</p>
          <ChangeCommisionAmountModal>
            <Button
              color="gray"
              size="iconXSmall"
              variant="text"
              leadingIcon={<Edit className="fill-tonal-dark-cream-50" />}
            />
          </ChangeCommisionAmountModal>
          <p className="text-tonal-dark-cream-50 text-sm">€ 250</p>
          <p className="text-tonal-dark-cream-50 text-sm">2023.01.01</p>
          <p className="text-success text-sm">Approved</p>
          <ChangeCommisionAmountModal>
            <Button
              color="gray"
              size="iconXSmall"
              variant="text"
              leadingIcon={<Edit className="fill-tonal-dark-cream-50" />}
            />
          </ChangeCommisionAmountModal>
          <p className="text-tonal-dark-cream-50 text-sm">€ 250</p>
          <p className="text-tonal-dark-cream-50 text-sm">2023.01.01</p>
          <p className="text-error text-sm">Denied</p>
          <ChangeCommisionAmountModal>
            <Button
              color="gray"
              size="iconXSmall"
              variant="text"
              leadingIcon={<Edit className="fill-tonal-dark-cream-50" />}
            />
          </ChangeCommisionAmountModal>
          <p className="text-tonal-dark-cream-50 text-sm">€ 250</p>
          <p className="text-tonal-dark-cream-50 text-sm">2023.01.01</p>
          <p className="text-success text-sm">Approved</p>
          <ChangeCommisionAmountModal>
            <Button
              color="gray"
              size="iconSmall"
              variant="text"
              leadingIcon={<Edit className="fill-tonal-dark-cream-50" />}
            />
          </ChangeCommisionAmountModal>
          <p className="text-tonal-dark-cream-50 text-sm">€ 250</p>
          <p className="text-tonal-dark-cream-50 text-sm">2023.01.01</p>
          <p className="text-error text-sm">Denied</p>
          <ChangeCommisionAmountModal>
            <Button
              color="gray"
              size="iconSmall"
              variant="text"
              leadingIcon={<Edit className="fill-tonal-dark-cream-50" />}
            />
          </ChangeCommisionAmountModal>
          <p className="text-tonal-dark-cream-50 text-sm">€ 250</p>
          <p className="text-tonal-dark-cream-50 text-sm">2023.01.01</p>
          <p className="text-success text-sm">Approved</p>
        </div>
        <Divider initialMarginDisabled className="mt-2" />
        <div className="flex items-center justify-start">
          <Button color="light-blue" size="small" variant="text" leadingIcon={<Download />}>
            PDF
          </Button>
          <Button
            color="gray"
            size="small"
            variant="text"
            leadingIcon={<Download />}
            className="text-[#66A73F] fill-[#66A73F]"
            onClick={() => {}}
          >
            Excel
          </Button>
        </div>
      </div>
    </div>
  );
}

function PayCommissionModal({ children }: { children: React.ReactNode }) {
  const [amountToBePayed, setAmountToBePayed] = useState(200);

  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="bg-surface-01">
        <DialogHeader>
          <DialogTitle>Pay Commission</DialogTitle>
          <DialogDescription className="text-tonal-dark-cream-20">
            Manually pay out only a specific commission amount.
          </DialogDescription>
        </DialogHeader>
        <div className="bg-background rounded-[20px] p-4 grid grid-cols-3 gap-4">
          <p className="text-sm text-tonal-dark-cream-30">
            Total Amount <br />
            <span className="text-base text-primary">€ 150</span>
          </p>
          <p className="text-sm text-tonal-dark-cream-30">
            Request in <br />
            <span className="text-base text-primary">12.09.2023</span>
          </p>
          <p className="text-sm text-tonal-dark-cream-30">
            Status <br />
            <span className="text-base text-alert">On Hold</span>
          </p>
        </div>
        <div className="w-80 mt-4 mb-2">
          <Input
            placeholder="Amount"
            label="Amount to be payed"
            value={amountToBePayed}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setAmountToBePayed(Number(e.target.value))}
          />
        </div>
        {amountToBePayed !== 150 && <p className="text-error mb-3">Amount to be payed is not equal to total amount.</p>}
        <p className="text-tonal-dark-cream-30">
          Open balance: <b>€ 50</b>
        </p>
        <DialogClose asChild>
          <Button color="yellow" size="medium" variant="filled" className="mt-10 ml-auto w-44">
            Payout
          </Button>
        </DialogClose>
      </DialogContent>
    </Dialog>
  );
}

function ChangeCommisionAmountModal({ children }: { children: React.ReactNode }) {
  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Change Commision Amount</DialogTitle>
          <DialogDescription className="text-tonal-dark-cream-20">
            Please set a new amount to payed for this commission.
          </DialogDescription>
        </DialogHeader>
        <Input placeholder="Amount" label="Change Amount" />
        <div className="flex items-center gap-4 mt-6 mb-1 justify-end">
          <DialogClose asChild>
            <Button color="dark-blue" size="small" variant="outlined" className="w-32">
              Back
            </Button>
          </DialogClose>
          <DialogClose asChild>
            <Button color="yellow" size="small" variant="filled" className="w-32">
              Save
            </Button>
          </DialogClose>
        </div>
      </DialogContent>
    </Dialog>
  );
}
