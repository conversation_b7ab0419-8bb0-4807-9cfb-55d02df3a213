"use client";

import { Divider } from "@/components/common/divider";
import { ModuleContent } from "@/components/common/module-content";
import { ModuleTitle } from "@/components/common/module-title";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { useQuery } from "@tanstack/react-query";
import { useParams, useRouter } from "next/navigation";
import { getPartner } from "@/lib/api/partner";
import { PartnerContract } from "./partner-contract";
import { PartnerGlobalStatisticsAndCommission } from "./partner-global-statistics-and-commission";
import { PartnerCouponsPerformance } from "./partner-coupons-performance";
import { PartnerLicensesPurchasedOvertime } from "./partner-licenses-purchased-overtime";
import { PartnerTable } from "./partner-table";
import { Edit } from "@interzero/oneepr-react-ui/Icon";

export function PartnerProfileModule() {
  const { id } = useParams();
  const router = useRouter();

  const { data: partner, isLoading } = useQuery({
    queryKey: ["partner", id],
    queryFn: async () => {
      if (!id) return;
      const partner = await getPartner(Number(id));
      return partner;
    },
  });

  const handleEditPartnerInfo = () => {
    router.push(`/partner-hub/${id}/edit`);
  };

  return (
    <ModuleContent>
      {/* TODO: Implement this */}
      {false && (
        <div className="py-6 px-14 bg-primary rounded-[20px] gap-2 flex items-center justify-between mb-10">
          <p className="text-white font-bold">
            This partner has request to redeem his credits! <br />
            Amount to be payed: €250 <br />
            Data to pay: 26.09
          </p>
          <div className="flex items-center gap-4">
            <Button color="yellow" size="small" variant="filled" className="min-w-44">
              Approve
            </Button>
            <Button
              color="dark-blue"
              size="small"
              variant="outlined"
              className="min-w-44 text-tertiary border-tertiary"
            >
              Deny
            </Button>
          </div>
        </div>
      )}
      <div className="flex items-start justify-between">
        <ModuleTitle
          title={!partner && isLoading ? "Loading..." : `${partner?.first_name} ${partner?.last_name}`}
          description="See partner details"
          className="w-full"
          afterTitle={
            <Button
              color="dark-blue"
              size="small"
              variant="filled"
              className="ml-auto"
              leadingIcon={<Edit />}
              onClick={handleEditPartnerInfo}
            >
              Edit partner info
            </Button>
          }
        />
      </div>
      <div className="flex flex-col gap-10 pb-7">
        <PartnerContract partner={partner} />
        <PartnerGlobalStatisticsAndCommission partner={partner} />
        <PartnerCouponsPerformance />
        <PartnerLicensesPurchasedOvertime />
        <Divider initialMarginDisabled />
        <PartnerTable />
      </div>
    </ModuleContent>
  );
}
