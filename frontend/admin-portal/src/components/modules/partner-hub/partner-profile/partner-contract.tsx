"use client";

import { Divider } from "@/components/common/divider";
import { Skeleton } from "@/components/ui/skeleton";
import { Textarea } from "@/components/ui/textarea";
import { updatePartner, updatePartnerContract } from "@/lib/api/partner";
import { Partner, UpdatePartnerContractParams } from "@/lib/api/partner/types";
import { dateManager } from "@/utils/date-manager";
import { downloadFile } from "@/utils/download-file";
import { Role } from "@/utils/user";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Add, Delete, Download, File, KeyboardArrowLeft, Upload } from "@interzero/oneepr-react-ui/Icon";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Loader2 } from "lucide-react";
import { enqueueSnackbar } from "notistack";
import { useRef, useState } from "react";

type ContractStatus = "NO_CONTRACT" | "TO_BE_SIGNED" | "DENIED" | "SIGNED";

interface ContractHeaderProps {
  status: ContractStatus;
  agreedOn?: string;
  onDownload?: () => void;
}

const ContractHeader = ({ status, agreedOn, onDownload }: ContractHeaderProps) => {
  const getStatusColor = () => {
    switch (status) {
      case "DENIED":
        return "fill-error";
      case "TO_BE_SIGNED":
        return "fill-on-surface-04";
      default:
        return "fill-primary";
    }
  };

  const getStatusBadge = () => {
    switch (status) {
      case "DENIED":
        return { bg: "bg-error-container", text: "text-error", label: "Denied" };
      case "TO_BE_SIGNED":
        return { bg: "bg-surface-04", text: "text-on-surface-04", label: "Waiting for confirmation" };
      default:
        return null;
    }
  };

  return (
    <div className="flex items-center gap-2">
      <File className={`size-20 ${getStatusColor()}`} />
      <div className="flex flex-col">
        <p className={`text-xl font-bold ${status === "TO_BE_SIGNED" ? "text-tonal-dark-cream-20" : "text-primary"}`}>
          {status === "NO_CONTRACT" ? "No Contract" : "Partnership Contract"}
        </p>
        {agreedOn ? (
          <p className="text-sm text-tonal-dark-cream-30 mt-2">
            Agreed on {dateManager(agreedOn).format("DD.MM.YYYY")}
          </p>
        ) : (
          <p className="text-sm text-tonal-dark-cream-30 mt-2">Agreed on: -</p>
        )}
      </div>
      {status === "SIGNED" && (
        <Button
          color="light-blue"
          size="small"
          variant="text"
          className="ml-auto"
          leadingIcon={<Download />}
          onClick={onDownload}
        >
          Download
        </Button>
      )}
      {getStatusBadge() && (
        <div className={`w-48 h-8 ${getStatusBadge()?.bg} rounded-lg grid place-items-center ml-auto`}>
          <p className={`${getStatusBadge()?.text} font-bold text-sm`}>{getStatusBadge()?.label}</p>
        </div>
      )}
    </div>
  );
};

interface ContractChangesProps {
  changes?: Array<{ id: number; change_description?: string; created_at: string }>;
}

const ContractChanges = ({ changes }: ContractChangesProps) => {
  if (!changes?.length) {
    return <p className="text-tonal-dark-cream-50 text-sm">No changes yet</p>;
  }

  return (
    <div className="flex flex-col gap-4">
      <p className="text-xl font-bold text-tonal-dark-cream-10">Changes agreed via e-mail</p>
      <div className="text-tonal-dark-cream-10">
        {changes.map((change, index) => (
          <p className="text-tonal-dark-cream-10 text-base mb-2" key={change.id}>
            {index + 1}. {change.change_description ?? "No description"} <br />
            <span className="text-tonal-dark-cream-50 text-sm ml-3">
              Update in {dateManager(change.created_at).format("DD.MM.YYYY")}
            </span>
          </p>
        ))}
      </div>
    </div>
  );
};

interface ChangeFormProps {
  type: "NOTE" | "FILE";
  onBack: () => void;
  onSubmit: () => void;
  isSubmitting: boolean;
  note?: string;
  setNote?: (value: string) => void;
  file?: File | null;
  setFile?: (file: File | null) => void;
}

const ChangeForm = ({ type, onBack, onSubmit, isSubmitting, note, setNote, file, setFile }: ChangeFormProps) => {
  return (
    <div className="flex flex-col items-start gap-4">
      <Button
        color="light-blue"
        size="small"
        variant="text"
        leadingIcon={<KeyboardArrowLeft className="size-6 -mt-[2px]" />}
        className="-ml-4"
        onClick={onBack}
        disabled={isSubmitting}
      >
        Back
      </Button>
      {type === "NOTE" ? (
        <>
          <Textarea
            placeholder="Add a note"
            label="Text description"
            className="w-[600px] h-40"
            onChange={(e) => setNote?.(e.target.value)}
          />
          <Button
            color="yellow"
            size="small"
            variant="filled"
            className="min-w-40"
            disabled={isSubmitting || !note}
            leadingIcon={isSubmitting ? <Loader2 className="animate-spin size-5" /> : undefined}
            onClick={onSubmit}
          >
            Save
          </Button>
        </>
      ) : (
        <>
          {file && (
            <div className="flex items-center gap-4 w-1/2">
              <File className="size-6 fill-primary" />
              <div className="flex flex-col justify-center">
                <p className="text-primary text-base font-bold">{file.name}</p>
                <p className="text-xs text-tonal-dark-cream-40 font-medium">
                  {dateManager(new Date()).format("DD.MM.YYYY")}
                </p>
              </div>
              <Button
                color="dark-blue"
                variant="text"
                size="iconMedium"
                leadingIcon={<Delete className="fill-primary" />}
                className="ml-auto"
                onClick={() => {
                  setFile?.(null);
                  onBack();
                }}
              />
            </div>
          )}
          <Button
            color="yellow"
            size="small"
            variant="filled"
            className="min-w-40"
            disabled={isSubmitting || !file}
            leadingIcon={isSubmitting ? <Loader2 className="animate-spin size-5" /> : undefined}
            onClick={onSubmit}
          >
            Save
          </Button>
        </>
      )}
    </div>
  );
};

export function PartnerContract({ partner }: { partner?: Partner }) {
  const queryClient = useQueryClient();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isCreatingChange, setIsCreatingChange] = useState(false);
  const [changeType, setChangeType] = useState<"NOTE" | "FILE" | null>(null);
  const [note, setNote] = useState("");
  const [file, setFile] = useState<File | null>(null);

  const getContractStatus = (): ContractStatus => {
    if (!partner?.partner_contract) return "NO_CONTRACT";
    return partner.partner_contract.status as ContractStatus;
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newFile = e.target.files?.[0];
    if (newFile) {
      setFile(newFile);
      if (getContractStatus() === "NO_CONTRACT") {
        createPartnerContract(newFile);
      } else if (getContractStatus() === "DENIED") {
        updateContract({
          file: newFile,
          change_type: "FILE",
          contract_id: partner?.partner_contract?.id as number,
        });
      } else {
        setChangeType("FILE");
        setIsCreatingChange(true);
      }
    }
  };

  const { mutate: createPartnerContract, isPending: isCreatingPartnerContract } = useMutation({
    mutationFn: async (file: File) => {
      if (!partner?.id) return;
      const formData = new FormData();
      formData.append("file", file);
      const response = await updatePartner(partner?.id, {
        contract_file: file,
      });
      return response.data;
    },
    onSuccess: () => {
      enqueueSnackbar("Contract updated successfully", { variant: "success" });
    },
    onError: () => {
      enqueueSnackbar("Failed to update contract", { variant: "error" });
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["partner"] });
      queryClient.refetchQueries({ queryKey: ["partner"] });
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
      setIsCreatingChange(false);
      setChangeType(null);
      setNote("");
      setFile(null);
    },
  });

  const { mutate: updateContract, isPending: isUpdatingPartnerContract } = useMutation({
    mutationFn: async (params: UpdatePartnerContractParams) => {
      if (!partner?.id || !partner?.partner_contract?.id) return;
      const response = await updatePartnerContract(partner.id, partner.partner_contract.id, params);
      return response.data;
    },
    onSuccess: () => {
      enqueueSnackbar("Contract updated successfully", { variant: "success" });
    },
    onError: () => {
      enqueueSnackbar("Failed to update contract", { variant: "error" });
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["partner"] });
      queryClient.refetchQueries({ queryKey: ["partner"] });
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
      setIsCreatingChange(false);
      setChangeType(null);
      setNote("");
      setFile(null);
    },
  });

  const handleSubmitChange = () => {
    if (!partner?.partner_contract?.id) return;

    if (changeType === "NOTE" && note) {
      updateContract({
        change_type: "NOTE",
        contract_id: partner.partner_contract.id,
        change_description: note,
      });
    } else if (changeType === "FILE" && file) {
      updateContract({
        change_type: "EMAIL",
        contract_id: partner.partner_contract.id,
        change_description: "Email sent to partner",
        file,
      });
    }
  };

  const status = getContractStatus();

  if (!partner) {
    return (
      <div className="w-full rounded-[40px] bg-tonal-dark-blue-96 p-10 gap-6 flex flex-col">
        <Skeleton className="w-32 h-8" />

        <div className="flex items-center gap-2">
          <Skeleton className="size-20 rounded-md" />
          <div className="flex flex-col gap-2">
            <Skeleton className="w-48 h-6" />
            <Skeleton className="w-32 h-4" />
          </div>
        </div>

        <Divider initialMarginDisabled />

        <div className="flex flex-col gap-4">
          <Skeleton className="w-64 h-6" />
          <div className="flex flex-col gap-2">
            <Skeleton className="w-full h-8" />
            <Skeleton className="w-full h-8" />
            <Skeleton className="w-full h-8" />
          </div>
        </div>

        <Divider initialMarginDisabled />

        <div className="flex items-center gap-2">
          <Skeleton className="w-32 h-8" />
        </div>
      </div>
    );
  }

  return (
    <div className="w-full rounded-[40px] bg-tonal-dark-blue-96 p-10 gap-6 flex flex-col">
      <p className="text-primary text-[28px] font-bold">Contract</p>

      <ContractHeader
        status={status}
        agreedOn={partner?.partner_contract?.agreed_on}
        onDownload={() => {
          if (!partner?.partner_contract?.files) return;
          const file = partner.partner_contract.files.sort((a, b) => b.created_at.localeCompare(a.created_at))[0];
          if (!file) return;
          downloadFile({
            userId: partner?.id,
            userRole: Role.PARTNER,
            fileId: file.id,
            fileName: file.name,
          });
        }}
      />

      {status !== "NO_CONTRACT" && <Divider initialMarginDisabled />}

      {status === "NO_CONTRACT" ? (
        <div className="flex items-center gap-2">
          <input type="file" ref={fileInputRef} className="hidden" onChange={handleFileChange} />
          <Button
            color="light-blue"
            size="small"
            variant="text"
            leadingIcon={isCreatingPartnerContract ? <Loader2 className="animate-spin size-5" /> : <Upload />}
            onClick={() => fileInputRef.current?.click()}
            disabled={isCreatingPartnerContract}
          >
            Upload contract
          </Button>
        </div>
      ) : (
        <>
          <ContractChanges changes={partner?.partner_contract?.changes} />
          {partner?.partner_contract?.changes?.length ? <Divider initialMarginDisabled /> : null}

          {isCreatingChange ? (
            <ChangeForm
              type={changeType as "NOTE" | "FILE"}
              onBack={() => {
                setIsCreatingChange(false);
                setChangeType(null);
                setFile(null);
              }}
              onSubmit={handleSubmitChange}
              isSubmitting={isUpdatingPartnerContract}
              note={note}
              setNote={setNote}
              file={file}
              setFile={setFile}
            />
          ) : (
            status !== "DENIED" && (
              <div className="flex items-center gap-2">
                <input type="file" ref={fileInputRef} className="hidden" onChange={handleFileChange} />
                <Button
                  color="light-blue"
                  size="small"
                  variant="text"
                  leadingIcon={<Upload />}
                  onClick={() => fileInputRef.current?.click()}
                >
                  Attach mail file
                </Button>
                <p className="text-primary text-sm">or</p>
                <Button
                  color="light-blue"
                  size="small"
                  variant="text"
                  leadingIcon={<Add />}
                  onClick={() => {
                    setChangeType("NOTE");
                    setIsCreatingChange(true);
                  }}
                >
                  Add note
                </Button>
              </div>
            )
          )}
        </>
      )}
    </div>
  );
}
