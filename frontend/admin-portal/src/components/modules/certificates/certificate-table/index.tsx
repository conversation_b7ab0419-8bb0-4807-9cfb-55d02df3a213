"use client";

import { useEffect, useState } from "react";
import { createColumnHelper } from "@tanstack/react-table";
import { MoreVerticalIcon, RefreshCcwIcon } from "lucide-react";

import { Delete, Download, FilterAlt, KeyboardArrowDown, SortDown } from "@interzero/oneepr-react-ui/Icon";
import { DatatableSearch } from "@/components/common/datatable/datatable-search";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Dropdown, DropdownItem } from "@/components/ui/dropdown";
import { Datatable } from "@/components/common/datatable";
import { CheckboxInput } from "@/components/ui/checkbox";
import { YEARS } from "@/utils/get-years";
import { useDateRangeFilter } from "@/hooks/use-date-range-filter";
import { dateManager } from "@/utils/date-manager";
import { DateRangeFilter } from "@/components/common/date-range-filter";

import { ReuploadCertificateDialog } from "../reupload-certificate-dialog";
import { DeleteCertificateDialog } from "../delete-certificate-dialog";

interface CertificateData {
  id: string;
  company_id: string;
  company_name: string;
  registration_number: string;
  certificate_year: number;
  certificate_file_name: string;
  status: "VALID" | "INVALID";
  created_at: string;
}

const mockData: CertificateData[] = Array.from({ length: 40 }).map((_, idx) => ({
  id: (idx + 1).toString(),
  company_id: "1411",
  company_name: "Lauter & Co.",
  registration_number: "DE1714028249764",
  certificate_year: 2023,
  certificate_file_name: "DE17140228249764 Participation Certificate",
  status: idx === 2 ? "INVALID" : "VALID",
  created_at: "2023.01.01",
}));

const columnHelper = createColumnHelper<CertificateData>();

export function CertificateTable() {
  const { selectedMonth, selectedYear, endDate, setSelectedYear, setSelectedMonth } = useDateRangeFilter();

  const [data] = useState<CertificateData[]>(mockData);
  const [filteredData, setFilteredData] = useState<CertificateData[]>(data);
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    let filtered = [...data];

    // Filter by search term
    if (searchTerm.trim()) {
      const term = searchTerm.toLowerCase().trim();
      filtered = filtered.filter((item) => item.company_name.toLowerCase().includes(term));
    }

    // Filter by date range
    filtered = filtered.filter((item) => {
      // Convert DD.MM.YYYY to Date object
      const [day, month, year] = item.created_at.split(".");
      const itemDate = dateManager(`${year}-${month}-${day}`);

      const startFilterDate = dateManager()
        .setYear(parseInt(selectedYear.value))
        .setMonth(parseInt(selectedMonth.value) - 1)
        .startOf("month");
      const endFilterDate = dateManager()
        .setYear(parseInt(endDate.year.value))
        .setMonth(parseInt(endDate.month.value) - 1)
        .endOf("month");

      return itemDate.toDate() >= startFilterDate.toDate() && itemDate.toDate() <= endFilterDate.toDate();
    });

    setFilteredData(filtered);

    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, selectedMonth, selectedYear, searchTerm]);

  const columns = [
    columnHelper.accessor("id", {
      header: ({ table }) => (
        <CheckboxInput
          checked={table.getIsAllPageRowsSelected() || table.getIsSomePageRowsSelected()}
          onChange={(e) => table.toggleAllPageRowsSelected(!!e.target.checked)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <CheckboxInput
          checked={row.getIsSelected()}
          onChange={(e) => row.toggleSelected(!!e.target.checked)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    }),
    columnHelper.accessor("company_id", {
      header: "Comp. ID",
    }),
    columnHelper.accessor("company_name", {
      header: ({ column }) => {
        return (
          <Button
            type="button"
            variant="text"
            size="small"
            color="gray"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Company Name <SortDown className="ml-2 h-4 w-4 fill-tonal-dark-cream-40" />
          </Button>
        );
      },
      cell: (info) => {
        return <span className="pl-2 text-base">{info.getValue()}</span>;
      },
    }),
    columnHelper.accessor("registration_number", {
      header: ({ column }) => {
        return (
          <Button
            type="button"
            variant="text"
            size="small"
            color="gray"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Reg. Nr. <SortDown className="ml-2 h-4 w-4 fill-tonal-dark-cream-40" />
          </Button>
        );
      },
      cell: (info) => {
        return <span className="pl-2 text-base">{info.getValue()}</span>;
      },
    }),
    columnHelper.accessor("certificate_year", {
      header: ({ column }) => {
        return (
          <Button
            type="button"
            variant="text"
            size="small"
            color="gray"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="text-left"
          >
            Certificate <br /> Year <SortDown className="ml-2 h-4 w-4 fill-tonal-dark-cream-40" />
          </Button>
        );
      },
      cell: (info) => {
        return <span className="pl-2 text-base">{info.getValue()}</span>;
      },
    }),
    columnHelper.accessor("certificate_file_name", {
      header: ({ column }) => {
        return (
          <Button
            type="button"
            variant="text"
            size="small"
            color="gray"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="text-left"
          >
            Certificate <br /> file name <SortDown className="ml-2 h-4 w-4 fill-tonal-dark-cream-40" />
          </Button>
        );
      },
      cell: (info) => {
        return (
          <div className="pl-2 truncate max-w-[132px]">
            <span className="truncate text-base">{info.getValue()}</span>
          </div>
        );
      },
    }),
    columnHelper.accessor("status", {
      header: "Status",
      cell: (info) => {
        const certificateId = info.row.original.id;
        const certificateStatus = info.row.original.status;

        return (
          <div className="flex items-center justify-between gap-2">
            <div data-status={info.getValue()} className="group flex items-center gap-1">
              <div className="size-2 rounded-full bg-primary group-data-[status=VALID]:bg-success group-data-[status=INVALID]:bg-tonal-dark-cream-30" />
              <strong className="truncate capitalize text-sm font-bold text-primary group-data-[status=VALID]:text-success group-data-[status=INVALID]:text-tonal-dark-cream-30">
                {info.getValue().toLowerCase()}
              </strong>
            </div>
            <Dropdown
              trigger={
                <button className="flex items-center justify-center rounded-full size-6 text-primary hover:bg-tonal-dark-cream-70 transition-colors">
                  <MoreVerticalIcon className="size-4" />
                </button>
              }
            >
              {certificateStatus === "VALID" && (
                <DropdownItem className="flex flex-shrink-0 items-center gap-4 text-primary hover:bg-support-blue/10 py-5 px-4 outline-none text-base hover:cursor-pointer">
                  <Download className="size-4 fill-primary" /> Download
                </DropdownItem>
              )}
              <DropdownItem asChild>
                <ReuploadCertificateDialog certificateId={certificateId}>
                  <button className="flex flex-shrink-0 items-center gap-4 w-full text-primary hover:bg-support-blue/10 py-5 px-4 outline-none text-base hover:cursor-pointer">
                    <RefreshCcwIcon className="size-4 text-primary" /> Reupload
                  </button>
                </ReuploadCertificateDialog>
              </DropdownItem>
              {certificateStatus === "VALID" && (
                <DropdownItem asChild>
                  <DeleteCertificateDialog certificateId={certificateId}>
                    <button className="flex flex-shrink-0 items-center gap-4 w-full text-primary hover:bg-support-blue/10 py-5 px-4 outline-none text-base hover:cursor-pointer">
                      <Delete className="size-4 fill-primary" /> Delete
                    </button>
                  </DeleteCertificateDialog>
                </DropdownItem>
              )}
            </Dropdown>
          </div>
        );
      },
    }),
  ];

  const yearsFilter = YEARS.filter((y) => Number(y.value) <= new Date().getFullYear()).reverse();

  return (
    <div className="flex flex-col gap-6 bg-cream rounded-3xl">
      <div className="px-8 flex items-center justify-between gap-4">
        <div className="relative flex-1 max-w-[320px]">
          <DatatableSearch onSearch={setSearchTerm} defaultValue={searchTerm} placeholder="Search" />
        </div>
        <div className="flex items-center gap-6">
          <Dropdown
            trigger={
              <button className="flex items-center text-support-blue font-bold text-base">
                <FilterAlt width={20} height={20} className="fill-support-blue" />
                <span className="ml-1 mr-2 mt-1 text-left">{selectedYear.label}</span>
                <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
              </button>
            }
          >
            {yearsFilter.map((filter, idx) => (
              <DropdownItem
                key={idx}
                onClick={() => setSelectedYear(filter)}
                className="flex items-center gap-2 text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
              >
                {filter.label}
              </DropdownItem>
            ))}
          </Dropdown>
          <div className="h-4 border w-px border-tonal-dark-cream-60" />
          <DateRangeFilter
            endDate={endDate}
            selectedMonth={selectedMonth}
            selectedYear={selectedYear}
            onMonthChange={setSelectedMonth}
            onYearChange={setSelectedYear}
          />
        </div>
      </div>
      <div className="px-8 pb-8">
        <div className="bg-gray-50 rounded-xl overflow-hidden">
          <Datatable columns={columns} data={filteredData} />
        </div>
      </div>
    </div>
  );
}
