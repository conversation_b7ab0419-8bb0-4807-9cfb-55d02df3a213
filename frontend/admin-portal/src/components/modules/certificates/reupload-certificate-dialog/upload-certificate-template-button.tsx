"use client";

import { useCallback } from "react";
import { useDropzone, type FileWithPath } from "react-dropzone";

import { Button } from "@interzero/oneepr-react-ui/Button";
import { Delete, Error, File, Upload } from "@interzero/oneepr-react-ui/Icon";

interface UploadCertificateTemplateButtonProps {
  selectedFile?: FileWithPath;
  setSelectedFile: (file?: FileWithPath) => void;
}

export function UploadCertificateTemplateButton(props: UploadCertificateTemplateButtonProps) {
  const { selectedFile, setSelectedFile } = props;

  const onDrop = useCallback((acceptedFiles: FileWithPath[]) => {
    const file = acceptedFiles[0];
    setSelectedFile(undefined);
    setSelectedFile(file);

    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "application/pdf": [".pdf"],
    },
    // disabled: isDisabled,
    maxFiles: 1,
    multiple: false,
    // preventDropOnDocument: true,
  });

  if (!selectedFile) {
    return (
      <div {...getRootProps()} className="inline-flex">
        <input {...getInputProps()} />
        <Button
          type="button"
          size="small"
          variant="text"
          color="light-blue"
          leadingIcon={<Upload />}
          disabled={isDragActive}
        >
          Upload Document template
        </Button>
      </div>
    );
  }

  const isError = selectedFile.type !== "application/pdf";

  return (
    <div data-error={isError} className="group">
      <div className="flex items-start gap-1">
        <File className="fill-primary group-data-[error=true]:fill-error size-6" />
        <div className="flex flex-1 flex-col gap-1 text-primary group-data-[error=true]:text-error">
          <div className="flex items-center gap-3">
            <p>{selectedFile.name}</p>
            {isError && <Error className="fill-error size-6" />}
          </div>
          {isError && <span className="text-xs font-bold">Supported files PDF.</span>}
        </div>
        <Button variant="text" color="dark-blue" size="iconSmall" onClick={() => setSelectedFile(undefined)}>
          <Delete className="fill-primary size-6" />
        </Button>
      </div>
    </div>
  );
}
