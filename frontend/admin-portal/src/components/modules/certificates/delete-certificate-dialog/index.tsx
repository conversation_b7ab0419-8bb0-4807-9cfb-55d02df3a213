"use client";

import { useState } from "react";
import { enqueueSnackbar } from "notistack";
import Image from "next/image";

import { useMutation } from "@tanstack/react-query";
import { Button } from "@interzero/oneepr-react-ui/Button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { queryClient } from "@/lib/react-query";

interface DeleteCertificateDialogProps {
  certificateId: string | number;
  children: React.ReactNode;
}

export function DeleteCertificateDialog({ certificateId, children }: DeleteCertificateDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isDone, setIsDone] = useState(false);

  const { mutate, isPending } = useMutation({
    mutationFn: () => {
      // return deleteCertificate(certificateId)
      return new Promise((resolve) => setTimeout(resolve, 3000));
    },
    onSuccess: () => {
      queryClient.refetchQueries({ queryKey: ["certificate", certificateId] });
      setIsDone(true);
    },
    onError: (error) => {
      enqueueSnackbar(error.message, { variant: "error" });
    },
  });

  async function handleDelete() {
    mutate();
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>

      {!isDone && (
        <DialogContent className="bg-surface-02 py-9 max-w-2xl">
          <DialogHeader>
            <DialogTitle className="font-bold sm:text-[1.75rem]">Delete certificate?</DialogTitle>
            <DialogDescription className="text-tonal-dark-cream-20 font-normal">
              By clicking on ”confirm” you are deleting this certificate for this broker. Are you sure you want to do
              this action?{" "}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <DialogClose asChild>
              <Button type="button" variant="outlined" color="dark-blue" size="medium">
                Back
              </Button>
            </DialogClose>
            <Button variant="filled" color="red" size="medium" onClick={handleDelete} disabled={isPending}>
              {isPending ? "Deleting..." : "Confirm"}
            </Button>
          </DialogFooter>
        </DialogContent>
      )}

      {isDone && (
        <DialogContent className="bg-surface-02 py-9 max-w-2xl">
          <div className="flex items-center gap-2 mb-10">
            <Image src="/assets/images/leaf-seal.svg" alt="leaf seal" width={36} height={36} className="size-9" />
            <h3 className="font-bold text-[1.75rem] text-primary">Certificate deleted with success</h3>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button type="button" variant="filled" color="yellow" size="medium">
                Close
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      )}
    </Dialog>
  );
}
