/* eslint-disable no-console */
/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";
import { CONTAINS_LETTER_REGEX, CONTAINS_NON_ALPHANUMERIC_REGEX, CONTAINS_NUMBER_REGEX } from "@/utils/regex";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { CheckCircle, Visibility, VisibilityOff } from "@interzero/oneepr-react-ui/Icon";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { zodResolver } from "@hookform/resolvers/zod";
import { signIn } from "next-auth/react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const loginFormSchema = z.object({
  email: z
    .string({
      required_error: "E-mail Invalid",
    })
    .email({ message: "E-mail Invalid" })
    .min(1, { message: "E-mail Invalid" }),
  password: z
    .string({
      required_error: "translations.password.error",
    })
    .regex(CONTAINS_LETTER_REGEX, "Enter a letter")
    .regex(CONTAINS_NUMBER_REGEX, "Enter a number")
    .regex(CONTAINS_NON_ALPHANUMERIC_REGEX, "Enter a special character"),
});

type LoginFormData = z.infer<typeof loginFormSchema>;

export function LoginForm() {
  const [seePassword, setSeePassword] = useState(false);
  const [isAccountValid, setIsAccountValid] = useState(false);

  const router = useRouter();

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors, isSubmitting, isValid },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginFormSchema),
    mode: `onBlur`,
  });

  async function submit(data: LoginFormData) {
    try {
      const response = await signIn("credentials", {
        email: data.email,
        password: data.password,
        redirect: false,
      });

      if (!response || response.error) {
        setError("email", { message: "Invalid email or password" });
        setError("password", { message: "Invalid email or password" });

        return;
      }

      setIsAccountValid(true);

      router.push("/en/dashboard");
    } catch (error) {
      alert("Error");
    }
  }

  const HandlePassword = () => {
    switch (seePassword) {
      case true:
        return <Visibility onClick={() => setSeePassword(!seePassword)} width={20} />;
      case false:
        return <VisibilityOff onClick={() => setSeePassword(!seePassword)} width={20} />;
      default:
        return <Visibility onClick={() => setSeePassword(!seePassword)} width={20} />;
    }
  };

  return (
    <>
      <form onSubmit={handleSubmit(submit)}>
        <div className="flex -mx-3">
          <div className="w-full px-3 mb-5">
            <div className="flex">
              <Input
                label="E-mail"
                placeholder="Enter your email"
                type="email"
                variant={errors.email ? "error" : "enabled"}
                {...register("email", { required: true })}
                rightIcon={isAccountValid && <CheckCircle width={20} height={20} className="fill-tonal-green-40" />}
                errorMessage={errors.email && errors.email.message}
              />
            </div>
          </div>
        </div>
        <div className="flex -mx-3">
          <div className="w-full px-3 mb-12">
            <div className="flex">
              <Input
                label="Password"
                placeholder="Enter your password"
                type={seePassword ? "text" : "password"}
                {...register("password", { required: true })}
                rightIcon={<HandlePassword />}
                variant={errors.password ? "error" : "enabled"}
                errorMessage={errors.password && errors.password.message}
              />
            </div>
            <Link
              href="/[locale]/auth/forgot-password"
              as={`/en/auth/forgot-password`}
              className="block text-sm cursor-pointer font-medium text-tonal-blue-40 mt-2 hover:underline underline-offset-2"
            >
              Forgot password?
            </Link>
          </div>
        </div>
        <div className="flex -mx-3">
          <div className="w-full px-3 mb-3">
            <Button
              variant="filled"
              size="medium"
              color="yellow"
              disabled={!isValid || isSubmitting}
              className="w-full"
            >
              {isSubmitting ? "Loading..." : "Login"}
            </Button>
          </div>
        </div>
      </form>
    </>
  );
}
