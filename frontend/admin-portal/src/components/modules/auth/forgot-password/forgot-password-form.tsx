/* eslint-disable no-console */
/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import Link from "next/link";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { requestPasswordReset } from "@/lib/api/auth";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Visibility, VisibilityOff } from "@interzero/oneepr-react-ui/Icon";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { zodResolver } from "@hookform/resolvers/zod";
import { SuccessResetPassword } from "./success-reset-password";

const forgotPasswordFormSchema = z.object({
  email: z
    .string({
      required_error: "E-mail Invalid",
    })
    .email({ message: "E-mail Invalid" })
    .min(1, { message: "E-mail Invalid" }),
});

type ForgotPasswordFormData = z.infer<typeof forgotPasswordFormSchema>;

export function ForgotPasswordForm() {
  const [seePassword, setSeePassword] = useState(false);
  const [recover, setRecover] = useState(false);

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors, isValid, isSubmitting },
  } = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordFormSchema),
    mode: `onBlur`,
  });

  async function submit(data: ForgotPasswordFormData) {
    const baseUrl = window.location.origin;

    try {
      const res = await requestPasswordReset(data.email, `${baseUrl}/en/auth/recover-password`);

      if (res.error || res.response?.status === 404) {
        setError("email", {
          message: "The email is not registered",
        });
        return;
      } else if (res.response?.status === 500) {
        setError("email", {
          message: res.response.statusText,
        });
        return;
      }

      setRecover(true);
    } catch (error: unknown) {}
  }

  const HandlePassword = () => {
    switch (seePassword) {
      case true:
        return <Visibility onClick={() => setSeePassword(!seePassword)} width={20} />;
      case false:
        return <VisibilityOff onClick={() => setSeePassword(!seePassword)} width={20} />;
      default:
        return <Visibility onClick={() => setSeePassword(!seePassword)} width={20} />;
    }
  };

  if (recover) return <SuccessResetPassword />;

  return (
    <>
      <div className="text-left text-primary mb-10">
        <h1 className="text-4xl font-bold mb-4">Password recovery</h1>
        <p className="font-light">Please enter the e-mail you’re using for your account</p>
      </div>
      <form onSubmit={handleSubmit(submit)}>
        <div className="flex -mx-3">
          <div className="w-full px-3 mb-12">
            <div className="flex">
              <Input
                label="E-mail"
                placeholder="Enter your email"
                type="email"
                variant={errors.email ? "error" : "enabled"}
                {...register("email", { required: true })}
                errorMessage={errors.email && errors.email.message}
              />
            </div>
          </div>
        </div>
        <div className="flex -mx-3">
          <div className="w-full px-3 mb-3">
            <Button
              variant="filled"
              size="medium"
              color="yellow"
              disabled={!isValid || isSubmitting}
              className="w-full"
            >
              {isSubmitting ? "Loading..." : "Continue"}
            </Button>
            <Link
              href="/[locale]/auth/login"
              as={`/en/auth/login`}
              className="block text-center text-sm cursor-pointer font-medium text-tonal-blue-40 mt-4 hover:underline underline-offset-2"
            >
              Back to login
            </Link>
          </div>
        </div>
      </form>
    </>
  );
}
