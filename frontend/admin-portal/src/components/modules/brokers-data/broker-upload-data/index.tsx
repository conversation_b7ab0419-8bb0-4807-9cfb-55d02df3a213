"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger } from "@/components/ui/tabs";
import { checkFileBroker } from "@/lib/api/broker-file";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Download, File, Upload } from "@interzero/oneepr-react-ui/Icon";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { ChevronLeftIcon } from "lucide-react";
import { enqueueSnackbar } from "notistack";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { CgSpinnerAlt } from "react-icons/cg";
import { z } from "zod";
import { BrokerUploadCompanyTable } from "../broker-upload-table/broker-upload-company-table";
import { BrokerUploadOrderTable } from "../broker-upload-table/broker-upload-order-table";
import { <PERSON><PERSON><PERSON>UploadSubmit } from "../broker-upload-table/broker-upload-submit";

const UPLOAD_TYPE = [
  {
    label: "Order data",
    value: "order_data",
  },
];
const ENCODING_TYPE = [
  {
    label: "UTF-8",
    value: "UTF_8",
  },
];

const UploadDataSchema = z.object({
  file_name: z.string().min(1, { message: "File is required" }),
  upload_type: z.string().min(1, { message: "Upload Type is required" }),
  encoding: z.string().min(1, { message: "Encoding is required" }),
});

type UploadDataForm = z.infer<typeof UploadDataSchema>;

const initialData: Partial<UploadDataForm> = {
  encoding: ENCODING_TYPE[0].value,
  upload_type: UPLOAD_TYPE[0].value,
};

export function BrokerUploadData() {
  const {
    formState: { errors },
    setValue,
  } = useForm<UploadDataForm>({
    resolver: zodResolver(UploadDataSchema),
    defaultValues: initialData,
  });
  const [memoryFile, setMemoryFile] = useState<File | null>(null);

  const {
    data: mutateData,
    mutate: checkFileMutate,
    isPending: isCheckingFileMutate,
    reset,
  } = useMutation({
    mutationFn: (file: File) => checkFileBroker(file),
  });

  const handleOnInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setMemoryFile(file);
    handleUploadSheetFile(file);
  };

  function handleOnDragOver(e: React.DragEvent<HTMLDivElement>) {
    e.preventDefault();
    e.stopPropagation();
  }

  async function handleOnDrop(e: React.DragEvent<HTMLDivElement>) {
    e.preventDefault();
    e.stopPropagation();

    const file = e.dataTransfer.files[0];

    setMemoryFile(file);
    handleUploadSheetFile(file);
  }

  async function handleUploadSheetFile(file: File) {
    if (!file) return;

    if (!file.name.endsWith(".xls") && !file.name.endsWith(".xlsx")) return;

    checkFileMutate(file, {
      onSuccess: () => {
        enqueueSnackbar("File checking successfully", { variant: "success" });
        setValue(`file_name`, file.name);
      },
      onError: () => {
        setMemoryFile(null);
        enqueueSnackbar("Error checking file", { variant: "error" });
      },
    });
  }

  if (mutateData)
    return (
      <div>
        <Button
          variant="text"
          color="light-blue"
          size="small"
          leadingIcon={<ChevronLeftIcon className="size-5" />}
          className="mb-10"
          onClick={reset}
        >
          Back to upload
        </Button>
        <Tabs defaultValue="company-data" className="w-full flex-1">
          <TabsList className="w-full grid grid-cols-2 pt-6 pb-4 gap-0">
            <TabsTrigger
              className="px-10 !bg-[transparent] !text-primary border-b-2 border-tonal-dark-cream-80 data-[state=active]:border-primary opacity-40 data-[state=active]:opacity-100 rounded-none"
              value="company-data"
            >
              Company data
            </TabsTrigger>
            <TabsTrigger
              className="px-10 !bg-[transparent] !text-primary border-b-2 border-tonal-dark-cream-80 data-[state=active]:border-primary opacity-40 data-[state=active]:opacity-100 rounded-none"
              value="order-data"
            >
              Order data
            </TabsTrigger>
          </TabsList>
          <TabsContent value="company-data">
            <BrokerUploadCompanyTable data={mutateData.company} />
          </TabsContent>
          <TabsContent value="order-data">
            <BrokerUploadOrderTable data={mutateData.order} />
          </TabsContent>
        </Tabs>
        <BrokerUploadSubmit onClickCancel={reset} data={mutateData} originalFile={memoryFile} />
      </div>
    );

  return (
    <div className="flex flex-col gap-10">
      <div className="bg-white rounded-3xl p-8">
        {/* <div>
          <div className="flex justify-start gap-6">
            <div className="flex flex-col gap-7 w-2/5">
              <div>
                <p className="mb-2 text-primary">Upload type</p>
                <Controller
                  name="upload_type"
                  control={control}
                  render={({ field }) => (
                    <Select value={field.value} onValueChange={(value) => field.onChange(value)}>
                      <SelectTrigger id="status" className="w-full disabled:bg-[#BEBDBB61] disabled:text-[#8C8A87]">
                        <SelectValue placeholder="Select a upload type" />
                      </SelectTrigger>
                      <SelectContent>
                        {UPLOAD_TYPE.map(({ value, label }) => (
                          <SelectItem key={value} value={value} className="hover:!bg-support-blue/10 transition-colors">
                            {label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
              </div>
              <div>
                <p className="mb-2 text-primary">Broker</p>
                <div className="bg-white rounded-2xl py-3 px-4 flex items-center gap-4 border border-tonal-cream-90">
                  <input
                    value={searchBroker}
                    onChange={(event) => setSearchBroker(event.target.value)}
                    placeholder="Select"
                    className="outline-none text-primary flex-1"
                  />
                  <Search width={24} className="fill-primary flex-none" />
                </div>
              </div>
            </div>
            <div className="w-1/5">
              <p className="mb-2 text-primary">Encoding</p>
              <Controller
                name="encoding"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={(value) => field.onChange(value)}>
                    <SelectTrigger id="status" className="w-full disabled:bg-[#BEBDBB61] disabled:text-[#8C8A87]">
                      <SelectValue placeholder="Select a encoding" />
                    </SelectTrigger>
                    <SelectContent>
                      {ENCODING_TYPE.map(({ value, label }) => (
                        <SelectItem key={value} value={value} className="hover:!bg-support-blue/10 transition-colors">
                          {label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
            </div>
          </div>
        </div>

        <Divider initialMarginDisabled className="my-7" /> */}
        {isCheckingFileMutate ? (
          <div className="flex items-center justify-center w-full p-8 gap-4 rounded-2xl border-2 border-dashed bg-tonal-dark-cream-80  border-tonal-dark-cream-60">
            <CgSpinnerAlt className="size-10 animate-spin" />
          </div>
        ) : (
          <div>
            <input
              type="file"
              className="hidden"
              accept=".xls,.xlsx"
              onChange={handleOnInputChange}
              id="sheet-file-input"
            />
            <div
              data-error={!!errors.file_name}
              className="group flex items-center justify-center flex-col w-full p-8 gap-4 rounded-2xl border-2 border-dashed cursor-pointer bg-tonal-dark-blue-90  border-support-blue data-[error=true]:border-error data-[error=true]:bg-on-error"
              onClick={() => document.getElementById("sheet-file-input")?.click()}
              onDragOver={handleOnDragOver}
              onDrop={handleOnDrop}
            >
              <Upload className="size-12 fill-support-blue group-data-[error=true]:fill-error" />
              <p className="text-primary group-data-[error=true]:text-error">
                Drag and drop Excel file or{" "}
                <span className="underline underline-offset-4 text-support-blue group-data-[error=true]:text-error">
                  click here
                </span>
                .
              </p>
            </div>
          </div>
        )}
      </div>
      <div className="rounded-3xl p-8 bg-white flex flex-col gap-4">
        <p className="text-primary text-xl font-bold">Templates</p>
        <div className="py-5 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <File className="fill-tonal-dark-green-30 size-12" />
            <p className="font-bold text-tonal-dark-cream-10">order_import_template</p>
          </div>
          <Button
            variant="text"
            color="light-blue"
            size="small"
            leadingIcon={<Download />}
            className="ml-auto"
            disabled
          >
            Download
          </Button>
        </div>
      </div>
    </div>
  );
}
