"use client";

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Toolt<PERSON>Trigger } from "@/components/ui/tooltip";
import {
  CheckCompanyExcel,
  CheckCompanyExcelErrors,
  CheckExcelOrder,
  CheckOrderExcelErrors,
} from "@/lib/api/broker-file/types";
import { Error } from "@interzero/oneepr-react-ui/Icon";
import { CellContext } from "@tanstack/react-table";

export type InfoTable = CellContext<CheckCompanyExcel | CheckExcelOrder, string | null>;

function isCompanyRow(row: CheckCompanyExcel | CheckExcelOrder): row is CheckCompanyExcel {
  return "contact_email" in row;
}

function isCompanyErrors(
  errors: CheckCompanyExcelErrors | CheckOrderExcelErrors | undefined
): errors is CheckCompanyExcelErrors {
  return !!errors && ("contact_name" in errors || "contact_email" in errors);
}

export function RenderInfoTable({ info }: { info: InfoTable }) {
  const nameColumn = info.column.id;
  const rowOriginal = info.row.original;
  const errors = rowOriginal.errors;
  const isContactRow = nameColumn === "contact_name";

  let errorMsg: string | undefined;

  if (isCompanyErrors(errors)) {
    errorMsg =
      errors[nameColumn as keyof CheckCompanyExcelErrors] ??
      (isContactRow ? errors["contact_name"] || errors["contact_email"] : undefined);
  } else if (errors) {
    errorMsg = errors[nameColumn as keyof CheckOrderExcelErrors];
  }

  const contactEmail = isCompanyRow(rowOriginal) ? rowOriginal.contact_email : null;

  return (
    <div data-error={!!errorMsg} className="data-[error=true]:text-error text-primary flex items-center gap-2">
      <div>
        <p>{info.getValue() || "----"}</p>
        {isContactRow && contactEmail && <p className="text-xs text-[#656773]">{contactEmail}</p>}
      </div>

      {!!errorMsg && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <Error className="size-5 fill-error transition-all duration-300" />
            </TooltipTrigger>
            <TooltipContent className="bg-error text-white">
              <p>{errorMsg}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
    </div>
  );
}
