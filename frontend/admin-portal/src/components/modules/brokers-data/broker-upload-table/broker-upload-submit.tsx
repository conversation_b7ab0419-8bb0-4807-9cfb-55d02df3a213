import { createFileBroker } from "@/lib/api/broker-file";
import { CheckFileBrokerResponse } from "@/lib/api/broker-file/types";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { useMutation } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { enqueueSnackbar } from "notistack";

export function BrokerUploadSubmit({
  onClickCancel,
  data: { company, order },
  originalFile,
}: {
  onClickCancel?: () => void;
  data: CheckFileBrokerResponse;
  originalFile: File | null;
}) {
  const session = useSession();

  const { mutate: mutateCreate, isPending: isMutateCreating } = useMutation({
    mutationFn: (data: CheckFileBrokerResponse) => {
      if (!originalFile) throw new Error();

      return createFileBroker(session.data!.user.id, data, originalFile);
    },
  });

  const isThereError =
    company.some((item) => Object.keys(item.errors || {}).length) ||
    order.some((item) => Object.keys(item.errors || {}).length);

  async function handleSubmit() {
    mutateCreate(
      { company, order },
      {
        onSuccess: () => {
          enqueueSnackbar("Data created successfully", { variant: "success" });
          onClickCancel?.();
        },
        onError: () => {
          enqueueSnackbar("Error creating data", { variant: "error" });
        },
      }
    );
  }

  return (
    <div className="w-full flex flex-col justify-end items-end gap-6 mt-7">
      <div className="w-full flex justify-end items-center gap-10">
        <Button variant="text" color="dark-blue" size="small" onClick={() => onClickCancel?.()}>
          Cancel
        </Button>
        <Button
          variant="filled"
          color={isThereError ? "red" : "yellow"}
          size="small"
          onClick={() => !isThereError && handleSubmit()}
          disabled={isMutateCreating}
        >
          {isMutateCreating ? "Loading..." : "Confirm Upload"}
        </Button>
      </div>
      {isThereError && (
        <p className="text-error text-sm">To be able to upload this information please correct the erros.</p>
      )}
    </div>
  );
}
