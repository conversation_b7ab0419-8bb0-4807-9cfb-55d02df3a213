"use client";

import AsyncPaginatedTable from "@/components/common/async-paginated-table";
import { DatatableSearch } from "@/components/common/datatable/datatable-search";
import { CheckExcelOrder } from "@/lib/api/broker-file/types";
import { createColumnHelper } from "@tanstack/react-table";
import { useState } from "react";
import { InfoTable, RenderInfoTable } from "./broker-info-table";

const columnHelper = createColumnHelper<CheckExcelOrder>();

const columns = [
  columnHelper.accessor("order_number", {
    header: () => <span className="text-sm text-primary">Order Number</span>,
    cell: (info) => <RenderInfoTable info={info as InfoTable} />,
  }),
  columnHelper.accessor("register_number", {
    header: () => <span className="text-sm text-primary">Reg. Nr.</span>,
    cell: (info) => <RenderInfoTable info={info as InfoTable} />,
  }),
  columnHelper.accessor("year", {
    header: () => <span className="text-sm text-primary">Order Year</span>,
    cell: (info) => <RenderInfoTable info={info as InfoTable} />,
  }),
  columnHelper.accessor("orderfrac_amt_glass_KG", {
    header: () => <span className="text-sm text-primary">Glass</span>,
    cell: (info) => <RenderInfoTable info={info as InfoTable} />,
  }),
  columnHelper.accessor("orderfrac_amt_ppc_KG", {
    header: () => <span className="text-sm text-primary">Ppk</span>,
    cell: (info) => <RenderInfoTable info={info as InfoTable} />,
  }),
  columnHelper.accessor("orderfrac_amt_fe_KG", {
    header: () => <span className="text-sm text-primary">Fe</span>,
    cell: (info) => <RenderInfoTable info={info as InfoTable} />,
  }),
  columnHelper.accessor("orderfrac_amt_alu_KG", {
    header: () => <span className="text-sm text-primary">Alu</span>,
    cell: (info) => <RenderInfoTable info={info as InfoTable} />,
  }),
  columnHelper.accessor("orderfrac_amt_cbc_KG", {
    header: () => <span className="text-sm text-primary">Cb</span>,
    cell: (info) => <RenderInfoTable info={info as InfoTable} />,
  }),
  columnHelper.accessor("orderfrac_amt_ocp_KG", {
    header: () => <span className="text-sm text-primary">Sv</span>,
    cell: (info) => <RenderInfoTable info={info as InfoTable} />,
  }),
  columnHelper.accessor("orderfrac_amt_plstcs_KG", {
    header: () => <span className="text-sm text-primary">Plstc</span>,
    cell: (info) => <RenderInfoTable info={info as InfoTable} />,
  }),
  columnHelper.accessor("orderfrac_amt_om_KG", {
    header: () => <span className="text-sm text-primary">Sm</span>,
    cell: (info) => <RenderInfoTable info={info as InfoTable} />,
  }),
  columnHelper.accessor("errors", {
    header: () => <span className="text-sm text-primary">Error</span>,
    cell: ({ row }) => {
      const uniqueValues = [...new Set(Object.values(row.original.errors || {}))];
      const errors = uniqueValues.join(", ");

      return (
        <p data-error={!!errors} className="text-primary data-[error=true]:text-error">
          {errors || "----"}
        </p>
      );
    },
  }),
];

export function BrokerUploadOrderTable({ data }: { data: CheckExcelOrder[] }) {
  const [search, setSearch] = useState("");

  const filterData = data.filter((item) => JSON.stringify(item).toLowerCase().includes(search.toLowerCase()));

  return (
    <div>
      <div className="flex justify-between items-center my-10">
        <DatatableSearch onSearch={(value) => setSearch(value)} />
        <p className="text-tonal-dark-cream-30">{data.length} Results</p>
      </div>
      <AsyncPaginatedTable
        columns={columns}
        data={filterData}
        currentPage={1}
        pages={1}
        onPageChange={() => {}}
        noResultsMessage={"no data"}
      />
    </div>
  );
}
