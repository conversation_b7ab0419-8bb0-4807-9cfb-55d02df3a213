"use client";

import { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { useMutation, useQuery } from "@tanstack/react-query";
import { zodResolver } from "@hookform/resolvers/zod";
import { enqueueSnackbar } from "notistack";
import { ChevronLeftIcon } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { z } from "zod";

import { AccountCircle, Error } from "@interzero/oneepr-react-ui/Icon";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableRow } from "@/components/ui/table";
import { ModuleContent } from "@/components/common/module-content";
import { ModuleTitle } from "@/components/common/module-title";
import { Skeleton } from "@/components/ui/skeleton";
import { queryClient } from "@/lib/react-query";
import { getBroker } from "@/lib/api/broker";
import { dateManager } from "@/utils/date-manager";
import { COUNTRIES } from "@/utils/countries";
import { YEARS } from "@/utils/get-years";
import { cn } from "@/utils/cn";

import { EditBrokerContatoInfoDialog } from "../edit-broker-contact-info-dialog";
import { TerminateBrokerDialog } from "../terminate-broker-dialog";
import { EditBrokerDataDialog } from "../edit-broker-data-dialog";
import { BrokerCompaniesTable } from "../broker-companies-table";

const PRICE_LIST_SETS = [{ label: "Prices EU 2024", value: "EU_2024" }];

const editActivePriceListFormSchema = z.object({
  license_year: z.string().min(1),
  fraction: z.string().min(1),
});

type EditActivePriceListFormData = z.infer<typeof editActivePriceListFormSchema>;

interface BrokerProfileModuleProps {
  brokerId: number;
}

export function BrokerProfileModule({ brokerId }: BrokerProfileModuleProps) {
  const {
    data: broker,
    status: queryStatus,
    refetch,
  } = useQuery({
    queryKey: ["broker", brokerId],
    queryFn: async () => await getBroker(brokerId),
  });

  const defaultValues: Partial<EditActivePriceListFormData> = {
    // license_year: broker?.license_year.toString() || undefined,
    // fraction: broker?.fraction || undefined,
  };

  const { formState, ...form } = useForm<EditActivePriceListFormData>({
    resolver: zodResolver(editActivePriceListFormSchema),
    defaultValues,
  });

  const [isEditingActivePriceList, setIsEditingActivePriceList] = useState(false);

  useEffect(() => {
    if (brokerId) refetch();
    if (!broker) return;

    form.reset(defaultValues);

    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [brokerId, broker]);

  const { mutateAsync } = useMutation({
    mutationFn: (data: EditActivePriceListFormData) => {
      // const payload = {} as UpdateBrokerDto;
      // return updateBroker(brokerId, payload);
      return new Promise((resolve) => setTimeout(() => resolve(data), 3000));
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["broker", brokerId] });
      enqueueSnackbar("Active Price List updated successfully", { variant: "success" });
      setIsEditingActivePriceList(false);
    },
    onError: (error) => {
      enqueueSnackbar(error.message, { variant: "error" });
    },
  });

  async function handleFormSubmit(data: EditActivePriceListFormData) {
    await mutateAsync(data);
  }

  const countryCode = broker?.phone?.split(" ")[0];
  const phoneWithoutCode = broker?.phone?.replace(countryCode || "", "");
  const country = COUNTRIES.find((country) => country.phone.code === countryCode);

  if (queryStatus === "pending") {
    return (
      <div className="flex flex-col flex-1">
        <ModuleContent containerClassName="pb-10">
          <ModuleTitle
            icon={AccountCircle}
            title="Brokers Data"
            description="Lorem ipsum dolor sit amet consectetur. Risus nulla egestas orci non hendrerit. "
            className="mb-0"
          />
        </ModuleContent>
        <div className="bg-tonal-dark-cream-90 w-full flex-1 py-8">
          <div className="w-full lg:max-w-[944px] mx-auto px-4">
            <Skeleton className="mb-5 h-3 w-16" />
            <div className="mb-8 flex items-center justify-between gap-6">
              <Skeleton className="h-5 w-1/4" />
              <Skeleton className="h-10 w-1/5 rounded-full" />
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              <Skeleton className="w-full min-h-[249px]" />
              <Skeleton className="w-full min-h-[249px]" />
              <Skeleton className="col-span-full w-full min-h-[249px]" />
              <Skeleton className="col-span-full w-full min-h-[912px]" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col flex-1">
      <ModuleContent containerClassName="pb-10">
        <ModuleTitle
          icon={AccountCircle}
          title="Brokers Data"
          description="Lorem ipsum dolor sit amet consectetur. Risus nulla egestas orci non hendrerit. "
          className="mb-0"
        />
      </ModuleContent>
      <div className="bg-tonal-dark-cream-90 w-full flex-1 py-8">
        <div className="w-full lg:max-w-[944px] mx-auto px-4">
          <Link
            href="/brokers-data"
            className="inline-flex items-center gap-2 font-bold text-support-blue hover:opacity-75"
          >
            <ChevronLeftIcon className="size-5" /> Back
          </Link>
          <div className="mt-6 flex flex-col gap-4 items-start sm:flex-row sm:items-center sm:justify-between">
            <h3 className="text-[#183362] text-[1.75rem] font-bold">{broker?.name}</h3>
            <TerminateBrokerDialog brokerId={brokerId}>
              <Button variant="text" color="red" size="small">
                Terminate Broker
              </Button>
            </TerminateBrokerDialog>
          </div>
          <div className="mt-8 grid md:grid-cols-2 gap-4">
            {/* Contact informations */}
            <Card>
              <CardHeader className="flex-row items-center justify-between gap-3 p-7 pb-0">
                <CardTitle className="!text-base">Contact informations</CardTitle>
                <EditBrokerContatoInfoDialog brokerId={brokerId}>
                  <Button variant="text" color="light-blue" size="small">
                    Edit
                  </Button>
                </EditBrokerContatoInfoDialog>
              </CardHeader>
              <CardContent className="p-7 pt-6 space-y-8">
                <div className="flex flex-col gap-2">
                  <p className="text-primary">E-mail *</p>
                  <span className="text-tonal-dark-cream-10">{broker?.email}</span>
                </div>
                <div className="flex flex-col gap-2">
                  <p className="text-primary">Mobile</p>
                  <div className="flex items-center gap-2">
                    {country && (
                      <Image
                        src={country?.flag_url}
                        alt={country?.code}
                        width={24}
                        height={24}
                        className="size-6 rounded-full object-cover"
                      />
                    )}
                    {!!country?.phone.code && (
                      <>
                        <div className="flex items-center gap-1">
                          <span className="text-primary">{country?.phone.code}</span>
                        </div>
                        <div className="h-6 border border-tonal-dark-cream-80" />
                      </>
                    )}
                    <span className="text-tonal-dark-cream-10">
                      {phoneWithoutCode ? phoneWithoutCode : broker?.phone}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
            {/* Broker Data  */}
            <Card>
              <CardHeader className="flex-row items-center justify-between gap-3 p-7 pb-0">
                <CardTitle className="!text-base">Broker Data</CardTitle>
                <EditBrokerDataDialog brokerId={brokerId} enableContactInfo={false}>
                  <Button variant="text" color="light-blue" size="small">
                    Edit
                  </Button>
                </EditBrokerDataDialog>
              </CardHeader>
              <CardContent className="p-7 pt-6 space-y-8">
                <div className="flex flex-col gap-2">
                  <p className="text-primary">Broker Name</p>
                  <span className="text-tonal-dark-cream-10">{broker?.name}</span>
                </div>
                <div className="grid grid-cols-2 gap-6">
                  <div className="flex flex-col gap-2">
                    <p className="text-primary">VAT</p>
                    <span className="text-tonal-dark-cream-10">{broker?.vat}</span>
                  </div>
                  <div className="flex flex-col gap-2 text-[#8C8A87]">
                    <p>TAX Number</p>
                    <span className="text-tonal-dark-cream-10">{broker?.tax || "--------"}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
            {/* Active Price List */}
            <form onSubmit={form.handleSubmit(handleFormSubmit)} className="w-full col-span-full">
              <Card>
                <CardHeader className="flex-row items-center justify-between gap-3 p-7 pb-0">
                  <CardTitle className="!text-base">Active Price List</CardTitle>
                  {isEditingActivePriceList && (
                    <div className="flex items-center gap-6">
                      <Button
                        type="button"
                        variant="text"
                        color="dark-blue"
                        size="small"
                        onClick={() => setIsEditingActivePriceList(false)}
                      >
                        Cancelar
                      </Button>
                      <Button variant="filled" color="yellow" size="small" disabled={formState.isSubmitting}>
                        {formState.isSubmitting ? "Saving..." : "Save changes"}
                      </Button>
                    </div>
                  )}
                  {!isEditingActivePriceList && (
                    <Button
                      type="button"
                      variant="text"
                      color="light-blue"
                      size="small"
                      onClick={() => setIsEditingActivePriceList(true)}
                    >
                      Edit
                    </Button>
                  )}
                </CardHeader>
                <CardContent className="p-7 pt-6 space-y-8">
                  <div className="grid grid-cols-2 md:grid-cols-5 gap-6 items-center">
                    <div className="col-span-full md:col-span-1">
                      {isEditingActivePriceList && (
                        <Controller
                          name="license_year"
                          control={form.control}
                          render={({ field, fieldState: { error } }) => (
                            <div className="space-y-2">
                              <label htmlFor="license_year" className="text-primary text-base font-centra">
                                License Year
                              </label>
                              <Select
                                defaultValue={field.value}
                                value={field.value}
                                onValueChange={(value) => value && field.onChange(value)}
                              >
                                <SelectTrigger
                                  id="license_year"
                                  data-error={!!error}
                                  className="data-[error=true]:border-error data-[error=true]:bg-error/10"
                                >
                                  <SelectValue placeholder="License Year" />
                                </SelectTrigger>
                                <SelectContent>
                                  {YEARS.map((year) => (
                                    <SelectItem key={year.value} value={year.value}>
                                      {year.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          )}
                        />
                      )}
                      {!isEditingActivePriceList && (
                        <ReadOnlyInput
                          label="License Year"
                          //  value={broker?.license_year}
                        />
                      )}
                    </div>
                    <div className="col-span-full md:col-span-2">
                      {isEditingActivePriceList && (
                        <Controller
                          name="fraction"
                          control={form.control}
                          render={({ field, fieldState: { error } }) => (
                            <div className="space-y-2">
                              <label htmlFor="fraction" className="text-primary text-base font-centra">
                                Fraction/Price list sets
                              </label>
                              <Select
                                defaultValue={field.value}
                                value={field.value}
                                onValueChange={(value) => value && field.onChange(value)}
                              >
                                <SelectTrigger
                                  id="fraction"
                                  data-error={!!error}
                                  className="data-[error=true]:border-error data-[error=true]:bg-error/10"
                                >
                                  <SelectValue placeholder="License Year" />
                                </SelectTrigger>
                                <SelectContent>
                                  {PRICE_LIST_SETS.map((item) => (
                                    <SelectItem key={item.value} value={item.value}>
                                      {item.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          )}
                        />
                      )}
                      {!isEditingActivePriceList && (
                        <ReadOnlyInput label="Fraction/Price list sets" value="Prices EU 2024" />
                      )}
                    </div>
                    <div className="md:col-span-1">
                      <ReadOnlyInput
                        label="Start date"
                        // value={dateManager(broker?.start_date).format("DD/MM/YYYY")}
                        inputClassname="border-0 px-0"
                      />
                    </div>
                    <div className="md:col-span-1">
                      <ReadOnlyInput
                        label="End date"
                        // value={dateManager(broker?.end_date).format("DD/MM/YYYY")}
                        inputClassname="border-0 px-0"
                      />
                    </div>
                    {form.watch("license_year") === "2024" && (
                      <div className="-mt-4 col-span-full flex items-center gap-2">
                        <Error width={16} height={16} className="fill-error" />
                        <span className="col-span-full text-error text-sm">
                          Price list is overlapping. Please choose another list or license year.
                        </span>
                      </div>
                    )}
                  </div>
                  <Accordion type="single" collapsible>
                    <AccordionItem value="item-1" className="border-0">
                      <AccordionTrigger className="flex-0 justify-start gap-2 font-bold text-tonal-dark-cream-10 hover:no-underline [&>*]:text-support-blue">
                        Price list history
                      </AccordionTrigger>
                      <AccordionContent>
                        <Table className="w-full">
                          <TableBody>
                            {[...Array(4)].map((_, i) => (
                              <TableRow key={i} className="border-b-0 grid grid-cols-4 items-center gap-6">
                                <TableCell className="flex flex-col gap-2">
                                  <p>License Year</p>
                                  <span className="py-4 text-tonal-dark-cream-20">2023</span>
                                </TableCell>
                                <TableCell className="flex flex-col gap-2">
                                  <p>Fraction/Price list sets</p>
                                  <span className="py-4 text-tonal-dark-cream-20">Prices EU 2023</span>
                                </TableCell>
                                <TableCell className="flex flex-col gap-2">
                                  <p>Start date</p>
                                  <span className="py-4 text-tonal-dark-cream-20">
                                    {dateManager("2023-06-02").format("DD/MM/YYYY")}
                                  </span>
                                </TableCell>
                                <TableCell className="flex flex-col gap-2">
                                  <p>End date</p>
                                  <span className="py-4 text-tonal-dark-cream-20">
                                    {dateManager("2023-12-01").format("DD/MM/YYYY")}
                                  </span>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </CardContent>
              </Card>
            </form>
          </div>
          <h4 className="mt-10 mb-7 font-bold text-2xl text-[#183362]">Companies</h4>
          <BrokerCompaniesTable brokerId={brokerId} />
        </div>
      </div>
    </div>
  );
}

interface ReadOnlyInputProps {
  label: string;
  value?: string | number;
  inputClassname?: string;
}

function ReadOnlyInput({ label, value, inputClassname }: ReadOnlyInputProps) {
  return (
    <div className="flex flex-col gap-2">
      <label htmlFor={label} className="text-primary">
        {label}
      </label>
      <input
        disabled
        value={value}
        className={cn(
          "bg-background placeholder-tonal-dark-cream-60 border-tonal-dark-cream-80 w-full border rounded-2xl p-4 text-tonal-dark-cream-10",
          inputClassname
        )}
      />
    </div>
  );
}
