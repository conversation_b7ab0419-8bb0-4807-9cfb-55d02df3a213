"use client";

import { useState } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { enqueueSnackbar } from "notistack";
import { useMutation } from "@tanstack/react-query";

import { Button } from "@interzero/oneepr-react-ui/Button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { queryClient } from "@/lib/react-query";
import { deleteBroker } from "@/lib/api/broker";

interface TerminateBrokerDialogProps {
  brokerId: number;
  children: React.ReactNode;
}

export function TerminateBrokerDialog({ brokerId, children }: TerminateBrokerDialogProps) {
  const router = useRouter();

  const [isSuccess, setIsSuccess] = useState(false);

  const { mutate, status: mutationStatus } = useMutation({
    mutationFn: async () => await deleteBroker(brokerId),
    onSuccess: () => {
      setIsSuccess(true);
      queryClient.invalidateQueries({ queryKey: ["brokers-data"] });
    },
    onError: (error) => {
      enqueueSnackbar(error.message, { variant: "error" });
    },
  });

  function handleConfirm() {
    mutate();
  }

  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="bg-surface-02 py-9 max-w-2xl">
        {!isSuccess && (
          <>
            <DialogHeader>
              <DialogTitle className="font-bold sm:text-[1.75rem] text-primary">Terminate Broker</DialogTitle>
              <DialogDescription className="text-tonal-dark-cream-20">
                By clicking on ”confirm” you are terminating this broker as well as his client list. Are you sure you
                want to do this action?
              </DialogDescription>
            </DialogHeader>
            <DialogFooter className="!space-x-0 gap-6">
              <DialogClose asChild>
                <Button variant="outlined" color="dark-blue" size="medium">
                  Back
                </Button>
              </DialogClose>
              <Button
                variant="filled"
                color="red"
                size="medium"
                onClick={handleConfirm}
                disabled={mutationStatus === "pending"}
              >
                {mutationStatus === "pending" ? "Processing..." : "Confirm"}
              </Button>
            </DialogFooter>
          </>
        )}
        {isSuccess && (
          <>
            <DialogHeader className="mb-0">
              <div className="flex items-center gap-2 mb-10">
                <Image src="/assets/images/leaf-seal.svg" alt="leaf seal" width={36} height={36} className="size-9" />
                <h3 className="font-bold text-[1.75rem] text-primary">Broker terminated with success</h3>
              </div>
            </DialogHeader>
            <DialogFooter>
              <DialogClose asChild>
                <Button
                  variant="filled"
                  color="yellow"
                  size="medium"
                  onClick={() => {
                    setIsSuccess(false);
                    router.push("/brokers-data");
                  }}
                >
                  Close
                </Button>
              </DialogClose>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
