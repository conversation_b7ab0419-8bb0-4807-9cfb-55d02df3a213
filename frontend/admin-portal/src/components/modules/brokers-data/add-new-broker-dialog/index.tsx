"use client";

import { useState } from "react";
import { Controller, useForm, useWatch } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { enqueueSnackbar } from "notistack";
import { z } from "zod";

import { Button } from "@interzero/oneepr-react-ui/Button";
import { Input } from "@interzero/oneepr-react-ui/Input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Divider } from "@/components/common/divider";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { PhoneInput } from "@/components/common/phone-input";
import type { CreateBrokerDto } from "@/lib/api/broker/types";
import { createBroker } from "@/lib/api/broker";
import { queryClient } from "@/lib/react-query";
import { dateManager } from "@/utils/date-manager";

const addNewBrokerFormSchema = z
  .object({
    name: z.string().min(1, "Required"),
    company_name: z.string().min(1, "Required"),
    vat: z.string().optional(),
    tax_number: z.string().optional(),
    radio_selection: z.enum(["VAT", "TAX"]),
    email: z.string().email("Invalid e-mail").min(1, "Required"),
    phone: z.string().min(1, "Required"),
    login_email: z.string().email("Invalid e-mail").min(1, "Required"),
  })
  .superRefine((data, ctx) => {
    if (data.radio_selection === "VAT" && !data.vat?.trim()) {
      ctx.addIssue({ path: ["vat"], message: "Required", code: z.ZodIssueCode.custom });
    }
    if (data.radio_selection === "TAX" && !data.tax_number?.trim()) {
      ctx.addIssue({ path: ["tax_number"], message: "Required", code: z.ZodIssueCode.custom });
    }
  });

type AddNewBrokerFormData = z.infer<typeof addNewBrokerFormSchema>;

interface AddNewBrokerDialogProps {
  children: React.ReactNode;
}

export function AddNewBrokerDialog({ children }: AddNewBrokerDialogProps) {
  const [open, setOpen] = useState(false);

  const { formState, ...form } = useForm<AddNewBrokerFormData>({
    resolver: zodResolver(addNewBrokerFormSchema),
    defaultValues: {
      name: "",
      company_name: "",
      vat: "",
      tax_number: "",
      radio_selection: "VAT",
      email: "",
      phone: "",
      login_email: "",
    },
  });

  const { phone: phoneWatch, radio_selection: radioWatch } = useWatch({ control: form.control });

  const { mutateAsync } = useMutation({
    mutationFn: (data: AddNewBrokerFormData) => {
      const payload = {
        name: data.name,
        email: data.email,
        phone: data.phone,
        enroled_at: dateManager().toISOString(),
        company_name: data.company_name,
      } as CreateBrokerDto;

      if (data.radio_selection === "VAT") payload.vat = data.vat;
      else if (data.radio_selection === "TAX") payload.tax = data.tax_number;

      return createBroker(payload);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["brokers-data"] });
      enqueueSnackbar("Broker created successfully", { variant: "success" });
      setOpen(false);
      form.reset();
    },
    onError: (error) => {
      enqueueSnackbar(error.message, { variant: "error" });
    },
  });

  async function handleFormSubmit(data: AddNewBrokerFormData) {
    await mutateAsync(data);
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="bg-surface-02 py-9 max-w-2xl !z-[1001]">
        <DialogHeader>
          <DialogTitle className="font-bold sm:text-[1.75rem] text-support-blue">Add new broker</DialogTitle>
          <DialogDescription />
        </DialogHeader>
        <form onSubmit={form.handleSubmit(handleFormSubmit)} className="w-full">
          <div className="mb-10 grid md:grid-cols-2 gap-6">
            <p className="col-span-full text-tonal-dark-cream-30">Broker informations</p>
            <Controller
              name="name"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <Input
                  label="Broker Name *"
                  placeholder="Name"
                  variant={error ? "error" : "default"}
                  errorMessage={error?.message}
                  {...field}
                />
              )}
            />
            <Controller
              name="company_name"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <Input
                  label="Company name *"
                  placeholder="Name"
                  variant={error ? "error" : "default"}
                  errorMessage={error?.message}
                  {...field}
                />
              )}
            />
            <Controller
              control={form.control}
              name="radio_selection"
              render={({ field: { onChange, value } }) => (
                <RadioGroup value={value} onValueChange={(newValue) => onChange(newValue)} className="col-span-full">
                  <div data-value={radioWatch} className="group w-full grid md:grid-cols-2 gap-6 items-start px-1">
                    <label className="flex flex-col">
                      <div className="flex items-center gap-2 text-primary group-data-[value='TAX']:!text-[#8C8A87] cursor-pointer">
                        <RadioGroupItem value="VAT" className="block" />
                        VAT
                      </div>
                      {radioWatch === "VAT" ? (
                        <Input
                          placeholder="VAT Number"
                          variant={formState.errors.vat ? "error" : "default"}
                          errorMessage={formState.errors.vat?.message}
                          {...form.register("vat")}
                        />
                      ) : (
                        <input
                          disabled
                          placeholder="VAT Number"
                          className="bg-[#BEBDBB61] placeholder-[#8C8A87] border-tonal-dark-cream-80 mt-1.5 block w-full border rounded-2xl p-4 focus:outline-[#8C8A87]"
                        />
                      )}
                    </label>
                    <label className="flex flex-col">
                      <div className="flex items-center gap-2 text-primary group-data-[value='VAT']:!text-[#8C8A87] cursor-pointer">
                        <RadioGroupItem value="TAX" className="block" />
                        TAX
                      </div>
                      {radioWatch === "TAX" ? (
                        <Input
                          placeholder="TAX Number"
                          variant={formState.errors.tax_number ? "error" : "default"}
                          errorMessage={formState.errors.tax_number?.message}
                          {...form.register("tax_number")}
                        />
                      ) : (
                        <input
                          disabled
                          placeholder="TAX Number"
                          className="bg-[#BEBDBB61] placeholder-[#8C8A87] border-tonal-dark-cream-80 mt-1.5 block w-full border rounded-2xl p-4 focus:outline-[#8C8A87]"
                        />
                      )}
                    </label>
                  </div>
                </RadioGroup>
              )}
            />
            <Divider className="col-span-full !my-0" />
            <p className="col-span-full text-tonal-dark-cream-30">Contact informations</p>
            <Controller
              name="email"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <Input
                  type="email"
                  label="E-mail *"
                  placeholder="E-mail"
                  variant={error ? "error" : "default"}
                  errorMessage={error?.message}
                  {...field}
                />
              )}
            />
            <div className="flex flex-col gap-2">
              <label htmlFor="mobile-phone" className="text-primary">
                Mobile *
              </label>
              <PhoneInput
                id="mobile-phone"
                name="phone"
                defaultValue={phoneWatch}
                valueSetter={(value) => form.setValue("phone", value ?? "")}
                errorSetter={(isValid) =>
                  isValid ? form.clearErrors("phone") : form.setError("phone", { message: "Invalid phone number." })
                }
                isError={!!formState.errors.phone}
                contentClassName="z-[1002]"
                isModal
              />
              {!!formState.errors.phone && <span className="text-sm text-error">{formState.errors.phone.message}</span>}
            </div>
            <Divider className="col-span-full !my-0" />
            <p className="col-span-full text-tonal-dark-cream-30">Login informations</p>
            <Controller
              name="login_email"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <Input
                  type="email"
                  label="E-mail *"
                  placeholder="E-mail"
                  variant={error ? "error" : "default"}
                  errorMessage={error?.message}
                  {...field}
                />
              )}
            />
          </div>
          <DialogFooter className="flex justify-end">
            <Button
              type="submit"
              variant="filled"
              color="yellow"
              size="medium"
              disabled={formState.isSubmitting}
              className="w-full sm:max-w-[200px]"
            >
              {formState.isSubmitting ? "Creating..." : "Add"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
