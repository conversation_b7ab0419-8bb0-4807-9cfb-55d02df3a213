import * as React from "react";

import { cn } from "@/utils/cn";
import { CheckBox, CheckBoxOutlineBlank } from "@interzero/oneepr-react-ui/Icon";

export interface CheckboxInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string | React.ReactNode;
  errorMessage?: string;
}

const CheckboxInput = React.forwardRef<HTMLInputElement, CheckboxInputProps>(
  ({ className, label, errorMessage, ...props }, ref) => {
    return (
      <div className="space-y-2">
        <label
          htmlFor={props.id}
          className="group text-tonal-dark-cream-10 has-[:checked]:text-primary cursor-pointer has-[:disabled]:cursor-default has-[:disabled]:text-tonal-dark-cream-60 flex items-center gap-2"
        >
          <input
            type="checkbox"
            data-error={!!errorMessage}
            className={cn(
              "hidden peer disabled:cursor-default disabled:opacity-50 data-[error=true]:border-tonal-red-40 data-[error=true]:bg-tonal-red-90",
              className
            )}
            ref={ref}
            {...props}
          />
          <CheckBoxOutlineBlank
            width={20}
            className="block peer-checked:hidden fill-on-tertiary peer-disabled:fill-tonal-dark-cream-60 size-5 flex-none"
          />
          <CheckBox width={20} className="hidden peer-checked:block fill-primary size-5 flex-none" />
          {label}
        </label>
        {!!errorMessage && (
          <div className="flex justify-start items-center mt-2.5 space-x-2 ">
            <span slot="errorMessage" className="font-centra text-sm text-tonal-red-40">
              {errorMessage}
            </span>
          </div>
        )}
      </div>
    );
  }
);

CheckboxInput.displayName = "CheckboxInput";

export { CheckboxInput };
