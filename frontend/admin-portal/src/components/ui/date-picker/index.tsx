"use client";

import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import * as React from "react";
import { DateRange } from "react-day-picker";

import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { Popover, PopoverContent, PopoverTrigger } from "../popover";

export function DatePickerWithRange({ className }: React.HTMLAttributes<HTMLDivElement>) {
  const [date, setDate] = React.useState<DateRange | undefined>(undefined);

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={"ghost"}
            className={cn(
              "w-auto justify-start text-left font-normal hover:bg-transparent",
              !date && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="text-support-blue" />
            {date?.from ? (
              date.to ? (
                <>
                  <span className="text-support-blue font-bold">{format(date.from, "LLL dd, y")}</span>{" "}
                  <span className="text-tonal-dark-cream-60">to </span>
                  <span className="text-support-blue font-bold">{format(date.to, "LLL dd, y")}</span>
                </>
              ) : (
                <span className="text-support-blue">{format(date.from, "LLL dd, y")}</span>
              )
            ) : (
              <>
                <span className="text-support-blue font-bold">---</span>{" "}
                <span className="text-tonal-dark-cream-60">to </span>
                <span className="text-support-blue font-bold">---</span>
              </>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            initialFocus
            mode="range"
            defaultMonth={date?.from}
            selected={date}
            onSelect={setDate}
            numberOfMonths={2}
          />
        </PopoverContent>
      </Popover>
    </div>
  );
}
