import * as React from "react";
import { cn } from "@/lib/utils";

// Extract constants for better maintainability
const CHARACTER_COUNTER_DEFAULTS = {
  TEMPLATE: "{value}/{max} characters",
  MAX_TITLE_LENGTH: 150,
  MAX_HELP_TEXT_LENGTH: 500,
} as const;

interface CharacterCounterProps {
  id?: string;
  className?: string;
  value: number;
  max: number;

  /**
   * A string template for the counter text.
   * Use `{value}` for the current character count and `{max}` for the maximum.
   * @default "{value}/{max} characters"
   */
  template?: string;
}
export const CharacterCounter = React.memo(function CharacterCounter(props: CharacterCounterProps) {
  const { id, className, value, max, template = CHARACTER_COUNTER_DEFAULTS.TEMPLATE } = props;
  const text = template.replace(/{value}/g, String(value || 0)).replace(/{max}/g, String(max));
  return (
    <div className={cn("flex justify-start", className)}>
      <p id={id} className="text-sm text-tonal-dark-cream-60">
        {text}
      </p>
    </div>
  );
});

// Export constants for reuse in other components
export { CHARACTER_COUNTER_DEFAULTS };
