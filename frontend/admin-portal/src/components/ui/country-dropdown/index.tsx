"use client";

import { localeOptions } from "@/utils/locales";
import { KeyboardArrowDown } from "@interzero/oneepr-react-ui/Icon";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import Image from "next/image";
import { useState } from "react";

export function CountryDropdown() {
  const locale = "en";
  const [currentLocale, setCurrentLocale] = useState(
    localeOptions.find((option) => option.code === locale) || localeOptions[0]
  );

  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger className="" asChild>
        <div className="flex items-center gap-1 cursor-pointer">
          <div className="flex items-center gap-1 lg:gap-2">
            <div className="w-6 h-6 rounded-full overflow-hidden">
              <Image
                src={currentLocale.flag}
                alt="Country image"
                width={30}
                height={30}
                className="w-full h-full object-cover"
              />
            </div>
            <span className="text-primary font-bold">{currentLocale.code.toUpperCase()}</span>
          </div>
          <KeyboardArrowDown className="fill-primary" width={24} height={24} />
        </div>
      </DropdownMenu.Trigger>

      <DropdownMenu.Portal>
        <DropdownMenu.Content
          className="w-[310px] bg-background py-3 rounded-2xl focus:outline-none z-[999999]"
          style={{ boxShadow: "0px 2px 3px 0px rgba(0,0,0,0.3)" }}
          sideOffset={5}
          align="end"
        >
          {localeOptions.map((option) => (
            <DropdownMenu.Item
              onClick={() => setCurrentLocale(option)}
              key={option.code}
              className="py-5 px-4 bg-background hover:bg-tonal-dark-cream-90 cursor-pointer outline-none"
            >
              <div className="flex items-center gap-4 w-full">
                <div className="w-6 h-6 rounded-full overflow-hidden">
                  <Image
                    src={option.flag}
                    alt="Country image"
                    width={30}
                    height={30}
                    className="w-full h-full object-cover"
                  />
                </div>
                <p className="text-primary">{option.name}</p>
              </div>
            </DropdownMenu.Item>
          ))}
        </DropdownMenu.Content>
      </DropdownMenu.Portal>
    </DropdownMenu.Root>
  );
}
