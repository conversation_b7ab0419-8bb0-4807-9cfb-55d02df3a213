"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { cn } from "@/lib/utils";
import { Button as DefaultButton } from "@interzero/oneepr-react-ui/Button";
import { endOfMonth, isBefore, startOfMonth } from "date-fns";
import { CalendarIcon } from "lucide-react";
import * as React from "react";
import { DateRange } from "react-day-picker";
import { Popover, PopoverContent, PopoverTrigger } from "../popover";

interface MonthDatePickerWithRangeProps extends React.HTMLAttributes<HTMLDivElement> {
  onDateChange?: (date: DateRange | undefined) => void;
}

interface YearMonth {
  year: number;
  month: number;
}

export function MonthDatePickerWithRange({ className, onDateChange }: MonthDatePickerWithRangeProps) {
  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth() + 1;

  // Applied states (shown in the button)
  const [appliedStartDate, setAppliedStartDate] = React.useState<YearMonth | null>(null);
  const [appliedEndDate, setAppliedEndDate] = React.useState<YearMonth | null>(null);

  // Temporary states (used in the popover)
  const [tempStartDate, setTempStartDate] = React.useState<YearMonth | null>(null);
  const [tempEndDate, setTempEndDate] = React.useState<YearMonth | null>(null);
  const [open, setOpen] = React.useState(false);

  const months = [
    { value: "1", label: "Jan", numeric: "01" },
    { value: "2", label: "Feb", numeric: "02" },
    { value: "3", label: "Mar", numeric: "03" },
    { value: "4", label: "Apr", numeric: "04" },
    { value: "5", label: "May", numeric: "05" },
    { value: "6", label: "Jun", numeric: "06" },
    { value: "7", label: "Jul", numeric: "07" },
    { value: "8", label: "Aug", numeric: "08" },
    { value: "9", label: "Sep", numeric: "09" },
    { value: "10", label: "Oct", numeric: "10" },
    { value: "11", label: "Nov", numeric: "11" },
    { value: "12", label: "Dec", numeric: "12" },
  ];

  const years = Array.from({ length: 61 }, (_, i) => currentYear - 30 + i);

  const formatDateDisplay = (yearMonth: YearMonth | null) => {
    if (!yearMonth) return "--/----";
    const monthStr = yearMonth.month.toString().padStart(2, "0");
    return `${monthStr}/${yearMonth.year}`;
  };

  const handleClear = () => {
    setAppliedStartDate(null);
    setAppliedEndDate(null);
    setTempStartDate(null);
    setTempEndDate(null);
    onDateChange?.(undefined);
    setOpen(false);
  };

  const handleApply = () => {
    if (!tempStartDate || !tempEndDate) {
      handleClear();
      return;
    }

    const fromDate = startOfMonth(new Date(tempStartDate.year, tempStartDate.month - 1));
    const toDate = endOfMonth(new Date(tempEndDate.year, tempEndDate.month - 1));

    if (isBefore(toDate, fromDate)) {
      // If end date is before start date, set end date equal to start date
      setTempEndDate(tempStartDate);
      setAppliedStartDate(tempStartDate);
      setAppliedEndDate(tempStartDate);
      onDateChange?.({
        from: fromDate,
        to: endOfMonth(new Date(tempStartDate.year, tempStartDate.month - 1)),
      });
    } else {
      setAppliedStartDate(tempStartDate);
      setAppliedEndDate(tempEndDate);
      onDateChange?.({
        from: fromDate,
        to: toDate,
      });
    }
    setOpen(false);
  };

  // When opening the popover, initialize temp values with applied values
  React.useEffect(() => {
    if (open) {
      setTempStartDate(appliedStartDate);
      setTempEndDate(appliedEndDate);
    }
  }, [open, appliedStartDate, appliedEndDate]);

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant={"ghost"}
            className={"w-auto justify-start text-left font-normal hover:bg-transparent text-muted-foreground p-0"}
          >
            <CalendarIcon className="text-support-blue h-4 w-4" />
            <span className="text-support-blue font-bold">{formatDateDisplay(appliedStartDate)}</span>
            <span className="text-tonal-dark-cream-60 mx-2">to</span>
            <span className="text-support-blue font-bold">{formatDateDisplay(appliedEndDate)}</span>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[300px] p-4" align="start">
          <div className="space-y-4">
            <div>
              <p className="text-sm font-medium mb-3">Start Date</p>
              <div className="grid grid-cols-2 gap-2">
                <Select
                  value={tempStartDate?.month?.toString()}
                  onValueChange={(value) =>
                    setTempStartDate((prev) => ({
                      year: prev?.year || currentYear,
                      month: parseInt(value),
                    }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Month" />
                  </SelectTrigger>
                  <SelectContent>
                    {months.map(({ value, label }) => (
                      <SelectItem key={value} value={value}>
                        {label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select
                  value={tempStartDate?.year?.toString()}
                  onValueChange={(value) =>
                    setTempStartDate((prev) => ({
                      month: prev?.month || currentMonth,
                      year: parseInt(value),
                    }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Year" />
                  </SelectTrigger>
                  <SelectContent>
                    {years.map((year) => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="h-[1px] w-full bg-border my-2" />

            <div>
              <p className="text-sm font-medium mb-3">End Date</p>
              <div className="grid grid-cols-2 gap-2">
                <Select
                  value={tempEndDate?.month?.toString()}
                  onValueChange={(value) =>
                    setTempEndDate((prev) => ({
                      year: prev?.year || currentYear,
                      month: parseInt(value),
                    }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Month" />
                  </SelectTrigger>
                  <SelectContent>
                    {months.map(({ value, label }) => (
                      <SelectItem key={value} value={value}>
                        {label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select
                  value={tempEndDate?.year?.toString()}
                  onValueChange={(value) =>
                    setTempEndDate((prev) => ({
                      month: prev?.month || currentMonth,
                      year: parseInt(value),
                    }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Year" />
                  </SelectTrigger>
                  <SelectContent>
                    {years.map((year) => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex justify-end space-x-2">
              <DefaultButton variant="text" color="dark-blue" size="medium" onClick={handleClear}>
                Clear
              </DefaultButton>
              <DefaultButton variant="filled" color="yellow" size="medium" onClick={handleApply}>
                Apply
              </DefaultButton>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
