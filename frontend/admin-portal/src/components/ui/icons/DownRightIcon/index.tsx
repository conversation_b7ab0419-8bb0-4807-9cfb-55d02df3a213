import React, { memo, forwardRef, SVGProps } from "react";
interface BaseProps extends Partial<SVGProps<SVGSVGElement>> {
  size?: number;
}
type IconProps = Omit<BaseProps, "ref"> & React.RefAttributes<SVGSVGElement>;

export const DownRightIcon = memo(
  forwardRef(function DownRightIcon(props, ref) {
    const { size = 28, fill = "#BEBDBB", ...rest } = props;
    return (
      <svg
        ref={ref}
        width={size}
        height={size}
        viewBox="0 0 18 29"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...rest}
      >
        <path
          d="M11.0005 19.1496L14.8805 23.0296L11.0005 26.9096C10.6105 27.2996 10.6105 27.9296 11.0005 28.3196C11.3905 28.7096 12.0205 28.7096 12.4105 28.3196L17.0005 23.7296C17.3905 23.3396 17.3905 22.7096 17.0005 22.3196L12.4105 17.7296C12.0205 17.3396 11.3905 17.3396 11.0005 17.7296C10.6205 18.1196 10.6105 18.7596 11.0005 19.1496Z"
          fill={fill}
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M1.5 0.674805C2.05228 0.674805 2.5 1.12252 2.5 1.6748V22.5247H15V24.5247H1.5C0.947715 24.5247 0.5 24.0769 0.5 23.5247V1.6748C0.5 1.12252 0.947715 0.674805 1.5 0.674805Z"
          fill={fill}
        />
      </svg>
    );
  }) satisfies React.ForwardRefExoticComponent<IconProps>
);
