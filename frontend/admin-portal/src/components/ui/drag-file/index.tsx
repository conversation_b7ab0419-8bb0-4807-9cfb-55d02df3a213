import { useState, ChangeEvent, ComponentProps, useRef, useEffect } from "react";
import { formatFileSize } from "@/utils/format-file-size";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Delete, Download } from "@interzero/oneepr-react-ui/Icon";

interface FileInputProps extends ComponentProps<"input"> {
  title?: string;
  description?: string;
  onFile?: (file: File) => void;
  label?: string;
  errorMessage?: string;
  hideDownloadButton?: boolean;
  selectedFile?: File;
}

export function DragFile({
  title = "Upload a file",
  description = "or drag it here",
  onFile,
  label,
  errorMessage,
  hideDownloadButton = false,
  selectedFile,
  ...props
}: FileInputProps) {
  const [file, setFile] = useState<File | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    props.onChange?.(e);

    if (!e.target.files || !e.target.files[0]) return;

    const file = e.target.files[0];

    setFile(file);
    onFile && onFile(file);
  };

  useEffect(() => {
    setFile(selectedFile || null);
  }, [selectedFile]);

  return (
    <div className="space-y-2 w-full">
      {!!label && (
        <label htmlFor="description" className="text-primary text-base font-centra mb-2">
          {label}
        </label>
      )}
      {!file && (
        <div
          onClick={() => inputRef.current?.click()}
          data-error={!!errorMessage}
          className="flex items-center justify-center border-2 border-tonal-dark-cream-60 border-dashed rounded-2xl h-20 p-6 text-center cursor-pointer bg-surface-02 data-[error=true]:border-error data-[error=true]:bg-error/10"
        >
          <input type="file" ref={inputRef} onChange={handleFileChange} className="hidden" {...props} />
          <p className="text-tonal-dark-cream-20 font-bold text-sm ">
            {title}
            <span className="text-tonal-dark-cream-50 font-normal ml-1">{description}</span>
          </p>
        </div>
      )}
      {!!file && (
        <div className="w-full flex flex-col gap-2">
          <div className="w-full flex items-center justify-between text-sm text-tonal-dark-cream-20 gap-4">
            <div className="w-[300px] overflow-hidden text-ellipsis whitespace-nowrap">{file.name}</div>
            <div className="flex-none flex items-center justify-end gap-2">
              <div className="w-20 flex items-center justify-end">{formatFileSize(file.size)}</div>
              <div className="w-20 flex items-center justify-end">{new Date().toLocaleDateString()}</div>
              {!hideDownloadButton && (
                <Button type="button" variant="text" color="dark-blue" size="iconSmall" trailingIcon={<Download />} />
              )}
              <Button
                type="button"
                variant="text"
                color="dark-blue"
                size="iconSmall"
                trailingIcon={<Delete className="fill-primary" />}
                onClick={() => setFile(null)}
              />
            </div>
          </div>
        </div>
      )}
      {!!errorMessage && (
        <div className="flex justify-start items-center mt-2.5 space-x-2 ">
          <span slot="errorMessage" className="font-centra text-sm text-tonal-red-40">
            {errorMessage}
          </span>
        </div>
      )}
    </div>
  );
}
