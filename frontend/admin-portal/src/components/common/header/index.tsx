import { Breadcrumb } from "../breadcrumb";
import { CountryDropdown } from "@/components/ui/country-dropdown";
import { HeaderLogout } from "./header-logout";
import { HeaderSidebarToggle } from "./header-sidebar-toggle";

export function Header() {
  return (
    <div className="bg-surface-02 w-full h-20 pt-6 pb-4 border-b-[1px] border-tonal-dark-cream-80 px-4">
      <div className="w-full my-auto lg:max-w-[912px] mx-auto flex justify-between items-center">
        <div className="flex items-center gap-3">
          <HeaderSidebarToggle />
          <Breadcrumb paths={[{ href: "/", label: "Dashboard" }]} />
        </div>
        <div className="flex items-center gap-4 lg:gap-6">
          <CountryDropdown />
          <HeaderLogout />
        </div>
      </div>
    </div>
  );
}
