import { Check } from "lucide-react";
import * as React from "react";
import { cn } from "@/lib/utils";
import { Command } from "@/components/ui/command";
import { Command as CommandPrimitive } from "cmdk";
import { CommandEmpty, CommandGroup, CommandItem, CommandList } from "@/components/ui/command";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { Popover, PopoverAnchor, PopoverContent } from "@/components/ui/popover";
import { Skeleton } from "@/components/ui/skeleton";

type Props<T extends string> = {
  value: T;
  onChange: (value: T) => void;
  searchValue: string;
  onSearchValueChange: (value: string) => void;
  items: { value: T; label: string }[];
  isLoading?: boolean;
  emptyMessage?: string;
  placeholder?: string;
  disabled?: boolean;
  onScrollEnd?: () => void;
  id?: string;
  contentClassName?: string;
  hasError?: boolean;
  onSelect?: ({ value, label }: { value: T; label: string }) => void;
  label?: string;
};

export function AutoComplete<T extends string>({
  value,
  onChange,
  searchValue,
  onSearchValueChange,
  items,
  isLoading,
  emptyMessage = "No items found",
  placeholder = "Search by name",
  disabled,
  id,
  contentClassName,
  hasError,
  onSelect,
  label,
}: Props<T>) {
  const [open, setOpen] = React.useState(false);
  const [isPopoverFocused, setIsPopoverFocused] = React.useState(true);
  const [isFirstRenderFocus, setIsFirstRenderFocus] = React.useState(true);
  const [selectedItem, setSelectedItem] = React.useState({
    value,
    label: searchValue,
  });

  const labels = React.useMemo(
    () =>
      items.reduce(
        (acc, item) => {
          acc[item.value] = item.label;
          return acc;
        },
        {} as Record<string, string>
      ),
    [items]
  );

  const reset = () => {
    onChange("" as T);
    onSearchValueChange("");
    setSelectedItem({ value: "" as T, label: "" });
  };

  const onInputBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    if (open && !isPopoverFocused) {
      setOpen(false);
    }
    if (!e.relatedTarget?.hasAttribute("cmdk-list") && labels[value] !== searchValue) {
      if (value) {
        onSearchValueChange(selectedItem.label);
      } else {
        reset();
      }
    }
  };

  const onSelectItem = (inputValue: string) => {
    if (inputValue === value?.toString()) {
      reset();
    } else {
      onChange(inputValue as T);
      onSearchValueChange(labels[inputValue] ?? "");
      setSelectedItem({
        value: inputValue as T,
        label: labels[inputValue] ?? "",
      });
      onSelect?.({ value: inputValue as T, label: labels[inputValue] ?? "" });
    }
    setOpen(false);
  };

  const handleScroll = (e: React.WheelEvent<HTMLDivElement>) => {
    if (e.deltaY > 0) {
      e.currentTarget.scrollBy(0, 10);
    } else {
      e.currentTarget.scrollBy(0, -10);
    }
  };

  return (
    <Popover open={open} onOpenChange={setOpen} key={value}>
      <Command shouldFilter={false}>
        <PopoverAnchor asChild>
          <CommandPrimitive.Input
            asChild
            value={searchValue}
            onValueChange={onSearchValueChange}
            onKeyDown={(e) => setOpen(["Escape", "Enter", "Tab"].includes(e.key) ? false : open)}
            onMouseDown={() => setOpen((open) => !!searchValue || !open)}
            onFocus={() => {
              if (isFirstRenderFocus) {
                setIsFirstRenderFocus(false);
              } else {
                setOpen(true);
              }
            }}
            onBlur={onInputBlur}
            disabled={disabled}
            className="rounded-md focus-visible:-border focus-visible:border-ring"
            id={id}
          >
            <Input
              placeholder={placeholder}
              className="focus-visible:border-b-0"
              variant={hasError ? "error" : "default"}
              label={label}
            />
          </CommandPrimitive.Input>
        </PopoverAnchor>
        {!open && <CommandList aria-hidden="true" className="hidden" />}
        <PopoverContent
          asChild
          onFocus={() => {
            setIsPopoverFocused(true);
          }}
          onBlur={() => {
            setIsPopoverFocused(false);
          }}
          onOpenAutoFocus={(e) => e.preventDefault()}
          onInteractOutside={(e) => {
            if (e.target instanceof Element && e.target.hasAttribute("cmdk-input")) {
              e.preventDefault();
            }
          }}
          className={cn("w-[--radix-popover-trigger-width] p-0", contentClassName)}
        >
          <CommandList onWheel={handleScroll} className="border-primary shadow-elevation-01-1">
            {isLoading ? (
              <CommandPrimitive.Loading>
                <div className="p-1">
                  <Skeleton className="h-6 w-full" />
                </div>
              </CommandPrimitive.Loading>
            ) : null}
            {items.length > 0 && !isLoading ? (
              <CommandGroup>
                {items.map((option) => (
                  <CommandItem
                    key={option.value}
                    value={option.value?.toString()}
                    onMouseDown={(e) => e.preventDefault()}
                    onSelect={onSelectItem}
                    className="cursor-pointer hover:bg-surface-01 text-primary"
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        value?.toString() === option.value?.toString() ? "opacity-100" : "opacity-0"
                      )}
                    />
                    {option.label}
                  </CommandItem>
                ))}
                {isLoading ? (
                  <CommandPrimitive.Loading>
                    <div className="p-1">
                      <Skeleton className="h-6 w-full" />
                    </div>
                  </CommandPrimitive.Loading>
                ) : null}
              </CommandGroup>
            ) : null}
            {!isLoading ? <CommandEmpty>{emptyMessage ?? "No items."}</CommandEmpty> : null}
          </CommandList>
        </PopoverContent>
      </Command>
    </Popover>
  );
}
