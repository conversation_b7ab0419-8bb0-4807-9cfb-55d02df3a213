"use client";

import { useMemo } from "react";

import { CalendarToday, KeyboardArrowDown } from "@interzero/oneepr-react-ui/Icon";
import { Dropdown, DropdownItem } from "@/components/ui/dropdown";
import { YEARS } from "@/utils/get-years";
import { MONTHS } from "@/utils/get-months";

interface DateRangeFilterProps {
  endDate: { month: { label: string; value: string }; year: { label: string; value: string } };
  selectedMonth: { label: string; value: string };
  selectedYear: { label: string; value: string };
  onMonthChange: (month: { label: string; value: string }) => void;
  onYearChange: (year: { label: string; value: string }) => void;
}

export function DateRangeFilter(props: DateRangeFilterProps) {
  const { endDate, selectedMonth, selectedYear, onMonthChange, onYearChange } = props;

  // Get available months based on selected year
  const availableMonths = useMemo(() => {
    const currentDate = new Date("2025-01-16");
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth();

    const selectedYearValue = parseInt(selectedYear.value);

    if (selectedYearValue > currentYear) {
      return [];
    }
    if (selectedYearValue === currentYear) {
      return MONTHS.filter((_, index) => index <= currentMonth);
    }
    return MONTHS;
  }, [selectedYear]);

  return (
    <div className="flex items-center gap-2">
      <div className="flex items-center gap-2">
        <Dropdown
          trigger={
            <button className="flex items-center text-support-blue font-bold text-base">
              <CalendarToday className="w-5 h-5 fill-support-blue" />
              <strong className="ml-1 mt-1 text-left font-bold">
                {selectedMonth.value}/{selectedYear.value}
              </strong>
              {/* <KeyboardArrowDown width={20} height={20} className="fill-support-blue" /> */}
            </button>
          }
        >
          <div className="p-2">
            <Dropdown
              trigger={
                <button className="w-full flex items-center justify-between text-support-blue font-bold text-base p-2 hover:bg-surface-01 rounded">
                  <span>{selectedYear.label}</span>
                  <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
                </button>
              }
            >
              {YEARS.map((year, idx) => (
                <DropdownItem
                  key={idx}
                  onClick={() => onYearChange(year)}
                  className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
                >
                  {year.label}
                </DropdownItem>
              ))}
            </Dropdown>
            {availableMonths.length > 0 && (
              <div className="grid grid-cols-3 gap-1 mt-2">
                {availableMonths.map((month, idx) => (
                  <button
                    key={idx}
                    onClick={() => onMonthChange(month)}
                    className={`p-2 text-base rounded hover:bg-surface-01 ${
                      month.value === selectedMonth.value ? "bg-surface-01 text-primary" : "text-tonal-dark-cream-10"
                    }`}
                  >
                    {month.label.slice(0, 3)}
                  </button>
                ))}
              </div>
            )}
          </div>
        </Dropdown>
        <span className="flex items-center text-tonal-dark-cream-40 font-normal text-base">to</span>
        <strong className="flex items-center text-support-blue font-bold text-base">
          {endDate.month.value}/{endDate.year.value}
        </strong>
      </div>
    </div>
  );
}
