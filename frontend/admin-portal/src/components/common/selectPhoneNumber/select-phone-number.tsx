"use client";
import { binarySearch } from "@/utils/binary-search";
import { Add, CheckCircle, Error, KeyboardArrowDown, KeyboardArrowUp } from "@interzero/oneepr-react-ui/Icon";
import Image from "next/image";
import { ChangeEvent, useEffect, useState } from "react";
import ReactInputMask from "react-input-mask";
import { validatePhoneNumber } from "./validatePhoneNumber";
import { COUNTRIES } from "@/utils/countries";

type Country = (typeof COUNTRIES)[number];

const germanyData = binarySearch(COUNTRIES, "Germany")[0];

export const CustomSelectPhone = ({
  label,
  onChange = () => {},
  onBlur = () => {},
  value,
  error,
}: {
  label: string;
  value: string;
  onChange?: (newValue: string, isValid: boolean) => void;
  onBlur?: (newValue: string, isValid: boolean) => void;
  error?: string | false;
}) => {
  const [phoneIsValid, setPhoneIsValid] = useState(false);
  const [country, setCountry] = useState<Country | null>(germanyData);
  const [countryCode, setCountryCode] = useState(germanyData?.code);
  const [openSelect, setOpenSelect] = useState(false);
  const [isFocusInputNumber, setIsFocusInputNumber] = useState(false);

  const handleSelectCountries = (newCountry: Country) => {
    setPhoneIsValid(false);
    onChange(newCountry.code + " ", false);
    setCountryCode(newCountry.code);

    setCountry(newCountry);
  };

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    event.target.value = country?.code + " " + event.target.value;
    setCountryCode(country?.code);
    setPhoneIsValid(validatePhoneNumber(event.target.value.replaceAll(`${country?.code} `, ""), mask));

    // onChange(event.target.value, validatePhoneNumber(event.target.value.replaceAll(`${country?.code} `, ""), mask));
  };

  const handleBlur = (event: ChangeEvent<HTMLInputElement>) => {
    setIsFocusInputNumber(false);

    if (!onBlur) return;

    event.target.value = country?.code + " " + event.target.value;
    setCountryCode(country?.code);
    setPhoneIsValid(validatePhoneNumber(event.target.value.replaceAll(`${country?.code} `, ""), mask));

    onBlur(event.target.value, validatePhoneNumber(event.target.value.replaceAll(`${country?.code} `, ""), mask));
  };

  const checkValue = () => {
    const countrySelect = COUNTRIES.find((item) => item.code === value.split(" ")[0]);

    const maskSelect =
      (typeof countrySelect?.phone.mask === "string"
        ? countrySelect?.phone.mask
        : countrySelect?.phone.mask[countrySelect?.phone.mask.length - 1]) || "(*************";

    setPhoneIsValid(validatePhoneNumber(value.replaceAll(`${countrySelect?.code} `, ""), maskSelect));
    onChange(value, validatePhoneNumber(value.replaceAll(`${countrySelect?.code} `, ""), maskSelect));

    setCountry(countrySelect || null);
    setCountryCode(countrySelect?.code || "");
  };

  const handleOpenSelect = () => {
    setOpenSelect((current) => !current);
  };

  const handleChangeCode = (event: ChangeEvent<HTMLInputElement>) => {
    setCountryCode(event.target.value);
    const selectCountry = COUNTRIES.find((item) => item.code === event.target.value);

    if (selectCountry) {
      handleSelectCountries(selectCountry);
      return;
    }
  };

  useEffect(() => {
    if (value) {
      checkValue();
    }
    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (value && value?.split(" ")[0] !== country?.code) {
      checkValue();
    }

    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value]);

  const mask =
    country && country?.phone.mask
      ? typeof country?.phone.mask === "string"
        ? country?.phone.mask
        : country?.phone.mask[country?.phone.mask.length - 1]
      : "(*************";

  const isValide = error ? false : value.replaceAll(`${country?.code} `, "") ? phoneIsValid : true;

  return (
    <div className="relative">
      <p className="'text-primary text-base font-centra mb-2 text-primary">{label}</p>
      <div
        className={`flex w-full items-center border ${
          isValide || isFocusInputNumber ? "bg-white" : "bg-tonal-red-90"
        } rounded-2xl ${isValide || isFocusInputNumber ? "border-tonal-dark-cream-80" : "border-error"}`}
      >
        <div className={`relative w-[8rem] h-12 cursor-pointer`} tabIndex={0} onClick={handleOpenSelect}>
          {country && (
            <div
              style={{
                backgroundImage: `url(${country.flag_url})`,
                backgroundSize: "cover",
                backgroundPosition: "center",
                backgroundRepeat: "no-repeat",
              }}
              className="h-6 w-6 rounded-full absolute top-3 left-5 "
            />
          )}
          <div className="absolute top-4 right-1 text-gray-700 z-10">
            {openSelect ? (
              <KeyboardArrowUp className="fill-success" width={15} height={15} />
            ) : (
              <KeyboardArrowDown className="fill-success" width={15} height={15} />
            )}
          </div>
        </div>
        <div className="flex-none">
          <ReactInputMask
            maskChar=""
            mask={"+999"}
            className="text-primary outline-0 p-0 flex-none w-10"
            style={{
              backgroundColor: "transparent",
            }}
            onChange={handleChangeCode}
            value={countryCode}
            size={countryCode?.length > 1 ? countryCode?.length - 1 : 3}
          />
        </div>
        <div
          className={`border-r flex-none z-50 rounded ${
            isValide ? "border-tonal-dark-cream-80" : "border-error"
          } border-tonal-dark-cream-80 py-2 mx-2`}
        />

        <ReactInputMask
          maskChar=""
          mask={mask}
          placeholder={mask}
          value={value ? value.replaceAll(`${country?.code} `, "") : ""}
          onChange={handleChange}
          onBlur={handleBlur}
          className={`text-primary rounded-r-2xl py-4 focus:outline-none p-0 ${phoneIsValid && "pr-8"} ${
            !isValide && !isFocusInputNumber ? "bg-tonal-red-90" : "bg-white"
          }`}
          onFocus={() => setIsFocusInputNumber(true)}
        />
        {phoneIsValid && <CheckCircle width={20} height={20} className="fill-success absolute top-12 right-2" />}
      </div>
      {!isValide && !openSelect && !isFocusInputNumber && (
        <div className="flex items-center gap-2 mt-2">
          <Error className="fill-error size-4" />
          <p className="text-error text-sm">{error || "Invalid number"}</p>
        </div>
      )}
      {openSelect && (
        <div className="w-full max-h-52 overflow-auto bg-white absolute top-24 rounded-2xl py-3 shadow-elevation-04-1 z-50">
          {COUNTRIES.map((country, idx) => (
            <button
              className="py-5 px-4 flex items-center justify-between w-full"
              key={idx}
              onClick={() => handleSelectCountries(country)}
              type="button"
            >
              <div className="flex items-center gap-4">
                <Image
                  src={country.flag_url}
                  alt="Country flag"
                  width={28}
                  height={28}
                  className="rounded-full size-7 object-cover"
                />
                <p className="text-primary text-base">{country.name}</p>
              </div>
              <Add className="fill-success size-5 flex-none" />
            </button>
          ))}
        </div>
      )}
    </div>
  );
};
