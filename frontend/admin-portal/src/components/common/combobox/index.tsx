"use client";

import * as React from "react";

import { cn } from "@/lib/utils";

import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Check } from "@interzero/oneepr-react-ui/Icon";
import { BiChevronDown } from "react-icons/bi";

type ComboboxItem = {
  value: string;
  label: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
} & { [key: string]: any };

interface ComboboxProps {
  items: Array<ComboboxItem>;
  onSelect?: (value: ComboboxItem | null) => void;
  placeholder: string;
  emptyText: string;
  value?: string;
  searchText: string;
  disabled?: boolean;
  invalid?: boolean;
  renderItem?: (item: ComboboxItem) => React.ReactNode;
}

export function Combobox({
  items,
  placeholder,
  emptyText,
  searchText,
  value,
  onSelect = () => {},
  disabled = false,
  invalid = false,
  renderItem,
}: ComboboxProps) {
  const [open, setOpen] = React.useState(false);
  const [selectedItem, setSelectedItem] = React.useState<ComboboxItem | null>(() => {
    if (!value) return null;

    const foundItem = items.find((i) => i.value === value);

    return foundItem || null;
  });

  function handleSelectItem(newValue: string) {
    const item = items.find((i) => i.value === newValue);

    if (!item) return;

    if (!selectedItem || selectedItem.value !== item.value) {
      setSelectedItem(item);
      onSelect(item);
      setOpen(false);

      return;
    }

    setSelectedItem(null);
    onSelect(null);
    setOpen(false);

    return;
  }

  React.useEffect(() => {
    if (!value) return;

    const foundItem = items.find((i) => i.value === value);

    setSelectedItem(foundItem || null);
    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [items]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <button
          disabled={disabled}
          aria-expanded={open}
          data-selected={!!selectedItem}
          data-invalid={invalid}
          className=" h-[55px] flex items-center justify-between bg-background w-full rounded-2xl p-4 border border-tonal-dark-cream-80 text-tonal-dark-cream-60 data-[selected=true]:text-tonal-dark-cream-10 aria-expanded:outline-primary disabled:bg-tonal-dark-cream-80 data-[invalid=true]:border-error data-[invalid=true]:bg-tonal-red-90"
        >
          {selectedItem ? (renderItem ? renderItem(selectedItem) : selectedItem.label) : placeholder}
          {selectedItem
            ? !disabled && <Check className=" fill-success h-5 w-5" />
            : !disabled && <BiChevronDown className=" fill-primary h-5 w-5" />}
        </button>
      </PopoverTrigger>
      <PopoverContent sideOffset={8} className="w-[var(--radix-popover-trigger-width)] p-0 shadow-elevation-04-1">
        <Command>
          <CommandInput placeholder={searchText} />
          <CommandList>
            <CommandEmpty>{emptyText}</CommandEmpty>
            <CommandGroup>
              {items.map((item) => (
                <CommandItem
                  key={item.value}
                  value={item.value}
                  onSelect={handleSelectItem}
                  className="hover:bg-secondary"
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4 fill-primary",
                      selectedItem?.value === item.value ? "opacity-100" : "opacity-0"
                    )}
                  />
                  {renderItem ? renderItem(item) : item.label}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
