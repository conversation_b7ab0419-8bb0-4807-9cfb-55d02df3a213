"use client";
import { Skeleton } from "@/components/ui/skeleton";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { cn } from "@/utils/cn";
import {
  ColumnDef,
  PaginationState,
  SortingState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { CircleX } from "lucide-react";
import React, { memo, useCallback, useEffect, useMemo, useState } from "react";
import { ArrowDropDown, ArrowDropUp } from "@interzero/oneepr-react-ui/Icon";

interface SortingIcon {
  dir: "asc" | "desc" | false;
  size?: number;
}
const SortingIcon = memo(function SortingIcon(props: SortingIcon) {
  const { dir, size = 24 } = props;
  return (
    <div className="flex flex-col">
      <ArrowDropUp
        className={cn({
          "fill-tonal-dark-cream-40": dir === "asc",
          "stroke-tonal-dark-cream-40": dir !== "asc",
          "fill-none": dir !== "asc",
        })}
        width={size}
        height={size}
      />
      <ArrowDropDown
        className={cn("-mt-4", {
          "fill-tonal-dark-cream-40": dir === "desc",
          "stroke-tonal-dark-cream-40": dir !== "desc",
          "fill-none": dir !== "desc",
        })}
        width={size}
        height={size}
      />
    </div>
  );
});

export interface DatatableProps<TData extends { id: string | number }> {
  data: TData[];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  columns: ColumnDef<TData, any>[];
  loading?: boolean;
  onRowClick?: (row: TData) => void;
  isRowExpanded?: (rowId: string) => boolean;
  renderExpandedRow?: (row: TData) => React.ReactNode;
  selection?: {
    rowSelection: NonNullable<unknown>;
    setRowSelection: (rowSelection: NonNullable<unknown>) => void;
  };
  totalPages?: number;

  /**
   * Array of column indices that should have sorting enabled.
   *
   * @example
   * // Enable sorting for the first and second columns (0-indexed)
   * sortings: [0, 1]
   */
  sortings?: number[];
}

export function Datatable<TData extends { id: string | number }>({
  columns,
  data,
  loading,
  onRowClick,
  isRowExpanded,
  renderExpandedRow,
  selection,
  totalPages,
  sortings = [],
}: DatatableProps<TData>) {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [sorting, setSorting] = useState<SortingState>([]);
  totalPages = typeof totalPages === "number" ? totalPages : Math.ceil((data?.length || 0) / 10);
  const table = useReactTable({
    columns,
    data,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onPaginationChange: setPagination,
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onRowSelectionChange: selection?.setRowSelection,
    state: {
      pagination,
      sorting,
      rowSelection: selection?.rowSelection,
    },
  });

  const setPage = useCallback((page: number) => {
    setPagination({ pageIndex: page, pageSize: 10 });
  }, []);

  return (
    <div className="flex flex-col">
      <div className="rounded-3xl overflow-hidden  overflow-x-auto">
        <table className="w-full text-primary">
          <thead className="bg-surface-03">
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header, index) => {
                  const enabledSorting = sortings.includes(index);
                  return (
                    <th
                      key={header.id}
                      data-first={index === 0}
                      data-actions={header.column.columnDef.id === "actions"}
                      className={cn(
                        "py-3 px-2 text-left h-12 text-nowrap font-normal text-sm data-[first=true]:pl-6 data-[actions=true]:w-7",
                        enabledSorting && "flex flex-row items-center gap-1 cursor-pointer select-none"
                      )}
                      onClick={enabledSorting ? header.column.getToggleSortingHandler() : void 0}
                    >
                      {flexRender(header.column.columnDef.header, header.getContext())}
                      {enabledSorting ? <SortingIcon dir={header.column.getIsSorted()} /> : null}
                    </th>
                  );
                })}
              </tr>
            ))}
          </thead>
          <tbody className="bg-white">
            {!loading &&
              table.getRowModel().rows.map((row) => (
                <React.Fragment key={row.id}>
                  <tr
                    onClick={() => onRowClick?.(row.original)}
                    className={onRowClick ? "cursor-pointer hover:bg-[#009DD32E]" : ""}
                  >
                    {row.getVisibleCells().map((cell, index) => (
                      <td
                        key={cell.id}
                        data-first={index === 0}
                        data-actions={cell.column.columnDef.id === "actions"}
                        data-expanded={isRowExpanded?.(row.id)}
                        className="py-3 px-2 font-normal text-sm data-[first=true]:pl-6 data-[actions=true]:w-7 data-[expanded=true]:bg-support-blue/10 data-[expanded=true]:!font-bold"
                      >
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </td>
                    ))}
                  </tr>
                  {isRowExpanded?.(row.id) && renderExpandedRow && (
                    <tr>
                      <td colSpan={columns.length + 1}>{renderExpandedRow(row.original)}</td>
                    </tr>
                  )}
                </React.Fragment>
              ))}
          </tbody>
        </table>
        {!loading && !data.length && (
          <div className="py-3 px-2 w-full flex items-center justify-center gap-2">
            <CircleX width={20} height={20} className="fill-primary" />
            <p className="text-lg text-primary">No data found</p>
          </div>
        )}
        {loading && (
          <div className="w-full">
            <div className="py-3 px-2 w-full">
              <Skeleton className="h-10 w-full" />
            </div>
            <div className="py-3 px-2 w-full">
              <Skeleton className="h-10 w-full" />
            </div>
            <div className="py-3 px-2 w-full">
              <Skeleton className="h-10 w-full" />
            </div>
            <div className="py-3 px-2 w-full">
              <Skeleton className="h-10 w-full" />
            </div>
            <div className="py-3 px-2 w-full">
              <Skeleton className="h-10 w-full" />
            </div>
          </div>
        )}
      </div>
      <Pagination setPage={setPage} totalPages={totalPages} />
    </div>
  );
}

const Pagination = memo(function Pagination({
  setPage,
  totalPages,
}: {
  setPage: (p: number) => void;
  totalPages: number;
}) {
  const { paramValues, changeParams } = useQueryFilter(["page"]);
  const pages: number[] = useMemo(() => {
    return Array.from({ length: totalPages }, (_, i) => i + 1);
  }, [totalPages]);
  useEffect(() => {
    setPage(0);
    changeParams({
      page: "1",
    });

    // Unnecessary dependency that will cause frequent re-rendering, as changeParams updates constantly
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [totalPages]);
  return (
    <div className="flex items-center gap-1 text-primary mx-auto mt-6">
      {pages.map((p, i) => (
        <span
          key={i}
          onClick={() => {
            setPage(p - 1);
            changeParams({
              page: p.toString(),
            });
          }}
          data-current={p.toString() === (paramValues.page || "1")}
          className={cn(
            "py-1 px-2 rounded-lg cursor-pointer font-bold data-[current=true]:bg-primary data-[current=true]:text-white"
          )}
        >
          {p}
        </span>
      ))}
    </div>
  );
});
