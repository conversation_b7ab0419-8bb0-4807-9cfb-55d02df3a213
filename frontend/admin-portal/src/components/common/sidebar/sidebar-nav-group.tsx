"use client";

import { ArrowDropDown } from "@interzero/oneepr-react-ui/Icon";
import { ComponentProps, DetailsHTMLAttributes, ReactNode } from "react";

interface SidebarNavGroupProps extends DetailsHTMLAttributes<HTMLDetailsElement> {
  title: string;
  Icon: (props: ComponentProps<"svg">) => JSX.Element;
  children: ReactNode;
  startOpen?: boolean;
}

export function SidebarNavGroup({ title, Icon, children, startOpen = false, ...props }: SidebarNavGroupProps) {
  return (
    <details
      className="
        open:bg-white w-full
      [&>summary]:has-[a[data-active=true]]:border-support-blue [&>summary]:has-[a[data-active=true]]:text-support-blue
      [&>summary>svg]:has-[a[data-active=true]]:fill-support-blue 
      [&>summary>p]:has-[a[data-active=true]]:text-support-blue [&>summary>p]:has-[a[data-active=true]]:hover:underline
      [&>summary>.sidebar-chevron]:open:rotate-180
    "
      open={startOpen}
      {...props}
    >
      <summary className="px-6 py-4 flex items-center gap-4 cursor-pointer hover:bg-[#FCFCFC] border-l-[3px] border-white">
        <Icon width={20} className="fill-primary" />
        <p className="text-primary text-md font-bold underline-offset-2">{title}</p>
        <ArrowDropDown width={32} className="sidebar-chevron ml-auto fill-primary transition-all duration-300" />
      </summary>
      <div className="animate-slideDownAndFade transition-all duration-300">{children}</div>
    </details>
  );
}
