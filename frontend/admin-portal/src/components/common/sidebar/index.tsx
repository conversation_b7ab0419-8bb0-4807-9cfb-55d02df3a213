"use client";

import { useSidebar } from "@/hooks/use-sidebar";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@interzero/oneepr-react-ui/Figure";
import {
  AccountCircle,
  Dashboard,
  Grade,
  Handshake,
  LocalOffer,
  MapsHomeWork,
  Payments,
  PeopleAlt,
  Pin,
  Receipt,
  StarRate,
  Upload,
  WorkspacePremium,
} from "@interzero/oneepr-react-ui/Icon";
import { BiX } from "react-icons/bi";
import { SidebarNavItem } from "./sidebar-nav-item";
import { SidebarNavGroup } from "./sidebar-nav-group";
import { SidebarProfile } from "./sidebar-profile";
import { getCountries } from "@/lib/api/countries";
import { CountryIcon } from "@/components/ui/country-icon";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { Role } from "@/utils/user";
export function Sidebar() {
  const session = useSession();
  const sidebar = useSidebar();

  const user = session.data?.user || null;

  const { data: countries } = useQuery({
    queryKey: ["countries"],
    queryFn: getCountries,
  });

  return (
    <div
      data-show={sidebar.open}
      className="flex-none max-lg:data-[show=false]:hidden fixed overflow-hidden max-lg:z-[9999] max-lg:h-full lg:relative w-full overflow-y-auto lg:w-[300px] border-r-[1px] border-tonal-dark-cream-80 bg-[#FCFCFC] z-50"
    >
      <div className="h-3 w-full bg-primary hidden lg:block"></div>
      <div className="border-b-[1px] border-tonal-dark-cream-80 p-6 space-y-10">
        <div className="h-10 w-full flex justify-between items-center">
          <LizenzeroLogo width={150} />
          <BiX
            onClick={() => sidebar.setOpen(false)}
            size={24}
            className="fill-primary hover:fill-grey-blue cursor-pointer lg:hidden"
          />
        </div>
        {/* Profile */}
        <SidebarProfile />
      </div>
      <div className="w-full flex flex-col gap-3 py-6">
        {!!user && [Role.SUPER_ADMIN, Role.ADMIN].includes(user.role) && (
          <>
            <SidebarNavItem href="/en/dashboard" title="Dashboard" Icon={Dashboard} />
            <SidebarNavItem href="/en/accounts" title="Accounts" Icon={AccountCircle} />
            <SidebarNavItem href="/en/price-lists" title="General Price Lists" Icon={Receipt} />
            <SidebarNavGroup title="Countries" Icon={StarRate}>
              {countries?.map((country) => (
                <SidebarNavItem
                  key={country.id}
                  href={`/countries/${country.code}`}
                  title={country.name}
                  Icon={(props) => <CountryIcon country={country} {...props} />}
                />
              ))}
            </SidebarNavGroup>
            {/* <SidebarNavItem href="/en/notifications" title="Notifications" Icon={NotificationAdd} /> */}
            {user.role === Role.SUPER_ADMIN && (
              <SidebarNavItem href="/en/access-manager" title="Access Manager" Icon={PeopleAlt} />
            )}
          </>
        )}
        {!!user && user.role === Role.MARKETING_MANAGER && (
          <>
            <SidebarNavItem href="/en/dashboard-marketing" title="Dashboard" Icon={Dashboard} />
            <SidebarNavItem href="/en/discount-codes" title="Discount Code Manager" Icon={LocalOffer} />
            <SidebarNavItem href="/en/invite-customers-manager" title="Invite Customers Manager" Icon={Handshake} />
            <SidebarNavItem href="/en/customer-clusters" title="Customer's Clusters" Icon={Pin} />
            <SidebarNavItem href="/en/partner-hub" title="Partner Hub" Icon={LocalOffer} />
            <SidebarNavItem href="/en/marketing-materials" title="Marketing Materials" Icon={Pin} />
          </>
        )}
        {!!user && [Role.BROKER_MANAGER, Role.BROKER].includes(user.role) && (
          <>
            {user.role === Role.BROKER_MANAGER && (
              <SidebarNavItem href="/en/brokers-data" title="Brokers Data" Icon={AccountCircle} />
            )}
            {user.role === Role.BROKER && (
              <SidebarNavItem href="/en/my-companies" title="My Companies" Icon={MapsHomeWork} />
            )}
            <SidebarNavItem href="/en/certificates" title="Certificates" Icon={WorkspacePremium} />
            <SidebarNavItem href="/en/invoices" title="Invoices" Icon={Payments} />
            <SidebarNavItem href="/en/orders" title="Orders" Icon={Grade} />
            <SidebarNavItem href="/en/upload-data" title="Upload Data" Icon={Upload} />
          </>
        )}
      </div>
    </div>
  );
}
