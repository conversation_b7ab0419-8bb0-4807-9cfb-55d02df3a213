"use client";

import { cn } from "@/utils/cn";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { AnchorHTMLAttributes } from "react";

interface SidebarNavItemProps extends AnchorHTMLAttributes<HTMLAnchorElement> {
  href: string;
  title: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Icon: (props: any) => JSX.Element;
  active?: boolean;
  className?: string;
  exact?: boolean;
}

export function SidebarNavItem({ href, title, Icon, active, className, exact, ...props }: SidebarNavItemProps) {
  const pathname = usePathname();

  const isActive = exact ? `/${pathname.split("/").slice(-2).join("/")}` === href : pathname.includes(href);

  return (
    <Link
      aria-selected={isActive || active || false}
      href={href}
      data-active={isActive || active || false}
      className={cn(
        "group/nav-item px-6 py-3 flex items-center gap-4 cursor-pointer hover:bg-white border-l-[3px] data-[active=true]:bg-white border-white  data-[active=true]:border-support-blue",
        className
      )}
      {...props}
    >
      <Icon width={24} className="fill-primary group-data-[active=true]/nav-item:fill-support-blue" />
      <p className="text-primary group-data-[active=true]/nav-item:text-support-blue group-data-[active=true]/nav-item:font-bold group-hover/nav-item:text-support-blue text-md group-hover/nav-item:underline underline-offset-2">
        {title}
      </p>
    </Link>
  );
}
