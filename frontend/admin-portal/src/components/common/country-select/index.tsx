"use client";

import { CountryIcon } from "@/components/common/country-icon";
import {
  Combobox,
  ComboboxContent,
  ComboboxEmpty,
  ComboboxGroup,
  ComboboxItem,
  ComboboxList,
  ComboboxSearch,
  ComboboxTrigger,
} from "@/components/common/combobox/refactor";
import { COUNTRIES } from "@/utils/countries";
import { cn } from "@/utils/cn";
import { BiChevronDown } from "react-icons/bi";
import { useState } from "react";

interface CountrySelectProps {
  value?: string;
  onValueChange?: (value: string) => void;
  disabled?: boolean;
  className?: string;
  contentClassName?: string;
  placeholder?: string;
  modal?: boolean;
  errorMessage?: string;
}

export function CountrySelect({
  value,
  onValueChange,
  disabled,
  className,
  contentClassName,
  placeholder = "Select a country",
  modal = false,
  errorMessage,
}: CountrySelectProps) {
  const [isOpen, setIsOpen] = useState(false);

  const selectedCountry = COUNTRIES.find((country) => country.code === value);

  const handleSelect = (countryName: string) => {
    const country = COUNTRIES.find((c) => c.name === countryName);
    if (country && onValueChange) {
      onValueChange(country.code);
      setIsOpen(false);
    }
  };

  return (
    <>
      <div
        data-disabled={disabled}
        className={cn(
          "flex items-center bg-background w-full rounded-2xl p-4 border border-tonal-dark-cream-80 text-tonal-dark-cream-60 data-[selected=true]:text-tonal-dark-cream-10 aria-expanded:outline-primary data-[disabled=true]:bg-tonal-dark-cream-80",
          errorMessage && "border-error text-error bg-tonal-red-90",
          className
        )}
      >
        <Combobox modal={modal} open={isOpen} onOpenChange={setIsOpen}>
          <ComboboxTrigger disabled={disabled} asChild>
            <button className="flex items-center gap-2 w-full">
              {selectedCountry ? (
                <CountryIcon
                  country={selectedCountry}
                  data-disabled={disabled}
                  className="data-[disabled=true]:grayscale"
                />
              ) : null}
              <span className={cn("flex-1 text-left truncate", !selectedCountry && "text-tonal-dark-cream-60")}>
                {selectedCountry ? selectedCountry.name : placeholder}
              </span>
              <BiChevronDown className={cn("h-5 w-5", disabled ? "fill-tonal-dark-cream-80" : "fill-primary")} />
            </button>
          </ComboboxTrigger>
          <ComboboxContent
            alignOffset={0}
            sideOffset={16}
            align="center"
            className={cn("w-full p-0 shadow-elevation-04-1", contentClassName)}
          >
            <ComboboxSearch className="text-primary" placeholder="Search country..." />
            <ComboboxList>
              <ComboboxEmpty>No country found</ComboboxEmpty>
              <ComboboxGroup>
                {COUNTRIES.map((country) => (
                  <ComboboxItem
                    key={country.code}
                    value={country.name}
                    onSelect={handleSelect}
                    className="hover:bg-secondary text-primary"
                  >
                    <CountryIcon country={country} className="size-6 mr-2" />
                    {country.name}
                  </ComboboxItem>
                ))}
              </ComboboxGroup>
            </ComboboxList>
          </ComboboxContent>
        </Combobox>
      </div>
      {errorMessage && <span className="text-error text-sm">{errorMessage}</span>}
    </>
  );
}
