import React from "react";
import { Dropdown } from "@/components/common/dropdown";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { FilterAlt, KeyboardArrowDown } from "@interzero/oneepr-react-ui/Icon";
import { cn } from "@/lib/utils";

export interface DropdownItem {
  label: string;
  value: string;
}

export interface CustomDropdownProps {
  options: DropdownItem[];
  showFilterIcon?: boolean;
  triggerClassName?: string;
  selectedOption?: string;
  handleSelect?: (selectedOptionValue: string, selectedOption: DropdownItem) => void;
  textClassName?: string;
}

export function CustomDropdown({
  options,
  selectedOption,
  showFilterIcon = true,
  triggerClassName,
  handleSelect,
  textClassName,
}: CustomDropdownProps) {
  const selectedDropdownOption = options.find((filter) => filter.value === selectedOption) || options[0];

  return (
    <Dropdown
      trigger={
        <button className={cn("flex items-center gap-1 text-support-blue font-bold text-base", triggerClassName)}>
          {showFilterIcon && <FilterAlt width={20} height={20} className="fill-support-blue" />}
          <span className={textClassName}>{selectedDropdownOption.label}</span>
          <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
        </button>
      }
    >
      {options.map((option, idx) => (
        <DropdownMenu.Item
          key={idx}
          className="group py-5 px-4 text-base focus:outline-none cursor-pointer hover:bg-surface-02"
          onClick={(e) => {
            e.stopPropagation();
            if (handleSelect) handleSelect(option.value, option);
          }}
          style={{
            color: option.value === selectedDropdownOption.value ? "#002652" : "#242423",
            fontWeight: option.value === selectedDropdownOption.value ? "bold" : "normal",
          }}
        >
          {option.label}
        </DropdownMenu.Item>
      ))}
    </Dropdown>
  );
}
