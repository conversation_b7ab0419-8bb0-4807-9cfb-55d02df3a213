import { FileCopy, Pin } from "@interzero/oneepr-react-ui/Icon";
import { ModuleContent } from "@/components/common/module-content";
import { ModuleTitle } from "@/components/common/module-title";
import { DuplicateCustomerClusterForm } from "@/components/modules/customer-cluster/duplicate-customer-cluster-form";

interface DuplicateCustomerClusterPageProps {
  params: {
    customerClusterId: string;
  };
}

export default function DuplicateCustomerClusterPage({ params }: DuplicateCustomerClusterPageProps) {
  const { customerClusterId } = params;

  return (
    <div className="flex flex-col flex-1">
      <ModuleContent containerClassName="pb-12">
        <div className="mb-3 flex items-center gap-2">
          <Pin className="size-6 fill-primary" />
          <span className="text-[#183362]">Customer&apos;s Clusters</span>
        </div>
        <ModuleTitle
          icon={FileCopy}
          title="Duplicate cluster"
          description="Review settings before duplicating a cluster."
          className="mb-0"
        />
      </ModuleContent>
      <div className="bg-[#EDE9E4] w-full flex-1 py-[72px]">
        <DuplicateCustomerClusterForm customerClusterId={customerClusterId} />
      </div>
    </div>
  );
}
