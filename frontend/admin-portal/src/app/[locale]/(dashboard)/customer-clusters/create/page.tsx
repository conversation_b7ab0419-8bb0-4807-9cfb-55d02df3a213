import { ModuleTitle } from "@/components/common/module-title";
import { ModuleContent } from "@/components/common/module-content";
import { FileCopy, Pin } from "@interzero/oneepr-react-ui/Icon";
import { CreateCustomerClusterForm } from "@/components/modules/customer-cluster/create-customer-cluster-form";

export default function CreateCustomerClusterPage() {
  return (
    <div className="flex flex-col flex-1">
      <ModuleContent containerClassName="pb-12">
        <div className="mb-3 flex items-center gap-2">
          <Pin className="size-6 fill-primary" />
          <span className="text-[#183362]">Customer&apos;s Clusters</span>
        </div>
        <ModuleTitle
          icon={FileCopy}
          title="Create new cluster"
          description="Create a new customer's cluster."
          className="mb-0"
        />
      </ModuleContent>
      <div className="bg-[#EDE9E4] w-full flex-1 py-[72px]">
        <CreateCustomerClusterForm />
      </div>
    </div>
  );
}
