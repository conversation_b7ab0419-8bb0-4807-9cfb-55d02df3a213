import { CountryDropdown } from "@/components/ui/country-dropdown";
import { <PERSON><PERSON><PERSON><PERSON>Logo } from "@interzero/oneepr-react-ui/Figure";

export default function AuthLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="h-full min-h-screen w-full flex items-stretch">
      <div className="flex-1 bg-surface-02">
        <div className="bg-surface-02 w-full h-20 pt-6 pb-4 border-b-[1px] border-tonal-dark-cream-80 px-4">
          <div className="w-full my-auto lg:max-w-screen-lg mx-auto flex justify-between items-center">
            <LizenzeroLogo width={150} />
            <div className="flex items-center gap-4 lg:gap-10">
              <CountryDropdown />
            </div>
          </div>
        </div>
        <div className="w-full">{children}</div>
      </div>
    </div>
  );
}
