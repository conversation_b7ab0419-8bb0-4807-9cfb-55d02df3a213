/* eslint-disable @typescript-eslint/no-explicit-any */

/* eslint-disable @typescript-eslint/no-unused-vars */

import { ProvidersContext } from "@/context/providers.context";
import { ShortLocaleOptions, ShortLocales, ShortLocalesArr } from "@/i18n/locales";
import "@/styles/globals.css";
import type { Metadata } from "next";
import localFont from "next/font/local";

const centraNo2 = localFont({
  src: [
    {
      path: "../../public/fonts/CentraNo2/CentraNo2-Thin.woff",
      weight: "100",
    },
    {
      path: "../../public/fonts/CentraNo2/CentraNo2-Light.woff",
      weight: "300",
    },
    {
      path: "../../public/fonts/CentraNo2/CentraNo2-Book.woff",
      weight: "400",
    },
    {
      path: "../../public/fonts/CentraNo2/CentraNo2-Medium.woff",
      weight: "500",
    },
    {
      path: "../../public/fonts/CentraNo2/CentraNo2-Bold.woff",
      weight: "700",
    },
    {
      path: "../../public/fonts/CentraNo2/CentraNo2-Extrabold.woff",
      weight: "800",
    },
    {
      path: "../../public/fonts/CentraNo2/CentraNo2-Black.woff",
      weight: "900",
    },
  ],
  variable: "--font-centra",
});

export const metadata: Metadata = {
  title: "Lizenzero | Admin",
  description: "Lizenzero | Admin",
};

export default function RootLayout({
  children,
  params: { locale },
}: {
  children: React.ReactNode;
  params: { locale: ShortLocaleOptions; session: any };
}) {
  return (
    <html lang={ShortLocalesArr.includes(locale as ShortLocaleOptions) ? locale : ShortLocales.ENGLISH}>
      <ProvidersContext>
        <body className={`${centraNo2.className} leading-none bg-background`}>{children}</body>
      </ProvidersContext>
    </html>
  );
}
