import { useForm, UseFormProps, UseFormReturn } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { CreateCouponParams } from "@/lib/api/coupon/types";

export const couponFormSchema = z
  .object({
    status: z.enum(["active", "inactive"]),
    linkOrCode: z.enum(["LINK", "CODE"]).default("CODE"),
    voucherName: z.string().trim().min(1, "Mandatory"),
    description: z.string().trim().optional(),
    note: z.string().trim().optional(),
    redeemableVouchersPerCustomer: z.coerce.number().min(1, "Mandatory"),
    isRedeemableToNewCustomers: z.boolean(),
    vouchersNumber: z.coerce.number().min(1, "Mandatory"),
    start_date: z.coerce.date().refine((date) => date > new Date(), "Start date must be in the future"),
    end_date: z.coerce.date().refine((date) => date > new Date(), "End date must be in the future"),
    voucherMode: z.enum(["GENERAL", "INDIVIDUAL", "GROUP_SEGMENT"]),
    voucherType: z.enum(["PERCENTAGE", "ABSOLUTE", "BUY_X_PRODUCTS_GET_Y_PRODUCTS", "BUY_X_PRODUCTS_GET_Y_DISCOUNT"]),
    couponValue: z.coerce.number().optional(),
    buyValue: z.coerce.number().optional(),
    getValue: z.coerce.number().optional(),
    buyValueProduct: z.string().optional(),
    getValueProduct: z.string().optional(),
    minimumOrderValue: z.coerce.number({ invalid_type_error: "Mandatory" }).min(1, "Mandatory"),
    maximumOrderValue: z.coerce.number({ invalid_type_error: "Mandatory" }).min(1, "Mandatory"),
    minimumProductsToPromotion: z.coerce.number({ invalid_type_error: "Mandatory" }).min(1, "Mandatory"),
    isApplyToAllProducts: z.boolean(),
    voucherIndividualModeType: z.enum(["INDIVIDUAL", "GROUP_SEGMENT"]),
    selectedCustomers: z
      .array(
        z.object({
          label: z.string(),
          value: z.string(),
        })
      )
      .optional(),
    selectedCountries: z.array(z.string()).optional(),
    selectedProducts: z.array(z.enum(["DIRECT_LICENSE", "EU_LICENSE", "ACTION_GUIDE"])).optional(),
    directLicenseYears: z.array(z.coerce.number()).optional(),
    euLicenseCountries: z.array(z.string()).optional(),
    actionGuideCountries: z.array(z.string()).optional(),
  })
  .refine((data) => data.end_date > data.start_date, {
    message: "End date cannot be earlier than start date.",
    path: ["end_date"],
  })
  .refine(
    (data) => {
      if (data.voucherType === "PERCENTAGE" || data.voucherType === "ABSOLUTE") {
        if (!data.couponValue) return false;
      }
      return true;
    },
    { message: "Mandatory", path: ["couponValue"] }
  );

export type CouponFormData = z.infer<typeof couponFormSchema>;

export function useCouponForm(params?: UseFormProps<CouponFormData>) {
  return useForm<CouponFormData>({
    resolver: zodResolver(couponFormSchema),
    defaultValues: {
      status: "active",
      isRedeemableToNewCustomers: false,
      voucherMode: "GENERAL",
      voucherIndividualModeType: "INDIVIDUAL",
      isApplyToAllProducts: true,
      selectedProducts: [],
      directLicenseYears: [],
      euLicenseCountries: [],
      actionGuideCountries: [],
    },
    ...params,
  });
}

type ValidateCouponFormParams = {
  data: CouponFormData;
  form: UseFormReturn<CouponFormData>;
  /* Default is true */
  throwOnError?: boolean;
};
export function validateCouponForm({ data, form, throwOnError = true }: ValidateCouponFormParams) {
  const params: CreateCouponParams = {
    code: data.voucherName,
    link: data.linkOrCode === "LINK" ? `/${data.voucherName}` : undefined,
    discount_type: data.voucherType,
    end_date: data.end_date.toISOString(),
    is_active: data.status === "active",
    max_amount: data.maximumOrderValue,
    max_uses: data.vouchersNumber,
    max_uses_per_customer: data.redeemableVouchersPerCustomer,
    min_amount: data.minimumOrderValue,
    mode: data.voucherMode,
    redeemable_by_new_customers: data.isRedeemableToNewCustomers,
    start_date: data.start_date.toISOString(),
    type: "SYSTEM",
    value: data.couponValue ?? 0,
    customers: data.selectedCustomers?.map((customer) => Number(customer.value)) ?? [],
    description: data.description,
    min_products: data.minimumProductsToPromotion,
    note: data.note,
  };

  const isGeneral = data.voucherMode === "GENERAL";
  const isPercentageOrAbsolute = data.voucherType === "PERCENTAGE" || data.voucherType === "ABSOLUTE";

  if (isGeneral && isPercentageOrAbsolute) {
    if (!data.couponValue) {
      form.setError("couponValue", { message: "Mandatory" });
      if (throwOnError) throw new Error("Mandatory");
      return;
    }
    // README: The value come in cents IF its using FractionInput, if not, you need to convert it.
    // Today, only the FractionInput is used in the fields: 'couponValue', 'minimumOrderValue' and 'maximumOrderValue'
    params.value = data.couponValue;
    params.min_amount = data.minimumOrderValue;
    params.max_amount = data.maximumOrderValue;
    params.discount_type = data.voucherType;
  }

  const isBuyXGetYProductsForFree = data.voucherType === "BUY_X_PRODUCTS_GET_Y_PRODUCTS";

  if (isGeneral && isBuyXGetYProductsForFree) {
    if (!data.buyValue || !data.getValue) {
      form.setError("buyValue", { message: "Mandatory" });
      form.setError("getValue", { message: "Mandatory" });
      if (throwOnError) throw new Error("Mandatory");
      return;
    }

    if (!data.buyValueProduct || !data.getValueProduct) {
      form.setError("buyValueProduct", { message: "Mandatory" });
      form.setError("getValueProduct", { message: "Mandatory" });
      if (throwOnError) throw new Error("Mandatory");
      return;
    }

    const productToBeBuyed =
      data.buyValueProduct === "EU License"
        ? "eu_license"
        : data.buyValueProduct === "Direct License"
          ? "direct_license"
          : "action_guide";
    const productToBeReceived = data.getValueProduct === "Workshop" ? "workshop" : "action_guide";

    params.buy_x_get_y = {
      buyProduct: productToBeBuyed,
      buyAtLeast: data.buyValue,
      receiveProduct: productToBeReceived,
      receiveAtLeast: data.getValue,
    };
    params.value = 0;
  }

  const isBuyXGetYDiscount = data.voucherType === "BUY_X_PRODUCTS_GET_Y_DISCOUNT";

  if (isGeneral && isBuyXGetYDiscount) {
    if (!data.buyValue || !data.getValue) {
      form.setError("buyValue", { message: "Mandatory" });
      form.setError("getValue", { message: "Mandatory" });
      if (throwOnError) throw new Error("Mandatory");
      return;
    }

    const productToBeBuyed =
      data.buyValueProduct === "EU License"
        ? "eu_license"
        : data.buyValueProduct === "Direct License"
          ? "direct_license"
          : "action_guide";
    const discountType = data.getValueProduct === "Discount (%)" ? "PERCENTAGE" : "ABSOLUTE";

    params.buy_x_get_y = {
      buyProduct: productToBeBuyed,
      buyAtLeast: data.buyValue,
      discountType: discountType,
      discountValue: data.getValue * 1000,
    };
    params.value = 0;
  }

  if (!data.isApplyToAllProducts) {
    params.elegible_products = {
      ...((data?.directLicenseYears ?? []).length > 0 && { direct_license: { years: data.directLicenseYears } }),
      ...((data?.euLicenseCountries ?? []).length > 0 && { eu_license: { countries: data.euLicenseCountries } }),
      ...((data?.actionGuideCountries ?? []).length > 0 && { action_guide: { countries: data.actionGuideCountries } }),
    };
  }

  return params;
}
