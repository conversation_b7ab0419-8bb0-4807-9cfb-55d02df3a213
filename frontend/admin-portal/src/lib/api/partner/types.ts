import { Coupon } from "../coupon/types";
import { Company, UploadedFile } from "../customer/types";
import { MarketingMaterial } from "../marketing-materials/types";

export type Partner = {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  user_id: number;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  commission_mode?: string;
  no_provision_negotiated?: boolean;
  payout_cycle?: string;
  commission_amount?: number;
  status?: PartnerStatus;
  companies?: Company[];
  coupons?: CouponPartners[];
  marketing_material_partners?: MarketingMaterialPartner[];
  partner_banking?: PartnerBanking;
  partner_contract?: PartnerContract;
};

export type PartnerStatus =
  | "NO_UPDATES"
  | "IMPROVED_CONTRACT"
  | "DENIED_CONTRACT"
  | "REQUESTED_COMMISSION"
  | "CHANGED_INFORMATION";

export type CouponPartners = {
  id: number;
  coupon_id: number;
  partner_id: number;
  coupon: Coupon;
};

export type MarketingMaterialPartner = {
  id: number;
  marketing_material_id: number;
  partner_id: number;
  marketing_material: MarketingMaterial;
};

export type PartnerBanking = {
  id: number;
  partner_id: number;
  business_identifier_code: string;
  international_account_number: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
};

export type PartnerContract = {
  id: number;
  partner_id: number;
  status: PartnerContractStatus;
  agreed_on: string;
  start_date: string;
  end_date: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  files: UploadedFile[];
  changes: PartnerContractChange[];
};

export type PartnerContractStatus = "DRAFT" | "ACTIVE" | "EXPIRED" | "TERMINATED" | "TO_BE_SIGNED" | "DENIED";

export type PartnerContractChange = {
  id: number;
  partner_contract_id: number;
  partner_contract: PartnerContract;
  change_type: PartnerContractChangeType;
  change_description?: string;
  created_at: string;
};

export type PartnerContractChangeType = "EMAIL" | "NOTE" | "FILE";

export type GetPartnersPaginatedResponse = {
  partners: Partner[];
  count: number;
  pages: number;
  current_page: number;
  limit: number;
};

export type CreatePartnerParams = {
  contract_file: File;
  partner_firstname: string;
  partner_lastname: string;
  partner_email: string;
  partner_password: string;
  user_id?: number;
  banking?: {
    international_account_number: string;
    business_identifier_code: string;
  };
  company?: {
    id?: number;
    name: string;
    industry_sector: string;
    starting_date: string;
    website: string;
    description: string;
    owner_name: string;
    country_code: string;
    city: string;
    zip_code: string;
    street_and_number: string;
    additional_address_line?: string;
    contact_name: string;
    contact_email: string;
    contact_phone: string;
  };
  no_provision_negotiated?: boolean;
  payout_cycle?: string;
  commission_mode?: string;
  coupons?: number[];
  marketing_material_id?: number;
  new_marketing_material_name?: string;
  new_marketing_material_files?: File[];
};

export type UpdatePartnerParams = {
  contract_file?: File;
  partner_firstname?: string;
  partner_lastname?: string;
  banking?: CreatePartnerParams["banking"];
  company?: CreatePartnerParams["company"];
  no_provision_negotiated?: boolean;
  payout_cycle?: string;
  commission_mode?: string;
  coupons?: number[];
  marketing_material_id?: number;
};

export type UpdatePartnerContractParams = {
  contract_id: number;
  status?: PartnerContractStatus;
  agreed_on?: string;
  start_date?: string;
  end_date?: string;
  change_type: PartnerContractChangeType;
  change_description?: string;
  file?: File;
};

export type GetPartnerCommissionsParams = {
  product?: "all" | "direct_license" | "eu_license" | "action_guide";
  lead_type?: "all" | "code" | "link";
  query?: string;
  start_date?: string;
  end_date?: string;
  page?: number;
  limit?: number;
};

export type GetPartnerCommissionsResponse = {
  items: Commission[];
  count: number;
  pages: number;
  current_page: number;
  limit: number;
};

export type GetPartnerGlobalStatisticsResponse = {
  total_acquired_clients: number;
  total_earnings: number;
  direct_license: number;
  eu_license: number;
  action_guide: number;
};

export type GetRevenuePlusEarningsOfTopFivePartnersResponse = {
  year: number;
  partners: Array<{
    partner_name: string;
    revenue: number;
    earnings: number;
  }>;
};

export type GetDiscountLinksUsageResponse = {
  year: number;
  partners: Array<{
    partner_name: string;
    discount_uses: number;
    link_uses: number;
  }>;
};

export type GetPartnerCouponsPerformanceResponse = {
  year: number;
  groupBy: "year" | "quarter";
  data: {
    code: string;
    values:
      | {
          quarter: string;
          value: number;
        }[]
      | {
          year: string;
          value: number;
        }[];
  }[];
};

export type GetLicensesPurchasedOvertimeResponse = {
  year: number;
  eu_license: number;
  direct_license: number;
  action_guide: number;
};

export type Commission = {
  id: number;
  user_id: number;
  user_type: "CUSTOMER" | "PARTNER";
  commission_percentage: number;
  commission_value: number;
  net_turnover: number;
  type: "AFFILIATE_LINK" | "COUPON";
  coupon_id?: number;
  coupon_code?: string;
  affiliate_link?: string;
  order_id: string;
  service_type: "DIRECT_LICENSE" | "EU_LICENSE" | "ACTION_GUIDE";
  created_at: string;
  order_customer_id?: number;
  coupon?: Coupon;
};
