import { adminApi } from "@/lib/api";
import { ApiEndpoints } from "@/lib/api/endpoints";
import { CreateOtherCost, OtherCost, UpdateOtherCost } from "@/types/service-setup/other-cost";

export async function getOtherCosts(countryId: number) {
  const response = await adminApi.get<OtherCost[]>(ApiEndpoints.otherCosts.findAll, {
    params: {
      countryId,
    },
  });

  return response.data;
}

export async function createOtherCost(otherCost: CreateOtherCost) {
  const response = await adminApi.post(ApiEndpoints.otherCosts.create, otherCost);

  return response.data;
}

export async function updateOtherCost(otherCostId: number, otherCost: UpdateOtherCost) {
  const response = await adminApi.put(ApiEndpoints.otherCosts.update(otherCostId), otherCost);

  return response.data;
}

export async function deleteOtherCost(otherCostId: number) {
  await adminApi.delete(ApiEndpoints.otherCosts.delete(otherCostId));
}
