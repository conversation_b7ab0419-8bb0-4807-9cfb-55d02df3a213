import { adminApi } from "@/lib/api";
import { ApiEndpoints } from "@/lib/api/endpoints";
import { AdminUser } from "@/types/admin-user";
export interface GetAdminUsersParams {
  search?: string;
  role?: "ADMIN" | "CLERK" | "MARKETING_MANAGER" | "BROKER_MANAGER";
  is_active?: boolean;
}

export async function getAdminUsers({ search: name, role, is_active }: GetAdminUsersParams) {
  const response = await adminApi.get<AdminUser[]>(ApiEndpoints.adminUsers.findAll, {
    params: { name, role, is_active },
  });

  return response.data;
}

export async function getAdminUserById(userId: number) {
  const response = await adminApi.get<AdminUser>(ApiEndpoints.adminUsers.findById(userId));

  return response.data;
}

export async function updateAdminUser(userId: number, data: Partial<AdminUser>) {
  const response = await adminApi.put<AdminUser>(ApiEndpoints.adminUsers.update(userId), data);

  return response.data;
}
