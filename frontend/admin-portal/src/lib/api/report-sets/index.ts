import { adminApi } from "@/lib/api";
import { ApiEndpoints } from "@/lib/api/endpoints";
import { CreateReportSet, ReportSet, UpdateReportSet } from "@/types/service-setup/report-set";

interface GetReportSetsParams {
  packagingServiceId?: number;
}

export async function getReportSets({ packagingServiceId }: GetReportSetsParams) {
  const response = await adminApi.get<ReportSet[]>(ApiEndpoints.reportSets.findAll, {
    params: {
      packaging_service_id: packagingServiceId,
    },
  });

  return response.data;
}

export async function createReportSet(reportSet: CreateReportSet) {
  const response = await adminApi.post<ReportSet>(ApiEndpoints.reportSets.create, reportSet);

  return response.data;
}

export async function updateReportSet(reportSetId: number, reportSet: UpdateReportSet) {
  const response = await adminApi.put<ReportSet>(ApiEndpoints.reportSets.update(reportSetId), reportSet);

  return response.data;
}

export async function duplicateReportSet(reportSetId: number) {
  const response = await adminApi.post<ReportSet>(ApiEndpoints.reportSets.duplicate(reportSetId));

  return response.data;
}

export async function deleteReportSet(reportSetId: number) {
  await adminApi.delete(ApiEndpoints.reportSets.delete(reportSetId));
}
