export type MarketingMaterial = {
  id: number;
  name: string;
  start_date?: string;
  end_date?: string;
  category: "STANDARD" | "SPECIFIC_MATERIAL";
  partner_restriction: "ALL" | "CLUSTER" | "SPECIFIC";
  files?: MarketingMaterialFile[];
  partners?: Array<{ id: number; marketing_material_id: number; partner_id: number }>;
};

type MarketingMaterialFile = {
  id: string;
  user_id: string;
  name: string;
  original_name: string;
  extension: string;
  size: string;
  type: FileType;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  marketing_material_id: number;
};

type FileType =
  | "GENERAL_INFORMATION"
  | "REQUIRED_INFORMATION"
  | "CONTRACT"
  | "CONTRACT_TERMINATION"
  | "LICENSE_CONTRACT"
  | "CERTIFICATE"
  | "INVOICE"
  | "PAYMENT"
  | "LICENSE_PROOF_OF_REGISTRATION";

export type GetAllMarketingMaterialsParams = {
  page?: number;
  limit?: number;
  name?: string;
  is_active?: boolean;
};

export type GetAllMarketingMaterialsResponse = {
  marketingMaterials: MarketingMaterial[];
  count: number;
  pages: number;
  current_page: number;
  limit: number;
};

export type CreateMarketingMaterialParams = {
  name: string;
  start_date?: string;
  end_date?: string;
  category: MarketingMaterial["category"];
  partner_restriction: MarketingMaterial["partner_restriction"];
  partners?: number[];
  files: File[];
};

export type UpdateMarketingMaterialParams = CreateMarketingMaterialParams;

export type MarketingMaterialWithPartners = Omit<MarketingMaterial, "partners"> & {
  partners: {
    id: number;
    marketing_material_id: number;
    partner_id: number;
    partner: {
      created_at: string;
      deleted_at?: string;
      email: string;
      first_name: string;
      id: number;
      last_name: string;
      updated_at: string;
      user_id: number;
    };
  }[];
};
