import { customerApi } from "@/lib/api";

export async function authenticate(email: string, password: string) {
  try {
    const response = await customerApi.post(
      "/auth/login",
      {
        email,
        password,
      },
      {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      }
    );

    if (response.status !== 201) {
      return response;
    }

    return response.data;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.log("LOGIN ERROR", error);
    return error;
  }
}

export async function requestPasswordReset(email: string, callbackUrl: string) {
  try {
    const response = await customerApi.post(
      "/auth/user/request/password",
      {
        email,
        callbackUrl,
      },
      {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      }
    );

    if (response.status !== 201) {
      return response;
    }

    return response.data;
  } catch (error) {
    return error;
  }
}

export async function recoverPassword(token: string, password: string, type: string) {
  try {
    const response = await customerApi.post(
      "/auth/user/reset/password",
      {
        token,
        password,
        type,
      },
      {
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      }
    );

    if (response.status !== 201) {
      return response;
    }

    return response.data;
  } catch (error) {
    return error;
  }
}
