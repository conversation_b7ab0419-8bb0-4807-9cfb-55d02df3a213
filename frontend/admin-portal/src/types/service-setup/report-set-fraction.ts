export interface ReportSetFraction {
  id: number;
  parent_id: number | null;
  code: string;
  parent_code: string | null;
  name: string;
  description: string;
  icon: string;
  fraction_icon_id: number;
  fraction_icon: {
    id: number;
    image_url: string;
  };
  is_active: boolean;
  report_set_id: number;
  level: number;
  order: number;
  has_second_level: boolean;
  has_third_level: boolean;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
}

export type CreateReportSetFraction = Omit<ReportSetFraction, "id" | "created_at" | "updated_at" | "deleted_at">;

export type UpdateReportSetFraction = Partial<
  Omit<ReportSetFraction, "id" | "report_set_id" | "created_at" | "updated_at" | "deleted_at">
>;
