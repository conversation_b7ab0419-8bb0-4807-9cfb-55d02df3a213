export interface RepresentativeTier {
  id: number;
  name: string;
  price: number;
  country_id: number;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
  has_criteria?: boolean;
}

export type CreateRepresentativeTier = Omit<RepresentativeTier, "id" | "created_at" | "updated_at" | "deleted_at">;

export type UpdateRepresentativeTier = Partial<
  Omit<RepresentativeTier, "id" | "country_id" | "created_at" | "updated_at" | "deleted_at">
>;
