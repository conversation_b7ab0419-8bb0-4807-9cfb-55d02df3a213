import type { Metadata } from "next";
import Link from "next/link";

import { KeyboardArrowLeft } from "@arthursenno/lizenzero-ui-react/Icon";

import Container from "@/components/_common/container/container";
import Breadcrumb from "@/components/_common/breadcrumb/breadcrumb";
import { TitleAndSubTitle } from "@/components/_common/title-and-subtitle";
import { AddCountriesToLicensingForm } from "@/components/modules/customers/components/add-countries-to-licensing-form";
import { getCustomerById } from "@/lib/api/customer";
import { LicensingSelectPriceListModal } from "@/components/modules/customers/components/licensing-select-price-list-modal";
import { useTranslations } from "next-intl";

type LicensingPageProps = {
  params: Promise<{ customerId: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
};

export async function generateMetadata({ params }: LicensingPageProps): Promise<Metadata> {
  // read route params
  const customersCode = (await params).customerId;

  // fetch data
  const customer = await getCustomerById(Number(customersCode));

  return {
    title: `Licensing | ${customer?.first_name} ${customer?.last_name}`,
  };
}

export default async function LicensingPage({ params }: LicensingPageProps) {
  const customersCode = (await params).customerId;

  const customerUrl = `/customers/${customersCode}`;
  const t = useTranslations("Licensing");
  const c = useTranslations("common");
  const breadcrumbPaths = [
    { label: "Customers", href: "/customers" },
    { label: `Customer #${customersCode}`, href: customerUrl },
  ];

  return (
    <>
      <Container>
        <Breadcrumb paths={breadcrumbPaths} />

        <div className="mb-8 inline-flex">
          <Link href={customerUrl} className="flex items-center gap-2 hover:opacity-75">
            <KeyboardArrowLeft className="fill-support-blue size-6" />{" "}
            <span className="mt-1.5 font-centra font-bold text-support-blue">{c("backToCustomer")}</span>
          </Link>
        </div>

        <TitleAndSubTitle
          icon={false}
          title={t("title")}
          subText={t("subText")}
          className="[&_p]:sm:text-2xl [&_p]:text-primary [&_p]:font-bold"
        />

        <section className="sm:mt-11 pb-10">
          <AddCountriesToLicensingForm />
        </section>
      </Container>
    </>
  );
}
