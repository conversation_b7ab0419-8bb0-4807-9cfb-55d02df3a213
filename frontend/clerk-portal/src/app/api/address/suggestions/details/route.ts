import { NextRequest } from "next/server";
import { googlePlaces } from "@/lib/google/places";

export const dynamic = "force-dynamic";

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const placeId = searchParams.get("placeId") || undefined;

  const result = await googlePlaces.getPlaceDetails(placeId, "development");

  if (!result.success) return Response.json({ error: result.error }, { status: 400 });

  return Response.json(result.data);
}
