/**
 * Formats a number value into a currency string.
 * @param {number | undefined} value - The value to format, in cents.
 * @returns {string} The formatted currency string. If the value is undefined, returns '- €'.
 * @description The value must be passed in cents.
 */

// TODO: i18n
export function formatCurrency(value?: number) {
  if (!value || Number.isNaN(value)) return "- €";

  let newValue = (value / 100)
    .toLocaleString("es-ES", {
      style: "currency",
      currency: "EUR",
      minimumFractionDigits: 2,
      maximumFractionDigits: 3,
      useGrouping: true,
    })
    .replace(".", "XXX");

  newValue = newValue.replace(",", ".");

  newValue = newValue.replace("XXX", ",");

  return newValue;
}
