export interface CreateAccountResult {
  userId: string;
  email: string;
  createdAt: string;
  updatedAt: string;
}

export interface IAccountCreate {
  email: string;
  password: string;
  // newsletter: boolean;
  name: string;
  role_id: number;
  type: string;
  is_active: boolean;
}

export interface IRequestPasswordRequest {
  email: string;
  callbackUrl: string;
}

export interface IResetPasswordRequest {
  token: string | null;
  password: string;
  type: "PASSWORD_RESET";
}
