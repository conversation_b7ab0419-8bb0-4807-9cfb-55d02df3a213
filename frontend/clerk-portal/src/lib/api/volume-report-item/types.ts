import { ReportDeclineReason } from "../report-decline-reason/types";

export interface VolumeReportItem {
  id: number;
  license_volume_report_id: number;
  setup_fraction_id: number;
  setup_fraction_code: string;
  setup_column_id: number;
  value: number;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  errors?: LicenseVolumeReportError[];
}

export type LicenseVolumeReportError = {
  id: number;
  license_volume_report_id?: number;
  license_volume_report_item_id?: number;
  description: string;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  decline_reasons: LicenseVolumeReportDeclineReason[];
};

export type LicenseVolumeReportDeclineReason = {
  id: number;
  license_volume_report_error_id: number;
  report_decline_reason_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  report_decline_reason: ReportDeclineReason;
};

export type CreateVolumeReportItem = Omit<VolumeReportItem, "id" | "created_at" | "updated_at" | "deleted_at">;

export type BulkUpdateVolumeReportItems = Array<{
  license_volume_report_id: number;
  volume_report_item_id: number;
  value: number;
}>;
