import { api } from "..";
import { ApiEndpoints } from "../endpoints";
import { CreateThirdPartyInvoiceDTO, ThirdPartyInvoice } from "./types";

export interface GetThirdPartyInvoicesParams {
  license_id: number;
  from_date?: string;
  to_date?: string;
  license_year?: string;
}

export async function getThirdPartyInvoices(params: GetThirdPartyInvoicesParams) {
  const response = await api.get<ThirdPartyInvoice[]>(ApiEndpoints.thirdPartyInvoice.getAll, {
    params,
  });

  return response.data;
}

export async function getThirdPartyInvoice(thirdPartyInvoiceId: number) {
  const response = await api.get<ThirdPartyInvoice>(ApiEndpoints.thirdPartyInvoice.findById(thirdPartyInvoiceId));

  return response.data;
}

export const createThirdPartyInvoice = async (data: CreateThirdPartyInvoiceDTO) => {
  try {
    const res = await api.post(ApiEndpoints.thirdPartyInvoices.create, data);
    return res.data;
  } catch {
    return null;
  }
};
