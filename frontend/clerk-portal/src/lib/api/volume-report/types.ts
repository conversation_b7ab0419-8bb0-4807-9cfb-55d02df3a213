import { VolumeReportItem } from "../volume-report-item/types";

export interface VolumeReport {
  id: number;
  license_packaging_service_id: number;
  status: LicenseVolumeReportStatus;
  year: number;
  interval: string;
  report_table: {
    fractions: VolumeReportFraction[];
    columns: VolumeReportColumn[];
  };
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  volume_report_items: VolumeReportItem[];
}

export type LicenseVolumeReportStatus = "OPEN" | "DONE" | "DECLINED" | "APPROVED";

export type VolumeReportFraction = {
  id: number;
  name: string;
  description: string;
  icon: string;
  is_active: boolean;
  report_set_id: number;
  parent_id: number | null;
  children?: VolumeReportFraction[];
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
};

export type VolumeReportColumn = {
  id: number;
  name: string;
  description: string;
  unit_type: ReportSetColumnUnitType;
  report_set_id: number;
  parent_id: number | null;
  children?: VolumeReportColumn[];
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
};

export type ReportSetColumnUnitType = "KG" | "UNITS" | "EACH";

export enum ReportingEnum {
  INTRA_YEAR = "Intra-year volume report",
  VOLUME_REPORTING = "Volume reporting XML",
  END_OF_YEAR = "End-of-year volume report",
  INITIAL_PLANNED = "Initial planned volume report",
  BALANCE = "Balance",
}
