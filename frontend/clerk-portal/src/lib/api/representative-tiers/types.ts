import { PublishedCountry } from "@/hooks/use-liberated-countries";

export interface RepresentativeTier {
  id: number;
  name: string;
  price: number;
  country_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  country: PublishedCountry;
  has_criteria: boolean;
  setup_representative_tier_id: number;
}

export interface RepresentativeTierDto {
  license_id: number;
  setup_representative_tier_id: number;
  name: string;
  price: number;
}
