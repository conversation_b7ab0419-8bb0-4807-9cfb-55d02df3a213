export const ApiEndpoints = {
  addressSuggestions: {
    getSuggestions: "/api/address/suggestions",
    getDetails: "/api/address/suggestions/details",
  },
  auth: {
    login: "/auth/login",
    requestPassword: "/auth/user/request/password",
    resetPassword: "/auth/user/reset/password",
  },
  user: {
    findByEmail: (email: string) => `/auth/user/request/email/${email}`,
  },
  payment: {
    purchase: "/payment/purchase",
  },
  account: {
    postCreate: `/auth/user`,
    getStatus: (email: string) => `/auth/user/status/${email}`,
    postVerify: `/auth/user/account/verify`,
    postVerificationCode: `/auth/user/account/verify/sendToken`,
    postVerificationMagicToken: `/auth/user/account/verify/magic-link`,
    postConfirmVerificationCode: `/auth/user/account/verify/confirmToken`,
    postResendToken: `/auth/user/account/verify/resendToken`,
    postLogin: `/account/login`,
    putPersonalData: `/account`,
  },
  company: {
    postVatId: `/customer/company/vatId`,
  },
  password: {
    request: `/auth/user/request/password`,
    reset: `/auth/user/reset/password`,
  },
  country: {
    getAll: "/countries",
    getFollowed: (userId: string) => `/countries/user/${userId}`,
    getByCode: (code: string) => `/admin/countries/code/${code}`,
    getOverview: `/admin/countries/overview`,
    getOverviewByCode: (code: string) => `/admin/countries/code/${code}/overview`,
  },
  home: {
    getTasks: `/tasks`,
  },
  invoices: {
    get: `/payment/invoice`,
    balance: `/invoices/balance`,
    getTransactions: `/transactions`,
  },
  customers: {
    getAll: `/customer/customer`,
    getById: (id: number) => `/customer/customer/${id}/details`,
  },
  files: {
    downloadCustomerFile: (fileId: string) => `/customer/files/${fileId}`,
    downloadAdminFile: (fileId: string) => `/admin/upload-files/${fileId}`,
    downloadFile: (fileId: string) => `/customer/files/${fileId}`,
    create: `/customer/files`,
    createAdmin: `/admin/upload-files`,
    deleteCustomerFile: (fileId: string) => `/customer/files/${fileId}`,
    deleteAdminFile: (fileId: string) => `/admin/upload-files/${fileId}`,
  },
  license: {
    getAll: `/customer/licenses`,
    getOne: (licenseId: number) => `/customer/licenses/${licenseId}`,
  },
  thirdPartyInvoice: {
    getAll: `/customer/third-party-invoices`,
    findById: (id: number) => `/customer/third-party-invoices/${id}`,
  },
  contracts: {
    findAll: "/customer/contracts",
    findById: (id: number) => `/customer/contracts/${id}`,
  },
  certificate: {
    getAll: "/customer/certificates",
  },
  requiredInformations: {
    getAll: `/customer/required-informations`,
    create: `/customer/required-informations`,
    createAdmin: `/admin/required-informations`,
    update: (requiredInformationId: number) => `/customer/required-informations/${requiredInformationId}`,
    updateAnswer: (requiredInformationId: number) => `/customer/required-informations/${requiredInformationId}`,
    decline: (requiredInformationId: number) => `/customer/required-informations/${requiredInformationId}/decline`,
  },
  packagingServices: {
    getAll: "/customer/packaging-services",
    findById: (packagingServiceId: number) => `/customer/packaging-services/${packagingServiceId}`,
  },
  volumeReport: {
    create: "/customer/license-volume-report",
    update: (volumeReportId: number) => `/customer/license-volume-report/${volumeReportId}`,
  },
  volumeReportItem: {
    create: "/customer/license-volume-report-item",
    createMany: "/customer/license-volume-report-item/bulk",
    getByVolumeReportId: (volumeReportId: number) =>
      `/customer/license-volume-report-item?license_volume_report_id=${volumeReportId}`,
    update: (volumeReportItemId: number) => `/customer/license-volume-report-item/${volumeReportItemId}`,
    bulkUpdate: "/customer/license-volume-report-item/bulk-update",
  },
  serviceNextSteps: {
    getAll: "/customer/service-next-steps",
    update: (serviceNextStepId: number) => `/customer/service-next-steps/${serviceNextStepId}`,
  },
  purchaseServices: {
    purchase: "/customer/purchases",
  },
  thirdPartyInvoices: {
    findAll: `/customer/third-party-invoices`,
    findById: (id: number) => `/customer/third-party-invoices/${id}`,
    create: "/customer/third-party-invoices",
  },
  termination: {
    create: "/customer/terminations",
    update: (terminationId: number) => `/customer/terminations/${terminationId}`,
    get: (terminationId: number) => `/customer/terminations/${terminationId}`,
  },
  generalInformation: {
    getAll: "/customer/general-informations",
    update: (generalInformationId: number) => `/customer/general-informations/${generalInformationId}`,
    updateAnswer: (generalInformationId: number) => `/customer/general-informations/${generalInformationId}`,
  },
  tasks: {
    getAll: (customerId?: number, country?: string) => {
      const searchParams = new URLSearchParams();
      if (customerId) {
        searchParams.append("customerId", customerId.toString());
      }
      if (country) {
        searchParams.append("countryName", country);
      }
      return searchParams.size > 0 ? `/crm/clerks/boards?${searchParams.toString()}` : "/crm/clerks/boards";
    },
  },
  serviceSetups: {
    getRequiredInformations: (countryCode: string) => `/admin/service-setups/${countryCode}/required-informations`,
  },
  reportDeclineReason: {
    getAll: "/customer/report-decline-reasons",
  },
  licenseVolumeReportError: {
    create: "/customer/license-volume-report-error",
  },
  countryFollowers: {
    create: "/admin/country-followers",
    delete: "/admin/country-followers",
  },
  admin: {
    getAll: "/admin/admin",
  },
  transaction: {
    get: "/payment/transaction",
    getById: (transactionId: number) => `/payment/transaction/${transactionId}`,
    getPotentialMatches: (transactionId: number) => `/payment/transaction/potential-matches/${transactionId}`,
    assign: (id: string) => `/payment/transaction/${id}/assign`,
    totals: "/payment/transaction/totals",
    refund: (id: string) => `/payment/transaction/${id}/refund`,
  },
  reasons: {
    getAll: "/customer/reasons",
  },
  reportSet: {
    get: (countryCode: string) => `/admin/service-setups/${countryCode}/report-sets`,
    put: (licenseReportSetId: string) => `/customer/license-report-set/${licenseReportSetId}`,
  },
  representativeTiers: {
    get: (countryCode: string) => `/admin/service-setups/${countryCode}/representative-tiers`,
    post: "/customer/representative-tiers",
  },
  otherCosts: {
    get: (countryCode: string) => `/admin/service-setups/${countryCode}/other-costs`,
    post: "/customer/other-costs",
    delete: (otherCostsId: string | number) => `/customer/other-costs/${otherCostsId}`,
  },
};
