ARG NODE_VERSION=18.17.1

FROM nexus.interzero.de:5000/node:${NODE_VERSION}-alpine AS base
RUN yarn global add pnpm
WORKDIR /app
COPY . .

FROM base AS builder

WORKDIR /app

COPY --from=base /app/package.json /app/pnpm-lock.yaml ./

RUN pnpm install --frozen-lockfile

COPY --from=base /app .
COPY --from=base /app/prisma ./prisma

RUN pnpm prisma generate

ARG PORT=3000
ENV PORT=${PORT}
ARG DATABASE_URL
ENV DATABASE_URL=${DATABASE_URL}
ARG AUTH_API_URL
ENV AUTH_API_URL=${AUTH_API_URL}
ARG CRM_API_URL
ENV CRM_API_URL=${CRM_API_URL}
ARG PAYMENT_API_URL
ENV PAYMENT_API_URL=${PAYMENT_API_URL}
ARG ADMIN_API_URL
ENV ADMIN_API_URL=${ADMIN_API_URL}
ARG CUSTOMER_IO_SITE_ID
ENV CUSTOMER_IO_SITE_ID=${CUSTOMER_IO_SITE_ID}
ARG CUSTOMER_IO_API_KEY
ENV CUSTOMER_IO_API_KEY=${CUSTOMER_IO_API_KEY}
ARG CUSTOMER_IO_KEY
ENV CUSTOMER_IO_KEY=${CUSTOMER_IO_KEY}
ARG SYSTEM_API_KEY
ENV SYSTEM_API_KEY=${SYSTEM_API_KEY}


RUN apk add --no-cache \
    chromium \
    nss \
    freetype \
    harfbuzz \
    ca-certificates \
    ttf-freefont \
    dbus \
    udev \
    && mkdir -p /var/run/dbus \
    && dbus-daemon --system --fork --nopidfile --nosyslog \
    && apk add --no-cache \
    libx11 \
    libxcomposite \
    libxrandr \
    libxi \
    libxtst \
    libxrender \
    libxext
RUN pnpm nest build

EXPOSE 3000

CMD ["node", "dist/src/main"]