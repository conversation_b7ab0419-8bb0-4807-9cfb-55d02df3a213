---
apiVersion: v1
kind: Namespace
metadata:
  name: <NAMESPACE>
---
apiVersion: v1
kind: Secret
metadata:
  name: <PREFIX><PROJECT_NAME>-secret
  namespace: <NAMESPACE>
stringData:
  spring.datasource.url: "jdbc:postgresql://<DB_URL>:3000/<DB_SERVICE_NAME>"
  spring.datasource.username: <DB_USER>
  spring.datasource.password: <DB_PASSWORD>
  frontend_hostname: <FRONTEND_HOSTNAME>
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: <PREFIX><PROJECT_NAME>-<COMPONENT>
  namespace: <NAMESPACE>
  labels:
    loadbalancer: <PREFIX><PROJECT_NAME>-<COMPONENT>
spec:
  replicas: 1
  selector:
    matchLabels:
      loadbalancer: <PREFIX><PROJECT_NAME>-<COMPONENT>
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        loadbalancer: <PREFIX><PROJECT_NAME>-<COMPONENT>
        app: <PREFIX><PROJECT_NAME>-<COMPONENT>
        ignore-gnp: 'y'
    spec:
      containers:
        - envFrom:
            - secretRef:
                name: <PREFIX><PROJECT_NAME>-secret
          name: <PREFIX><PROJECT_NAME>-<COMPONENT>
          image: nexus.interzero.de:<NEXUS_PORT>/<PROJECT_NAME>-<COMPONENT>:<BRANCH>
          imagePullPolicy: Always
          resources:
            requests:
              memory: <MEMORY_REQUEST>
              cpu: <CPU_REQUEST>
            limits:
              memory: <MEMORY_LIMIT>
              cpu: <CPU_LIMIT>
          env:
            - name: "server.port"
              value: "8080"
            - name: "server.servlet.context-path"
              value: "/"
            - name: "TZ"
              value: "Europe/Berlin"
            - name: "LC_ALL"
              value: "de_DE"
            - name: "spring.profiles.active"
              value: "<SPRING_ENVIRONMENT>"
---
apiVersion: v1
kind: Service
metadata:
  name: <PREFIX><PROJECT_NAME>-<COMPONENT>
  namespace: <NAMESPACE>
spec:
  ports:
    - protocol: TCP
      port: 8080
      targetPort: 8080
  selector:
    loadbalancer: <PREFIX><PROJECT_NAME>-<COMPONENT>
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: <PREFIX><PROJECT_NAME>-<COMPONENT>
  namespace: <NAMESPACE>
  annotations:
    alb.ingress.kubernetes.io/scheme: <ALB_SCHEME>
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/group.name: <ALB_GROUP>
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
spec:
  ingressClassName: alb
  rules:
    - host: <HOSTNAME>
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: <PREFIX><PROJECT_NAME>-<COMPONENT>
                port:
                  number: 8080
