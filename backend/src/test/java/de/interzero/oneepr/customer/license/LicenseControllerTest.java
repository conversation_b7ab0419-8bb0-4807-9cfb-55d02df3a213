package de.interzero.oneepr.customer.license;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.contract.ContractRepository;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import de.interzero.oneepr.customer.license.dto.CreateLicenseDto;
import de.interzero.oneepr.customer.license.dto.LicensePendenciesResultDto;
import de.interzero.oneepr.customer.license.dto.UpdateLicenseDto;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import de.interzero.oneepr.customer.shared.auth.Role;
import de.interzero.oneepr.customer.termination.Termination;
import de.interzero.oneepr.customer.termination.TerminationRepository;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDate;

import static com.github.tomakehurst.wiremock.client.WireMock.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Comprehensive integration tests for LicenseController with 100% coverage.
 * Uses WireMock for external service mocking. Uses real service and repository instances.
 *
 * Test Coverage:
 * - All 5 controller endpoints (findAll, findOne, create, update, remove)
 * - All HTTP methods (GET, POST, PUT, DELETE)
 * - All success and error scenarios
 * - All authentication and authorization cases
 * - All parameter validation scenarios
 * - All edge cases and boundary conditions
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
class LicenseControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    // Real service and repository instances (not mocked as per requirement)
    @Autowired
    private LicenseService licenseService;

    @Autowired
    private LicenseRepository licenseRepository;

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private TerminationRepository terminationRepository;

    private WireMockServer wireMockServer;
    private MockedStatic<de.interzero.oneepr.common.AuthUtil> authUtilMock;

    // Test data constants
    private static final Integer TEST_CUSTOMER_ID = 1;
    private static final Integer TEST_CONTRACT_ID = 1;
    private static final Integer TEST_LICENSE_ID = 1;
    private static final Integer TEST_TERMINATION_ID = 1;
    private static final String TEST_USER_ID = "1";
    private static final String TEST_USER_EMAIL = "<EMAIL>";
    private static final Role TEST_USER_ROLE = Role.CUSTOMER;

    // Test entities
    private Customer testCustomer;
    private Contract testContract;
    private License testLicense;
    private Termination testTermination;

    @BeforeEach
    void setUp() {
        // Setup WireMock server for external HTTP calls
        wireMockServer = new WireMockServer(8089);
        wireMockServer.start();
        WireMock.configureFor("localhost", 8089);

        // Setup WireMock stubs for external services
        setupWireMockStubs();

        // Mock AuthUtil static method
        authUtilMock = Mockito.mockStatic(de.interzero.oneepr.common.AuthUtil.class);
        AuthenticatedUser mockUser = new AuthenticatedUser(TEST_USER_ID, TEST_USER_ROLE, TEST_USER_EMAIL);
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails).thenReturn(mockUser);

        // Create test data in database
        createTestData();
    }

    @AfterEach
    void tearDown() {
        if (wireMockServer != null) {
            wireMockServer.stop();
        }
        if (authUtilMock != null) {
            authUtilMock.close();
        }
    }

    private void setupWireMockStubs() {
        // Stub for external API calls
        wireMockServer.stubFor(post(urlMatching("/external/.*"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("Content-Type", "application/json")
                        .withBody("{\"status\":\"success\"}")));

        // Stub for email notifications
        wireMockServer.stubFor(post(urlEqualTo("/notifications/email"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("Content-Type", "application/json")
                        .withBody("{\"messageId\":\"test-message-id\"}")));
    }

    private void createTestData() {
        // Create Customer
        testCustomer = new Customer();
        testCustomer.setId(TEST_CUSTOMER_ID);
        testCustomer.setUserId(TEST_CUSTOMER_ID);
        testCustomer.setEmail(TEST_USER_EMAIL);
        testCustomer.setCreatedAt(Instant.now());
        testCustomer.setUpdatedAt(Instant.now());
        testCustomer = customerRepository.save(testCustomer);

        // Create Contract
        testContract = new Contract();
        testContract.setId(TEST_CONTRACT_ID);
        testContract.setCustomer(testCustomer);
        testContract.setType("EU_LICENSE");
        testContract.setStatus("ACTIVE");
        testContract.setCreatedAt(Instant.now());
        testContract.setUpdatedAt(Instant.now());
        testContract = contractRepository.save(testContract);

        // Create Termination
        testTermination = new Termination();
        testTermination.setId(TEST_TERMINATION_ID);
        testTermination.setStatus("REQUESTED");
        testTermination.setCreatedAt(Instant.now());
        testTermination.setUpdatedAt(Instant.now());
        testTermination = terminationRepository.save(testTermination);
    }

    // ========== FIND ALL METHOD TESTS (GET /licenses) ==========

    /**
     * Test findAll method - Success case without filter
     * Tests: GET /licenses
     */
    @Test
    void findAll_ShouldReturnList_WhenNoFilter() throws Exception {
        // First create a test license
        testLicense = createAndSaveTestLicense();

        // When & Then
        mockMvc.perform(get("/licenses"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].license.id").value(testLicense.getId()))
                .andExpect(jsonPath("$[0].license.country_name").value("Germany"))
                .andExpect(jsonPath("$[0].license.year").value(2024))
                .andExpect(jsonPath("$[0].pendencies").exists())
                .andExpect(jsonPath("$[0].pendencies_status").exists());
    }

    /**
     * Test findAll method - Success case with contract_id filter
     * Tests: GET /licenses?contract_id=1
     */
    @Test
    void findAll_ShouldReturnFilteredList_WhenContractIdProvided() throws Exception {
        // First create a test license
        testLicense = createAndSaveTestLicense();

        // When & Then
        mockMvc.perform(get("/licenses")
                        .param("contract_id", TEST_CONTRACT_ID.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].license.id").value(testLicense.getId()));
    }

    /**
     * Test findAll method - Empty list when no licenses exist
     * Tests: GET /licenses when no licenses exist
     */
    @Test
    void findAll_ShouldReturnEmptyList_WhenNoLicensesExist() throws Exception {
        // When & Then
        mockMvc.perform(get("/licenses"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.length()").value(0));
    }

    /**
     * Test findAll method - Invalid contract_id parameter
     * Tests: GET /licenses?contract_id=invalid
     */
    @Test
    void findAll_ShouldReturnBadRequest_WhenInvalidContractId() throws Exception {
        // When & Then
        mockMvc.perform(get("/licenses")
                        .param("contract_id", "invalid"))
                .andExpect(status().isBadRequest());
    }

    // ========== FIND ONE METHOD TESTS (GET /licenses/{id}) ==========

    /**
     * Test findOne method - Success case
     * Tests: GET /licenses/1
     */
    @Test
    void findOne_ShouldReturnLicense_WhenLicenseExists() throws Exception {
        // First create a test license
        testLicense = createAndSaveTestLicense();

        // When & Then
        mockMvc.perform(get("/licenses/{id}", testLicense.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.license.id").value(testLicense.getId()))
                .andExpect(jsonPath("$.license.country_name").value("Germany"))
                .andExpect(jsonPath("$.license.year").value(2024))
                .andExpect(jsonPath("$.license.registration_status").value("DONE"))
                .andExpect(jsonPath("$.pendencies").exists())
                .andExpect(jsonPath("$.pendencies_status").exists());
    }

    /**
     * Test findOne method - License doesn't exist
     * Tests: GET /licenses/999 (non-existent ID)
     */
    @Test
    void findOne_ShouldReturnNotFound_WhenLicenseNotExists() throws Exception {
        // When & Then
        mockMvc.perform(get("/licenses/{id}", 999))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.message").value("License not found"));
    }

    /**
     * Test findOne method - Customer role accessing other customer's data
     * Tests: Authorization check for findOne endpoint
     */
    @Test
    void findOne_ShouldReturnForbidden_WhenCustomerAccessesOtherCustomerData() throws Exception {
        // First create a test license
        testLicense = createAndSaveTestLicense();

        // Given - Mock different user
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails)
                .thenReturn(new AuthenticatedUser("999", Role.CUSTOMER, "<EMAIL>"));

        // When & Then
        mockMvc.perform(get("/licenses/{id}", testLicense.getId()))
                .andExpect(status().isForbidden())
                .andExpect(jsonPath("$.message").value("You do not have permission to access this resource"));
    }

    /**
     * Test findOne method - Admin role should have access
     * Tests: Authorization check for ADMIN role
     */
    @Test
    void findOne_ShouldReturnLicense_WhenAdminRole() throws Exception {
        // Given - Mock admin user
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails)
                .thenReturn(new AuthenticatedUser("999", Role.ADMIN, "<EMAIL>"));

        // First create a test license
        testLicense = createAndSaveTestLicense();

        // When & Then
        mockMvc.perform(get("/licenses/{id}", testLicense.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.license.id").value(testLicense.getId()));
    }

    // ========== CREATE METHOD TESTS (POST /licenses) ==========

    /**
     * Test create method - Success case
     * Tests: POST /licenses with valid DTO
     */
    @Test
    void create_ShouldReturnCreatedLicense_WhenValidDto() throws Exception {
        // Given
        CreateLicenseDto createDto = createTestCreateDto();

        // When & Then
        mockMvc.perform(post("/licenses")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.country_name").value("Germany"))
                .andExpect(jsonPath("$.country_code").value("DE"))
                .andExpect(jsonPath("$.year").value(2024))
                .andExpect(jsonPath("$.registration_status").value("DONE"))
                .andExpect(jsonPath("$.clerk_control_status").value("DONE"))
                .andExpect(jsonPath("$.contract_status").value("ACTIVE"))
                .andExpect(jsonPath("$.registration_number").exists())
                .andExpect(jsonPath("$.created_at").exists())
                .andExpect(jsonPath("$.updated_at").exists());
    }

    /**
     * Test create method - Invalid JSON
     * Tests: POST /licenses with malformed JSON
     */
    @Test
    void create_ShouldReturnBadRequest_WhenInvalidJson() throws Exception {
        // When & Then
        mockMvc.perform(post("/licenses")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("invalid json"))
                .andExpect(status().isBadRequest());
    }

    /**
     * Test create method - Missing content type
     * Tests: POST /licenses without Content-Type header
     */
    @Test
    void create_ShouldReturnUnsupportedMediaType_WhenMissingContentType() throws Exception {
        // Given
        CreateLicenseDto createDto = createTestCreateDto();

        // When & Then
        mockMvc.perform(post("/licenses")
                        .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isUnsupportedMediaType());
    }

    /**
     * Test create method - Null DTO fields
     * Tests: POST /licenses with null required fields
     */
    @Test
    void create_ShouldReturnBadRequest_WhenDtoFieldsAreNull() throws Exception {
        // Given
        CreateLicenseDto createDto = new CreateLicenseDto();
        // Leave fields null

        // When & Then
        mockMvc.perform(post("/licenses")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isBadRequest());
    }

    // ========== UPDATE METHOD TESTS (PUT /licenses/{id}) ==========

    /**
     * Test update method - Success case
     * Tests: PUT /licenses/1 with valid DTO
     */
    @Test
    void update_ShouldReturnUpdatedLicense_WhenValidDto() throws Exception {
        // First create a test license
        testLicense = createAndSaveTestLicense();

        UpdateLicenseDto updateDto = createTestUpdateDto();

        // When & Then
        mockMvc.perform(put("/licenses/{id}", testLicense.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testLicense.getId()))
                .andExpect(jsonPath("$.registration_status").value("PENDING"))
                .andExpect(jsonPath("$.clerk_control_status").value("PENDING"))
                .andExpect(jsonPath("$.contract_status").value("INACTIVE"));
    }

    /**
     * Test update method - Non-existent license
     * Tests: PUT /licenses/999 (non-existent ID)
     */
    @Test
    void update_ShouldReturnNotFound_WhenLicenseNotExists() throws Exception {
        // Given
        UpdateLicenseDto updateDto = createTestUpdateDto();

        // When & Then
        mockMvc.perform(put("/licenses/{id}", 999)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.message").value("License not found"));
    }

    /**
     * Test update method - Invalid JSON
     * Tests: PUT /licenses/1 with malformed JSON
     */
    @Test
    void update_ShouldReturnBadRequest_WhenInvalidJson() throws Exception {
        // First create a test license
        testLicense = createAndSaveTestLicense();

        // When & Then
        mockMvc.perform(put("/licenses/{id}", testLicense.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("invalid json"))
                .andExpect(status().isBadRequest());
    }

    /**
     * Test update method - Customer role accessing other customer's data
     * Tests: Authorization check for update endpoint
     */
    @Test
    void update_ShouldReturnForbidden_WhenCustomerAccessesOtherCustomerData() throws Exception {
        // First create a test license
        testLicense = createAndSaveTestLicense();

        // Given - Mock different user
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails)
                .thenReturn(new AuthenticatedUser("999", Role.CUSTOMER, "<EMAIL>"));

        UpdateLicenseDto updateDto = createTestUpdateDto();

        // When & Then
        mockMvc.perform(put("/licenses/{id}", testLicense.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isForbidden())
                .andExpect(jsonPath("$.message").value("You do not have permission to access this resource"));
    }

    /**
     * Test create method - Invalid JSON
     * Tests: POST /licenses with malformed JSON
     */
    @Test
    void create_ShouldReturnBadRequest_WhenInvalidJson() throws Exception {
        // When & Then
        mockMvc.perform(post("/licenses")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("invalid json"))
                .andExpect(status().isBadRequest());
    }

    /**
     * Test create method - Missing content type
     * Tests: POST /licenses without Content-Type header
     */
    @Test
    void create_ShouldReturnUnsupportedMediaType_WhenMissingContentType() throws Exception {
        // Given
        CreateLicenseDto createDto = createTestCreateDto();

        // When & Then
        mockMvc.perform(post("/licenses")
                        .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isUnsupportedMediaType());
    }

    /**
     * Test create method - Null DTO fields
     * Tests: POST /licenses with null required fields
     */
    @Test
    void create_ShouldReturnBadRequest_WhenDtoFieldsAreNull() throws Exception {
        // Given
        CreateLicenseDto createDto = new CreateLicenseDto();
        // Leave fields null

        // When & Then
        mockMvc.perform(post("/licenses")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isBadRequest());
    }

    // ========== UPDATE METHOD TESTS (PUT /licenses/{id}) ==========

    /**
     * Test update method - Success case
     * Tests: PUT /licenses/1 with valid DTO
     */
    @Test
    void update_ShouldReturnUpdatedLicense_WhenValidDto() throws Exception {
        // First create a test license
        testLicense = createAndSaveTestLicense();

        UpdateLicenseDto updateDto = createTestUpdateDto();

        // When & Then
        mockMvc.perform(put("/licenses/{id}", testLicense.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testLicense.getId()))
                .andExpect(jsonPath("$.registration_status").value("PENDING"))
                .andExpect(jsonPath("$.clerk_control_status").value("PENDING"))
                .andExpect(jsonPath("$.contract_status").value("INACTIVE"));
    }

    /**
     * Test update method - Non-existent license
     * Tests: PUT /licenses/999 (non-existent ID)
     */
    @Test
    void update_ShouldReturnNotFound_WhenLicenseNotExists() throws Exception {
        // Given
        UpdateLicenseDto updateDto = createTestUpdateDto();

        // When & Then
        mockMvc.perform(put("/licenses/{id}", 999)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.message").value("License not found"));
    }

    /**
     * Test update method - Invalid JSON
     * Tests: PUT /licenses/1 with malformed JSON
     */
    @Test
    void update_ShouldReturnBadRequest_WhenInvalidJson() throws Exception {
        // First create a test license
        testLicense = createAndSaveTestLicense();

        // When & Then
        mockMvc.perform(put("/licenses/{id}", testLicense.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("invalid json"))
                .andExpect(status().isBadRequest());
    }

    /**
     * Test update method - Customer role accessing other customer's data
     * Tests: Authorization check for update endpoint
     */
    @Test
    void update_ShouldReturnForbidden_WhenCustomerAccessesOtherCustomerData() throws Exception {
        // First create a test license
        testLicense = createAndSaveTestLicense();

        // Given - Mock different user
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails)
                .thenReturn(new AuthenticatedUser("999", Role.CUSTOMER, "<EMAIL>"));

        UpdateLicenseDto updateDto = createTestUpdateDto();

        // When & Then
        mockMvc.perform(put("/licenses/{id}", testLicense.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isForbidden())
                .andExpect(jsonPath("$.message").value("You do not have permission to access this resource"));
    }

    // ========== REMOVE METHOD TESTS (DELETE /licenses/{id}) ==========

    /**
     * Test remove method - Success case (soft delete)
     * Tests: DELETE /licenses/1
     */
    @Test
    void remove_ShouldReturnRemovedLicense_WhenValidId() throws Exception {
        // First create a test license
        testLicense = createAndSaveTestLicense();

        // When & Then
        mockMvc.perform(delete("/licenses/{id}", testLicense.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testLicense.getId()))
                .andExpect(jsonPath("$.deleted_at").exists());
    }

    /**
     * Test remove method - Non-existent license
     * Tests: DELETE /licenses/999 (non-existent ID)
     */
    @Test
    void remove_ShouldReturnNotFound_WhenLicenseNotExists() throws Exception {
        // When & Then
        mockMvc.perform(delete("/licenses/{id}", 999))
                .andExpect(status().isNotFound())
                .andExpected(jsonPath("$.message").value("License not found"));
    }

    /**
     * Test remove method - Customer role accessing other customer's data
     * Tests: Authorization check for remove endpoint
     */
    @Test
    void remove_ShouldReturnForbidden_WhenCustomerAccessesOtherCustomerData() throws Exception {
        // First create a test license
        testLicense = createAndSaveTestLicense();

        // Given - Mock different user
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails)
                .thenReturn(new AuthenticatedUser("999", Role.CUSTOMER, "<EMAIL>"));

        // When & Then
        mockMvc.perform(delete("/licenses/{id}", testLicense.getId()))
                .andExpect(status().isForbidden())
                .andExpect(jsonPath("$.message").value("You do not have permission to access this resource"));
    }
