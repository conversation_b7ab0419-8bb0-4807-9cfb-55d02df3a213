package de.interzero.oneepr.customer.customer_phone;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.TestRole;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import de.interzero.oneepr.customer.customer.dto.CreateCustomerDto;
import de.interzero.oneepr.customer.customer_document.CustomerDocumentRepository;
import de.interzero.oneepr.customer.customer_phone.dto.CreateCustomerPhoneDto;
import de.interzero.oneepr.customer.customer_phone.dto.UpdateCustomerPhoneDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;

import static org.hamcrest.Matchers.hasSize;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Integration tests for the {@link CustomerPhoneController}.
 * This class uses {@link SpringBootTest} to load the full application context
 * and {@link AutoConfigureMockMvc} to configure {@link MockMvc} for sending HTTP requests
 * to the controller endpoints. It verifies the behavior of the customer phone API,
 * including creation, retrieval, update, and deletion of customer phone records.
 * <p>
 * Each test method is designed to be independent, with common setup logic handled
 * in the {@link #setup()} method, which runs before each test. This setup includes
 * clearing relevant database tables and creating initial customer data.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class CustomerPhoneControllerTest {

    @Autowired
    private MockMvc mockMvc;

    /**
     * First test customer instance, initialized in {@link #setup()}.
     */
    private Customer customer1;

    /**
     * Second test customer instance, initialized in {@link #setup()}.
     */
    private Customer customer2;

    @Autowired
    private CustomerPhoneRepository customerPhoneRepository;

    /**
     * Repository for {@link CustomerPhone} entities, used for test data setup and verification.
     */
    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private CustomerDocumentRepository customerDocumentRepository;

    /**
     * Jackson ObjectMapper for serializing request DTOs to JSON and
     * deserializing JSON responses to DTOs or entities.
     */
    @Autowired
    private ObjectMapper objectMapper;

    /**
     * Sets up the test environment before each test method execution.
     * This method performs the following actions:
     * <p>
     * Clears all existing data from {@code customerPhoneRepository} and {@code customerRepository}
     * to ensure test isolation.
     * Creates and persists two distinct test {@link Customer} entities ({@code customer1} and
     * {@code customer2}) to be used as prerequisites in the tests.
     */
    @BeforeEach
    @Transactional
    void setup() {
        objectMapper.registerModule(new JavaTimeModule());
        customerDocumentRepository.deleteAllInBatch();
        customerRepository.deleteAll();
        customerPhoneRepository.deleteAll();

        CreateCustomerDto testCustomer1 = new CreateCustomerDto();
        testCustomer1.setEmail("<EMAIL>");
        testCustomer1.setFirstName("John");
        testCustomer1.setLastName("Doe");
        testCustomer1.setUserId(1);
        testCustomer1.setSalutation("Mr.");

        CreateCustomerDto testCustomer2 = new CreateCustomerDto();
        testCustomer2.setEmail("<EMAIL>");
        testCustomer2.setFirstName("Jane");
        testCustomer2.setLastName("Smith");
        testCustomer2.setUserId(2);
        testCustomer2.setSalutation("Ms.");
        this.customer1 = createTestCustomer(testCustomer1);
        this.customer2 = createTestCustomer(testCustomer2);

    }

    /**
     * Helper method to create and persist a {@link Customer} entity based on a DTO.
     * This is used during the {@link #setup()} phase to prepare test data.
     *
     * @param dto The {@link CreateCustomerDto} containing the data for the new customer.
     * @return The persisted {@link Customer} entity.
     */
    private Customer createTestCustomer(CreateCustomerDto dto) {
        Customer customer = new Customer();
        customer.setEmail(dto.getEmail());
        customer.setFirstName(dto.getFirstName());
        customer.setLastName(dto.getLastName());
        customer.setUserId(dto.getUserId());
        customer.setSalutation(dto.getSalutation());
        customer.setUpdatedAt(Instant.now());
        customer.setCreatedAt(Instant.now());
        return customerRepository.saveAndFlush(customer);
    }

    /**
     * Helper method to create and persist a {@link CustomerPhone} entity.
     * This is used within individual test methods to set up specific phone data.
     *
     * @param phoneNumber The phone number string for the new phone record.
     * @param customerId  The ID of the {@link Customer} to associate with this phone.
     * @return The persisted {@link CustomerPhone} entity.
     */
    private CustomerPhone createAndSaveTestPhone(String phoneNumber,
                                                 Integer customerId) {
        CustomerPhone phone = new CustomerPhone();
        phone.setPhoneNumber(phoneNumber);
        phone.setCustomer(customerRepository.findById(customerId)
                                  .orElseThrow(() -> new RuntimeException("Could not find customer with id " + customerId)));
        phone.setCreatedAt(Instant.now().minusSeconds(3600)); // An hour ago
        phone.setUpdatedAt(Instant.now().minusSeconds(3600));
        return customerPhoneRepository.save(phone);
    }

    /**
     * Test for {@link CustomerPhoneController#create(CreateCustomerPhoneDto)}
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void create_shouldCreateNewCustomerPhone() throws Exception {
        CreateCustomerPhoneDto dto = new CreateCustomerPhoneDto();
        dto.setPhoneNumber("+1234567890");
        dto.setCustomerId(this.customer1.getId());
        String jsonRequestContent = objectMapper.writeValueAsString(dto);
        ResultActions resultActions = mockMvc.perform(post(Api.CUSTOMER_PHONE).contentType(MediaType.APPLICATION_JSON)
                                                              .content(jsonRequestContent))
                .andExpect(status().isCreated())
                .andDo(print());
        String jsonResponse = resultActions.andReturn().getResponse().getContentAsString();
        CustomerPhone createdPhone = objectMapper.readValue(jsonResponse, CustomerPhone.class);
        assertNotNull(createdPhone.getId());
        assertEquals(dto.getPhoneNumber(), createdPhone.getPhoneNumber());
        assertNotNull(createdPhone.getCreatedAt());
        assertNotNull(createdPhone.getUpdatedAt());
        // Assuming deletedAt is set on creation as per earlier service logic discussion
        assertNotNull(createdPhone.getDeletedAt());
    }

    /**
     * Test for {@link CustomerPhoneController#findAll()}
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void findAll_shouldReturnAllCustomerPhones() throws Exception {
        createAndSaveTestPhone("+111", this.customer1.getId());
        createAndSaveTestPhone("+222", this.customer2.getId());
        mockMvc.perform(get(Api.CUSTOMER_PHONE).contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(2)));
    }

    /**
     * Test for {@link CustomerPhoneController#findOne(Integer)}
     */
    @Test
    @WithMockUser(
            username = "1",
            roles = {TestRole.CUSTOMER}
    )
    void findOne_shouldReturnSpecificCustomerPhone() throws Exception {
        CustomerPhone savedPhone = createAndSaveTestPhone("+333", this.customer1.getId());
        ResultActions resultActions = mockMvc.perform(get(Api.CUSTOMER_PHONE + "/" + savedPhone.getId()).contentType(
                MediaType.APPLICATION_JSON)).andExpect(status().isOk()).andDo(print());

        String jsonResponse = resultActions.andReturn().getResponse().getContentAsString();
        CustomerPhone foundPhone = objectMapper.readValue(jsonResponse, CustomerPhone.class);
        assertEquals(savedPhone.getId(), foundPhone.getId());
        assertEquals(savedPhone.getPhoneNumber(), foundPhone.getPhoneNumber());
    }

    /**
     * Test for {@link CustomerPhoneController#update(Integer, UpdateCustomerPhoneDto)}
     */
    @Test
    @WithMockUser(
            username = "1",
            roles = {TestRole.CUSTOMER}
    )
    void update_shouldUpdateCustomerPhone() throws Exception {
        CustomerPhone originalPhone = createAndSaveTestPhone("+444", this.customer1.getId());
        Instant originalUpdatedAt = originalPhone.getUpdatedAt();
        UpdateCustomerPhoneDto updateDto = new UpdateCustomerPhoneDto();
        updateDto.setPhoneNumber("+444-UPDATED");
        ResultActions resultActions = mockMvc.perform(put(Api.CUSTOMER_PHONE + "/" + originalPhone.getId()).contentType(
                        MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(updateDto)))
                .andDo(print())
                .andExpect(status().isOk());

        String jsonResponse = resultActions.andReturn().getResponse().getContentAsString();
        CustomerPhone updatedPhone = objectMapper.readValue(jsonResponse, CustomerPhone.class);

        assertEquals(originalPhone.getId(), updatedPhone.getId());
        assertEquals(updateDto.getPhoneNumber(), updatedPhone.getPhoneNumber());
        assertNotNull(updatedPhone.getUpdatedAt());
        // Check if updatedAt is actually later than original updatedAt or createdAt
        assertTrue(updatedPhone.getUpdatedAt().isAfter(originalUpdatedAt));
    }

    /**
     * Test for {@link CustomerPhoneController#remove(Integer)}
     */
    @Test
    @WithMockUser(
            username = "2",
            roles = {TestRole.CUSTOMER}
    )
    void remove_shouldDeleteCustomerPhone() throws Exception {
        CustomerPhone savedPhone = createAndSaveTestPhone("+666", this.customer2.getId());
        mockMvc.perform(delete(Api.CUSTOMER_PHONE + "/" + savedPhone.getId()))
                .andExpect(status().isOk()); // Controller's remove method is void, Spring defaults to 200 OK

        assertTrue(
                customerPhoneRepository.findById(savedPhone.getId()).isEmpty(),
                "Phone should have been deleted from the repository.");
    }
}