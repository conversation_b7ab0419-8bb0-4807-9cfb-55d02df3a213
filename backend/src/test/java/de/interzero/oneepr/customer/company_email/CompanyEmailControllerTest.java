package de.interzero.oneepr.customer.company_email;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.TestRole;
import de.interzero.oneepr.customer.company.Company;
import de.interzero.oneepr.customer.company.CompanyRepository;
import de.interzero.oneepr.customer.company_email.dto.CreateCompanyEmailDto;
import de.interzero.oneepr.customer.company_email.dto.UpdateCompanyEmailDto;
import de.interzero.oneepr.customer.consent.ConsentController;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Integration tests for {@link ConsentController}
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class CompanyEmailControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private CompanyEmailRepository companyEmailRepository;

    @Autowired
    private CompanyRepository companyRepository;

    @Autowired
    private ObjectMapper objectMapper;

    private Company testCompany;

    @BeforeEach
    void testCompany() {
        Company company = new Company();
        company.setName("Default Test Company");
        company.setCreatedAt(Instant.now());
        company.setUpdatedAt(Instant.now());
        testCompany = companyRepository.save(company);
    }

    @AfterEach
    void setup() {
        companyEmailRepository.deleteAll();
        companyRepository.deleteAll();
    }

    /**
     * {@link CompanyEmailController#create(CreateCompanyEmailDto)}
     *
     * @throws Exception any exception
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void create_successful() throws Exception {
        // create DTO
        CreateCompanyEmailDto dto = new CreateCompanyEmailDto();
        dto.setCompanyEmail("<EMAIL>");
        dto.setCompanyId(testCompany.getId());

        // hit endpoint
        ResultActions resultActions = mockMvc.perform(post(Api.COMPANY_EMAIL).contentType(MediaType.APPLICATION_JSON)
                                                              .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isCreated());

        // assert
        resultActions.andExpect(jsonPath("$.email").value(dto.getCompanyEmail()));
        resultActions.andExpect(jsonPath("$.company_id").value(dto.getCompanyId()));
        resultActions.andExpect(jsonPath("$.created_at").isNotEmpty());
        resultActions.andExpect(jsonPath("$.updated_at").isNotEmpty());
    }

    /**
     * {@link CompanyEmailController#findAll()}
     *
     * @throws Exception any exception
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void findAll_shouldReturnAllExceptDeleted() throws Exception {
        // create companyEmails
        CompanyEmail companyEmail1 = new CompanyEmail();
        companyEmail1.setEmail("<EMAIL>");
        companyEmail1.setCompany(testCompany);
        companyEmail1.setCreatedAt(Instant.now());
        companyEmail1.setUpdatedAt(Instant.now());
        companyEmailRepository.save(companyEmail1);

        CompanyEmail companyEmail2 = new CompanyEmail();
        companyEmail2.setEmail("<EMAIL>");
        companyEmail2.setCompany(testCompany);
        companyEmail2.setCreatedAt(Instant.now());
        companyEmail2.setUpdatedAt(Instant.now());
        companyEmailRepository.save(companyEmail2);

        CompanyEmail companyEmail3 = new CompanyEmail();
        companyEmail3.setEmail("<EMAIL>");
        companyEmail3.setCompany(testCompany);
        companyEmail3.setCreatedAt(Instant.now());
        companyEmail3.setUpdatedAt(Instant.now());
        companyEmail3.setDeletedAt(LocalDate.now());
        companyEmailRepository.save(companyEmail3);


        // hit endpoint
        ResultActions resultActions = mockMvc.perform(get(Api.COMPANY_EMAIL).contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        // assert
        resultActions.andExpect(jsonPath("$.length()").value(2));

        //in DB should be all three (one is deleted, so not in api call)
        assertEquals(3, companyEmailRepository.findAll().size());
    }

    /**
     * {@link CompanyEmailController#findOne(String)} ()}
     *
     * @throws Exception any exception
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void findOne_successful() throws Exception {

        // create companyEmails
        CompanyEmail companyEmail1 = new CompanyEmail();
        companyEmail1.setEmail("<EMAIL>");
        companyEmail1.setCompany(testCompany);
        companyEmail1.setCreatedAt(Instant.now());
        companyEmail1.setUpdatedAt(Instant.now());
        companyEmailRepository.save(companyEmail1);

        CompanyEmail companyEmail2 = new CompanyEmail();
        companyEmail2.setEmail("<EMAIL>");
        companyEmail2.setCompany(testCompany);
        companyEmail2.setCreatedAt(Instant.now());
        companyEmail2.setUpdatedAt(Instant.now());
        companyEmail2.setDeletedAt(LocalDate.now());
        companyEmailRepository.save(companyEmail2);

        // hit endpoint
        ResultActions resultActions = mockMvc.perform(get(Api.COMPANY_EMAIL + "/" + companyEmail1.getId()).contentType(
                MediaType.APPLICATION_JSON)).andExpect(status().isOk());

        // assert
        resultActions.andExpect(jsonPath("$.email").value(companyEmail1.getEmail()));
        resultActions.andExpect(jsonPath("$.company_id").value(companyEmail1.getCompanyId()));
        resultActions.andExpect(jsonPath("$.created_at").isNotEmpty());
        resultActions.andExpect(jsonPath("$.updated_at").isNotEmpty());
    }

    /**
     * {@link CompanyEmailController#findOne(String)} ()}
     *
     * @throws Exception any exception
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void findOne_failsDueToBeingDeleted() throws Exception {

        //Create company
        Company company = new Company();
        company.setName("test company");
        company.setCreatedAt(Instant.now());
        company.setUpdatedAt(Instant.now());
        this.companyRepository.save(company);

        CompanyEmail companyEmailDeleted = new CompanyEmail();
        companyEmailDeleted.setEmail("<EMAIL>");
        companyEmailDeleted.setCompany(company);
        companyEmailDeleted.setCreatedAt(Instant.now());
        companyEmailDeleted.setUpdatedAt(Instant.now());
        companyEmailDeleted.setDeletedAt(LocalDate.now());
        this.companyEmailRepository.save(companyEmailDeleted);

        // hit endpoint
        mockMvc.perform(get(Api.COMPANY_EMAIL + "/" + companyEmailDeleted.getId())).andExpect(status().isNotFound());
    }

    /**
     * {@link CompanyEmailController#update(String, UpdateCompanyEmailDto)}
     *
     * @throws Exception any exception
     * @ts-legacy see comment in {@link CompanyEmailService#update(Integer, UpdateCompanyEmailDto)}, but I think only email should be updated, and company ignored. Yet it seems swapped.
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void update_shouldUpdateCompanyEmail() throws Exception {
        final String originalEmail = "<EMAIL>";

        // new company
        Company newCompany = new Company();
        newCompany.setName("test company");
        newCompany.setCreatedAt(Instant.now());
        newCompany.setUpdatedAt(Instant.now());
        companyRepository.save(newCompany);

        // create companyEmails
        CompanyEmail companyEmail = new CompanyEmail();
        companyEmail.setEmail(originalEmail);
        companyEmail.setCompany(testCompany);
        companyEmail.setCreatedAt(Instant.now());
        companyEmail.setUpdatedAt(Instant.now());
        companyEmailRepository.save(companyEmail);

        // create dto
        UpdateCompanyEmailDto updateCompanyEmailDto = new UpdateCompanyEmailDto();
        updateCompanyEmailDto.setCompanyEmail("<EMAIL>");
        updateCompanyEmailDto.setCompanyId(newCompany.getId());

        // hit endpoint
        ResultActions result = mockMvc.perform(put(Api.COMPANY_EMAIL + "/" + companyEmail.getId()).contentType(MediaType.APPLICATION_JSON)
                                                       .content(objectMapper.writeValueAsString(updateCompanyEmailDto)))
                .andExpect(status().isOk());

        result.andExpect(jsonPath("$.company_id").value(newCompany.getId()));
        result.andExpect(jsonPath("$.email").value(originalEmail));  // should still be the original email, because service logic does not update email field
    }

    /**
     * {@link CompanyEmailController#update(String, UpdateCompanyEmailDto)}
     *
     * @throws Exception any exception
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void update_shouldUpdateCompanyEmail_notExist_throwsUnprocessable() throws Exception {
        // make sure no companyEmail exists
        assertEquals(0, companyEmailRepository.count());

        // create dto
        UpdateCompanyEmailDto updateCompanyEmailDto = new UpdateCompanyEmailDto();
        updateCompanyEmailDto.setCompanyEmail("<EMAIL>");
        updateCompanyEmailDto.setCompanyId(testCompany.getId());

        // hit endpoint
        mockMvc.perform(put(Api.COMPANY_EMAIL + "/" + 1).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateCompanyEmailDto)))
                .andExpect(status().isUnprocessableEntity());
    }


    /**
     * {@link CompanyEmailController#remove(String)}
     *
     * @throws Exception any exception
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void remove_shouldRemoveCompanyEmail() throws Exception {
        // create companyEmails
        CompanyEmail companyEmail = new CompanyEmail();
        companyEmail.setEmail("<EMAIL>");
        companyEmail.setCompany(testCompany);
        companyEmail.setCreatedAt(Instant.now());
        companyEmail.setUpdatedAt(Instant.now());
        this.companyEmailRepository.save(companyEmail);

        // confirm that the deletedAt is null
        assertNull(companyEmail.getDeletedAt(), "DeletedAt should be null before deletion");

        // hit endpoint
        ResultActions result = mockMvc.perform(delete(Api.COMPANY_EMAIL + "/" + companyEmail.getId()))
                .andExpect(status().isOk());

        result.andExpect(jsonPath("$.email").value(companyEmail.getEmail()));
        result.andExpect(jsonPath("$.company_id").value(companyEmail.getCompanyId()));
        result.andExpect(jsonPath("$.created_at").isNotEmpty());
        result.andExpect(jsonPath("$.updated_at").isNotEmpty());
        result.andExpect(jsonPath("$.deleted_at").isNotEmpty());
    }

}
