package de.interzero.oneepr.customer.license_other_cost;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.contract.ContractRepository;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import de.interzero.oneepr.customer.license.License;
import de.interzero.oneepr.customer.license.LicenseRepository;
import de.interzero.oneepr.customer.license_other_cost.dto.CreateLicenseOtherCostDto;
import de.interzero.oneepr.customer.license_other_cost.dto.UpdateLicenseOtherCostDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.Year;
import java.time.temporal.ChronoUnit;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Integration tests for the {@link LicenseOtherCostController}, focusing on successful "happy path" scenarios.
 * <p>
 * This test class validates the full HTTP request-response cycle, including security and database interactions.
 * It uses {@code @Transactional} to ensure that any data created is rolled back after each test,
 * providing a clean slate and guaranteeing test isolation.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
@WithMockUser(
        username = "101",
        roles = {"CUSTOMER"}
)
class LicenseOtherCostControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private LicenseRepository licenseRepository;

    @Autowired
    private LicenseOtherCostRepository licenseOtherCostRepository;

    private License testLicense;

    private LicenseOtherCost cost1;

    /**
     * Sets up a consistent database state before each test method runs.
     * <p>
     * This method is responsible for creating a primary {@link Customer}, {@link Contract}, and {@link License},
     * along with associated {@link LicenseOtherCost} records. This data is used by the test methods
     * to perform their assertions and is automatically rolled back by the {@code @Transactional} annotation on the class.
     */
    @BeforeEach
    void setUp() {
        Customer testCustomer = new Customer();
        testCustomer.setUserId(101);
        testCustomer.setFirstName("Test");
        testCustomer.setLastName("User");
        testCustomer.setEmail("testuser" + Instant.now().toEpochMilli() + "@example.com");
        testCustomer.setCreatedAt(Instant.now());
        testCustomer.setUpdatedAt(Instant.now());

        testCustomer = customerRepository.save(testCustomer);

        Contract contract = new Contract();
        contract.setCustomer(testCustomer);
        contract.setTitle("Test Contract " + System.currentTimeMillis());
        contract.setStartDate(Instant.now());
        contract.setEndDate(Instant.now().plus(365, ChronoUnit.DAYS));
        contract.setCreatedAt(Instant.now());
        contract.setUpdatedAt(Instant.now());
        contract.setType(Contract.Type.EU_LICENSE);
        contract.setStatus(Contract.Status.ACTIVE);

        contract = contractRepository.save(contract);

        testLicense = createAndSaveTestLicense("Germany", contract);

        cost1 = new LicenseOtherCost(null, 1, testLicense, "Initial Cost", 1000, Instant.now(), Instant.now(), null);
        cost1 = licenseOtherCostRepository.save(cost1);

        LicenseOtherCost cost2 = new LicenseOtherCost(
                null,
                                                      2,
                                                      testLicense,
                                                      "Another Cost",
                                                      2500,
                                                      Instant.now(),
                                                      Instant.now(),
                                                      null);
        licenseOtherCostRepository.save(cost2);
    }

    /**
     * Verifies that a GET request to the root endpoint successfully returns a list of all existing costs.
     */
    @Test
    void findAll_shouldReturnAllCosts_whenNoFilterProvided() throws Exception {
        mockMvc.perform(get(Api.OTHER_COSTS)).andExpect(status().isOk()).andExpect(jsonPath("$", hasSize(2)));
    }

    /**
     * Verifies that a GET request with a {@code license_id} query parameter returns only the costs for that license.
     */
    @Test
    void findAll_shouldReturnFilteredCosts_whenLicenseIdProvided() throws Exception {
        mockMvc.perform(get(Api.OTHER_COSTS).param("license_id", testLicense.getId().toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(2)))
                .andExpect(jsonPath("$[0].license_id", is(testLicense.getId())))
                .andExpect(jsonPath("$[1].license_id", is(testLicense.getId())));
    }

    /**
     * Verifies that a GET request for a specific ID returns the correct cost entity.
     */
    @Test
    void findOne_shouldReturnCost_whenFoundAndUserHasPermission() throws Exception {
        mockMvc.perform(get(Api.OTHER_COSTS + "/{id}", cost1.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(cost1.getId())))
                .andExpect(jsonPath("$.name", is("Initial Cost")));
    }

    /**
     * Verifies that a POST request with a valid DTO successfully creates a new cost record in the database.
     */
    @Test
    void create_shouldCreateNewCostAndReturnIt() throws Exception {
        CreateLicenseOtherCostDto createDto = new CreateLicenseOtherCostDto(
                testLicense.getId(),
                                                                            99,
                                                                            "New Service Fee",
                                                                            5000);

        mockMvc.perform(post(Api.OTHER_COSTS).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.name", is("New Service Fee")))
                .andExpect(jsonPath("$.price", is(5000)))
                .andExpect(jsonPath("$.license_id", is(testLicense.getId())));
    }

    /**
     * Verifies that a PUT request with a valid DTO successfully updates an existing cost record.
     */
    @Test
    void update_shouldModifyExistingCost() throws Exception {
        UpdateLicenseOtherCostDto updateDto = new UpdateLicenseOtherCostDto(
                cost1.getSetupOtherCostId(),
                                                                            "Updated Cost Name",
                                                                            1500);

        mockMvc.perform(put(Api.OTHER_COSTS + "/{id}", cost1.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(cost1.getId())))
                .andExpect(jsonPath("$.name", is("Updated Cost Name")))
                .andExpect(jsonPath("$.price", is(1500)));
    }

    /**
     * Verifies that a DELETE request to a specific ID successfully soft-deletes the record.
     */
    @Test
    void remove_shouldSoftDeleteCost() throws Exception {
        mockMvc.perform(delete(Api.OTHER_COSTS + "/{id}", cost1.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.deleted_at").exists());

        // Verify in the database that the deleted_at field is set
        LicenseOtherCost deletedCost = licenseOtherCostRepository.findById(cost1.getId()).orElseThrow();
        assertNotNull(deletedCost.getDeletedAt());
    }

    /**
     * Helper method to create and persist a fully populated {@link License} entity.
     * This is used within the setUp method to create realistic and valid test data.
     *
     * @param countryName The name of the country for the new license.
     * @param contract    The {@link Contract} to associate with this license.
     * @return The persisted {@link License} entity.
     */
    private License createAndSaveTestLicense(String countryName,
                                             Contract contract) {
        License license = new License();
        license.setCountryName(countryName);
        license.setContract(contract);
        license.setRegistrationNumber("REG-" + System.currentTimeMillis());
        license.setCountryFlag("🇩🇪");
        license.setCountryId(1);
        license.setYear(Year.now().getValue());
        license.setStartDate(Instant.now().minus(30, ChronoUnit.DAYS));
        license.setCountryCode("DE");
        license.setCreatedAt(Instant.now());
        license.setUpdatedAt(Instant.now());
        return licenseRepository.saveAndFlush(license);
    }
}