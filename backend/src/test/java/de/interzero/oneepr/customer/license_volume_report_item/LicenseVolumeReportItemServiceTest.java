package de.interzero.oneepr.customer.license_volume_report_item;

import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer_io.CustomerIoService;
import de.interzero.oneepr.customer.entity.LicenseVolumeReportItem;
import de.interzero.oneepr.customer.license.License;
import de.interzero.oneepr.customer.license_packaging_service.LicensePackagingService;
import de.interzero.oneepr.customer.license_volume_report.LicenseVolumeReport;
import de.interzero.oneepr.customer.license_volume_report.LicenseVolumeReportRepository;
import de.interzero.oneepr.customer.license_volume_report_item.dto.CreateLicenseVolumeReportItemDto;
import de.interzero.oneepr.customer.license_volume_report_item.dto.DeclineLicenseVolumeReportItemDto;
import de.interzero.oneepr.customer.license_volume_report_item.dto.UpdateLicenseVolumeReportItemDto;
import de.interzero.oneepr.customer.license_volume_report_item.dto.UpdateLicenseVolumeReportItemValueDto;
import de.interzero.oneepr.customer.reason.Reason;
import de.interzero.oneepr.customer.reason.ReasonRepository;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import de.interzero.oneepr.customer.shared.auth.Role;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static com.github.tomakehurst.wiremock.client.WireMock.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for LicenseVolumeReportItemService with 100% coverage.
 * Uses WireMock for external service mocking.
 */
@ExtendWith(MockitoExtension.class)
class LicenseVolumeReportItemServiceTest {

    @Mock
    private LicenseVolumeReportItemRepository licenseVolumeReportItemRepository;

    @Mock
    private LicenseVolumeReportRepository licenseVolumeReportRepository;

    @Mock
    private CustomerIoService customerIoService;

    @Mock
    private ReasonRepository reasonRepository;

    @InjectMocks
    private LicenseVolumeReportItemService licenseVolumeReportItemService;

    private WireMockServer wireMockServer;

    // Test data constants
    private static final Integer TEST_ID = 1;
    private static final Integer TEST_LICENSE_VOLUME_REPORT_ID = 100;
    private static final String TEST_USER_ID = "123";
    private static final String TEST_USER_EMAIL = "<EMAIL>";
    private static final Role TEST_USER_ROLE = Role.CUSTOMER;

    @BeforeEach
    void setUp() {
        // Setup WireMock server for external service calls
        wireMockServer = new WireMockServer(8089);
        wireMockServer.start();
        WireMock.configureFor("localhost", 8089);
    }

    @AfterEach
    void tearDown() {
        if (wireMockServer != null) {
            wireMockServer.stop();
        }
    }

    /**
     * Test sendToCustomerIo method - Success case
     */
    @Test
    void sendToCustomerIo_ShouldCallCustomerIoService_WhenValidLicenseVolumeReportId() {
        // Given
        LicenseVolumeReport volumeReport = createTestLicenseVolumeReport();
        when(licenseVolumeReportRepository.findById(TEST_LICENSE_VOLUME_REPORT_ID))
                .thenReturn(Optional.of(volumeReport));

        // When
        licenseVolumeReportItemService.sendToCustomerIo(TEST_LICENSE_VOLUME_REPORT_ID);

        // Then
        verify(customerIoService).processPurchaseData(TEST_ID);
    }

    /**
     * Test sendToCustomerIo method - Null ID case
     */
    @Test
    void sendToCustomerIo_ShouldDoNothing_WhenLicenseVolumeReportIdIsNull() {
        // When
        licenseVolumeReportItemService.sendToCustomerIo(null);

        // Then
        verify(licenseVolumeReportRepository, never()).findById(any());
        verify(customerIoService, never()).processPurchaseData(any());
    }

    /**
     * Test sendToCustomerIo method - Volume report not found
     */
    @Test
    void sendToCustomerIo_ShouldDoNothing_WhenVolumeReportNotFound() {
        // Given
        when(licenseVolumeReportRepository.findById(TEST_LICENSE_VOLUME_REPORT_ID))
                .thenReturn(Optional.empty());

        // When
        licenseVolumeReportItemService.sendToCustomerIo(TEST_LICENSE_VOLUME_REPORT_ID);

        // Then
        verify(customerIoService, never()).processPurchaseData(any());
    }

    /**
     * Test sendToCustomerIo method - Exception handling
     */
    @Test
    void sendToCustomerIo_ShouldHandleException_WhenRepositoryThrowsException() {
        // Given
        when(licenseVolumeReportRepository.findById(TEST_LICENSE_VOLUME_REPORT_ID))
                .thenThrow(new RuntimeException("Database error"));

        // When & Then - Should not throw exception
        assertDoesNotThrow(() -> licenseVolumeReportItemService.sendToCustomerIo(TEST_LICENSE_VOLUME_REPORT_ID));
    }

    /**
     * Test create method - Success case
     */
    @Test
    void create_ShouldReturnCreatedItem_WhenValidDto() {
        // Given
        CreateLicenseVolumeReportItemDto createDto = createTestCreateDto();
        LicenseVolumeReport volumeReport = createTestLicenseVolumeReport();
        LicenseVolumeReportItem expectedItem = createTestLicenseVolumeReportItem();

        when(licenseVolumeReportRepository.findById(TEST_LICENSE_VOLUME_REPORT_ID))
                .thenReturn(Optional.of(volumeReport));
        when(licenseVolumeReportItemRepository.save(any(LicenseVolumeReportItem.class)))
                .thenReturn(expectedItem);

        // When
        LicenseVolumeReportItem result = licenseVolumeReportItemService.create(createDto);

        // Then
        assertNotNull(result);
        assertEquals(TEST_ID, result.getId());
        verify(licenseVolumeReportItemRepository).save(any(LicenseVolumeReportItem.class));
        verify(customerIoService).processPurchaseData(any());
    }

    /**
     * Test create method - License volume report not found
     */
    @Test
    void create_ShouldThrowException_WhenLicenseVolumeReportNotFound() {
        // Given
        CreateLicenseVolumeReportItemDto createDto = createTestCreateDto();
        when(licenseVolumeReportRepository.findById(TEST_LICENSE_VOLUME_REPORT_ID))
                .thenReturn(Optional.empty());

        // When & Then
        ResponseStatusException exception = assertThrows(ResponseStatusException.class,
                () -> licenseVolumeReportItemService.create(createDto));
        assertEquals(HttpStatus.NOT_FOUND, exception.getStatusCode());
        assertEquals("License Volume Report not found", exception.getReason());
    }

    /**
     * Test findAll method - Success case
     */
    @Test
    void findAll_ShouldReturnItems_WhenValidRequest() {
        // Given
        AuthenticatedUser user = new AuthenticatedUser(TEST_USER_ID, Role.ADMIN, TEST_USER_EMAIL);
        LicenseVolumeReport volumeReport = createTestLicenseVolumeReport();
        List<LicenseVolumeReportItem> expectedItems = Arrays.asList(createTestLicenseVolumeReportItem());

        when(licenseVolumeReportRepository.findFirstByIdAndDeletedAtIsNull(TEST_LICENSE_VOLUME_REPORT_ID))
                .thenReturn(Optional.of(volumeReport));
        when(licenseVolumeReportItemRepository.findByLicenseVolumeReportAndDeletedAtIsNull(volumeReport))
                .thenReturn(expectedItems);

        // When
        List<LicenseVolumeReportItem> result = licenseVolumeReportItemService.findAll(TEST_LICENSE_VOLUME_REPORT_ID, user);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(TEST_ID, result.get(0).getId());
    }

    /**
     * Test findAll method - License volume report not found
     */
    @Test
    void findAll_ShouldThrowException_WhenLicenseVolumeReportNotFound() {
        // Given
        AuthenticatedUser user = new AuthenticatedUser(TEST_USER_ID, Role.ADMIN, TEST_USER_EMAIL);
        when(licenseVolumeReportRepository.findFirstByIdAndDeletedAtIsNull(TEST_LICENSE_VOLUME_REPORT_ID))
                .thenReturn(Optional.empty());

        // When & Then
        ResponseStatusException exception = assertThrows(ResponseStatusException.class,
                () -> licenseVolumeReportItemService.findAll(TEST_LICENSE_VOLUME_REPORT_ID, user));
        assertEquals(HttpStatus.NOT_FOUND, exception.getStatusCode());
        assertEquals("License Volume Report Item not found", exception.getReason());
    }

    /**
     * Test findAll method - Packaging service not found
     */
    @Test
    void findAll_ShouldThrowException_WhenPackagingServiceNotFound() {
        // Given
        AuthenticatedUser user = new AuthenticatedUser(TEST_USER_ID, Role.ADMIN, TEST_USER_EMAIL);
        LicenseVolumeReport volumeReport = new LicenseVolumeReport();
        volumeReport.setPackagingService(null); // No packaging service

        when(licenseVolumeReportRepository.findFirstByIdAndDeletedAtIsNull(TEST_LICENSE_VOLUME_REPORT_ID))
                .thenReturn(Optional.of(volumeReport));

        // When & Then
        ResponseStatusException exception = assertThrows(ResponseStatusException.class,
                () -> licenseVolumeReportItemService.findAll(TEST_LICENSE_VOLUME_REPORT_ID, user));
        assertEquals(HttpStatus.NOT_FOUND, exception.getStatusCode());
        assertEquals("Packaging service not found", exception.getReason());
    }

    /**
     * Test findAll method - Customer permission check for CUSTOMER role
     */
    @Test
    void findAll_ShouldThrowForbidden_WhenCustomerAccessesOtherCustomerData() {
        // Given
        AuthenticatedUser user = new AuthenticatedUser("999", Role.CUSTOMER, "<EMAIL>");
        LicenseVolumeReport volumeReport = createTestLicenseVolumeReport();

        when(licenseVolumeReportRepository.findFirstByIdAndDeletedAtIsNull(TEST_LICENSE_VOLUME_REPORT_ID))
                .thenReturn(Optional.of(volumeReport));

        // When & Then
        ResponseStatusException exception = assertThrows(ResponseStatusException.class,
                () -> licenseVolumeReportItemService.findAll(TEST_LICENSE_VOLUME_REPORT_ID, user));
        assertEquals(HttpStatus.FORBIDDEN, exception.getStatusCode());
        assertEquals("You do not have permission to access this resource", exception.getReason());
    }

    /**
     * Test findOne method - Success case
     */
    @Test
    void findOne_ShouldReturnItem_WhenValidIdAndPermissions() {
        // Given
        AuthenticatedUser user = new AuthenticatedUser(TEST_USER_ID, Role.ADMIN, TEST_USER_EMAIL);
        LicenseVolumeReportItem expectedItem = createTestLicenseVolumeReportItem();

        // Mock the permission validation method
        doNothing().when(licenseVolumeReportItemRepository).findById(TEST_ID);
        when(licenseVolumeReportItemRepository.findById(TEST_ID))
                .thenReturn(Optional.of(expectedItem));

        // When
        Optional<LicenseVolumeReportItem> result = licenseVolumeReportItemService.findOne(TEST_ID, user);

        // Then
        assertTrue(result.isPresent());
        assertEquals(TEST_ID, result.get().getId());
    }

    // Helper methods for creating test data
    private CreateLicenseVolumeReportItemDto createTestCreateDto() {
        CreateLicenseVolumeReportItemDto dto = new CreateLicenseVolumeReportItemDto();
        dto.setLicenseVolumeReportId(TEST_LICENSE_VOLUME_REPORT_ID);
        dto.setSetupFractionId(1);
        dto.setSetupColumnId(2);
        dto.setValue(100);
        dto.setSetupFractionCode("DEOK34");
        dto.setSetupColumnCode("COL01");
        return dto;
    }

    private UpdateLicenseVolumeReportItemDto createTestUpdateDto() {
        UpdateLicenseVolumeReportItemDto dto = new UpdateLicenseVolumeReportItemDto();
        dto.setValue(200);
        dto.setSetupFractionCode("UPDATED_CODE");
        return dto;
    }

    private DeclineLicenseVolumeReportItemDto createTestDeclineDto() {
        DeclineLicenseVolumeReportItemDto dto = new DeclineLicenseVolumeReportItemDto();
        dto.setReasonIds(Arrays.asList(1, 2));
        dto.setTitle("Test decline reason");
        return dto;
    }

    private UpdateLicenseVolumeReportItemValueDto createTestUpdateValueDto() {
        UpdateLicenseVolumeReportItemValueDto dto = new UpdateLicenseVolumeReportItemValueDto();
        dto.setLicenseVolumeReportId(TEST_LICENSE_VOLUME_REPORT_ID);
        dto.setVolumeReportItemId(TEST_ID);
        dto.setValue(150);
        return dto;
    }

    private LicenseVolumeReportItem createTestLicenseVolumeReportItem() {
        LicenseVolumeReportItem item = new LicenseVolumeReportItem();
        item.setId(TEST_ID);
        item.setSetupFractionId(1);
        item.setSetupColumnId(2);
        item.setValue(100);
        item.setPrice(50);
        item.setSetupFractionCode("DEOK34");
        item.setSetupColumnCode("COL01");
        item.setCreatedAt(Instant.now());
        item.setUpdatedAt(Instant.now());
        return item;
    }

    private LicenseVolumeReport createTestLicenseVolumeReport() {
        LicenseVolumeReport volumeReport = new LicenseVolumeReport();
        volumeReport.setId(TEST_LICENSE_VOLUME_REPORT_ID);

        // Create nested structure for permission validation
        LicensePackagingService packagingService = new LicensePackagingService();
        License license = new License();
        Contract contract = new Contract();
        Customer customer = new Customer();
        customer.setId(Integer.parseInt(TEST_USER_ID));
        customer.setUserId(Integer.parseInt(TEST_USER_ID));
        customer.setEmail(TEST_USER_EMAIL);

        contract.setCustomer(customer);
        license.setContract(contract);
        license.setCountryName("Germany");
        license.setYear(2024);
        packagingService.setLicense(license);
        volumeReport.setPackagingService(packagingService);

        return volumeReport;
    }

    private Reason createTestReason() {
        Reason reason = new Reason();
        reason.setId(1);
        reason.setName("Test Reason");
        reason.setType(Reason.Type.DECLINE);
        return reason;
    }
}
