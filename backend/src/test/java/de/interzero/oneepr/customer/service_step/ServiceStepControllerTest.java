package de.interzero.oneepr.customer.service_step;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.TestRole;
import de.interzero.oneepr.customer.service_step.dto.CreateServiceStepDto;
import de.interzero.oneepr.customer.service_step.dto.UpdateServiceStepDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.temporal.ChronoUnit;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for the {@link ServiceStepController}.
 * This class uses {@link SpringBootTest} to load the full application context
 * and {@link AutoConfigureMockMvc} to configure {@link MockMvc} for sending HTTP requests
 * to the controller endpoints. It verifies the behavior of the service step API,
 * including creation, retrieval, update, and deletion of service step records,
 * interacting with the actual service and repository layers and a test database.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class ServiceStepControllerTest {


    private static final String API_SERVICE_STEP_BASE_URL = Api.SERVICE_STEP;

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ServiceStepRepository serviceStepRepository;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * Sets up the test environment before each test method execution.
     * This method performs the following actions:
     * <p>
     * Clears all existing data from {@code serviceStepRepository} to ensure test isolation.
     * Registers JavaTimeModule for proper Instant serialization/deserialization.
     */
    @BeforeEach
    void setup() {
        objectMapper.registerModule(new JavaTimeModule());
        serviceStepRepository.deleteAllInBatch(); // Use deleteAllInBatch for efficiency if applicable
    }

    /**
     * Helper method to create and persist a {@link ServiceStep} entity.
     *
     * @param type        The type of the service step.
     * @param title       The title of the service step.
     * @param countryCode Optional country code.
     * @param isActive    Active status.
     * @return The persisted {@link ServiceStep} entity.
     */
    private ServiceStep createAndSaveTestServiceStep(ServiceStep.Type type,
                                                     String title,
                                                     String countryCode,
                                                     boolean isActive) {
        ServiceStep step = new ServiceStep();
        step.setType(type);
        step.setTitle(title);
        step.setCountryCode(countryCode);
        step.setIsActive(isActive);
        step.setAvailableAt(Instant.now().minus(1, ChronoUnit.DAYS));
        step.setDeadlineAt(Instant.now().plus(10, ChronoUnit.DAYS));
        step.setCreatedAt(Instant.now().minusSeconds(3600));
        step.setUpdatedAt(Instant.now().minusSeconds(3600));
        return serviceStepRepository.saveAndFlush(step);
    }

    /**
     * Test for {@link ServiceStepController#create(CreateServiceStepDto)}
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void create_shouldCreateNewServiceStep() throws Exception {
        CreateServiceStepDto createDto = new CreateServiceStepDto();
        createDto.setType("LICENSE");
        createDto.setTitle("New License Step");
        createDto.setCountryCode("DE");
        createDto.setActive(true);
        createDto.setAvailableAt(Instant.now().toString());
        createDto.setDeadlineAt(Instant.now().plus(5, ChronoUnit.DAYS).toString());

        ResultActions resultActions = mockMvc.perform(post(API_SERVICE_STEP_BASE_URL).with(csrf())
                                                              .contentType(MediaType.APPLICATION_JSON)
                                                              .content(objectMapper.writeValueAsString(createDto))
                                                              .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isCreated());

        ServiceStep createdServiceStep = objectMapper.readValue(
                resultActions.andReturn()
                        .getResponse()
                        .getContentAsString(), ServiceStep.class);

        assertNotNull(createdServiceStep.getId());
        assertEquals(createDto.getType(), createdServiceStep.getType().name());
        assertEquals(createDto.getTitle(), createdServiceStep.getTitle());
        assertEquals(createDto.getCountryCode(), createdServiceStep.getCountryCode());
        assertEquals(createDto.isActive(), createdServiceStep.getIsActive());
        assertNotNull(createdServiceStep.getCreatedAt());
        assertNotNull(createdServiceStep.getUpdatedAt());
        assertNull(createdServiceStep.getDeletedAt()); // New steps shouldn't be soft-deleted
    }

    /**
     * Test for {@link ServiceStepController#findAll(String, String, boolean)}
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void findAll_shouldReturnFilteredServiceSteps() throws Exception {
        createAndSaveTestServiceStep(ServiceStep.Type.LICENSE, "License DE Active", "DE", true);
        createAndSaveTestServiceStep(ServiceStep.Type.ACTION_GUIDE, "Action Guide DE Inactive", "DE", false);
        createAndSaveTestServiceStep(ServiceStep.Type.LICENSE, "License FR Active", "FR", true);

        // Test fetching all LICENSE types
        mockMvc.perform(get(API_SERVICE_STEP_BASE_URL).param("type", "LICENSE").accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$", hasSize(2)))
                .andExpect(jsonPath("$[0].type", is("LICENSE")))
                .andExpect(jsonPath("$[1].type", is("LICENSE")));

        // Test fetching active LICENSE types in DE
        mockMvc.perform(get(API_SERVICE_STEP_BASE_URL).param("type", "LICENSE")
                                .param("countryCode", "DE")
                                .param("isActive", "true")
                                .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].title", is("License DE Active")));

        // Test with default isActive=true
        mockMvc.perform(get(API_SERVICE_STEP_BASE_URL).param("type", "ACTION_GUIDE")
                                .param("countryCode", "DE")
                                .param("isActive", "false")
                                .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].title", is("Action Guide DE Inactive")));
    }


    /**
     * Test for {@link ServiceStepController#findOne(Integer)}
     */
    @Test
    @WithMockUser(roles = {TestRole.CUSTOMER})
    void findOne_shouldReturnSpecificServiceStep() throws Exception {
        ServiceStep savedStep = createAndSaveTestServiceStep(ServiceStep.Type.LICENSE, "Specific Step", "ES", true);

        mockMvc.perform(get(API_SERVICE_STEP_BASE_URL + "/{id}", savedStep.getId()).accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value(savedStep.getId()))
                .andExpect(jsonPath("$.title").value("Specific Step"))
                .andExpect(jsonPath("$.country_code").value("ES"));
    }

    /**
     * Test for {@link ServiceStepController#update(Integer, UpdateServiceStepDto)}
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    // Assuming ADMIN role can update
    void update_shouldUpdateServiceStep() throws Exception {
        ServiceStep originalStep = createAndSaveTestServiceStep(
                ServiceStep.Type.ACTION_GUIDE,
                "Original Title",
                "IT",
                true);
        Instant originalUpdatedAt = originalStep.getUpdatedAt();
        if (originalUpdatedAt == null && originalStep.getCreatedAt() != null) { // If @UpdateTimestamp not triggered by save
            originalUpdatedAt = originalStep.getCreatedAt();
        }


        UpdateServiceStepDto updateDto = new UpdateServiceStepDto();
        updateDto.setTitle("Updated Action Guide Title");
        updateDto.setIsActive(false);
        updateDto.setType(ServiceStep.Type.ACTION_GUIDE.name()); // Keep type same or update
        // Set other fields from CreateServiceStepDto as UpdateServiceStepDto extends it
        updateDto.setCountryCode(originalStep.getCountryCode());
        updateDto.setAvailableAt(originalStep.getAvailableAt().toString());
        updateDto.setDeadlineAt(originalStep.getDeadlineAt().toString());


        ResultActions resultActions = mockMvc.perform(put(
                        API_SERVICE_STEP_BASE_URL + "/{id}",
                        originalStep.getId()).with(csrf())
                                                              .contentType(MediaType.APPLICATION_JSON)
                                                              .content(objectMapper.writeValueAsString(updateDto))
                                                              .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk());

        ServiceStep updatedServiceStep = objectMapper.readValue(
                resultActions.andReturn()
                        .getResponse()
                        .getContentAsString(), ServiceStep.class);

        assertEquals(originalStep.getId(), updatedServiceStep.getId());
        assertEquals("Updated Action Guide Title", updatedServiceStep.getTitle());
        assertEquals(false, updatedServiceStep.getIsActive());
        assertNotNull(updatedServiceStep.getUpdatedAt());
        // Ensure updatedAt is actually later, might need a slight delay or careful check if clocks are too fast
        if (originalUpdatedAt != null) {
            assertTrue(
                    updatedServiceStep.getUpdatedAt().isAfter(originalUpdatedAt) || updatedServiceStep.getUpdatedAt()
                            .equals(originalUpdatedAt),
                    "UpdatedAt should be after or equal to original for an update.");
        }


        ServiceStep updatedEntityInDb = serviceStepRepository.findById(originalStep.getId()).orElseThrow();
        assertEquals("Updated Action Guide Title", updatedEntityInDb.getTitle());
        assertEquals(false, updatedEntityInDb.getIsActive());
    }

    /**
     * Test for {@link ServiceStepController#remove(Integer)}
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void remove_shouldSoftDeleteServiceStep() throws Exception {
        ServiceStep savedStep = createAndSaveTestServiceStep(ServiceStep.Type.LICENSE, "To Be Deleted", "GB", true);

        mockMvc.perform(delete(API_SERVICE_STEP_BASE_URL + "/{id}", savedStep.getId()).with(csrf())
                                .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk()); // Controller returns the soft-deleted entity with 200 OK

        ServiceStep deletedStepInDb = serviceStepRepository.findById(savedStep.getId()).orElseThrow();
        assertNotNull(deletedStepInDb.getDeletedAt(), "DeletedAt timestamp should be set after soft delete.");

        // Verify it's not returned by findAll (which filters out deleted items)
        mockMvc.perform(get(API_SERVICE_STEP_BASE_URL).param("type", "LICENSE").accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[?(@.id == " + savedStep.getId() + ")]").doesNotExist());
    }
}