package de.interzero.oneepr.customer.customer_service_step;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.TestRole;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import de.interzero.oneepr.customer.customer_service_step.dto.ServiceStepResultDto;
import de.interzero.oneepr.customer.customer_service_step.dto.ToggleCustomerServiceStepDto;
import de.interzero.oneepr.customer.service_step.ServiceStep;
import de.interzero.oneepr.customer.service_step.ServiceStepRepository;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Integration tests for the {@link CustomerServiceStepController}.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class CustomerServiceStepControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private ServiceStepRepository serviceStepRepository;

    @Autowired
    private CustomerServiceStepRepository customerServiceStepRepository;

    private Customer testCustomer1;

    private Customer testCustomer2;

    private ServiceStep testServiceStep1;

    private ServiceStep testServiceStep2;

    private CustomerServiceStep testCustomerServiceStep1;

    private final Instant testInstant = Instant.now();


    /**
     * Sets up the test environment before each test method execution.
     * Clears relevant database tables and creates initial customer and consent type data.
     */
    @BeforeEach
    void setUp() {
        // first customer steps, with done completed
        Customer customer1 = new Customer();
        customer1.setFirstName("John");
        customer1.setLastName("Doe");
        customer1.setEmail("<EMAIL>");
        customer1.setUserId(101);
        customer1.setCreatedAt(Instant.now());
        customer1.setUpdatedAt(Instant.now());
        customer1.setType(Customer.Type.REGULAR);
        testCustomer1 = customerRepository.save(customer1);

        ServiceStep serviceStep1 = new ServiceStep();
        serviceStep1.setType(ServiceStep.Type.LICENSE);
        serviceStep1.setCountryCode("FR");
        serviceStep1.setTitle("Test Step");
        serviceStep1.setAvailableAt(Instant.now());
        serviceStep1.setDeadlineAt(Instant.now());
        testServiceStep1 = serviceStepRepository.save(serviceStep1);

        CustomerServiceStep customerServiceStep1 = new CustomerServiceStep();
        customerServiceStep1.setServiceStep(testServiceStep1);
        customerServiceStep1.setCustomer(customer1);
        customerServiceStep1.setDoneAt(testInstant);
        testCustomerServiceStep1 = customerServiceStepRepository.save(customerServiceStep1);

        // second customer steps, without done completed
        Customer customer2 = new Customer();
        customer2.setFirstName("Jane");
        customer2.setLastName("Smith");
        customer2.setEmail("<EMAIL>");
        customer2.setUserId(102);
        customer2.setCreatedAt(Instant.now());
        customer2.setUpdatedAt(Instant.now());
        customer2.setType(Customer.Type.PREMIUM);
        testCustomer2 = customerRepository.save(customer2);

        ServiceStep serviceStep2 = new ServiceStep();
        serviceStep2.setType(ServiceStep.Type.ACTION_GUIDE);
        serviceStep2.setCountryCode("DE");
        serviceStep2.setTitle("Test Step 2");
        serviceStep2.setAvailableAt(Instant.now());
        serviceStep2.setDeadlineAt(Instant.now());
        testServiceStep2 = serviceStepRepository.save(serviceStep2);

        CustomerServiceStep customerServiceStep2 = new CustomerServiceStep();
        customerServiceStep2.setServiceStep(testServiceStep2);
        customerServiceStep2.setCustomer(customer2);
        customerServiceStepRepository.save(customerServiceStep2);

        // Verify initial counts
        assertEquals(2, customerRepository.count());
        assertEquals(2, serviceStepRepository.count());
        assertEquals(2, customerServiceStepRepository.count());
    }

    @AfterEach
    void tearDown() {
        customerServiceStepRepository.deleteAll();
        serviceStepRepository.deleteAll();
        customerRepository.deleteAll();
    }


    /**
     * {@link CustomerServiceStepController#findAll(Integer, ServiceStep.Type, String)}
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void findAll_returnLicense() throws Exception {
        ResultActions result = mockMvc.perform(get(Api.CUSTOMER_SERVICE_STEP).param(
                                "customerId",
                                testCustomer1.getId().toString())
                                                       .param("type", ServiceStep.Type.LICENSE.name())
                                                       .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        result.andExpect(jsonPath("length()").value(1));
        result.andExpect(jsonPath("$[0].id").value(testServiceStep1.getId()));
        result.andExpect(jsonPath("$[0].title").value(testServiceStep1.getTitle()));
        result.andExpect(jsonPath("$[0].customer_id").value(testCustomer1.getId()));
        result.andExpect(jsonPath("$[0].done_at").value(testInstant.toString()));    // customerServiceStep1 should have doneAt set
    }

    /**
     * {@link CustomerServiceStepController#findAll(Integer, ServiceStep.Type, String)}
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void findAll_returnActionGuide() throws Exception {
        ResultActions result = mockMvc.perform(get(Api.CUSTOMER_SERVICE_STEP).param(
                                "customerId",
                                testCustomer2.getId().toString())
                                                       .param("type", ServiceStep.Type.ACTION_GUIDE.name())
                                                       .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        result.andExpect(jsonPath("length()").value(1));
        result.andExpect(jsonPath("$[0].id").value(testServiceStep2.getId()));
        result.andExpect(jsonPath("$[0].title").value(testServiceStep2.getTitle()));
        result.andExpect(jsonPath("$[0].customer_id").value(testCustomer2.getId()));
        result.andExpect(jsonPath("$[0].done_at").doesNotExist());  // customerServiceStep2 should be null
    }

    /**
     * {@link CustomerServiceStepController#findAll(Integer, ServiceStep.Type, String)}
     * Should return no {@link CustomerServiceStep}, but will still return the {@link ServiceStep}, therefore
     * confirm {@link ServiceStepResultDto#getDoneAt()} is null.
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void findAll_returnEmptyDoneAt() throws Exception {
        ResultActions result = mockMvc.perform(get(Api.CUSTOMER_SERVICE_STEP).param(
                                "customerId",
                                testCustomer1.getId().toString())
                                                       .param("type", ServiceStep.Type.ACTION_GUIDE.name())
                                                       .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        result.andExpect(jsonPath("length()").value(1));
        result.andExpect(jsonPath("$[0].title").value(testServiceStep2.getTitle()));    // serviceStep2 should be returned
        result.andExpect(jsonPath("$[0].customer_id").value(testCustomer1.getId()));    // with ID of customerServiceStep1
        result.andExpect(jsonPath("$[0].done_at").doesNotExist());                      // but customerServiceStep1 hasn't done it
    }

    /**
     * {@link CustomerServiceStepController#findAll(Integer, ServiceStep.Type, String)}
     * Since the country code is optional, but other parameters are required, this test checks that
     * searching by country code works correctly.
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void findAll_searchByCountryCode() throws Exception {
        // searching by FR should return customerServiceStep1
        ResultActions expectCss1 = mockMvc.perform(get(Api.CUSTOMER_SERVICE_STEP).param(
                                "customerId",
                                testCustomer1.getId()
                                        .toString())
                                                           .param("type", ServiceStep.Type.LICENSE.name())
                                                           .param("countryCode", "FR")
                                                           .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        expectCss1.andExpect(jsonPath("length()").value(1));
        expectCss1.andExpect(jsonPath("$[0].id").value(testServiceStep1.getId()));
        expectCss1.andExpect(jsonPath("$[0].title").value(testServiceStep1.getTitle()));
        expectCss1.andExpect(jsonPath("$[0].customer_id").value(testCustomer1.getId()));
        expectCss1.andExpect(jsonPath("$[0].done_at").value(testInstant.toString()));

        // searching by DE should return customerServiceStep2
        ResultActions expectCss2 = mockMvc.perform(get(Api.CUSTOMER_SERVICE_STEP).param(
                                "customerId",
                                testCustomer2.getId()
                                        .toString())
                                                           .param("type", ServiceStep.Type.ACTION_GUIDE.name())
                                                           .param("countryCode", "DE")
                                                           .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        expectCss2.andExpect(jsonPath("length()").value(1));
        expectCss2.andExpect(jsonPath("$[0].id").value(testServiceStep2.getId()));
        expectCss2.andExpect(jsonPath("$[0].title").value(testServiceStep2.getTitle()));
        expectCss2.andExpect(jsonPath("$[0].customer_id").value(testCustomer2.getId()));
        expectCss2.andExpect(jsonPath("$[0].done_at").doesNotExist());

        // change customerServiceStep1 to DE and then searching by FE should return no results
        testServiceStep1.setCountryCode("DE");
        customerServiceStepRepository.save(testCustomerServiceStep1);
        ResultActions expectNoResults = mockMvc.perform(get(Api.CUSTOMER_SERVICE_STEP).param(
                                "customerId",
                                testCustomer1.getId()
                                        .toString())
                                                                .param("type", ServiceStep.Type.LICENSE.name())
                                                                .param("countryCode", "FR")
                                                                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        expectNoResults.andExpect(jsonPath("length()").value(0));
    }

    /**
     * {@link CustomerServiceStepController#toggle(ToggleCustomerServiceStepDto)}
     * If an existing {@link CustomerServiceStep} is toggled, it should be marked as done or undone.
     *
     * @throws Exception if the request fails
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void toggle_existing() throws Exception {
        // confirm that customerServiceStep1 is marked as done
        assertEquals(testInstant, testCustomerServiceStep1.getDoneAt());

        // Create a DTO for toggling the service step
        ToggleCustomerServiceStepDto toggleDto = new ToggleCustomerServiceStepDto();
        toggleDto.setCustomerId(testCustomer1.getId());
        toggleDto.setServiceStepId(testServiceStep1.getId());

        // Perform the toggle request
        ResultActions togglingOffResult = mockMvc.perform(put(Api.CUSTOMER_SERVICE_STEP).contentType(MediaType.APPLICATION_JSON)
                                                                  .content(objectMapper.writeValueAsString(toggleDto)))
                .andExpect(status().isOk());

        // check that the proper CustomerServiceStep is returned and that doneAt is null
        togglingOffResult.andExpect(jsonPath("$.id").value(testCustomerServiceStep1.getId()));
        togglingOffResult.andExpect(jsonPath("$.customer_id").value(testCustomer1.getId()));
        togglingOffResult.andExpect(jsonPath("$.service_step_id").value(testServiceStep1.getId()));
        togglingOffResult.andExpect(jsonPath("$.done_at").doesNotExist());

        // try and toggle it back on
        ResultActions togglingOnResult = mockMvc.perform(put(Api.CUSTOMER_SERVICE_STEP).contentType(MediaType.APPLICATION_JSON)
                                                                 .content(objectMapper.writeValueAsString(toggleDto)))
                .andExpect(status().isOk());

        // check that the proper CustomerServiceStep is returned and that doneAt is set
        togglingOnResult.andExpect(jsonPath("$.id").value(testCustomerServiceStep1.getId()));
        togglingOnResult.andExpect(jsonPath("$.customer_id").value(testCustomer1.getId()));
        togglingOnResult.andExpect(jsonPath("$.service_step_id").value(testServiceStep1.getId()));
        togglingOnResult.andExpect(jsonPath("$.done_at").exists());
    }

    /**
     * {@link CustomerServiceStepController#toggle(ToggleCustomerServiceStepDto)}
     * If a non-existing {@link CustomerServiceStep} is toggled, it should be created and marked as done.
     *
     * @throws Exception if the request fails
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void toggle_nonExisting() throws Exception {
        // remove all customer service steps and confirm there are none
        customerServiceStepRepository.deleteAll();
        assertEquals(0, customerServiceStepRepository.count());

        // Create a DTO for toggling a non-existing service step
        ToggleCustomerServiceStepDto toggleDto = new ToggleCustomerServiceStepDto();
        toggleDto.setCustomerId(testCustomer1.getId());
        toggleDto.setServiceStepId(testServiceStep1.getId());

        // Perform the toggle request
        ResultActions result = mockMvc.perform(put(Api.CUSTOMER_SERVICE_STEP).contentType(MediaType.APPLICATION_JSON)
                                                       .content(objectMapper.writeValueAsString(toggleDto)))
                .andExpect(status().isOk());

        // check that the proper CustomerServiceStep is returned and that doneAt is set
        result.andExpect(jsonPath("$.id").exists());
        result.andExpect(jsonPath("$.customer_id").value(testCustomer1.getId()));
        result.andExpect(jsonPath("$.service_step_id").value(testServiceStep1.getId()));
        result.andExpect(jsonPath("$.done_at").exists());
        assertEquals(1, customerServiceStepRepository.count());
    }
}
