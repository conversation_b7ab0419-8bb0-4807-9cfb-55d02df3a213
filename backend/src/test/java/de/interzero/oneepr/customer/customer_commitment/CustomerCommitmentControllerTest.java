package de.interzero.oneepr.customer.customer_commitment;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.TestRole;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import de.interzero.oneepr.customer.customer_commitment.dto.*;
import de.interzero.oneepr.customer.http.AdminInterface;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Integration tests for the CustomerCommitmentController, focusing on happy path scenarios.
 * This class tests the successful request-response cycle for customer commitment endpoints.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class CustomerCommitmentControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private CustomerCommitmentRepository customerCommitmentRepository;

    @Autowired
    private ObjectMapper objectMapper;

    private Customer testCustomer;

    private CustomerCommitment testCommitment;

    @BeforeEach
    void setUp() {
        customerCommitmentRepository.deleteAll();
        customerRepository.deleteAll();

        testCustomer = new Customer();
        testCustomer.setUserId(101);
        testCustomer.setEmail("<EMAIL>");
        testCustomer.setFirstName("Test");
        testCustomer.setLastName("User");
        testCustomer.setCreatedAt(Instant.now());
        testCustomer.setUpdatedAt(Instant.now());
        testCustomer.setType(Customer.Type.REGULAR);
        testCustomer.setCustomerCommitment(new ArrayList<>());
        testCustomer = customerRepository.save(testCustomer);

        testCommitment = new CustomerCommitment();
        testCommitment.setCustomerEmail(testCustomer.getEmail());
        testCommitment.setCustomer(testCustomer);
        testCustomer.getCustomerCommitment().add(testCommitment);

        testCommitment.setCountryCode("DE");
        testCommitment.setYear(2025);
        testCommitment.setCommitment(new HashMap<>(Map.of("initial_key", "initial_value")));
        testCommitment.setServiceSetup(new HashMap<>(Map.of("initial_tier", "basic")));
        testCommitment.setIsLicenseRequired(true);
        testCommitment.setBlame(new HashMap<>(Map.of("user", "setup")));
        testCommitment.setCreatedAt(Instant.now());
        testCommitment.setUpdatedAt(Instant.now());

        testCommitment = customerCommitmentRepository.save(testCommitment);
    }

    /**
     * Test for {@link CustomerCommitmentController#create(CreateCustomerCommitmentDto)}.
     * This test mocks the static AdminInterface.admin() call to prevent it from throwing
     * its placeholder exception.
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = "CUSTOMER"
    )
    void create_whenAuthenticated_shouldCreateCommitment() throws Exception {
        CreateCustomerCommitmentDto createDto = getCreateCustomerCommitmentDto();

        CommitmentSubmissionResponse mockResponse = new CommitmentSubmissionResponse();
        mockResponse.setYear(createDto.getYear());

        AnsweredCommitment answeredItem = new AnsweredCommitment();
        answeredItem.setId(1);
        answeredItem.setAnswer("PROCESSED");
        mockResponse.setCommitment(List.of(answeredItem));

        CustomerServiceSetup mockSetup = new CustomerServiceSetup();
        CustomerServiceSetup.PackagingServiceExtended obligedService = new CustomerServiceSetup.PackagingServiceExtended();
        obligedService.setObliged(true);
        mockSetup.setPackagingServices(List.of(obligedService));
        mockResponse.setSetup(mockSetup);


        try (MockedStatic<AdminInterface> mockedAdminInterface = Mockito.mockStatic(AdminInterface.class)) {
            mockedAdminInterface.when(() -> AdminInterface.admin(
                    any(String.class), any(),
                    any(HttpMethod.class))).thenReturn(mockResponse);

            mockMvc.perform(post(Api.CUSTOMER_COMMITMENTS).contentType(MediaType.APPLICATION_JSON)
                                    .content(objectMapper.writeValueAsString(createDto)))
                    .andExpect(status().isCreated())
                    .andExpect(jsonPath("$.id", is(notNullValue())))
                    .andExpect(jsonPath("$.customer_email", is("<EMAIL>")))
                    .andExpect(jsonPath("$.country_code", is("FR")));
        }
    }

    /**
     * Helper method to generate a pre-populated and valid {@link CreateCustomerCommitmentDto}.
     * <p>
     * This factory method centralizes the creation of a standard DTO used across multiple tests,
     * ensuring consistency and reducing code duplication.
     *
     * @return A non-null instance of {@code CreateCustomerCommitmentDto} with standard test values.
     */
    private static @NotNull CreateCustomerCommitmentDto getCreateCustomerCommitmentDto() {
        CreateCustomerCommitmentDto.CommitmentAnswerDto commitmentAnswer = new CreateCustomerCommitmentDto.CommitmentAnswerDto();
        commitmentAnswer.setCriteriaId(1);
        commitmentAnswer.setAnswer("OBLIGED");
        commitmentAnswer.setToAnswer("ADDITIONAL_INFO");

        CreateCustomerCommitmentDto createDto = new CreateCustomerCommitmentDto();
        createDto.setCustomerEmail("<EMAIL>");
        createDto.setCountryCode("FR");
        createDto.setYear(2025);
        createDto.setCommitmentAnswers(List.of(commitmentAnswer));
        return createDto;
    }

    /**
     * Test for {@link CustomerCommitmentController#findAll(FindCustomerCommitmentDto)}
     * when an optional countryCode is also provided for filtering.
     */
    @Test
    @WithMockUser
    void findAll_withCountryCode_shouldReturnFilteredCommitments() throws Exception {
        mockMvc.perform(get(Api.CUSTOMER_COMMITMENTS).param("customerEmail", "<EMAIL>")
                                .param("year", "2025")
                                .param("countryCode", "DE"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].id", is(testCommitment.getId())))
                .andExpect(jsonPath("$[0].country_code", is("DE")));
    }

    /**
     * Test for {@link CustomerCommitmentController#findOne(Integer)}
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = "CUSTOMER"
    )
    void findOne_whenAuthenticatedAndAuthorized_shouldReturnCommitment() throws Exception {
        mockMvc.perform(get(Api.CUSTOMER_COMMITMENTS + "/" + testCommitment.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testCommitment.getId())))
                .andExpect(jsonPath("$.customer_email", is(testCustomer.getEmail())));
    }

    /**
     * Test for {@link CustomerCommitmentController#update(Integer, UpdateCustomerCommitmentDto)}
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = "CUSTOMER"
    )
    void update_whenAuthenticatedAndAuthorized_shouldUpdateCommitment() throws Exception {
        UpdateCustomerCommitmentDto updateDto = new UpdateCustomerCommitmentDto();
        updateDto.setCommitment(Map.of("updated_key", "updated_value"));
        updateDto.setServiceSetup(Map.of("updated_tier", "premium"));

        mockMvc.perform(put(Api.CUSTOMER_COMMITMENTS + "/" + testCommitment.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.commitment.updated_key", is("updated_value")))
                .andExpect(jsonPath("$.service_setup.updated_tier", is("premium")));
    }

    /**
     * Test for {@link CustomerCommitmentController#remove(Integer)}
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = TestRole.ADMIN
    )
    void remove_whenUserIsAdmin_shouldSoftDeleteCommitment() throws Exception {
        mockMvc.perform(delete(Api.CUSTOMER_COMMITMENTS + "/" + testCommitment.getId()))
                .andExpect(status().isNoContent());

        CustomerCommitment deletedCommitment = customerCommitmentRepository.findById(testCommitment.getId())
                .orElseThrow();
        assertNotNull(deletedCommitment.getDeletedAt());
    }
}
