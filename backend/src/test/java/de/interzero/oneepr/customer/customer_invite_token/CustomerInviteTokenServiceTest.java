package de.interzero.oneepr.customer.customer_invite_token;

import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import de.interzero.oneepr.customer.http.PaymentsInterface;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.ArgumentMatchers;
import org.mockito.Captor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpMethod;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Integration tests for {@link CustomerInviteTokenService}.
 * These tests verify the service logic including database interactions
 * and interactions with the mocked {@link PaymentsInterface}.
 */
@SpringBootTest
@Transactional
class CustomerInviteTokenServiceTest {

    @Autowired
    private CustomerInviteTokenService customerInviteTokenService;

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private CustomerInviteTokenRepository customerInviteTokenRepository;

    @MockBean
    private PaymentsInterface paymentsInterface;

    @Captor
    private ArgumentCaptor<Map<String, Object>> paramsCaptor;

    private Customer testCustomer;

    @BeforeEach
    void setUp() {
        customerInviteTokenRepository.deleteAllInBatch();
        customerRepository.deleteAllInBatch();

        Customer customer = new Customer();
        customer.setFirstName("Test");
        customer.setLastName("User");
        customer.setEmail("testuser.invite." + System.currentTimeMillis() + "@example.com");
        customer.setUserId(12345);
        customer.setCreatedAt(Instant.now());
        customer.setUpdatedAt(Instant.now());
        customer.setType(Customer.Type.REGULAR);
        testCustomer = customerRepository.saveAndFlush(customer);
    }

    /**
     * Tests successful creation of a customer invite token and ensures
     * the call to the PaymentsInterface is made with correct parameters.
     */
    @Test
    void create_shouldCreateTokenAndCallPaymentsInterface() {
        Integer customerId = testCustomer.getId();
        String expectedTokenCode = "LIZ-" + customerId;
        LocalDate expectedExpirationDate = LocalDate.ofInstant(Instant.now(), ZoneOffset.UTC).plusYears(1);

        when(paymentsInterface.payments(eq("/coupon"), ArgumentMatchers.any(), eq(HttpMethod.POST))).thenReturn(null);
        CustomerInviteToken resultToken = customerInviteTokenService.create(customerId);

        assertNotNull(resultToken);
        assertNotNull(resultToken.getId());
        assertEquals(customerId, resultToken.getCustomer().getId());
        assertEquals(expectedTokenCode, resultToken.getToken());
        assertEquals("/shop-invite/" + customerId, resultToken.getShareLink());
        assertNotNull(resultToken.getExpirationDate());
        assertNotNull(resultToken.getCreatedAt());
        assertNotNull(resultToken.getUpdatedAt());

        assertEquals(expectedExpirationDate, resultToken.getExpirationDate());

        Optional<CustomerInviteToken> savedInDb = customerInviteTokenRepository.findById(resultToken.getId());
        assertTrue(savedInDb.isPresent());
        assertEquals(resultToken.getToken(), savedInDb.get().getToken());
        assertEquals(testCustomer.getId(), savedInDb.get().getCustomer().getId());

        verify(paymentsInterface, times(1)).payments(eq("/coupon"), paramsCaptor.capture(), eq(HttpMethod.POST));
        Map<String, Object> capturedParams = paramsCaptor.getValue();
        assertEquals(expectedTokenCode, capturedParams.get("code"));
        assertEquals(360, capturedParams.get("discount"));
        assertEquals(expectedExpirationDate.toString(), capturedParams.get("expires_at").toString());
        assertEquals(100, capturedParams.get("max_uses"));
        assertEquals("AMOUNT", capturedParams.get("discount_type"));
        assertEquals(customerId, capturedParams.get("customer_creator_id"));
        assertEquals("CUSTOMER", capturedParams.get("type_creation"));
    }
}