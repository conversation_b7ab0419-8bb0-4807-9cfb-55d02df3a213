package de.interzero.oneepr.customer.license_volume_report_item;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.contract.ContractRepository;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import de.interzero.oneepr.customer.customer_io.CustomerIoService;
import de.interzero.oneepr.customer.entity.LicenseVolumeReportItem;
import de.interzero.oneepr.customer.license.License;
import de.interzero.oneepr.customer.license.LicenseRepository;
import de.interzero.oneepr.customer.license_packaging_service.LicensePackagingService;
import de.interzero.oneepr.customer.license_volume_report.LicenseVolumeReport;
import de.interzero.oneepr.customer.license_volume_report.LicenseVolumeReportRepository;
import de.interzero.oneepr.customer.license_volume_report_item.dto.CreateLicenseVolumeReportItemDto;
import de.interzero.oneepr.customer.license_volume_report_item.dto.DeclineLicenseVolumeReportItemDto;
import de.interzero.oneepr.customer.license_volume_report_item.dto.UpdateLicenseVolumeReportItemDto;
import de.interzero.oneepr.customer.license_volume_report_item.dto.UpdateLicenseVolumeReportItemValueDto;
import de.interzero.oneepr.customer.reason.Reason;
import de.interzero.oneepr.customer.reason.ReasonRepository;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import de.interzero.oneepr.customer.shared.auth.Role;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.github.tomakehurst.wiremock.client.WireMock.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Comprehensive integration tests for LicenseVolumeReportItemController with 100% coverage.
 * Uses WireMock for external service mocking. Uses real service and repository instances.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class LicenseVolumeReportItemControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private LicenseVolumeReportItemRepository licenseVolumeReportItemRepository;

    @Autowired
    private LicenseVolumeReportRepository licenseVolumeReportRepository;

    @Autowired
    private ReasonRepository reasonRepository;

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private LicenseRepository licenseRepository;



    private WireMockServer wireMockServer;
    private MockedStatic<de.interzero.oneepr.common.AuthUtil> authUtilMock;

    // Test data constants
    private static final Integer TEST_CUSTOMER_ID = 1;
    private static final Integer TEST_CONTRACT_ID = 1;
    private static final Integer TEST_LICENSE_ID = 1;
    private static final Integer TEST_PACKAGING_SERVICE_ID = 1;
    private static final Integer TEST_LICENSE_VOLUME_REPORT_ID = 1;
    private static final Integer TEST_ITEM_ID = 1;
    private static final String TEST_USER_ID = "1";
    private static final String TEST_USER_EMAIL = "<EMAIL>";
    private static final Role TEST_USER_ROLE = Role.CUSTOMER;

    // Test entities
    private Customer testCustomer;
    private Contract testContract;
    private License testLicense;
    private LicensePackagingService testPackagingService;
    private LicenseVolumeReport testVolumeReport;
    private LicenseVolumeReportItem testItem;
    private Reason testReason;

    @BeforeEach
    void setUp() {
        // Setup WireMock server for external HTTP calls
        wireMockServer = new WireMockServer(8089);
        wireMockServer.start();
        WireMock.configureFor("localhost", 8089);

        // Setup WireMock stubs for external services
        setupWireMockStubs();

        // Mock AuthUtil static method
        authUtilMock = Mockito.mockStatic(de.interzero.oneepr.common.AuthUtil.class);
        AuthenticatedUser mockUser = new AuthenticatedUser(TEST_USER_ID, TEST_USER_ROLE, TEST_USER_EMAIL);
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails).thenReturn(mockUser);

        // Create test data in database
        createTestData();
    }

    @AfterEach
    void tearDown() {
        if (wireMockServer != null) {
            wireMockServer.stop();
        }
        if (authUtilMock != null) {
            authUtilMock.close();
        }
    }

    private void setupWireMockStubs() {
        // Stub for CustomerIO integration
        wireMockServer.stubFor(post(urlMatching("/integrations/customer-io/.*"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("Content-Type", "application/json")
                        .withBody("{\"status\":\"success\"}")));

        // Stub for email sending
        wireMockServer.stubFor(post(urlEqualTo("/emails/send-message"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("Content-Type", "application/json")
                        .withBody("{\"messageId\":\"test-message-id\"}")));

        // Stub for external admin API calls
        wireMockServer.stubFor(post(urlMatching("/admin/.*"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("Content-Type", "application/json")
                        .withBody("{\"result\":\"success\"}")));
    }

    private void createTestData() {
        // Create Customer
        testCustomer = new Customer();
        testCustomer.setId(TEST_CUSTOMER_ID);
        testCustomer.setUserId(TEST_CUSTOMER_ID);
        testCustomer.setEmail(TEST_USER_EMAIL);
        testCustomer.setCreatedAt(Instant.now());
        testCustomer.setUpdatedAt(Instant.now());
        testCustomer = customerRepository.save(testCustomer);

        // Create Contract
        testContract = new Contract();
        testContract.setId(TEST_CONTRACT_ID);
        testContract.setCustomer(testCustomer);
        testContract.setType(Contract.Type.EU_LICENSE);
        testContract.setStatus(Contract.Status.ACTIVE);
        testContract.setCreatedAt(Instant.now());
        testContract.setUpdatedAt(Instant.now());
        testContract = contractRepository.save(testContract);

        // Create License
        testLicense = new License();
        testLicense.setId(TEST_LICENSE_ID);
        testLicense.setContract(testContract);
        testLicense.setCountryName("Germany");
        testLicense.setYear(2024);
        testLicense.setCreatedAt(Instant.now());
        testLicense.setUpdatedAt(Instant.now());
        testLicense = licenseRepository.save(testLicense);

        // Create LicenseVolumeReport
        testVolumeReport = new LicenseVolumeReport();
        testVolumeReport.setId(TEST_LICENSE_VOLUME_REPORT_ID);
        testVolumeReport.setPackagingService(testPackagingService);
        testVolumeReport.setYear(2024);
        testVolumeReport.setInterval("Q1");
        testVolumeReport.setStatus(LicenseVolumeReport.Status.OPEN);

        Map<String, Object> reportTable = new HashMap<>();
        reportTable.put("data", "test");
        testVolumeReport.setReportTable(reportTable);
        testVolumeReport.setCreatedAt(Instant.now());
        testVolumeReport.setUpdatedAt(Instant.now());
        testVolumeReport = licenseVolumeReportRepository.save(testVolumeReport);

        // Create test Reason
        testReason = new Reason();
        testReason.setTitle("Test Decline Reason");
        testReason.setType(Reason.Type.TERMINATION);
        testReason.setCreatedAt(Instant.now());
        testReason.setUpdatedAt(Instant.now());
        testReason = reasonRepository.save(testReason);
    }

    /**
     * Test create method - POST /license-volume-report-item
     * Integration test with real service and repositories
     */
    @Test
    void create_ShouldReturnCreatedItem_WhenValidDto() throws Exception {
        // Given
        CreateLicenseVolumeReportItemDto createDto = createTestCreateDto();

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post("/license-volume-report-item")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.setupFractionId").value(1))
                .andExpect(jsonPath("$.setupColumnId").value(2))
                .andExpect(jsonPath("$.value").value(100))
                .andExpect(jsonPath("$.setupFractionCode").value("DEOK34"))
                .andExpect(jsonPath("$.setupColumnCode").value("COL01"))
                .andExpect(jsonPath("$.createdAt").exists())
                .andExpect(jsonPath("$.updatedAt").exists());

        // Verify WireMock was called for external services
        wireMockServer.verify(postRequestedFor(urlMatching("/integrations/customer-io/.*")));
    }

    /**
     * Test create method with invalid JSON
     */
    @Test
    void create_ShouldReturnBadRequest_WhenInvalidJson() throws Exception {
        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post("/license-volume-report-item")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("\"invalid json\""))
                .andExpect(status().isBadRequest());
    }

    /**
     * Test create method with missing content type
     */
    @Test
    void create_ShouldReturnUnsupportedMediaType_WhenMissingContentType() throws Exception {
        // Given
        CreateLicenseVolumeReportItemDto createDto = createTestCreateDto();

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post("/license-volume-report-item")
                        .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isUnsupportedMediaType());
    }

    /**
     * Test create method with non-existent license volume report
     */
    @Test
    void create_ShouldReturnNotFound_WhenLicenseVolumeReportNotExists() throws Exception {
        // Given
        CreateLicenseVolumeReportItemDto createDto = createTestCreateDto();
        createDto.setLicenseVolumeReportId(999); // Non-existent ID

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post("/license-volume-report-item")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.message").value("License Volume Report not found"));
    }

    /**
     * Test create method with null DTO fields
     */
    @Test
    void create_ShouldReturnBadRequest_WhenDtoFieldsAreNull() throws Exception {
        // Given
        CreateLicenseVolumeReportItemDto createDto = new CreateLicenseVolumeReportItemDto();
        // Leave fields null

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post("/license-volume-report-item")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isBadRequest());
    }

    /**
     * Test findAll method - GET /license-volume-report-item
     * Integration test with real service
     */
    @Test
    void findAll_ShouldReturnList_WhenValidRequest() throws Exception {
        // First create a test item
        testItem = createAndSaveTestItem();

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.get("/license-volume-report-item")
                        .param("license_volume_report_id", TEST_LICENSE_VOLUME_REPORT_ID.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].setupFractionId").value(1))
                .andExpect(jsonPath("$[0].setupColumnId").value(2))
                .andExpect(jsonPath("$[0].value").value(100));
    }

    /**
     * Test findAll method without query parameter
     */
    @Test
    void findAll_ShouldReturnList_WhenNoQueryParameter() throws Exception {
        // First create a test item
        testItem = createAndSaveTestItem();

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.get("/license-volume-report-item"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.length()").value(1));
    }

    /**
     * Test findAll method with non-existent license volume report
     */
    @Test
    void findAll_ShouldReturnNotFound_WhenLicenseVolumeReportNotExists() throws Exception {
        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.get("/license-volume-report-item")
                        .param("license_volume_report_id", "999"))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.message").value("License Volume Report Item not found"));
    }

    /**
     * Test findAll method with customer role accessing other customer's data
     */
    @Test
    void findAll_ShouldReturnForbidden_WhenCustomerAccessesOtherCustomerData() throws Exception {
        // Given - Mock different user
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails)
                .thenReturn(new AuthenticatedUser("999", Role.CUSTOMER, "<EMAIL>"));

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.get("/license-volume-report-item")
                        .param("license_volume_report_id", TEST_LICENSE_VOLUME_REPORT_ID.toString()))
                .andExpect(status().isForbidden())
                .andExpect(jsonPath("$.message").value("You do not have permission to access this resource"));
    }

    /**
     * Test findAll method with admin role - should have access
     */
    @Test
    void findAll_ShouldReturnList_WhenAdminRole() throws Exception {
        // Given - Mock admin user
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails)
                .thenReturn(new AuthenticatedUser("999", Role.ADMIN, "<EMAIL>"));

        // First create a test item
        testItem = createAndSaveTestItem();

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.get("/license-volume-report-item")
                        .param("license_volume_report_id", TEST_LICENSE_VOLUME_REPORT_ID.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.length()").value(1));
    }

    /**
     * Test findOne method - GET /license-volume-report-item/{id}
     */
    @Test
    void findOne_ShouldReturnItem_WhenItemExists() throws Exception {
        // First create a test item
        testItem = createAndSaveTestItem();

        // When & Then
        mockMvc.perform(get("/license-volume-report-item/{id}", testItem.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testItem.getId()))
                .andExpect(jsonPath("$.setupFractionId").value(1))
                .andExpect(jsonPath("$.value").value(100));
    }

    /**
     * Test findOne method when item doesn't exist
     */
    @Test
    void findOne_ShouldReturnEmpty_WhenItemNotExists() throws Exception {
        // When & Then
        mockMvc.perform(get("/license-volume-report-item/{id}", 999))
                .andExpect(status().isOk())
                .andExpect(content().string(""));
    }

    /**
     * Test findOne method with invalid ID format
     */
    @Test
    void findOne_ShouldReturnBadRequest_WhenInvalidIdFormat() throws Exception {
        // When & Then
        mockMvc.perform(get("/license-volume-report-item/{id}", "invalid-id"))
                .andExpect(status().isBadRequest());
    }

    /**
     * Test findOne method with customer role accessing other customer's data
     */
    @Test
    void findOne_ShouldReturnForbidden_WhenCustomerAccessesOtherCustomerData() throws Exception {
        // First create a test item
        testItem = createAndSaveTestItem();

        // Given - Mock different user
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails)
                .thenReturn(new AuthenticatedUser("999", Role.CUSTOMER, "<EMAIL>"));

        // When & Then
        mockMvc.perform(get("/license-volume-report-item/{id}", testItem.getId()))
                .andExpect(status().isForbidden())
                .andExpect(jsonPath("$.message").value("You do not have permission to access this resource"));
    }

    /**
     * Test update method - PUT /license-volume-report-item/{id}
     */
    @Test
    void update_ShouldReturnUpdatedItem_WhenValidDto() throws Exception {
        // First create a test item
        testItem = createAndSaveTestItem();

        UpdateLicenseVolumeReportItemDto updateDto = createTestUpdateDto();

        // When & Then
        mockMvc.perform(put("/license-volume-report-item/{id}", testItem.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testItem.getId()))
                .andExpect(jsonPath("$.value").value(200));

        // Verify WireMock was called for external services
        wireMockServer.verify(postRequestedFor(urlMatching("/integrations/customer-io/.*")));
    }

    /**
     * Test update method with non-existent item
     */
    @Test
    void update_ShouldReturnNotFound_WhenItemNotExists() throws Exception {
        // Given
        UpdateLicenseVolumeReportItemDto updateDto = createTestUpdateDto();

        // When & Then
        mockMvc.perform(put("/license-volume-report-item/{id}", 999)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.message").value("License Volume Report Item not found"));
    }

    /**
     * Test update method with invalid JSON
     */
    @Test
    void update_ShouldReturnBadRequest_WhenInvalidJson() throws Exception {
        // First create a test item
        testItem = createAndSaveTestItem();

        // When & Then
        mockMvc.perform(put("/license-volume-report-item/{id}", testItem.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("\"invalid json\""))
                .andExpect(status().isBadRequest());
    }

    /**
     * Test update method with customer role accessing other customer's data
     */
    @Test
    void update_ShouldReturnForbidden_WhenCustomerAccessesOtherCustomerData() throws Exception {
        // First create a test item
        testItem = createAndSaveTestItem();

        // Given - Mock different user
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails)
                .thenReturn(new AuthenticatedUser("999", Role.CUSTOMER, "<EMAIL>"));

        UpdateLicenseVolumeReportItemDto updateDto = createTestUpdateDto();

        // When & Then
        mockMvc.perform(put("/license-volume-report-item/{id}", testItem.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isForbidden())
                .andExpect(jsonPath("$.message").value("You do not have permission to access this resource"));
    }

    /**
     * Test decline method - POST /license-volume-report-item/{id}/decline
     */
    @Test
    void decline_ShouldReturnDeclinedItem_WhenValidDto() throws Exception {
        // First create a test item
        testItem = createAndSaveTestItem();

        DeclineLicenseVolumeReportItemDto declineDto = createTestDeclineDto();

        // When & Then
        mockMvc.perform(post("/license-volume-report-item/{id}/decline", testItem.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(declineDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testItem.getId()));

        // Verify WireMock was called for external services
        wireMockServer.verify(postRequestedFor(urlMatching("/integrations/customer-io/.*")));
    }

    /**
     * Test decline method with non-existent item
     */
    @Test
    void decline_ShouldReturnNotFound_WhenItemNotExists() throws Exception {
        // Given
        DeclineLicenseVolumeReportItemDto declineDto = createTestDeclineDto();

        // When & Then
        mockMvc.perform(post("/license-volume-report-item/{id}/decline", 999)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(declineDto)))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.message").value("License Volume Report Item not found"));
    }

    /**
     * Test decline method with invalid JSON
     */
    @Test
    void decline_ShouldReturnBadRequest_WhenInvalidJson() throws Exception {
        // First create a test item
        testItem = createAndSaveTestItem();

        // When & Then
        mockMvc.perform(post("/license-volume-report-item/{id}/decline", testItem.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("\"invalid json\""))
                .andExpect(status().isBadRequest());
    }

    /**
     * Test decline method with large ID values
     */
    @Test
    void decline_ShouldWork_WithLargeId() throws Exception {
        // First create a test item
        testItem = createAndSaveTestItem();

        DeclineLicenseVolumeReportItemDto declineDto = createTestDeclineDto();

        // When & Then
        mockMvc.perform(post("/license-volume-report-item/{id}/decline", Integer.MAX_VALUE)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(declineDto)))
                .andExpect(status().isNotFound()); // Should not find item with MAX_VALUE ID
    }

    /**
     * Test remove method - DELETE /license-volume-report-item/{id}
     */
    @Test
    void remove_ShouldReturnRemovedItem_WhenValidId() throws Exception {
        // First create a test item
        testItem = createAndSaveTestItem();

        // When & Then
        mockMvc.perform(delete("/license-volume-report-item/{id}", testItem.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testItem.getId()))
                .andExpect(jsonPath("$.deletedAt").exists());

        // Verify WireMock was called for external services
        wireMockServer.verify(postRequestedFor(urlMatching("/integrations/customer-io/.*")));
    }

    /**
     * Test remove method with non-existent item
     */
    @Test
    void remove_ShouldReturnNotFound_WhenItemNotExists() throws Exception {
        // When & Then
        mockMvc.perform(delete("/license-volume-report-item/{id}", 999))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.message").value("License Volume Report Item not found"));
    }

    /**
     * Test remove method with negative ID
     */
    @Test
    void remove_ShouldWork_WithNegativeId() throws Exception {
        // When & Then
        mockMvc.perform(delete("/license-volume-report-item/{id}", -1))
                .andExpect(status().isNotFound()); // Should not find item with negative ID
    }

    /**
     * Test remove method with zero ID
     */
    @Test
    void remove_ShouldWork_WithZeroId() throws Exception {
        // When & Then
        mockMvc.perform(delete("/license-volume-report-item/{id}", 0))
                .andExpect(status().isNotFound()); // Should not find item with zero ID
    }

    /**
     * Test createBulk method - POST /license-volume-report-item/bulk
     */
    @Test
    void createBulk_ShouldCallService_WhenValidDtoList() throws Exception {
        // Given
        List<CreateLicenseVolumeReportItemDto> createDtoList = Arrays.asList(
                createTestCreateDto(),
                createTestCreateDto()
        );

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post("/license-volume-report-item/bulk")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createDtoList)))
                .andExpect(status().isOk());

        // Verify WireMock was called for external services
        wireMockServer.verify(postRequestedFor(urlMatching("/integrations/customer-io/.*")));
    }

    /**
     * Test createBulk method with empty list
     */
    @Test
    void createBulk_ShouldReturnBadRequest_WhenEmptyList() throws Exception {
        // Given
        List<CreateLicenseVolumeReportItemDto> emptyList = Arrays.asList();

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post("/license-volume-report-item/bulk")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(emptyList)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message").value("Items are required"));
    }

    /**
     * Test createBulk method with invalid JSON
     */
    @Test
    void createBulk_ShouldReturnBadRequest_WhenInvalidJson() throws Exception {
        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post("/license-volume-report-item/bulk")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("\"invalid json\""))
                .andExpect(status().isBadRequest());
    }

    /**
     * Test createBulk method with missing content type
     */
    @Test
    void createBulk_ShouldReturnUnsupportedMediaType_WhenMissingContentType() throws Exception {
        // Given
        List<CreateLicenseVolumeReportItemDto> createDtoList = Arrays.asList(createTestCreateDto());

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post("/license-volume-report-item/bulk")
                        .content(objectMapper.writeValueAsString(createDtoList)))
                .andExpect(status().isUnsupportedMediaType());
    }

    /**
     * Test updateBulkValues method - POST /license-volume-report-item/bulk-update
     */
    @Test
    void updateBulkValues_ShouldCallService_WhenValidDtoList() throws Exception {
        // First create a test item
        testItem = createAndSaveTestItem();

        List<UpdateLicenseVolumeReportItemValueDto> updateDtoList = Arrays.asList(
                createTestUpdateValueDto(testItem.getId())
        );

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post("/license-volume-report-item/bulk-update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateDtoList)))
                .andExpect(status().isOk());

        // Verify WireMock was called for external services
        wireMockServer.verify(postRequestedFor(urlMatching("/integrations/customer-io/.*")));
    }

    /**
     * Test updateBulkValues method with empty list
     */
    @Test
    void updateBulkValues_ShouldReturnBadRequest_WhenEmptyList() throws Exception {
        // Given
        List<UpdateLicenseVolumeReportItemValueDto> emptyList = Arrays.asList();

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post("/license-volume-report-item/bulk-update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(emptyList)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message").value("No items to update"));
    }

    /**
     * Test updateBulkValues method with invalid JSON
     */
    @Test
    void updateBulkValues_ShouldReturnBadRequest_WhenInvalidJson() throws Exception {
        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post("/license-volume-report-item/bulk-update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("\"invalid json\""))
                .andExpect(status().isBadRequest());
    }

    /**
     * Test updateBulkValues method with invalid value
     */
    @Test
    void updateBulkValues_ShouldReturnBadRequest_WhenInvalidValue() throws Exception {
        // First create a test item
        testItem = createAndSaveTestItem();

        UpdateLicenseVolumeReportItemValueDto dto = createTestUpdateValueDto(testItem.getId());
        dto.setValue(-1); // Invalid negative value
        List<UpdateLicenseVolumeReportItemValueDto> updateDtoList = Arrays.asList(dto);

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post("/license-volume-report-item/bulk-update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateDtoList)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message").value("Invalid value in some items"));
    }

    /**
     * Test updateBulkValues method with missing volume report item ID
     */
    @Test
    void updateBulkValues_ShouldReturnBadRequest_WhenMissingVolumeReportItemId() throws Exception {
        // Given
        UpdateLicenseVolumeReportItemValueDto dto = createTestUpdateValueDto(null);
        dto.setVolumeReportItemId(null); // Missing ID
        List<UpdateLicenseVolumeReportItemValueDto> updateDtoList = Arrays.asList(dto);

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post("/license-volume-report-item/bulk-update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateDtoList)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message").value("Missing volume_report_item_id in some items"));
    }

    /**
     * Test with different user roles - SUPER_ADMIN
     */
    @Test
    void findOne_ShouldWork_WithSuperAdminRole() throws Exception {
        // Given
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails)
                .thenReturn(new AuthenticatedUser("999", Role.SUPER_ADMIN, "<EMAIL>"));

        // First create a test item
        testItem = createAndSaveTestItem();

        // When & Then
        mockMvc.perform(get("/license-volume-report-item/{id}", testItem.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testItem.getId()));
    }

    /**
     * Test with different user roles - CLERK
     */
    @Test
    void update_ShouldWork_WithClerkRole() throws Exception {
        // Given
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails)
                .thenReturn(new AuthenticatedUser("999", Role.CLERK, "<EMAIL>"));

        // First create a test item
        testItem = createAndSaveTestItem();

        UpdateLicenseVolumeReportItemDto updateDto = createTestUpdateDto();

        // When & Then
        mockMvc.perform(put("/license-volume-report-item/{id}", testItem.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testItem.getId()));
    }

    /**
     * Test WireMock integration - External service integration
     */
    @Test
    void create_ShouldIntegrateWithExternalService_WhenUsingWireMock() throws Exception {
        // Given
        wireMockServer.stubFor(post(urlEqualTo("/external-service/license-volume-report-item"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("Content-Type", "application/json")
                        .withBody("{\"status\":\"success\"}")));

        CreateLicenseVolumeReportItemDto createDto = createTestCreateDto();

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post("/license-volume-report-item")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.setupFractionId").value(1));

        // Verify WireMock was called for CustomerIO integration
        wireMockServer.verify(postRequestedFor(urlMatching("/integrations/customer-io/.*")));
    }

    /**
     * Test error handling - Service exception
     */
    @Test
    void create_ShouldHandleServiceException_WhenServiceThrowsException() throws Exception {
        // Given - Create DTO with invalid license volume report ID that will cause service exception
        CreateLicenseVolumeReportItemDto createDto = createTestCreateDto();
        createDto.setLicenseVolumeReportId(999); // Non-existent ID

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post("/license-volume-report-item")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isNotFound());
    }

    /**
     * Test parameter validation - String to Integer conversion
     */
    @Test
    void findAll_ShouldWork_WithStringLicenseVolumeReportId() throws Exception {
        // First create a test item
        testItem = createAndSaveTestItem();

        // When & Then - Test string to integer conversion in controller
        mockMvc.perform(MockMvcRequestBuilders.get("/license-volume-report-item")
                        .param("license_volume_report_id", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.length()").value(1));
    }

    // Helper methods for creating test data
    private CreateLicenseVolumeReportItemDto createTestCreateDto() {
        CreateLicenseVolumeReportItemDto dto = new CreateLicenseVolumeReportItemDto();
        dto.setLicenseVolumeReportId(TEST_LICENSE_VOLUME_REPORT_ID);
        dto.setSetupFractionId(1);
        dto.setSetupColumnId(2);
        dto.setValue(100);
        dto.setSetupFractionCode("DEOK34");
        dto.setSetupColumnCode("COL01");
        return dto;
    }

    private UpdateLicenseVolumeReportItemDto createTestUpdateDto() {
        UpdateLicenseVolumeReportItemDto dto = new UpdateLicenseVolumeReportItemDto();
        dto.setLicenseVolumeReportId(TEST_LICENSE_VOLUME_REPORT_ID);
        dto.setValue(200);
        dto.setSetupFractionCode("UPDATED_CODE");
        return dto;
    }

    private DeclineLicenseVolumeReportItemDto createTestDeclineDto() {
        DeclineLicenseVolumeReportItemDto dto = new DeclineLicenseVolumeReportItemDto();
        dto.setReasonIds(Arrays.asList(testReason.getId()));
        dto.setTitle("Test decline reason");
        return dto;
    }

    private UpdateLicenseVolumeReportItemValueDto createTestUpdateValueDto(Integer itemId) {
        UpdateLicenseVolumeReportItemValueDto dto = new UpdateLicenseVolumeReportItemValueDto();
        dto.setLicenseVolumeReportId(TEST_LICENSE_VOLUME_REPORT_ID);
        dto.setVolumeReportItemId(itemId);
        dto.setValue(150);
        return dto;
    }

    private LicenseVolumeReportItem createAndSaveTestItem() {
        LicenseVolumeReportItem item = new LicenseVolumeReportItem();
        item.setLicenseVolumeReport(testVolumeReport);
        item.setSetupFractionId(1);
        item.setSetupColumnId(2);
        item.setValue(100);
        item.setPrice(50);
        item.setSetupFractionCode("DEOK34");
        item.setSetupColumnCode("COL01");
        item.setCreatedAt(Instant.now());
        item.setUpdatedAt(Instant.now());
        return licenseVolumeReportItemRepository.save(item);
    }
}
