package de.interzero.oneepr.customer.license_volume_report_item;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.contract.ContractRepository;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import de.interzero.oneepr.customer.customer_io.CustomerIoService;
import de.interzero.oneepr.customer.entity.LicenseVolumeReportItem;
import de.interzero.oneepr.customer.license.License;
import de.interzero.oneepr.customer.license.LicenseRepository;
import de.interzero.oneepr.customer.license_packaging_service.LicensePackagingService;
import de.interzero.oneepr.customer.license_packaging_service.LicensePackagingServiceRepository;
import de.interzero.oneepr.customer.license_volume_report.LicenseVolumeReport;
import de.interzero.oneepr.customer.license_volume_report.LicenseVolumeReportRepository;
import de.interzero.oneepr.customer.license_volume_report_item.dto.CreateLicenseVolumeReportItemDto;
import de.interzero.oneepr.customer.license_volume_report_item.dto.DeclineLicenseVolumeReportItemDto;
import de.interzero.oneepr.customer.license_volume_report_item.dto.UpdateLicenseVolumeReportItemDto;
import de.interzero.oneepr.customer.license_volume_report_item.dto.UpdateLicenseVolumeReportItemValueDto;
import de.interzero.oneepr.customer.reason.Reason;
import de.interzero.oneepr.customer.reason.ReasonRepository;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import de.interzero.oneepr.customer.shared.auth.Role;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.github.tomakehurst.wiremock.client.WireMock.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Comprehensive integration tests for LicenseVolumeReportItemController with 100% coverage.
 * Uses WireMock for external service mocking. Uses real service and repository instances.
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
class LicenseVolumeReportItemControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    // Real service and repository instances (not mocked)
    @Autowired
    private LicenseVolumeReportItemService licenseVolumeReportItemService;

    @Autowired
    private LicenseVolumeReportItemRepository licenseVolumeReportItemRepository;

    @Autowired
    private LicenseVolumeReportRepository licenseVolumeReportRepository;

    @Autowired
    private ReasonRepository reasonRepository;

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private LicenseRepository licenseRepository;

    @Autowired
    private LicensePackagingServiceRepository licensePackagingServiceRepository;

    // Mock only external services
    @MockBean
    private CustomerIoService customerIoService;

    private WireMockServer wireMockServer;
    private MockedStatic<de.interzero.oneepr.common.AuthUtil> authUtilMock;

    // Test data constants
    private static final Integer TEST_CUSTOMER_ID = 1;
    private static final Integer TEST_CONTRACT_ID = 1;
    private static final Integer TEST_LICENSE_ID = 1;
    private static final Integer TEST_PACKAGING_SERVICE_ID = 1;
    private static final Integer TEST_LICENSE_VOLUME_REPORT_ID = 1;
    private static final Integer TEST_ITEM_ID = 1;
    private static final String TEST_USER_ID = "1";
    private static final String TEST_USER_EMAIL = "<EMAIL>";
    private static final Role TEST_USER_ROLE = Role.CUSTOMER;

    // Test entities
    private Customer testCustomer;
    private Contract testContract;
    private License testLicense;
    private LicensePackagingService testPackagingService;
    private LicenseVolumeReport testVolumeReport;
    private LicenseVolumeReportItem testItem;
    private Reason testReason;

    @BeforeEach
    void setUp() {
        // Setup WireMock server for external HTTP calls
        wireMockServer = new WireMockServer(8089);
        wireMockServer.start();
        WireMock.configureFor("localhost", 8089);

        // Setup WireMock stubs for external services
        setupWireMockStubs();

        // Mock AuthUtil static method
        authUtilMock = Mockito.mockStatic(de.interzero.oneepr.common.AuthUtil.class);
        AuthenticatedUser mockUser = new AuthenticatedUser(TEST_USER_ID, TEST_USER_ROLE, TEST_USER_EMAIL);
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails).thenReturn(mockUser);

        // Create test data in database
        createTestData();
    }

    private void setupWireMockStubs() {
        // Stub for CustomerIO integration
        wireMockServer.stubFor(post(urlMatching("/integrations/customer-io/.*"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("Content-Type", "application/json")
                        .withBody("{\"status\":\"success\"}")));

        // Stub for email sending
        wireMockServer.stubFor(post(urlEqualTo("/emails/send-message"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("Content-Type", "application/json")
                        .withBody("{\"messageId\":\"test-message-id\"}")));

        // Stub for external admin API calls
        wireMockServer.stubFor(post(urlMatching("/admin/.*"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("Content-Type", "application/json")
                        .withBody("{\"result\":\"success\"}")));
    }

    private void createTestData() {
        // Create Customer
        testCustomer = new Customer();
        testCustomer.setId(TEST_CUSTOMER_ID);
        testCustomer.setUserId(TEST_CUSTOMER_ID);
        testCustomer.setEmail(TEST_USER_EMAIL);
        testCustomer.setCreatedAt(Instant.now());
        testCustomer.setUpdatedAt(Instant.now());
        testCustomer = customerRepository.save(testCustomer);

        // Create Contract
        testContract = new Contract();
        testContract.setId(TEST_CONTRACT_ID);
        testContract.setCustomer(testCustomer);
        testContract.setType("EU_LICENSE");
        testContract.setStatus("ACTIVE");
        testContract.setCreatedAt(Instant.now());
        testContract.setUpdatedAt(Instant.now());
        testContract = contractRepository.save(testContract);

        // Create License
        testLicense = new License();
        testLicense.setId(TEST_LICENSE_ID);
        testLicense.setContract(testContract);
        testLicense.setCountryName("Germany");
        testLicense.setYear(2024);
        testLicense.setCreatedAt(Instant.now());
        testLicense.setUpdatedAt(Instant.now());
        testLicense = licenseRepository.save(testLicense);

        // Create LicensePackagingService
        testPackagingService = new LicensePackagingService();
        testPackagingService.setId(TEST_PACKAGING_SERVICE_ID);
        testPackagingService.setLicense(testLicense);
        testPackagingService.setCreatedAt(Instant.now());
        testPackagingService.setUpdatedAt(Instant.now());
        testPackagingService = licensePackagingServiceRepository.save(testPackagingService);

        // Create LicenseVolumeReport
        testVolumeReport = new LicenseVolumeReport();
        testVolumeReport.setId(TEST_LICENSE_VOLUME_REPORT_ID);
        testVolumeReport.setPackagingService(testPackagingService);
        testVolumeReport.setYear(2024);
        testVolumeReport.setInterval("Q1");
        testVolumeReport.setStatus(LicenseVolumeReport.Status.OPEN);

        Map<String, Object> reportTable = new HashMap<>();
        reportTable.put("data", "test");
        testVolumeReport.setReportTable(reportTable);
        testVolumeReport.setCreatedAt(Instant.now());
        testVolumeReport.setUpdatedAt(Instant.now());
        testVolumeReport = licenseVolumeReportRepository.save(testVolumeReport);

        // Create test Reason
        testReason = new Reason();
        testReason.setName("Test Decline Reason");
        testReason.setType(Reason.Type.DECLINE);
        testReason.setCreatedAt(Instant.now());
        testReason.setUpdatedAt(Instant.now());
        testReason = reasonRepository.save(testReason);
    }

    @AfterEach
    void tearDown() {
        if (wireMockServer != null) {
            wireMockServer.stop();
        }
        if (authUtilMock != null) {
            authUtilMock.close();
        }
    }

    private void setupWireMockStubs() {
        // Stub for CustomerIO service calls
        wireMockServer.stubFor(post(urlEqualTo("/integrations/customer-io/process-purchase"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("Content-Type", "application/json")
                        .withBody("{\"status\":\"success\"}")));

        // Stub for email service calls
        wireMockServer.stubFor(post(urlEqualTo("/emails/send-message"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("Content-Type", "application/json")
                        .withBody("{\"status\":\"email_sent\"}")));

        // Stub for admin API calls
        wireMockServer.stubFor(post(urlMatching("/admin/.*"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("Content-Type", "application/json")
                        .withBody("{\"status\":\"admin_success\"}")));
    }

    /**
     * Test create method - POST /license-volume-report-item
     * Tests successful creation of a license volume report item
     */
    @Test
    void create_ShouldReturnCreatedItem_WhenValidDto() throws Exception {
        // Given
        CreateLicenseVolumeReportItemDto createDto = createTestCreateDto();
        LicenseVolumeReportItem expectedItem = createTestLicenseVolumeReportItem();

        when(licenseVolumeReportItemService.create(any(CreateLicenseVolumeReportItemDto.class)))
                .thenReturn(expectedItem);

        // When & Then
        mockMvc.perform(post("/license-volume-report-item")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(TEST_ID))
                .andExpect(jsonPath("$.setupFractionId").value(1))
                .andExpect(jsonPath("$.setupColumnId").value(2))
                .andExpect(jsonPath("$.value").value(100))
                .andExpect(jsonPath("$.setupFractionCode").value("DEOK34"))
                .andExpect(jsonPath("$.setupColumnCode").value("COL01"));

        // Verify service was called
        verify(licenseVolumeReportItemService).create(any(CreateLicenseVolumeReportItemDto.class));
    }

    /**
     * Test create method with invalid JSON
     */
    @Test
    void create_ShouldReturnBadRequest_WhenInvalidJson() throws Exception {
        // When & Then
        mockMvc.perform(post("/license-volume-report-item")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("invalid json"))
                .andExpect(status().isBadRequest());

        // Verify service was not called
        verify(licenseVolumeReportItemService, never()).create(any());
    }

    /**
     * Test create method with missing content type
     */
    @Test
    void create_ShouldReturnUnsupportedMediaType_WhenMissingContentType() throws Exception {
        // Given
        CreateLicenseVolumeReportItemDto createDto = createTestCreateDto();

        // When & Then
        mockMvc.perform(post("/license-volume-report-item")
                        .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isUnsupportedMediaType());

        // Verify service was not called
        verify(licenseVolumeReportItemService, never()).create(any());
    }

    /**
     * Test create method when service throws exception
     */
    @Test
    void create_ShouldReturnNotFound_WhenServiceThrowsNotFoundException() throws Exception {
        // Given
        CreateLicenseVolumeReportItemDto createDto = createTestCreateDto();
        when(licenseVolumeReportItemService.create(any(CreateLicenseVolumeReportItemDto.class)))
                .thenThrow(new ResponseStatusException(org.springframework.http.HttpStatus.NOT_FOUND, "License Volume Report not found"));

        // When & Then
        mockMvc.perform(post("/license-volume-report-item")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isNotFound());
    }

    /**
     * Test findAll method - GET /license-volume-report-item
     * Tests successful retrieval with query parameter
     */
    @Test
    void findAll_ShouldReturnList_WhenValidRequest() throws Exception {
        // Given
        List<LicenseVolumeReportItem> expectedItems = Arrays.asList(
                createTestLicenseVolumeReportItem(),
                createTestLicenseVolumeReportItem()
        );

        when(licenseVolumeReportItemService.findAll(eq(TEST_LICENSE_VOLUME_REPORT_ID), any(AuthenticatedUser.class)))
                .thenReturn(expectedItems);

        // When & Then
        mockMvc.perform(get("/license-volume-report-item")
                        .param("license_volume_report_id", TEST_LICENSE_VOLUME_REPORT_ID.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.length()").value(2))
                .andExpect(jsonPath("$[0].id").value(TEST_ID))
                .andExpect(jsonPath("$[1].id").value(TEST_ID));

        // Verify service was called with correct parameters
        verify(licenseVolumeReportItemService).findAll(eq(TEST_LICENSE_VOLUME_REPORT_ID), any(AuthenticatedUser.class));
    }

    /**
     * Test findAll method without query parameter
     */
    @Test
    void findAll_ShouldReturnList_WhenNoQueryParameter() throws Exception {
        // Given
        List<LicenseVolumeReportItem> expectedItems = Arrays.asList(createTestLicenseVolumeReportItem());

        when(licenseVolumeReportItemService.findAll(isNull(), any(AuthenticatedUser.class)))
                .thenReturn(expectedItems);

        // When & Then
        mockMvc.perform(get("/license-volume-report-item"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.length()").value(1));

        // Verify service was called with null parameter
        verify(licenseVolumeReportItemService).findAll(isNull(), any(AuthenticatedUser.class));
    }

    /**
     * Test findAll method when service throws exception
     */
    @Test
    void findAll_ShouldReturnNotFound_WhenServiceThrowsNotFoundException() throws Exception {
        // Given
        when(licenseVolumeReportItemService.findAll(eq(TEST_LICENSE_VOLUME_REPORT_ID), any(AuthenticatedUser.class)))
                .thenThrow(new ResponseStatusException(org.springframework.http.HttpStatus.NOT_FOUND, "License Volume Report Item not found"));

        // When & Then
        mockMvc.perform(get("/license-volume-report-item")
                        .param("license_volume_report_id", TEST_LICENSE_VOLUME_REPORT_ID.toString()))
                .andExpect(status().isNotFound());
    }

    /**
     * Test findAll method when service throws forbidden exception
     */
    @Test
    void findAll_ShouldReturnForbidden_WhenServiceThrowsForbiddenException() throws Exception {
        // Given
        when(licenseVolumeReportItemService.findAll(eq(TEST_LICENSE_VOLUME_REPORT_ID), any(AuthenticatedUser.class)))
                .thenThrow(new ResponseStatusException(org.springframework.http.HttpStatus.FORBIDDEN, "You do not have permission to access this resource"));

        // When & Then
        mockMvc.perform(get("/license-volume-report-item")
                        .param("license_volume_report_id", TEST_LICENSE_VOLUME_REPORT_ID.toString()))
                .andExpect(status().isForbidden());
    }

    /**
     * Test findOne method - GET /license-volume-report-item/{id}
     * Tests successful retrieval of a single item
     */
    @Test
    void findOne_ShouldReturnItem_WhenItemExists() throws Exception {
        // Given
        LicenseVolumeReportItem expectedItem = createTestLicenseVolumeReportItem();
        Optional<LicenseVolumeReportItem> optionalItem = Optional.of(expectedItem);

        when(licenseVolumeReportItemService.findOne(eq(TEST_ID), any(AuthenticatedUser.class)))
                .thenReturn(optionalItem);

        // When & Then
        mockMvc.perform(get("/license-volume-report-item/{id}", TEST_ID))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(TEST_ID))
                .andExpect(jsonPath("$.setupFractionId").value(1))
                .andExpect(jsonPath("$.value").value(100));

        // Verify service was called
        verify(licenseVolumeReportItemService).findOne(eq(TEST_ID), any(AuthenticatedUser.class));
    }

    /**
     * Test findOne method when item doesn't exist
     */
    @Test
    void findOne_ShouldReturnEmpty_WhenItemNotExists() throws Exception {
        // Given
        when(licenseVolumeReportItemService.findOne(eq(TEST_ID), any(AuthenticatedUser.class)))
                .thenReturn(Optional.empty());

        // When & Then
        mockMvc.perform(get("/license-volume-report-item/{id}", TEST_ID))
                .andExpect(status().isOk())
                .andExpect(content().string(""));

        // Verify service was called
        verify(licenseVolumeReportItemService).findOne(eq(TEST_ID), any(AuthenticatedUser.class));
    }

    /**
     * Test findOne method with invalid ID format
     */
    @Test
    void findOne_ShouldReturnBadRequest_WhenInvalidIdFormat() throws Exception {
        // When & Then
        mockMvc.perform(get("/license-volume-report-item/{id}", "invalid-id"))
                .andExpect(status().isBadRequest());

        // Verify service was not called
        verify(licenseVolumeReportItemService, never()).findOne(any(), any());
    }

    /**
     * Test findOne method when service throws exception
     */
    @Test
    void findOne_ShouldReturnNotFound_WhenServiceThrowsNotFoundException() throws Exception {
        // Given
        when(licenseVolumeReportItemService.findOne(eq(TEST_ID), any(AuthenticatedUser.class)))
                .thenThrow(new ResponseStatusException(org.springframework.http.HttpStatus.NOT_FOUND, "License Volume Report Item not found"));

        // When & Then
        mockMvc.perform(get("/license-volume-report-item/{id}", TEST_ID))
                .andExpect(status().isNotFound());
    }

    /**
     * Test update method - PUT /license-volume-report-item/{id}
     * Tests successful update of an item
     */
    @Test
    void update_ShouldReturnUpdatedItem_WhenValidDto() throws Exception {
        // Given
        UpdateLicenseVolumeReportItemDto updateDto = createTestUpdateDto();
        LicenseVolumeReportItem expectedItem = createTestLicenseVolumeReportItem();
        expectedItem.setValue(200); // Updated value

        when(licenseVolumeReportItemService.update(eq(TEST_ID), any(UpdateLicenseVolumeReportItemDto.class), any(AuthenticatedUser.class)))
                .thenReturn(expectedItem);

        // When & Then
        mockMvc.perform(put("/license-volume-report-item/{id}", TEST_ID)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(TEST_ID))
                .andExpect(jsonPath("$.value").value(200));

        // Verify service was called
        verify(licenseVolumeReportItemService).update(eq(TEST_ID), any(UpdateLicenseVolumeReportItemDto.class), any(AuthenticatedUser.class));
    }

    /**
     * Test update method with invalid JSON
     */
    @Test
    void update_ShouldReturnBadRequest_WhenInvalidJson() throws Exception {
        // When & Then
        mockMvc.perform(put("/license-volume-report-item/{id}", TEST_ID)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("invalid json"))
                .andExpect(status().isBadRequest());

        // Verify service was not called
        verify(licenseVolumeReportItemService, never()).update(any(), any(), any());
    }

    /**
     * Test update method when service throws exception
     */
    @Test
    void update_ShouldReturnNotFound_WhenServiceThrowsNotFoundException() throws Exception {
        // Given
        UpdateLicenseVolumeReportItemDto updateDto = createTestUpdateDto();
        when(licenseVolumeReportItemService.update(eq(TEST_ID), any(UpdateLicenseVolumeReportItemDto.class), any(AuthenticatedUser.class)))
                .thenThrow(new ResponseStatusException(org.springframework.http.HttpStatus.NOT_FOUND, "License Volume Report Item not found"));

        // When & Then
        mockMvc.perform(put("/license-volume-report-item/{id}", TEST_ID)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isNotFound());
    }

    /**
     * Test decline method - POST /license-volume-report-item/{id}/decline
     * Tests successful decline of an item
     */
    @Test
    void decline_ShouldReturnDeclinedItem_WhenValidDto() throws Exception {
        // Given
        DeclineLicenseVolumeReportItemDto declineDto = createTestDeclineDto();
        LicenseVolumeReportItem expectedItem = createTestLicenseVolumeReportItem();

        when(licenseVolumeReportItemService.decline(eq(TEST_ID), any(DeclineLicenseVolumeReportItemDto.class), any(AuthenticatedUser.class)))
                .thenReturn(expectedItem);

        // When & Then
        mockMvc.perform(post("/license-volume-report-item/{id}/decline", TEST_ID)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(declineDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(TEST_ID));

        // Verify service was called
        verify(licenseVolumeReportItemService).decline(eq(TEST_ID), any(DeclineLicenseVolumeReportItemDto.class), any(AuthenticatedUser.class));
    }

    /**
     * Test decline method with invalid JSON
     */
    @Test
    void decline_ShouldReturnBadRequest_WhenInvalidJson() throws Exception {
        // When & Then
        mockMvc.perform(post("/license-volume-report-item/{id}/decline", TEST_ID)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("invalid json"))
                .andExpect(status().isBadRequest());

        // Verify service was not called
        verify(licenseVolumeReportItemService, never()).decline(any(), any(), any());
    }

    /**
     * Test decline method when service throws exception
     */
    @Test
    void decline_ShouldReturnBadRequest_WhenServiceThrowsBadRequestException() throws Exception {
        // Given
        DeclineLicenseVolumeReportItemDto declineDto = createTestDeclineDto();
        when(licenseVolumeReportItemService.decline(eq(TEST_ID), any(DeclineLicenseVolumeReportItemDto.class), any(AuthenticatedUser.class)))
                .thenThrow(new ResponseStatusException(org.springframework.http.HttpStatus.BAD_REQUEST, "License Volume Report ID is invalid"));

        // When & Then
        mockMvc.perform(post("/license-volume-report-item/{id}/decline", TEST_ID)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(declineDto)))
                .andExpect(status().isBadRequest());
    }

    /**
     * Test remove method - DELETE /license-volume-report-item/{id}
     * Tests successful removal (soft delete) of an item
     */
    @Test
    void remove_ShouldReturnRemovedItem_WhenValidId() throws Exception {
        // Given
        LicenseVolumeReportItem expectedItem = createTestLicenseVolumeReportItem();
        expectedItem.setDeletedAt(LocalDate.now()); // Soft deleted

        when(licenseVolumeReportItemService.remove(eq(TEST_ID), any(AuthenticatedUser.class)))
                .thenReturn(expectedItem);

        // When & Then
        mockMvc.perform(delete("/license-volume-report-item/{id}", TEST_ID))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(TEST_ID))
                .andExpect(jsonPath("$.deletedAt").exists());

        // Verify service was called
        verify(licenseVolumeReportItemService).remove(eq(TEST_ID), any(AuthenticatedUser.class));
    }

    /**
     * Test remove method with invalid ID format
     */
    @Test
    void remove_ShouldReturnBadRequest_WhenInvalidIdFormat() throws Exception {
        // When & Then
        mockMvc.perform(delete("/license-volume-report-item/{id}", "invalid-id"))
                .andExpect(status().isBadRequest());

        // Verify service was not called
        verify(licenseVolumeReportItemService, never()).remove(any(), any());
    }

    /**
     * Test remove method when service throws exception
     */
    @Test
    void remove_ShouldReturnNotFound_WhenServiceThrowsNotFoundException() throws Exception {
        // Given
        when(licenseVolumeReportItemService.remove(eq(TEST_ID), any(AuthenticatedUser.class)))
                .thenThrow(new ResponseStatusException(org.springframework.http.HttpStatus.NOT_FOUND, "License Volume Report Item not found"));

        // When & Then
        mockMvc.perform(delete("/license-volume-report-item/{id}", TEST_ID))
                .andExpect(status().isNotFound());
    }

    /**
     * Test createBulk method - POST /license-volume-report-item/bulk
     * Tests successful bulk creation of items
     */
    @Test
    void createBulk_ShouldCallService_WhenValidDtoList() throws Exception {
        // Given
        List<CreateLicenseVolumeReportItemDto> createDtoList = Arrays.asList(
                createTestCreateDto(),
                createTestCreateDto()
        );

        // When & Then
        mockMvc.perform(post("/license-volume-report-item/bulk")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createDtoList)))
                .andExpect(status().isOk());

        // Verify service was called
        verify(licenseVolumeReportItemService).createMany(any(List.class));
    }

    /**
     * Test createBulk method with empty list
     */
    @Test
    void createBulk_ShouldCallService_WhenEmptyList() throws Exception {
        // Given
        List<CreateLicenseVolumeReportItemDto> emptyList = Arrays.asList();

        // When & Then
        mockMvc.perform(post("/license-volume-report-item/bulk")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(emptyList)))
                .andExpect(status().isOk());

        // Verify service was called
        verify(licenseVolumeReportItemService).createMany(any(List.class));
    }

    /**
     * Test createBulk method with invalid JSON
     */
    @Test
    void createBulk_ShouldReturnBadRequest_WhenInvalidJson() throws Exception {
        // When & Then
        mockMvc.perform(post("/license-volume-report-item/bulk")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("invalid json"))
                .andExpect(status().isBadRequest());

        // Verify service was not called
        verify(licenseVolumeReportItemService, never()).createMany(any());
    }

    /**
     * Test createBulk method when service throws exception
     */
    @Test
    void createBulk_ShouldReturnBadRequest_WhenServiceThrowsBadRequestException() throws Exception {
        // Given
        List<CreateLicenseVolumeReportItemDto> createDtoList = Arrays.asList();
        doThrow(new ResponseStatusException(org.springframework.http.HttpStatus.BAD_REQUEST, "Items are required"))
                .when(licenseVolumeReportItemService).createMany(any(List.class));

        // When & Then
        mockMvc.perform(post("/license-volume-report-item/bulk")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createDtoList)))
                .andExpect(status().isBadRequest());
    }

    /**
     * Test updateBulkValues method - POST /license-volume-report-item/bulk-update
     * Tests successful bulk update of item values
     */
    @Test
    void updateBulkValues_ShouldCallService_WhenValidDtoList() throws Exception {
        // Given
        List<UpdateLicenseVolumeReportItemValueDto> updateDtoList = Arrays.asList(
                createTestUpdateValueDto(),
                createTestUpdateValueDto()
        );

        // When & Then
        mockMvc.perform(post("/license-volume-report-item/bulk-update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateDtoList)))
                .andExpect(status().isOk());

        // Verify service was called
        verify(licenseVolumeReportItemService).updateBulkValues(any(List.class));
    }

    /**
     * Test updateBulkValues method with empty list
     */
    @Test
    void updateBulkValues_ShouldCallService_WhenEmptyList() throws Exception {
        // Given
        List<UpdateLicenseVolumeReportItemValueDto> emptyList = Arrays.asList();

        // When & Then
        mockMvc.perform(post("/license-volume-report-item/bulk-update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(emptyList)))
                .andExpect(status().isOk());

        // Verify service was called
        verify(licenseVolumeReportItemService).updateBulkValues(any(List.class));
    }

    /**
     * Test updateBulkValues method with invalid JSON
     */
    @Test
    void updateBulkValues_ShouldReturnBadRequest_WhenInvalidJson() throws Exception {
        // When & Then
        mockMvc.perform(post("/license-volume-report-item/bulk-update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("invalid json"))
                .andExpect(status().isBadRequest());

        // Verify service was not called
        verify(licenseVolumeReportItemService, never()).updateBulkValues(any());
    }

    /**
     * Test updateBulkValues method when service throws exception
     */
    @Test
    void updateBulkValues_ShouldReturnBadRequest_WhenServiceThrowsBadRequestException() throws Exception {
        // Given
        List<UpdateLicenseVolumeReportItemValueDto> updateDtoList = Arrays.asList();
        doThrow(new ResponseStatusException(org.springframework.http.HttpStatus.BAD_REQUEST, "No items to update"))
                .when(licenseVolumeReportItemService).updateBulkValues(any(List.class));

        // When & Then
        mockMvc.perform(post("/license-volume-report-item/bulk-update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateDtoList)))
                .andExpect(status().isBadRequest());
    }
}
