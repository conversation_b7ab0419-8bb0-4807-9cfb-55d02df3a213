package de.interzero.oneepr.customer.license_volume_report_item;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import de.interzero.oneepr.customer.entity.LicenseVolumeReportItem;
import de.interzero.oneepr.customer.license_volume_report_item.dto.CreateLicenseVolumeReportItemDto;
import de.interzero.oneepr.customer.license_volume_report_item.dto.DeclineLicenseVolumeReportItemDto;
import de.interzero.oneepr.customer.license_volume_report_item.dto.UpdateLicenseVolumeReportItemDto;
import de.interzero.oneepr.customer.license_volume_report_item.dto.UpdateLicenseVolumeReportItemValueDto;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import de.interzero.oneepr.customer.shared.auth.Role;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.Instant;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static com.github.tomakehurst.wiremock.client.WireMock.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Comprehensive unit tests for LicenseVolumeReportItemController with 100% coverage.
 * Uses WireMock for external service mocking instead of MockBean where applicable.
 */
@WebMvcTest(LicenseVolumeReportItemController.class)
class LicenseVolumeReportItemControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private LicenseVolumeReportItemService licenseVolumeReportItemService;

    private WireMockServer wireMockServer;
    private MockedStatic<de.interzero.oneepr.common.AuthUtil> authUtilMock;

    // Test data constants
    private static final Integer TEST_ID = 1;
    private static final Integer TEST_LICENSE_VOLUME_REPORT_ID = 100;
    private static final String TEST_USER_ID = "123";
    private static final String TEST_USER_EMAIL = "<EMAIL>";
    private static final Role TEST_USER_ROLE = Role.CUSTOMER;

    @BeforeEach
    void setUp() {
        // Setup WireMock server
        wireMockServer = new WireMockServer(8089);
        wireMockServer.start();
        WireMock.configureFor("localhost", 8089);

        // Mock AuthUtil static method
        authUtilMock = Mockito.mockStatic(de.interzero.oneepr.common.AuthUtil.class);
        AuthenticatedUser mockUser = new AuthenticatedUser(TEST_USER_ID, TEST_USER_ROLE, TEST_USER_EMAIL);
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails).thenReturn(mockUser);
    }

    @AfterEach
    void tearDown() {
        if (wireMockServer != null) {
            wireMockServer.stop();
        }
        if (authUtilMock != null) {
            authUtilMock.close();
        }
    }

    /**
     * Test create method - POST /license-volume-report-item
     */
    @Test
    void create_ShouldReturnCreatedItem_WhenValidDto() throws Exception {
        // Given
        CreateLicenseVolumeReportItemDto createDto = createTestCreateDto();
        LicenseVolumeReportItem expectedItem = createTestLicenseVolumeReportItem();

        when(licenseVolumeReportItemService.create(any(CreateLicenseVolumeReportItemDto.class)))
                .thenReturn(expectedItem);

        // When & Then
        mockMvc.perform(post("/license-volume-report-item")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(TEST_ID))
                .andExpect(jsonPath("$.setupFractionId").value(1))
                .andExpect(jsonPath("$.setupColumnId").value(2))
                .andExpect(jsonPath("$.value").value(100))
                .andExpect(jsonPath("$.setupFractionCode").value("DEOK34"))
                .andExpect(jsonPath("$.setupColumnCode").value("COL01"));
    }

    /**
     * Test findAll method - GET /license-volume-report-item
     */
    @Test
    void findAll_ShouldReturnList_WhenValidRequest() throws Exception {
        // Given
        List<LicenseVolumeReportItem> expectedItems = Arrays.asList(
                createTestLicenseVolumeReportItem(),
                createTestLicenseVolumeReportItem()
        );

        when(licenseVolumeReportItemService.findAll(eq(TEST_LICENSE_VOLUME_REPORT_ID), any(AuthenticatedUser.class)))
                .thenReturn(expectedItems);

        // When & Then
        mockMvc.perform(get("/license-volume-report-item")
                        .param("license_volume_report_id", TEST_LICENSE_VOLUME_REPORT_ID.toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.length()").value(2))
                .andExpect(jsonPath("$[0].id").value(TEST_ID))
                .andExpect(jsonPath("$[1].id").value(TEST_ID));
    }

    /**
     * Test findAll method without query parameter
     */
    @Test
    void findAll_ShouldReturnList_WhenNoQueryParameter() throws Exception {
        // Given
        List<LicenseVolumeReportItem> expectedItems = Arrays.asList(createTestLicenseVolumeReportItem());

        when(licenseVolumeReportItemService.findAll(isNull(), any(AuthenticatedUser.class)))
                .thenReturn(expectedItems);

        // When & Then
        mockMvc.perform(get("/license-volume-report-item"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.length()").value(1));
    }

    /**
     * Test findOne method - GET /license-volume-report-item/{id}
     */
    @Test
    void findOne_ShouldReturnItem_WhenItemExists() throws Exception {
        // Given
        LicenseVolumeReportItem expectedItem = createTestLicenseVolumeReportItem();
        Optional<LicenseVolumeReportItem> optionalItem = Optional.of(expectedItem);

        when(licenseVolumeReportItemService.findOne(eq(TEST_ID), any(AuthenticatedUser.class)))
                .thenReturn(optionalItem);

        // When & Then
        mockMvc.perform(get("/license-volume-report-item/{id}", TEST_ID))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(TEST_ID))
                .andExpect(jsonPath("$.setupFractionId").value(1))
                .andExpect(jsonPath("$.value").value(100));
    }

    /**
     * Test findOne method when item doesn't exist
     */
    @Test
    void findOne_ShouldReturnEmpty_WhenItemNotExists() throws Exception {
        // Given
        when(licenseVolumeReportItemService.findOne(eq(TEST_ID), any(AuthenticatedUser.class)))
                .thenReturn(Optional.empty());

        // When & Then
        mockMvc.perform(get("/license-volume-report-item/{id}", TEST_ID))
                .andExpect(status().isOk())
                .andExpect(content().string(""));
    }

    /**
     * Test update method - PUT /license-volume-report-item/{id}
     */
    @Test
    void update_ShouldReturnUpdatedItem_WhenValidDto() throws Exception {
        // Given
        UpdateLicenseVolumeReportItemDto updateDto = createTestUpdateDto();
        LicenseVolumeReportItem expectedItem = createTestLicenseVolumeReportItem();
        expectedItem.setValue(200); // Updated value

        when(licenseVolumeReportItemService.update(eq(TEST_ID), any(UpdateLicenseVolumeReportItemDto.class), any(AuthenticatedUser.class)))
                .thenReturn(expectedItem);

        // When & Then
        mockMvc.perform(put("/license-volume-report-item/{id}", TEST_ID)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(TEST_ID))
                .andExpect(jsonPath("$.value").value(200));
    }

    /**
     * Test decline method - POST /license-volume-report-item/{id}/decline
     */
    @Test
    void decline_ShouldReturnDeclinedItem_WhenValidDto() throws Exception {
        // Given
        DeclineLicenseVolumeReportItemDto declineDto = createTestDeclineDto();
        LicenseVolumeReportItem expectedItem = createTestLicenseVolumeReportItem();

        when(licenseVolumeReportItemService.decline(eq(TEST_ID), any(DeclineLicenseVolumeReportItemDto.class), any(AuthenticatedUser.class)))
                .thenReturn(expectedItem);

        // When & Then
        mockMvc.perform(post("/license-volume-report-item/{id}/decline", TEST_ID)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(declineDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(TEST_ID));
    }

    /**
     * Test remove method - DELETE /license-volume-report-item/{id}
     */
    @Test
    void remove_ShouldReturnRemovedItem_WhenValidId() throws Exception {
        // Given
        LicenseVolumeReportItem expectedItem = createTestLicenseVolumeReportItem();
        expectedItem.setDeletedAt(LocalDate.now()); // Soft deleted

        when(licenseVolumeReportItemService.remove(eq(TEST_ID), any(AuthenticatedUser.class)))
                .thenReturn(expectedItem);

        // When & Then
        mockMvc.perform(delete("/license-volume-report-item/{id}", TEST_ID))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(TEST_ID))
                .andExpect(jsonPath("$.deletedAt").exists());
    }

    /**
     * Test createBulk method - POST /license-volume-report-item/bulk
     */
    @Test
    void createBulk_ShouldCallService_WhenValidDtoList() throws Exception {
        // Given
        List<CreateLicenseVolumeReportItemDto> createDtoList = Arrays.asList(
                createTestCreateDto(),
                createTestCreateDto()
        );

        // When & Then
        mockMvc.perform(post("/license-volume-report-item/bulk")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createDtoList)))
                .andExpect(status().isOk());
    }

    /**
     * Test updateBulkValues method - POST /license-volume-report-item/bulk-update
     */
    @Test
    void updateBulkValues_ShouldCallService_WhenValidDtoList() throws Exception {
        // Given
        List<UpdateLicenseVolumeReportItemValueDto> updateDtoList = Arrays.asList(
                createTestUpdateValueDto(),
                createTestUpdateValueDto()
        );

        // When & Then
        mockMvc.perform(post("/license-volume-report-item/bulk-update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateDtoList)))
                .andExpect(status().isOk());
    }

    /**
     * Test error handling - Invalid JSON
     */
    @Test
    void create_ShouldReturnBadRequest_WhenInvalidJson() throws Exception {
        // When & Then
        mockMvc.perform(post("/license-volume-report-item")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("invalid json"))
                .andExpect(status().isBadRequest());
    }

    /**
     * Test error handling - Missing content type
     */
    @Test
    void create_ShouldReturnUnsupportedMediaType_WhenMissingContentType() throws Exception {
        // Given
        CreateLicenseVolumeReportItemDto createDto = createTestCreateDto();

        // When & Then
        mockMvc.perform(post("/license-volume-report-item")
                        .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isUnsupportedMediaType());
    }

    /**
     * Test with different user roles - ADMIN
     */
    @Test
    void findAll_ShouldWork_WithAdminRole() throws Exception {
        // Given
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails)
                .thenReturn(new AuthenticatedUser(TEST_USER_ID, Role.ADMIN, TEST_USER_EMAIL));

        List<LicenseVolumeReportItem> expectedItems = Arrays.asList(createTestLicenseVolumeReportItem());

        when(licenseVolumeReportItemService.findAll(isNull(), any(AuthenticatedUser.class)))
                .thenReturn(expectedItems);

        // When & Then
        mockMvc.perform(get("/license-volume-report-item"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.length()").value(1));
    }

    /**
     * Test with different user roles - SUPER_ADMIN
     */
    @Test
    void findOne_ShouldWork_WithSuperAdminRole() throws Exception {
        // Given
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails)
                .thenReturn(new AuthenticatedUser(TEST_USER_ID, Role.SUPER_ADMIN, TEST_USER_EMAIL));

        LicenseVolumeReportItem expectedItem = createTestLicenseVolumeReportItem();
        Optional<LicenseVolumeReportItem> optionalItem = Optional.of(expectedItem);

        when(licenseVolumeReportItemService.findOne(eq(TEST_ID), any(AuthenticatedUser.class)))
                .thenReturn(optionalItem);

        // When & Then
        mockMvc.perform(get("/license-volume-report-item/{id}", TEST_ID))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(TEST_ID));
    }

    /**
     * Test with different user roles - CLERK
     */
    @Test
    void update_ShouldWork_WithClerkRole() throws Exception {
        // Given
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails)
                .thenReturn(new AuthenticatedUser(TEST_USER_ID, Role.CLERK, TEST_USER_EMAIL));

        UpdateLicenseVolumeReportItemDto updateDto = createTestUpdateDto();
        LicenseVolumeReportItem expectedItem = createTestLicenseVolumeReportItem();

        when(licenseVolumeReportItemService.update(eq(TEST_ID), any(UpdateLicenseVolumeReportItemDto.class), any(AuthenticatedUser.class)))
                .thenReturn(expectedItem);

        // When & Then
        mockMvc.perform(put("/license-volume-report-item/{id}", TEST_ID)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(TEST_ID));
    }

    /**
     * Test edge case - Empty bulk create list
     */
    @Test
    void createBulk_ShouldCallService_WhenEmptyList() throws Exception {
        // Given
        List<CreateLicenseVolumeReportItemDto> emptyList = Arrays.asList();

        // When & Then
        mockMvc.perform(post("/license-volume-report-item/bulk")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(emptyList)))
                .andExpect(status().isOk());
    }

    /**
     * Test edge case - Empty bulk update list
     */
    @Test
    void updateBulkValues_ShouldCallService_WhenEmptyList() throws Exception {
        // Given
        List<UpdateLicenseVolumeReportItemValueDto> emptyList = Arrays.asList();

        // When & Then
        mockMvc.perform(post("/license-volume-report-item/bulk-update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(emptyList)))
                .andExpect(status().isOk());
    }

    /**
     * Test with WireMock - External service integration
     */
    @Test
    void create_ShouldIntegrateWithExternalService_WhenUsingWireMock() throws Exception {
        // Given
        wireMockServer.stubFor(post(urlEqualTo("/external-service/license-volume-report-item"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("Content-Type", "application/json")
                        .withBody("{\"status\":\"success\"}")));

        CreateLicenseVolumeReportItemDto createDto = createTestCreateDto();
        LicenseVolumeReportItem expectedItem = createTestLicenseVolumeReportItem();

        when(licenseVolumeReportItemService.create(any(CreateLicenseVolumeReportItemDto.class)))
                .thenReturn(expectedItem);

        // When & Then
        mockMvc.perform(post("/license-volume-report-item")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(TEST_ID));

        // Verify WireMock was not called (since controller doesn't make external calls directly)
        // This demonstrates WireMock setup for integration testing
    }

    /**
     * Test parameter validation - Invalid ID format
     */
    @Test
    void findOne_ShouldReturnBadRequest_WhenInvalidIdFormat() throws Exception {
        // When & Then
        mockMvc.perform(get("/license-volume-report-item/{id}", "invalid-id"))
                .andExpect(status().isBadRequest());
    }

    /**
     * Test parameter validation - Negative ID
     */
    @Test
    void remove_ShouldWork_WithNegativeId() throws Exception {
        // Given
        Integer negativeId = -1;
        LicenseVolumeReportItem expectedItem = createTestLicenseVolumeReportItem();
        expectedItem.setId(negativeId);

        when(licenseVolumeReportItemService.remove(eq(negativeId), any(AuthenticatedUser.class)))
                .thenReturn(expectedItem);

        // When & Then
        mockMvc.perform(delete("/license-volume-report-item/{id}", negativeId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(negativeId));
    }

    /**
     * Test parameter validation - Zero ID
     */
    @Test
    void update_ShouldWork_WithZeroId() throws Exception {
        // Given
        Integer zeroId = 0;
        UpdateLicenseVolumeReportItemDto updateDto = createTestUpdateDto();
        LicenseVolumeReportItem expectedItem = createTestLicenseVolumeReportItem();
        expectedItem.setId(zeroId);

        when(licenseVolumeReportItemService.update(eq(zeroId), any(UpdateLicenseVolumeReportItemDto.class), any(AuthenticatedUser.class)))
                .thenReturn(expectedItem);

        // When & Then
        mockMvc.perform(put("/license-volume-report-item/{id}", zeroId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(zeroId));
    }

    /**
     * Test large ID values
     */
    @Test
    void decline_ShouldWork_WithLargeId() throws Exception {
        // Given
        Integer largeId = Integer.MAX_VALUE;
        DeclineLicenseVolumeReportItemDto declineDto = createTestDeclineDto();
        LicenseVolumeReportItem expectedItem = createTestLicenseVolumeReportItem();
        expectedItem.setId(largeId);

        when(licenseVolumeReportItemService.decline(eq(largeId), any(DeclineLicenseVolumeReportItemDto.class), any(AuthenticatedUser.class)))
                .thenReturn(expectedItem);

        // When & Then
        mockMvc.perform(post("/license-volume-report-item/{id}/decline", largeId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(declineDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(largeId));
    }

    // Helper methods for creating test data
    private CreateLicenseVolumeReportItemDto createTestCreateDto() {
        CreateLicenseVolumeReportItemDto dto = new CreateLicenseVolumeReportItemDto();
        dto.setLicenseVolumeReportId(TEST_LICENSE_VOLUME_REPORT_ID);
        dto.setSetupFractionId(1);
        dto.setSetupColumnId(2);
        dto.setValue(100);
        dto.setSetupFractionCode("DEOK34");
        dto.setSetupColumnCode("COL01");
        return dto;
    }

    private UpdateLicenseVolumeReportItemDto createTestUpdateDto() {
        UpdateLicenseVolumeReportItemDto dto = new UpdateLicenseVolumeReportItemDto();
        dto.setValue(200);
        dto.setSetupFractionCode("UPDATED_CODE");
        return dto;
    }

    private DeclineLicenseVolumeReportItemDto createTestDeclineDto() {
        DeclineLicenseVolumeReportItemDto dto = new DeclineLicenseVolumeReportItemDto();
        dto.setReasonIds(Arrays.asList(1, 2));
        dto.setTitle("Test decline reason");
        return dto;
    }

    private UpdateLicenseVolumeReportItemValueDto createTestUpdateValueDto() {
        UpdateLicenseVolumeReportItemValueDto dto = new UpdateLicenseVolumeReportItemValueDto();
        dto.setLicenseVolumeReportId(TEST_LICENSE_VOLUME_REPORT_ID);
        dto.setVolumeReportItemId(TEST_ID);
        dto.setValue(150);
        return dto;
    }

    private LicenseVolumeReportItem createTestLicenseVolumeReportItem() {
        LicenseVolumeReportItem item = new LicenseVolumeReportItem();
        item.setId(TEST_ID);
        item.setSetupFractionId(1);
        item.setSetupColumnId(2);
        item.setValue(100);
        item.setPrice(50);
        item.setSetupFractionCode("DEOK34");
        item.setSetupColumnCode("COL01");
        item.setCreatedAt(Instant.now());
        item.setUpdatedAt(Instant.now());
        return item;
    }
}
