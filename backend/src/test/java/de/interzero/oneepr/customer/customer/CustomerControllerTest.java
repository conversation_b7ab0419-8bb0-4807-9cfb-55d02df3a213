package de.interzero.oneepr.customer.customer;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.action_guide.ActionGuide;
import de.interzero.oneepr.action_guide.ActionGuideRepository;
import de.interzero.oneepr.admin.mail.EmailMessage;
import de.interzero.oneepr.admin.mail.SandboxEmailGateway;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.TestRole;
import de.interzero.oneepr.customer.certificate.Certificate;
import de.interzero.oneepr.customer.certificate.CertificateRepository;
import de.interzero.oneepr.customer.company.Company;
import de.interzero.oneepr.customer.company.CompanyRepository;
import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.contract.ContractRepository;
import de.interzero.oneepr.customer.coupon.dto.InviteCustomersManager;
import de.interzero.oneepr.customer.customer.dto.CreateCustomerDto;
import de.interzero.oneepr.customer.customer.dto.UpdateCustomerDto;
import de.interzero.oneepr.customer.customer_phone.CustomerPhone;
import de.interzero.oneepr.customer.customer_phone.CustomerPhoneRepository;
import de.interzero.oneepr.customer.file.File;
import de.interzero.oneepr.customer.file.FileRepository;
import de.interzero.oneepr.customer.http.AdminInterface;
import de.interzero.oneepr.customer.http.AuthInterface;
import de.interzero.oneepr.customer.license.License;
import de.interzero.oneepr.customer.license.LicenseRepository;
import de.interzero.oneepr.customer.termination.Termination;
import de.interzero.oneepr.customer.termination.TerminationRepository;
import jakarta.persistence.EntityManager;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import static org.hamcrest.Matchers.hasSize;

import java.time.Instant;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@Slf4j
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class CustomerControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private CustomerRepository customerRepository;

    private Customer testCustomer;

    @Autowired
    private CompanyRepository companyRepository;

    @Autowired
    private CustomerPhoneRepository customerPhoneRepository;

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private LicenseRepository licenseRepository;

    @Autowired
    private ActionGuideRepository actionGuideRepository;

    @Autowired
    private TerminationRepository terminationRepository;

    @Autowired
    private CertificateRepository certificateRepository;

    @Autowired
    private EntityManager entityManager;

    @Autowired
    private FileRepository fileRepository;

    private Contract testContract;

    private License testLicense;

    private ActionGuide testActionGuide;

    private Certificate testCertificate;

    private File testFile;

    private Termination testTermination;

    @MockBean
    private SandboxEmailGateway emailGateway;

    @BeforeEach
    void setUp() {
        // 1. Create Customer
        testCustomer = new Customer();
        testCustomer.setFirstName("John");
        testCustomer.setLastName("Doe");
        testCustomer.setEmail("<EMAIL>");
        testCustomer.setUserId(101);
        testCustomer.setType(Customer.Type.REGULAR);
        testCustomer.setCreatedAt(Instant.now());
        testCustomer.setUpdatedAt(Instant.now());
        testCustomer = customerRepository.save(testCustomer);

        // 2. Create related top-level entities
        Company company = new Company();
        company.setName("Doe Inc.");
        company.setCustomer(testCustomer);
        company.setCreatedAt(Instant.now());
        company.setUpdatedAt(Instant.now());
        companyRepository.save(company);

        CustomerPhone phone = new CustomerPhone();
        phone.setCustomer(testCustomer);
        phone.setPhoneNumber("************");
        phone.setPhoneType(CustomerPhone.Type.PHONE);
        phone.setCreatedAt(Instant.now());
        phone.setUpdatedAt(Instant.now());
        customerPhoneRepository.save(phone);

        // 3. Create Contract
        testContract = new Contract();
        testContract.setCustomer(testCustomer);
        testContract.setTitle("Test EU License 2025");
        testContract.setStartDate(Instant.now());
        testContract.setEndDate(Instant.now().plus(365, ChronoUnit.DAYS));
        testContract.setCreatedAt(Instant.now());
        testContract.setUpdatedAt(Instant.now());
        testContract.setType(Contract.Type.EU_LICENSE);
        testContract.setStatus(Contract.Status.ACTIVE);
        testContract = contractRepository.save(testContract);

        // 4. Create License under the Contract
        testLicense = new License();
        testLicense.setContract(testContract);
        testLicense.setYear(2025);
        testLicense.setCountryCode("DE");
        testLicense.setCountryId(1);
        testLicense.setCountryName("Germany");
        testLicense.setCountryFlag("🇩🇪");
        testLicense.setRegistrationNumber("DE123456789");
        testLicense.setStartDate(Instant.now());
        testLicense.setCreatedAt(Instant.now());
        testLicense.setUpdatedAt(Instant.now());
        testLicense = licenseRepository.save(testLicense);

        // 5. Create Certificate under the License
        testCertificate = new Certificate();
        testCertificate.setLicense(testLicense);
        testCertificate.setName("Eco-Cert");
        testCertificate.setStatus(Certificate.Status.AVAILABLE);
        testCertificate.setCreatedAt(Instant.now());
        testCertificate.setUpdatedAt(Instant.now());
        testCertificate = certificateRepository.save(testCertificate);

        // 6. Create a File associated with the Certificate
        testFile = new File();
        testFile.setId("2134");
        testFile.setCertificate(testCertificate);
        testFile.setName("cert-file.pdf");
        testFile.setOriginalName("original-cert.pdf");
        testFile.setExtension("pdf");
        testFile.setUserId("test-user-123");
        testFile.setSize("1024");
        testFile.setType(File.Type.CERTIFICATE);
        testFile.setCreatedAt(Instant.now());
        testFile.setUpdatedAt(Instant.now());
        fileRepository.save(testFile);

        // 7. Create a DELETED File to test the filter
        File deletedFile = new File();
        deletedFile.setId("1234");
        deletedFile.setCertificate(testCertificate);
        deletedFile.setName("deleted-cert-file.pdf");
        deletedFile.setOriginalName("original-deleted-cert.pdf");
        deletedFile.setDeletedAt(LocalDate.now());
        deletedFile.setCreatedAt(Instant.now());
        deletedFile.setUpdatedAt(Instant.now());
        deletedFile.setExtension("pdf");
        deletedFile.setUserId("test-user-123");
        deletedFile.setSize("2048");
        deletedFile.setType(File.Type.CERTIFICATE);
        fileRepository.save(deletedFile);

        // 8. Create an Action Guide under the Contract
        testActionGuide = new ActionGuide();
        testActionGuide.setContract(testContract);
        testActionGuide.setCountryCode("FR");
        testActionGuide.setCountryId(2);
        testActionGuide.setCountryName("France");
        testActionGuide.setCountryFlag("🇫🇷");
        testActionGuide.setCreatedAt(Instant.now());
        testActionGuide.setUpdatedAt(Instant.now());
        actionGuideRepository.save(testActionGuide);

        // 9. Create a Termination to test the null/not-null logic
        testTermination = new Termination();
        testTermination.setRequestedAt(Instant.now());
        testTermination.setStatus(Termination.Status.REQUESTED);
        testTermination.setCreatedAt(Instant.now());
        testTermination.setUpdatedAt(Instant.now());
        testTermination = terminationRepository.save(testTermination);

        // Associate the termination with the license
        testLicense.setTermination(testTermination);
        licenseRepository.save(testLicense);

        entityManager.flush();
        entityManager.clear();
    }

    @Test
    @WithMockUser(
            roles = TestRole.ADMIN
    )
    @SuppressWarnings("unchecked")
    void create_shouldCreateCustomerAndReturn201() throws Exception {
        try (MockedStatic<AdminInterface> mockedAdminInterface = Mockito.mockStatic(AdminInterface.class)) {
            mockedAdminInterface.when(() -> AdminInterface.admin(
                    Mockito.anyString(),
                    Mockito.anyMap(),
                    eq(HttpMethod.PATCH))).thenReturn(Map.of());
            doNothing().when(emailGateway).sendEmail(any(EmailMessage.class));
            InviteCustomersManager validSettings = new InviteCustomersManager();
            validSettings.setCouponPrefix("someValue");
            String settingsJson = objectMapper.writeValueAsString(validSettings);

            Map<String, Object> mockResponseData = new HashMap<>();
            mockResponseData.put("value", settingsJson);

            Map<String, Object> mockFullResponse = new HashMap<>();
            mockFullResponse.put("data", mockResponseData);

            mockedAdminInterface.when(() -> AdminInterface.admin(
                            eq("/settings/INVITE_CUSTOMERS_MANAGER"),
                            any(Map.class),
                            eq(HttpMethod.GET)))
                    .thenReturn(mockFullResponse);

            mockedAdminInterface.when(() -> AdminInterface.admin(
                            eq("/settings/INVITE_CUSTOMERS_MANAGER"),
                            any(Map.class),
                            eq(HttpMethod.GET)))
                    .thenReturn(mockFullResponse);
            CreateCustomerDto createDto = new CreateCustomerDto();
            createDto.setEmail("<EMAIL>");
            createDto.setFirstName("Jane");
            createDto.setLastName("Doe");
            mockMvc.perform(post(Api.CUSTOMERS).contentType(MediaType.APPLICATION_JSON)
                                    .content(objectMapper.writeValueAsString(createDto)))
                    .andExpect(status().isCreated())
                    .andExpect(jsonPath("$.email").value("<EMAIL>"));
        }
    }

    @Test
    @WithMockUser(roles = TestRole.ADMIN)
    void findAll_shouldReturnPageOfCustomers() throws Exception {
        mockMvc.perform(get(Api.CUSTOMERS))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[0].id").value(testCustomer.getId()));
    }

    @Test
    @WithMockUser(roles = TestRole.ADMIN)
    void findOne_shouldReturnCustomer() throws Exception {
        mockMvc.perform(get(Api.CUSTOMERS + "/" + testCustomer.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testCustomer.getId()))
                .andExpect(jsonPath("$.email").value(testCustomer.getEmail()));
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = TestRole.CUSTOMER
    )
    void findByUserId_asCorrectUser_shouldReturnCustomer() throws Exception {
        mockMvc.perform(get(Api.CUSTOMERS + "/user/" + testCustomer.getUserId()))
                .andExpect(status().isOk())
                .andDo(print())
                .andExpect(jsonPath("$.user_id").value(testCustomer.getUserId()));
    }


    @Test
    @WithMockUser(roles = TestRole.ADMIN)
    void update_shouldUpdateAndReturnCustomer() throws Exception {
        UpdateCustomerDto updateDto = new UpdateCustomerDto();
        updateDto.setFirstName("Johnathan");

        try (MockedStatic<AuthInterface> mockedAuth = Mockito.mockStatic(AuthInterface.class)) {
            // Mock the external call made during the update
            mockedAuth.when(() -> AuthInterface.auth(Mockito.anyString(), Mockito.anyMap(), eq(HttpMethod.PATCH)))
                    .thenReturn(Map.of());

            mockMvc.perform(put(Api.CUSTOMERS + "/" + testCustomer.getId()).contentType(MediaType.APPLICATION_JSON)
                                    .content(objectMapper.writeValueAsString(updateDto)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.id").value(testCustomer.getId()))
                    .andExpect(jsonPath("$.first_name").value("Johnathan"));
        }
    }

    @Test
    @WithMockUser(roles = TestRole.ADMIN)
    void remove_shouldSoftDeleteAndReturnSuccessMessage() throws Exception {
        mockMvc.perform(delete(Api.CUSTOMERS + "/" + testCustomer.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("Customer deleted successfully"));

        // Verify the customer was soft-deleted
        Optional<Customer> deletedCustomer = customerRepository.findById(testCustomer.getId());
        assertNotNull(deletedCustomer.orElseThrow().getDeletedAt());
    }

    @Test
    @WithMockUser(roles = TestRole.ADMIN)
    void details_shouldReturnCustomerDetails() throws Exception {
        mockMvc.perform(get(Api.CUSTOMERS + "/" + testCustomer.getId() + "/details"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testCustomer.getId()))
                .andExpect(jsonPath("$.email").value("<EMAIL>"))
                // Assertions for companies and phones
                .andExpect(jsonPath("$.companies", hasSize(1)))
                .andExpect(jsonPath("$.companies[0].name").value("Doe Inc."))
                .andExpect(jsonPath("$.phones", hasSize(1)))
                .andExpect(jsonPath("$.phones[0].phone_number").value("************"))
                // Assertions for contracts
                .andExpect(jsonPath("$.contracts", hasSize(1)))
                .andExpect(jsonPath("$.contracts[0].id").value(testContract.getId()))
                // Assertions for licenses within the contract
                .andExpect(jsonPath("$.contracts[0].licenses", hasSize(1)))
                .andExpect(jsonPath("$.contracts[0].licenses[0].id").value(testLicense.getId()))
                .andExpect(jsonPath("$.contracts[0].licenses[0].year").value(2025))
                // Assertions for the license's termination
                .andExpect(jsonPath("$.contracts[0].licenses[0].termination.id").value(testTermination.getId()))
                // Assertions for certificates within the license
                .andExpect(jsonPath("$.contracts[0].licenses[0].certificates", hasSize(1)))
                .andExpect(jsonPath("$.contracts[0].licenses[0].certificates[0].id").value(testCertificate.getId()))
                // Assert that the filtered files list contains only the non-deleted file
                .andExpect(jsonPath("$.contracts[0].licenses[0].certificates[0].files", hasSize(1)))
                .andExpect(jsonPath("$.contracts[0].licenses[0].certificates[0].files[0].id").value(testFile.getId()))
                // Assertions for action guides
                .andExpect(jsonPath("$.contracts[0].action_guides", hasSize(1)))
                .andExpect(jsonPath("$.contracts[0].action_guides[0].country_code").value("FR"));
    }
}