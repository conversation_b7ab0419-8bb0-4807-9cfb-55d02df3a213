package de.interzero.oneepr.customer.license_volume_report;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import de.interzero.oneepr.common.string.TestRole;
import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.contract.ContractRepository;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import de.interzero.oneepr.customer.decline.DeclineRepository;
import de.interzero.oneepr.customer.license.License;
import de.interzero.oneepr.customer.license.LicenseRepository;
import de.interzero.oneepr.customer.license_packaging_service.LicensePackagingService;
import de.interzero.oneepr.customer.license_packaging_service.LicensePackagingServiceRepository;
import de.interzero.oneepr.customer.license_volume_report.dto.CreateLicenseVolumeReportDto;
import de.interzero.oneepr.customer.license_volume_report.dto.DeclineLicenseVolumeReportDto;
import de.interzero.oneepr.customer.license_volume_report.dto.UpdateLicenseVolumeReportDto;
import de.interzero.oneepr.customer.reason.Reason;
import de.interzero.oneepr.customer.reason.ReasonRepository;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import de.interzero.oneepr.customer.shared.auth.Role;
import jakarta.persistence.EntityManager;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.*;

import static de.interzero.oneepr.common.string.Api.LICENSE_VOLUME_REPORT;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Comprehensive integration tests for LicenseVolumeReportController with 100% coverage.
 * Uses WireMock for external service mocking. Uses real service and repository instances.
 * Test Coverage:
 * - All 6 controller endpoints (create, findAll, findOne, update, decline, remove)
 * - All HTTP methods (GET, POST, PUT, DELETE)
 * - All success and error scenarios
 * - All authentication and authorization cases
 * - All parameter validation scenarios
 * - All edge cases and boundary conditions
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class LicenseVolumeReportControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    // Real service and repository instances (not mocked as per requirement)
    @Autowired
    private LicenseVolumeReportService licenseVolumeReportService;

    @Autowired
    private LicenseVolumeReportRepository licenseVolumeReportRepository;

    @Autowired
    private LicensePackagingServiceRepository licensePackagingServiceRepository;

    @Autowired
    private DeclineRepository declineRepository;

    @Autowired
    private ReasonRepository reasonRepository;

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private LicenseRepository licenseRepository;

    @Autowired
    private EntityManager entityManager;

    private WireMockServer wireMockServer;

    private MockedStatic<de.interzero.oneepr.common.AuthUtil> authUtilMock;

    // Test data constants
    private static final Integer TEST_CUSTOMER_ID = 1;

    private static final Integer TEST_CONTRACT_ID = 1;

    private static final Integer TEST_LICENSE_ID = 1;

    private static final Integer TEST_PACKAGING_SERVICE_ID = 1;

    private static final Integer TEST_LICENSE_VOLUME_REPORT_ID = 1;

    private static final String TEST_USER_EMAIL = "<EMAIL>";

    private static final Role TEST_USER_ROLE = Role.CUSTOMER;

    // Test entities
    private Customer testCustomer;

    private LicensePackagingService testPackagingService;

    private LicenseVolumeReport testVolumeReport;

    private Reason testReason;

    @BeforeEach
    void setUp() {
        // Create test data in database
        createTestData();
        // Setup WireMock server for external HTTP calls
        wireMockServer = new WireMockServer(8089);
        wireMockServer.start();
        WireMock.configureFor("localhost", 8089);

        // Mock AuthUtil static method
        authUtilMock = Mockito.mockStatic(de.interzero.oneepr.common.AuthUtil.class);
        AuthenticatedUser mockUser = new AuthenticatedUser(
                String.valueOf(testCustomer.getUserId()),
                                                           TEST_USER_ROLE,
                                                           TEST_USER_EMAIL);
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails).thenReturn(mockUser);

    }

    @AfterEach
    void tearDown() {
        if (wireMockServer != null) {
            wireMockServer.stop();
        }
        if (authUtilMock != null) {
            authUtilMock.close();
        }
        entityManager.clear();
    }

    private void createTestData() {
        // Create Customer
        testCustomer = new Customer();
        testCustomer.setId(TEST_CUSTOMER_ID);
        testCustomer.setUserId(TEST_CUSTOMER_ID);
        testCustomer.setEmail(TEST_USER_EMAIL);
        testCustomer.setFirstName("test first name");
        testCustomer.setLastName("test last name");
        testCustomer.setCreatedAt(Instant.now());
        testCustomer.setUpdatedAt(Instant.now());
        testCustomer = customerRepository.save(testCustomer);

        // Create Contract
        Contract testContract = new Contract();
        testContract.setId(TEST_CONTRACT_ID);
        testContract.setCustomer(testCustomer);
        testContract.setType(Contract.Type.EU_LICENSE);
        testContract.setStatus(Contract.Status.ACTIVE);
        testContract.setCreatedAt(Instant.now());
        testContract.setUpdatedAt(Instant.now());
        testContract.setTitle("test contract title");
        testContract.setEndDate(Instant.now());
        testContract.setStartDate(Instant.now());
        testContract = contractRepository.save(testContract);

        // Create License
        License testLicense = new License();
        testLicense.setId(TEST_LICENSE_ID);
        testLicense.setContract(testContract);
        testLicense.setCountryName("Germany");
        testLicense.setYear(2024);
        testLicense.setCreatedAt(Instant.now());
        testLicense.setUpdatedAt(Instant.now());
        testLicense.setStartDate(Instant.now());
        testLicense.setCountryId(1);
        testLicense.setCountryName("Germany");
        testLicense.setRegistrationNumber("test registration number");
        testLicense.setCountryCode("Germany");
        testLicense.setCountryFlag("Germany");
        testLicense = licenseRepository.save(testLicense);

        // Create LicensePackagingService
        testPackagingService = new LicensePackagingService();
        testPackagingService.setId(TEST_PACKAGING_SERVICE_ID);
        testPackagingService.setLicense(testLicense);
        testPackagingService.setCreatedAt(Instant.now());
        testPackagingService.setUpdatedAt(Instant.now());
        testPackagingService.setName("test packaging service");
        testPackagingService.setSetupPackagingServiceId(TEST_PACKAGING_SERVICE_ID);
        testPackagingService.setDescription("test packaging service description");
        testPackagingService = licensePackagingServiceRepository.save(testPackagingService);

        // Create LicenseVolumeReport
        testVolumeReport = new LicenseVolumeReport();
        testVolumeReport.setId(TEST_LICENSE_VOLUME_REPORT_ID);
        testVolumeReport.setPackagingService(testPackagingService);
        testVolumeReport.setYear(2024);
        testVolumeReport.setInterval("Q1");
        testVolumeReport.setStatus(LicenseVolumeReport.Status.OPEN);

        Map<String, Object> reportTable = new HashMap<>();
        reportTable.put("data", "test");
        testVolumeReport.setReportTable(reportTable);
        testVolumeReport.setCreatedAt(Instant.now());
        testVolumeReport.setUpdatedAt(Instant.now());
        testVolumeReport.setDeletedAt(null);
        testVolumeReport = licenseVolumeReportRepository.save(testVolumeReport);

        // Create test Reason
        testReason = new Reason();
        testReason.setTitle("Test Decline Reason");
        testReason.setType(Reason.Type.TERMINATION);
        testReason.setCreatedAt(Instant.now());
        testReason.setUpdatedAt(Instant.now());
        testReason.setValue("test value");
        testReason = reasonRepository.save(testReason);
    }

    // ========== CREATE METHOD TESTS (POST /license-volume-report) ==========

    /**
     * Test create method - Success case
     * Tests: POST /license-volume-report with valid DTO
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void create_ShouldReturnCreatedReport_WhenValidDto() throws Exception {
        // Given
        CreateLicenseVolumeReportDto createDto = createTestCreateDto();

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post(LICENSE_VOLUME_REPORT)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.year").value(2024))
                .andExpect(jsonPath("$.interval").value("Q1"))
                .andExpect(jsonPath("$.status").value("OPEN"))
                .andExpect(jsonPath("$.report_table.data").value("test"))
                .andExpect(jsonPath("$.created_at").exists())
                .andExpect(jsonPath("$.updated_at").exists());
    }

    /**
     * Test create method - Invalid JSON
     * Tests: POST /license-volume-report with malformed JSON
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void create_ShouldReturnBadRequest_WhenInvalidJson() throws Exception {
        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post(LICENSE_VOLUME_REPORT)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content("\"invalid json\"")).andExpect(status().isBadRequest());
    }

    /**
     * Test create method - Missing content type
     * Tests: POST /license-volume-report without Content-Type header
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void create_ShouldReturnUnsupportedMediaType_WhenMissingContentType() throws Exception {
        // Given
        CreateLicenseVolumeReportDto createDto = createTestCreateDto();

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post(LICENSE_VOLUME_REPORT)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isUnsupportedMediaType());
    }

    /**
     * Test create method - Non-existent packaging service
     * Tests: POST /license-volume-report with invalid license_packaging_service_id
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void create_ShouldReturnNotFound_WhenPackagingServiceNotExists() throws Exception {
        // Given
        CreateLicenseVolumeReportDto createDto = createTestCreateDto();
        createDto.setLicensePackagingServiceId(999); // Non-existent ID

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post(LICENSE_VOLUME_REPORT)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto))).andExpect(status().isNotFound());
    }

    /**
     * Test create method - Null DTO fields
     * Tests: POST /license-volume-report with null required fields
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void create_ShouldReturnBadRequest_WhenDtoFieldsAreNull() throws Exception {
        // Given
        CreateLicenseVolumeReportDto createDto = new CreateLicenseVolumeReportDto();
        // Leave fields null

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post(LICENSE_VOLUME_REPORT)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto))).andExpect(status().isNotFound());
    }

    // ========== FIND ALL METHOD TESTS (GET /license-volume-report) ==========

    /**
     * Test findAll method - Success case
     * Tests: GET /license-volume-report
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findAll_ShouldReturnList_WhenReportsExist() throws Exception {
        // First create a test report
        testVolumeReport = createAndSaveTestReport();

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.get(LICENSE_VOLUME_REPORT))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.length()").value(2))
                .andExpect(jsonPath("$[0].year").value(2024))
                .andExpect(jsonPath("$[0].interval").value("Q1"))
                .andExpect(jsonPath("$[0].status").value("OPEN"));
    }

    /**
     * Test findAll method - Empty list
     * Tests: GET /license-volume-report when no reports exist
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findAll_ShouldReturnEmptyList_WhenNoReportsExist() throws Exception {
        licenseVolumeReportRepository.deleteAll();
        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.get(LICENSE_VOLUME_REPORT))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.length()").value(0));
    }

    // ========== FIND ONE METHOD TESTS (GET /license-volume-report/{id}) ==========

    /**
     * Test findOne method - Success case
     * Tests: GET /license-volume-report/1
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findOne_ShouldReturnReport_WhenReportExists() throws Exception {
        // First create a test report
        testVolumeReport = createAndSaveTestReport();

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.get(LICENSE_VOLUME_REPORT + "/{id}", testVolumeReport.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testVolumeReport.getId()))
                .andExpect(jsonPath("$.year").value(2024))
                .andExpect(jsonPath("$.interval").value("Q1"));
    }

    /**
     * Test findOne method - Report doesn't exist
     * Tests: GET /license-volume-report/999 (non-existent ID)
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findOne_ShouldReturnNotFound_WhenReportNotExists() throws Exception {
        // When & Then
        mockMvc.perform(get(LICENSE_VOLUME_REPORT + "/{id}", 999)).andExpect(status().isNotFound());
    }

    /**
     * Test findOne method - Invalid ID format
     * Tests: GET /license-volume-report/invalid-id
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findOne_ShouldReturnBadRequest_WhenInvalidIdFormat() throws Exception {
        // When & Then
        mockMvc.perform(get(LICENSE_VOLUME_REPORT + "/{id}", "invalid-id")).andExpect(status().isBadRequest());
    }

    /**
     * Test findOne method - Customer role accessing other customer's data
     * Tests: Authorization check for findOne endpoint
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findOne_ShouldReturnForbidden_WhenCustomerAccessesOtherCustomerData() throws Exception {
        // First create a test report
        testVolumeReport = createAndSaveTestReport();

        // Given - Mock different user
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails)
                .thenReturn(new AuthenticatedUser("999", Role.CUSTOMER, "<EMAIL>"));

        // When & Then
        mockMvc.perform(get(LICENSE_VOLUME_REPORT + "/{id}", testVolumeReport.getId()))
                .andExpect(status().isForbidden());
    }

    /**
     * Test findOne method - Admin role should have access
     * Tests: Authorization check for ADMIN role
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findOne_ShouldReturnReport_WhenAdminRole() throws Exception {
        // Given - Mock admin user
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails)
                .thenReturn(new AuthenticatedUser("999", Role.ADMIN, "<EMAIL>"));

        // First create a test report
        testVolumeReport = createAndSaveTestReport();

        // When & Then
        mockMvc.perform(get(LICENSE_VOLUME_REPORT + "/{id}", testVolumeReport.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testVolumeReport.getId()));
    }

    // ========== UPDATE METHOD TESTS (PUT /license-volume-report/{id}) ==========

    /**
     * Test update method - Success case
     * Tests: PUT /license-volume-report/1 with valid DTO
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void update_ShouldReturnUpdatedReport_WhenValidDto() throws Exception {
        UpdateLicenseVolumeReportDto updateDto = createTestUpdateDto();

        // When & Then
        mockMvc.perform(put(
                        LICENSE_VOLUME_REPORT + "/{id}",
                        testVolumeReport.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testVolumeReport.getId()))
                .andExpect(jsonPath("$.year").value(2025))
                .andExpect(jsonPath("$.interval").value("Q2"));
    }

    /**
     * Test update method - Non-existent report
     * Tests: PUT /license-volume-report/999 (non-existent ID)
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void update_ShouldReturnNotFound_WhenReportNotExists() throws Exception {
        // Given
        UpdateLicenseVolumeReportDto updateDto = createTestUpdateDto();

        // When & Then
        mockMvc.perform(put(LICENSE_VOLUME_REPORT + "/{id}", 999).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto))).andExpect(status().isNotFound());
    }

    /**
     * Test update method - Invalid JSON
     * Tests: PUT /license-volume-report/1 with malformed JSON
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void update_ShouldReturnBadRequest_WhenInvalidJson() throws Exception {
        // First create a test report
        testVolumeReport = createAndSaveTestReport();

        // When & Then
        mockMvc.perform(put(
                LICENSE_VOLUME_REPORT + "/{id}",
                testVolumeReport.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content("\"invalid json\"")).andExpect(status().isBadRequest());
    }

    /**
     * Test update method - Customer role accessing other customer's data
     * Tests: Authorization check for update endpoint
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void update_ShouldReturnForbidden_WhenCustomerAccessesOtherCustomerData() throws Exception {
        // First create a test report
        testVolumeReport = createAndSaveTestReport();

        // Given - Mock different user
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails)
                .thenReturn(new AuthenticatedUser("999", Role.CUSTOMER, "<EMAIL>"));

        UpdateLicenseVolumeReportDto updateDto = createTestUpdateDto();

        // When & Then
        mockMvc.perform(put(
                LICENSE_VOLUME_REPORT + "/{id}",
                testVolumeReport.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto))).andExpect(status().isForbidden());
    }

    // ========== DECLINE METHOD TESTS (POST /license-volume-report/{id}/decline) ==========

    /**
     * Test decline method - Success case
     * Tests: POST /license-volume-report/1/decline with valid DTO
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void decline_ShouldReturnDeclinedReport_WhenValidDto() throws Exception {
        DeclineLicenseVolumeReportDto declineDto = createTestDeclineDto();

        // When & Then
        mockMvc.perform(post(
                        LICENSE_VOLUME_REPORT + "/{id}/decline",
                        testVolumeReport.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(declineDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testVolumeReport.getId()))
                .andExpect(jsonPath("$.status").value("DECLINED"));
    }

    /**
     * Test decline method - Non-existent report
     * Tests: POST /license-volume-report/999/decline (non-existent ID)
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void decline_ShouldReturnNotFound_WhenReportNotExists() throws Exception {
        // Given
        DeclineLicenseVolumeReportDto declineDto = createTestDeclineDto();

        // When & Then
        mockMvc.perform(post(LICENSE_VOLUME_REPORT + "/{id}/decline", 999).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(declineDto))).andExpect(status().isNotFound());
    }

    /**
     * Test decline method - Invalid JSON
     * Tests: POST /license-volume-report/1/decline with malformed JSON
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void decline_ShouldReturnBadRequest_WhenInvalidJson() throws Exception {
        // First create a test report
        testVolumeReport = createAndSaveTestReport();

        // When & Then
        mockMvc.perform(post(
                LICENSE_VOLUME_REPORT + "/{id}/decline",
                testVolumeReport.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content("\"invalid json\"")).andExpect(status().isBadRequest());
    }

    /**
     * Test decline method - Empty reason IDs
     * Tests: POST /license-volume-report/1/decline with empty reason_ids
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void decline_ShouldReturnBadRequest_WhenEmptyReasonIds() throws Exception {
        // First create a test report
        testVolumeReport = createAndSaveTestReport();

        DeclineLicenseVolumeReportDto declineDto = createTestDeclineDto();
        declineDto.setReasonIds(List.of()); // Empty list

        // When & Then
        mockMvc.perform(post(
                        LICENSE_VOLUME_REPORT + "/{id}/decline",
                        testVolumeReport.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(declineDto)))
                .andExpect(status().isBadRequest());
    }

    /**
     * Test decline method - Invalid reason IDs
     * Tests: POST /license-volume-report/1/decline with non-existent reason IDs
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void decline_ShouldReturnBadRequest_WhenInvalidReasonIds() throws Exception {
        DeclineLicenseVolumeReportDto declineDto = createTestDeclineDto();
        declineDto.setReasonIds(Arrays.asList(999, 998)); // Non-existent IDs

        // When & Then
        mockMvc.perform(post(
                        LICENSE_VOLUME_REPORT + "/{id}/decline",
                        testVolumeReport.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(declineDto)))
                .andExpect(status().isBadRequest());
    }

    // ========== REMOVE METHOD TESTS (DELETE /license-volume-report/{id}) ==========

    /**
     * Test remove method - Success case (soft delete)
     * Tests: DELETE /license-volume-report/1
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void remove_ShouldReturnRemovedReport_WhenValidId() throws Exception {
        mockMvc.perform(delete(LICENSE_VOLUME_REPORT + "/{id}", testVolumeReport.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testVolumeReport.getId()))
                .andExpect(jsonPath("$.deleted_at").exists());
    }

    /**
     * Test remove method - Non-existent report
     * Tests: DELETE /license-volume-report/999 (non-existent ID)
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void remove_ShouldReturnNotFound_WhenReportNotExists() throws Exception {
        // When & Then
        mockMvc.perform(delete(LICENSE_VOLUME_REPORT + "/{id}", 999)).andExpect(status().isNotFound());
    }

    /**
     * Test remove method - Customer role accessing other customer's data
     * Tests: Authorization check for remove endpoint
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void remove_ShouldReturnForbidden_WhenCustomerAccessesOtherCustomerData() throws Exception {
        // First create a test report
        testVolumeReport = createAndSaveTestReport();

        // Given - Mock different user
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails)
                .thenReturn(new AuthenticatedUser("999", Role.CUSTOMER, "<EMAIL>"));

        // When & Then
        mockMvc.perform(delete(LICENSE_VOLUME_REPORT + "/{id}", testVolumeReport.getId()))
                .andExpect(status().isForbidden());
    }

    // ========== ROLE-BASED ACCESS TESTS ==========

    /**
     * Test with different user roles - SUPER_ADMIN
     * Tests: Authorization check for SUPER_ADMIN role
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findOne_ShouldWork_WithSuperAdminRole() throws Exception {
        // Given
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails)
                .thenReturn(new AuthenticatedUser("999", Role.SUPER_ADMIN, "<EMAIL>"));

        // First create a test report
        testVolumeReport = createAndSaveTestReport();

        // When & Then
        mockMvc.perform(get(LICENSE_VOLUME_REPORT + "/{id}", testVolumeReport.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testVolumeReport.getId()));
    }

    /**
     * Test with different user roles - CLERK
     * Tests: Authorization check for CLERK role
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void update_ShouldWork_WithClerkRole() throws Exception {
        // Given
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails)
                .thenReturn(new AuthenticatedUser("999", Role.CLERK, "<EMAIL>"));

        // First create a test report
        testVolumeReport = createAndSaveTestReport();

        UpdateLicenseVolumeReportDto updateDto = createTestUpdateDto();

        // When & Then
        mockMvc.perform(put(
                        LICENSE_VOLUME_REPORT + "/{id}",
                        testVolumeReport.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testVolumeReport.getId()));
    }

    // ========== EDGE CASE TESTS ==========

    /**
     * Test with negative ID values
     * Tests: GET /license-volume-report/-1
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findOne_ShouldReturnNotFound_WithNegativeId() throws Exception {
        // When & Then
        mockMvc.perform(get(LICENSE_VOLUME_REPORT + "/{id}", -1)).andExpect(status().isNotFound());
    }

    /**
     * Test with zero ID values
     * Tests: PUT /license-volume-report/0
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void update_ShouldReturnNotFound_WithZeroId() throws Exception {
        // Given
        UpdateLicenseVolumeReportDto updateDto = createTestUpdateDto();

        // When & Then
        mockMvc.perform(put(LICENSE_VOLUME_REPORT + "/{id}", 0).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto))).andExpect(status().isNotFound());
    }

    /**
     * Test with large ID values
     * Tests: DELETE /license-volume-report/********** (Integer.MAX_VALUE)
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void remove_ShouldReturnNotFound_WithLargeId() throws Exception {
        // When & Then
        mockMvc.perform(delete(LICENSE_VOLUME_REPORT + "/{id}", Integer.MAX_VALUE)).andExpect(status().isNotFound());
    }

    // ========== HELPER METHODS ==========

    /**
     * Helper method to create test CreateLicenseVolumeReportDto
     */
    private CreateLicenseVolumeReportDto createTestCreateDto() {
        CreateLicenseVolumeReportDto dto = new CreateLicenseVolumeReportDto();
        dto.setSetupReportSetFractionId(1);
        dto.setLicensePackagingServiceId(testPackagingService.getId());
        dto.setStatus(LicenseVolumeReport.Status.OPEN);
        dto.setYear(2024);
        dto.setInterval("Q1");

        Map<String, Object> reportTable = new HashMap<>();
        reportTable.put("data", "test");
        dto.setReportTable(reportTable);

        return dto;
    }

    /**
     * Helper method to create test UpdateLicenseVolumeReportDto
     */
    private UpdateLicenseVolumeReportDto createTestUpdateDto() {
        UpdateLicenseVolumeReportDto dto = new UpdateLicenseVolumeReportDto();
        dto.setStatus(LicenseVolumeReport.Status.APPROVED);
        dto.setYear(2025);
        dto.setInterval("Q2");

        Map<String, Object> reportTable = new HashMap<>();
        reportTable.put("data", "updated");
        dto.setReportTable(reportTable);

        return dto;
    }

    /**
     * Helper method to create test DeclineLicenseVolumeReportDto
     */
    private DeclineLicenseVolumeReportDto createTestDeclineDto() {
        DeclineLicenseVolumeReportDto dto = new DeclineLicenseVolumeReportDto();
        dto.setReasonIds(Collections.singletonList(testReason.getId()));
        dto.setTitle("Test decline reason");
        return dto;
    }

    /**
     * Helper method to create and save a test LicenseVolumeReport
     */
    private LicenseVolumeReport createAndSaveTestReport() {
        LicenseVolumeReport report = new LicenseVolumeReport();
        report.setPackagingService(testPackagingService);
        report.setStatus(LicenseVolumeReport.Status.OPEN);
        report.setYear(2024);
        report.setInterval("Q1");

        Map<String, Object> reportTable = new HashMap<>();
        reportTable.put("data", "test");
        report.setReportTable(reportTable);

        report.setCreatedAt(Instant.now());
        report.setUpdatedAt(Instant.now());

        return licenseVolumeReportRepository.save(report);
    }
}
