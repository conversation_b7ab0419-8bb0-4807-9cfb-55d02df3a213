package de.interzero.oneepr.admin.country_price_list;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.admin.country.Country;
import de.interzero.oneepr.admin.country.CountryRepository;
import de.interzero.oneepr.admin.country_price_list.dto.CreateCountryPriceListDto;
import de.interzero.oneepr.admin.price_list.PriceList;
import de.interzero.oneepr.admin.price_list.PriceListRepository;
import de.interzero.oneepr.common.string.Api;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.Year;
import java.time.temporal.ChronoUnit;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest
@AutoConfigureMockMvc
@Transactional
@WithMockUser(
        username = "101",
        roles = {"ADMIN"}
)
class CountryPriceListControllerTest {

    private static final String API_URL = Api.COUNTRY_PRICE_LISTS;

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private CountryPriceListRepository countryPriceListRepository;

    @Autowired
    private CountryRepository countryRepository;

    @Autowired
    private PriceListRepository priceListRepository;

    private Country testCountry;

    private PriceList testPriceList;

    private CountryPriceList association1;

    @BeforeEach
    void setUp() {
        testCountry = createAndSaveTestCountry("Germany", "DE");
        testPriceList = createAndSaveTestPriceList("EU License 2025");

        association1 = new CountryPriceList();
        association1.setCountry(testCountry);
        association1.setPriceList(testPriceList);
        association1.setCreatedAt(Instant.now());
        association1.setUpdatedAt(Instant.now());
        association1 = countryPriceListRepository.save(association1);

        Country anotherCountry = createAndSaveTestCountry("France", "FR");
        CountryPriceList association2 = new CountryPriceList();
        association2.setCountry(anotherCountry);
        association2.setPriceList(testPriceList);
        association2.setCreatedAt(Instant.now());
        association2.setUpdatedAt(Instant.now());
        countryPriceListRepository.save(association2);
    }

    @Test
    void findAll_shouldReturnAllActiveAssociations() throws Exception {
        mockMvc.perform(get(API_URL)).andExpect(status().isOk()).andExpect(jsonPath("$", hasSize(2)));
    }

    @Test
    void findOne_shouldReturnAssociation_whenFound() throws Exception {
        mockMvc.perform(get(API_URL + "/{id}", association1.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(association1.getId())))
                .andExpect(jsonPath("$.country_id", is(testCountry.getId())))
                .andExpect(jsonPath("$.price_list_id", is(testPriceList.getId())));
    }

    @Test
    void create_shouldCreateNewAssociationAndReturnIt() throws Exception {
        Country newCountry = createAndSaveTestCountry("Spain", "ES");
        CreateCountryPriceListDto createDto = new CreateCountryPriceListDto();
        createDto.setCountryId(newCountry.getId());
        createDto.setPriceListId(testPriceList.getId());

        mockMvc.perform(post(API_URL).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.country_id", is(newCountry.getId())))
                .andExpect(jsonPath("$.price_list_id", is(testPriceList.getId())));
    }

    @Test
    void remove_shouldSoftDeleteAssociation() throws Exception {
        mockMvc.perform(delete(API_URL + "/{id}", association1.getId())).andExpect(status().isOk());

        CountryPriceList deletedAssociation = countryPriceListRepository.findById(association1.getId()).orElseThrow();
        assertNotNull(deletedAssociation.getDeletedAt(), "deleted_at should be set after soft deletion");
    }

    @Test
    void findOne_shouldReturnNotFound_forInvalidId() throws Exception {
        mockMvc.perform(get(API_URL + "/{id}", 99999)).andExpect(status().isNotFound());
    }

    private Country createAndSaveTestCountry(String name,
                                             String code) {
        Country country = countryRepository.findByCode(code).orElse(new Country());
        country.setName(name);
        country.setCode(code);
        country.setFlagUrl("http://example.com/flag.png");
        country.setAuthorizeRepresentativeObligated(false);
        country.setOtherCostsObligated(true);
        country.setIsPublished(true);
        country.setCreatedAt(Instant.now());
        country.setUpdatedAt(Instant.now());
        return countryRepository.saveAndFlush(country);
    }

    private PriceList createAndSaveTestPriceList(String name) {
        PriceList priceList = new PriceList();
        priceList.setName(name);
        priceList.setDescription("Test Description");
        priceList.setStartDate(Instant.now());
        priceList.setEndDate(Instant.now().plus(365, ChronoUnit.DAYS));
        priceList.setBasicPrice(100);
        priceList.setType(PriceList.Type.EU_LICENSE);
        priceList.setConditionType(PriceList.ConditionType.LICENSE_YEAR);
        priceList.setConditionTypeValue(String.valueOf(Year.now().getValue()));
        priceList.setCreatedAt(Instant.now());
        priceList.setUpdatedAt(Instant.now());
        return priceListRepository.saveAndFlush(priceList);
    }
}