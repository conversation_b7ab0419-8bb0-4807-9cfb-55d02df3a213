package de.interzero.oneepr.admin.other_cost;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.admin.country.Country;
import de.interzero.oneepr.admin.country.CountryRepository;
import de.interzero.oneepr.admin.other_cost.dto.CreateOtherCostDto;
import de.interzero.oneepr.admin.other_cost.dto.UpdateOtherCostDto;
import de.interzero.oneepr.common.string.Api;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Integration tests for the {@link OtherCostController}.
 * This class validates the full HTTP request-response cycle for the other cost module.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class OtherCostControllerTest {

    private static final String API_URL = Api.ADMIN_OTHER_COSTS;

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private CountryRepository countryRepository;

    @Autowired
    private OtherCostRepository otherCostRepository;

    private Country testCountry;

    private OtherCost testOtherCost;

    /**
     * Sets up a consistent database state before each test method runs.
     * This data is automatically rolled back by the @Transactional annotation after each test.
     */
    @BeforeEach
    void setUp() {
        testCountry = createAndSaveTestCountry();
        testOtherCost = createAndSaveTestOtherCost(testCountry);
    }

    /**
     * Verifies that a POST request to {@link OtherCostController#create(CreateOtherCostDto)}
     * successfully creates a new other cost.
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = {"ADMIN"}
    )
    void create_shouldCreateNewOtherCost() throws Exception {
        CreateOtherCostDto createDto = new CreateOtherCostDto();
        createDto.setName("Handling Surcharge");
        createDto.setPrice(1500);
        createDto.setCountryId(testCountry.getId());

        mockMvc.perform(post(API_URL).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.name", is("Handling Surcharge")))
                .andExpect(jsonPath("$.price", is(1500)))
                .andExpect(jsonPath("$.country_id", is(testCountry.getId())));
    }

    /**
     * Verifies that a GET request to {@link OtherCostController#findAll()} returns a list of all active other costs.
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = {"ADMIN"}
    )
    void findAll_shouldReturnListOfOtherCosts() throws Exception {
        mockMvc.perform(get(API_URL))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].id", is(testOtherCost.getId())));
    }

    /**
     * Verifies that a GET request to {@link OtherCostController#findOne(Integer)} returns the correct other cost.
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = {"ADMIN"}
    )
    void findOne_shouldReturnCorrectOtherCost() throws Exception {
        mockMvc.perform(get(API_URL + "/{id}", testOtherCost.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testOtherCost.getId())))
                .andExpect(jsonPath("$.name", is("Setup Fee")));
    }

    /**
     * Verifies that a PUT request to {@link OtherCostController#update(Integer, UpdateOtherCostDto)}
     * successfully updates an existing other cost.
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = {"ADMIN"}
    )
    void update_shouldModifyExistingOtherCost() throws Exception {
        UpdateOtherCostDto updateDto = new UpdateOtherCostDto();
        updateDto.setName("Updated Setup Fee");
        updateDto.setPrice(5500);

        mockMvc.perform(put(API_URL + "/{id}", testOtherCost.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testOtherCost.getId())))
                .andExpect(jsonPath("$.name", is("Updated Setup Fee")))
                .andExpect(jsonPath("$.price", is(5500)));
    }

    /**
     * Verifies that a DELETE request to {@link OtherCostController#remove(Integer)}
     * successfully soft-deletes an other cost.
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = {"ADMIN"}
    )
    void remove_shouldSoftDeleteOtherCost() throws Exception {
        mockMvc.perform(delete(API_URL + "/{id}", testOtherCost.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.deleted_at").exists());

        // Verify it's gone from the "active" list
        mockMvc.perform(get(API_URL + "/{id}", testOtherCost.getId())).andExpect(status().isNotFound());

        // Verify in the database that the deleted_at field is set
        OtherCost deletedCost = otherCostRepository.findById(testOtherCost.getId()).orElseThrow();
        assertNotNull(deletedCost.getDeletedAt());
    }

    // --- Helper Methods for Test Setup ---

    /**
     * Creates and persists a valid {@link Country} entity for test setup.
     */
    private Country createAndSaveTestCountry() {
        Country country = new Country();
        country.setName("Testland");
        country.setCode("TL");
        country.setFlagUrl("http://example.com/flag.png");
        country.setCreatedAt(Instant.now());
        country.setUpdatedAt(Instant.now());
        return countryRepository.saveAndFlush(country);
    }

    /**
     * Creates and persists a valid {@link OtherCost} entity for test setup.
     */
    private OtherCost createAndSaveTestOtherCost(@NotNull Country country) {
        OtherCost otherCost = new OtherCost();
        otherCost.setName("Setup Fee");
        otherCost.setPrice(5000);
        otherCost.setCountry(country);
        return otherCostRepository.saveAndFlush(otherCost);
    }
}
