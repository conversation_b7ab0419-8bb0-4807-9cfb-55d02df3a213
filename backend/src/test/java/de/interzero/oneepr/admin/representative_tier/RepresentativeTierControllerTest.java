package de.interzero.oneepr.admin.representative_tier;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.admin.country.Country;
import de.interzero.oneepr.admin.country.CountryRepository;
import de.interzero.oneepr.admin.representative_tier.dto.CreateRepresentativeTierDto;
import de.interzero.oneepr.admin.representative_tier.dto.UpdateRepresentativeTierDto;
import de.interzero.oneepr.common.string.Api;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Integration tests for the {@link RepresentativeTierController}.
 * This class validates the full HTTP request-response cycle for the representative tier module.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class RepresentativeTierControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private RepresentativeTierRepository representativeTierRepository;

    @Autowired
    private CountryRepository countryRepository;

    private Country testCountry;

    private RepresentativeTier testTier;

    /**
     * Sets up a consistent and valid database state before each test method runs.
     */
    @BeforeEach
    void setUp() {
        // Clear repositories in reverse order of dependency
        representativeTierRepository.deleteAll();
        countryRepository.deleteAll();

        // Create prerequisite entities
        testCountry = createAndSaveTestCountry();
        testTier = createAndSaveTestRepresentativeTier(testCountry);
    }

    /**
     * Verifies that a POST request successfully creates a new representative tier.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void create_shouldCreateNewTier() throws Exception {
        CreateRepresentativeTierDto createDto = new CreateRepresentativeTierDto();
        createDto.setName("Premium Tier");
        createDto.setPrice(3000);
        createDto.setCountryId(testCountry.getId());

        mockMvc.perform(post(Api.ADMIN_REPRESENTATIVE_TIERS).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.name", is("Premium Tier")))
                .andExpect(jsonPath("$.price", is(3000)))
                .andExpect(jsonPath("$.country_id", is(testCountry.getId())));
    }

    /**
     * Verifies that a POST request with an invalid Country ID returns a Not Found error.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void create_withInvalidCountryId_shouldReturnNotFound() throws Exception {
        CreateRepresentativeTierDto createDto = new CreateRepresentativeTierDto();
        createDto.setName("Orphan Tier");
        createDto.setPrice(500);
        createDto.setCountryId(99999); // Non-existent ID

        mockMvc.perform(post(Api.ADMIN_REPRESENTATIVE_TIERS).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto))).andExpect(status().isNotFound());
    }

    /**
     * Verifies that a GET request returns all active representative tiers.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findAll_shouldReturnListOfTiers() throws Exception {
        mockMvc.perform(get(Api.ADMIN_REPRESENTATIVE_TIERS))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].id", is(testTier.getId())));
    }

    /**
     * Verifies that a GET request for a specific ID returns the correct tier.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findOne_shouldReturnCorrectTier() throws Exception {
        mockMvc.perform(get(Api.ADMIN_REPRESENTATIVE_TIERS + "/{id}", testTier.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testTier.getId())))
                .andExpect(jsonPath("$.name", is("Standard Tier")));
    }

    /**
     * Verifies that a PUT request successfully updates an existing tier.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void update_shouldModifyExistingTier() throws Exception {
        UpdateRepresentativeTierDto updateDto = new UpdateRepresentativeTierDto();
        updateDto.setName("Updated Standard Tier");
        updateDto.setPrice(1750);

        mockMvc.perform(put(
                        Api.ADMIN_REPRESENTATIVE_TIERS + "/{id}",
                        testTier.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testTier.getId())))
                .andExpect(jsonPath("$.name", is("Updated Standard Tier")))
                .andExpect(jsonPath("$.price", is(1750)));
    }

    /**
     * Verifies that a DELETE request correctly soft-deletes a tier.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void remove_shouldSoftDeleteTier() throws Exception {
        mockMvc.perform(delete(Api.ADMIN_REPRESENTATIVE_TIERS + "/{id}", testTier.getId())).andExpect(status().isOk());

        // Verify it's gone from the "active" list via the API
        mockMvc.perform(get(Api.ADMIN_REPRESENTATIVE_TIERS + "/{id}", testTier.getId()))
                .andExpect(status().isNotFound());

        // Verify in the database that the deleted_at field is now set
        RepresentativeTier deletedTier = representativeTierRepository.findById(testTier.getId()).orElseThrow();
        assertNotNull(deletedTier.getDeletedAt());
    }

    // --- Helper Methods for Test Setup ---

    private Country createAndSaveTestCountry() {
        Country country = new Country();
        country.setName("Testland");
        country.setCode("TL");
        country.setFlagUrl("http://example.com/flag.png");
        return countryRepository.saveAndFlush(country);
    }

    private RepresentativeTier createAndSaveTestRepresentativeTier(Country country) {
        RepresentativeTier tier = new RepresentativeTier();
        tier.setName("Standard Tier");
        tier.setPrice(1500);
        tier.setCountry(country);
        return representativeTierRepository.saveAndFlush(tier);
    }
}