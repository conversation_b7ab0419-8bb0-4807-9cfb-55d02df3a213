package de.interzero.oneepr.admin.settings;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.admin.settings.dto.UpsertSettingDto;
import de.interzero.oneepr.common.string.Api;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Integration tests for the {@link SettingsController}.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class SettingsControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private SettingsService settingsService;

    private Settings testSetting;

    private final String testKey = "test-setting";

    @BeforeEach
    void setUp() {
        testSetting = new Settings();
        testSetting.setId(1);
        testSetting.setKey(testKey);
        Map<String, Object> valueMap = new HashMap<>();
        valueMap.put("enabled", true);
        testSetting.setValue(valueMap);
        testSetting.setCreatedAt(Instant.now());
        testSetting.setUpdatedAt(Instant.now());
    }

    /**
     * Test for {@link SettingsController#findAll()}.
     * Verifies successful retrieval of all settings.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findAll_shouldReturnAllSettings() throws Exception {
        List<Settings> settingsList = Collections.singletonList(testSetting);
        when(settingsService.findAll()).thenReturn(settingsList);

        mockMvc.perform(get(Api.SETTINGS))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].key", is(testKey)));
    }

    /**
     * Test for {@link SettingsController#findOne(String)}.
     * Verifies successful retrieval of a single setting by its key.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findOne_shouldReturnSettingByKey() throws Exception {
        when(settingsService.findOne(testKey)).thenReturn(testSetting);

        mockMvc.perform(get(Api.SETTINGS + "/{key}", testKey))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.key", is(testKey)))
                .andExpect(jsonPath("$.value.enabled", is(true)));
    }

    /**
     * Test for {@link SettingsController#upsert(String, UpsertSettingDto)} for creating a new setting.
     * Verifies successful creation of a setting.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void upsert_whenCreating_shouldReturnNewSetting() throws Exception {
        UpsertSettingDto createDto = new UpsertSettingDto();
        createDto.setValue("{\"enabled\": false}");
        createDto.setTermOrConditionFileId("file-123");

        String newKey = "new-setting";
        Settings createdSetting = new Settings();
        createdSetting.setKey(newKey);
        createdSetting.setValue(new ObjectMapper().readValue(createDto.getValue(), Map.class));

        when(settingsService.upsert(eq(newKey), any(UpsertSettingDto.class))).thenReturn(createdSetting);

        mockMvc.perform(put(Api.SETTINGS + "/{key}", newKey).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.key", is(newKey)))
                .andExpect(jsonPath("$.value.enabled", is(false)));
    }

    /**
     * Test for {@link SettingsController#upsert(String, UpsertSettingDto)} for updating an existing setting.
     * Verifies successful update of a setting.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void upsert_whenUpdating_shouldReturnUpdatedSetting() throws Exception {
        UpsertSettingDto updateDto = new UpsertSettingDto();
        updateDto.setValue("{\"mode\": \"auto\"}");

        testSetting.setValue(new ObjectMapper().readValue(updateDto.getValue(), Map.class));

        when(settingsService.upsert(eq(testKey), any(UpsertSettingDto.class))).thenReturn(testSetting);

        mockMvc.perform(put(Api.SETTINGS + "/{key}", testKey).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.key", is(testKey)))
                .andExpect(jsonPath("$.value.mode", is("auto")));
    }


    /**
     * Test for {@link SettingsController#remove(String)}.
     * Verifies successful deletion of a setting by its key.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void remove_shouldDeleteSetting() throws Exception {
        doNothing().when(settingsService).remove(testKey);

        mockMvc.perform(delete(Api.SETTINGS + "/{key}", testKey)).andExpect(status().isOk());
    }
}
