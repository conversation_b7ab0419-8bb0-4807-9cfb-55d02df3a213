package de.interzero.oneepr.auth.user;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import de.interzero.oneepr.auth.auth.TokenService;
import de.interzero.oneepr.auth.auth.dto.LoginResponse;
import de.interzero.oneepr.auth.role.Role;
import de.interzero.oneepr.auth.role.RoleRepository;
import de.interzero.oneepr.auth.user.dto.*;
import de.interzero.oneepr.auth.user.dto.email.EmailCallbackDto;
import de.interzero.oneepr.auth.user.dto.email.EmailDto;
import de.interzero.oneepr.auth.user.dto.email.EmailResendTokenDto;
import de.interzero.oneepr.auth.user.dto.email.EmailTokenDto;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.TestRole;
import de.interzero.oneepr.customer.coupon.Coupon;
import de.interzero.oneepr.customer.coupon.CouponService;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import jakarta.transaction.Transactional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.containsString;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class UserControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private PasswordResetRequestRepository passwordResetRequestRepository;

    @Autowired
    private ChangeUserEmailRepository changeUserEmailRepository;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private CouponService couponService;

    @MockBean
    private JwtDecoder jwtDecoder;

    @MockBean
    private TokenService tokenService;

    @BeforeEach
    void setup() {
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        userRepository.deleteAll();
        when(couponService.create(any())).thenReturn(mock(Coupon.class));
    }


    /**
     * Integration test for {@link UserController(CreateUserDto, CreateUserParamsDto)}
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void create_shouldCreateUser() throws Exception {
        // given: CreateUserDto with valid user data
        CreateUserDto dto = new CreateUserDto();
        dto.setName("john.doe");
        dto.setEmail("<EMAIL>");
        dto.setPassword("securePassword123");
        dto.setRoleId(1);
        dto.setIsActive(true);
        dto.setType("CLIENT");

        CreateUserParamsDto paramsDto = new CreateUserParamsDto();
        paramsDto.setCreateByAdmin("true");

        String jsonRequest = objectMapper.writeValueAsString(dto);

        ResultActions result = mockMvc.perform(post(Api.USER).param("create_by_admin", paramsDto.getCreateByAdmin())
                                                       .contentType(MediaType.APPLICATION_JSON)
                                                       .content(jsonRequest)).andExpect(status().isOk()).andDo(print());

        String jsonResponse = result.andReturn().getResponse().getContentAsString();
        User createdUser = objectMapper.readValue(jsonResponse, User.class);

        assertNotNull(createdUser.getId());
        assertEquals(dto.getEmail(), createdUser.getEmail());
        assertEquals(dto.getName(), createdUser.getName());
        assertTrue(createdUser.getIsActive());
    }

    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findAllRoles_shouldReturnOnlyActiveNonDeletedRoles() throws Exception {
        Role clerk = roleRepository.findById(3).orElseThrow();
        clerk.setIsActive(false);
        clerk.setDeletedAt(Instant.now());
        roleRepository.save(clerk);

        ResultActions result = mockMvc.perform(get(Api.USER + "/all-roles").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andDo(print());

        String jsonResponse = result.andReturn().getResponse().getContentAsString();

        assertTrue(jsonResponse.contains("SUPER_ADMIN"));
        assertTrue(jsonResponse.contains("ADMIN"));
        assertTrue(jsonResponse.contains("CUSTOMER"));
        assertFalse(jsonResponse.contains("CLERK"));
    }

    /**
     * Integration test for {@link UserController#findAll(String, String, String, String)}.
     * This test verifies that the endpoint returns filtered users based on role, activity status and name.
     *
     * @throws Exception if the request fails
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findAll_shouldFilterUsersByRoleAndStatusAndName() throws Exception {
        Role adminRole = roleRepository.findByName("ADMIN").orElseThrow();

        User user = new User();
        user.setEmail("<EMAIL>");
        user.setName("admin");
        user.setPassword("secret");
        user.setRole(adminRole);
        user.setIsActive(true);
        user.setStatus(User.Status.COMPLETE);

        userRepository.save(user);

        ResultActions result = mockMvc.perform(get(Api.USER).param("role", "admin")
                                                       .param("is_active", "true")
                                                       .param("name", "admin")
                                                       .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andDo(print());

        String jsonResponse = result.andReturn().getResponse().getContentAsString();
        assertTrue(jsonResponse.contains("<EMAIL>"));
        assertFalse(jsonResponse.contains("<EMAIL>"));
        assertFalse(jsonResponse.contains("<EMAIL>"));
    }

    /**
     * Integration test for {@link UserController#findAll(String, String, String, String)}.
     * This test verifies that the endpoint returns filtered users based on name.
     *
     * @throws Exception if the request fails
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findAll_shouldFilterUsersByName() throws Exception {
        Role adminRole = roleRepository.findByName("ADMIN").orElseThrow();

        User user = new User();
        user.setEmail("<EMAIL>");
        user.setName("admin");
        user.setPassword(passwordEncoder.encode("pass"));
        user.setRole(adminRole);
        user.setIsActive(true);
        user.setStatus(User.Status.COMPLETE);

        User user2 = new User();
        user2.setEmail("<EMAIL>");
        user2.setName("admin 2");
        user2.setPassword(passwordEncoder.encode("pass"));
        user2.setRole(adminRole);
        user2.setIsActive(true);
        user2.setStatus(User.Status.COMPLETE);

        userRepository.saveAll(List.of(user, user2));

        ResultActions result = mockMvc.perform(get(Api.USER).param("name", "admin")
                                                       .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andDo(print());

        String jsonResponse = result.andReturn().getResponse().getContentAsString();
        assertTrue(jsonResponse.contains("<EMAIL>"));
        assertTrue(jsonResponse.contains("<EMAIL>"));
        assertFalse(jsonResponse.contains("<EMAIL>"));
    }

    /**
     * Integration test for {@link UserController#findAll(String, String, String, String)}.
     * This test verifies that the endpoint returns all users.
     *
     * @throws Exception if the request fails
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findAll_shouldGetAllUsers() throws Exception {
        Role adminRole = roleRepository.findByName("ADMIN").orElseThrow();
        Role customerRole = roleRepository.findByName("CUSTOMER").orElseThrow();

        User user = new User();
        user.setEmail("<EMAIL>");
        user.setName("admin");
        user.setPassword(passwordEncoder.encode("pass"));
        user.setRole(adminRole);
        user.setIsActive(true);
        user.setStatus(User.Status.COMPLETE);

        User user2 = new User();
        user2.setEmail("<EMAIL>");
        user2.setName("Customer");
        user2.setPassword(passwordEncoder.encode("pass"));
        user2.setRole(customerRole);
        user2.setIsActive(true);
        user2.setStatus(User.Status.COMPLETE);

        userRepository.saveAll(List.of(user, user2));

        ResultActions result = mockMvc.perform(get(Api.USER).contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andDo(print());

        String jsonResponse = result.andReturn().getResponse().getContentAsString();
        assertTrue(jsonResponse.contains("<EMAIL>"));
        assertTrue(jsonResponse.contains("<EMAIL>"));
    }

    /**
     * {@link UserController#findOne(String)} should return user by ID.
     *
     * @throws Exception if request fails
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findOne_shouldReturnUserById() throws Exception {
        Role adminRole = roleRepository.findByName("ADMIN").orElseThrow();

        User user = new User();
        user.setEmail("<EMAIL>");
        user.setName("byidadmin");
        user.setPassword("secret");
        user.setRole(adminRole);
        user.setIsActive(true);
        user.setStatus(User.Status.COMPLETE);

        User saved = userRepository.save(user);

        ResultActions result = mockMvc.perform(get(Api.USER + "/" + saved.getId()).contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andDo(print());

        String jsonResponse = result.andReturn().getResponse().getContentAsString();

        assertTrue(jsonResponse.contains("<EMAIL>"));
        assertFalse(jsonResponse.contains("<EMAIL>"));
    }

    /**
     * {@link UserController#findOne(String)} should return user by email.
     *
     * @throws Exception if request fails
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findOne_shouldReturnUserByEmail() throws Exception {
        Role adminRole = roleRepository.findByName("ADMIN").orElseThrow();

        User user = new User();
        user.setEmail("<EMAIL>");
        user.setName("byemailadmin");
        user.setPassword("secret");
        user.setRole(adminRole);
        user.setIsActive(true);
        user.setStatus(User.Status.COMPLETE);

        userRepository.save(user);

        ResultActions result = mockMvc.perform(get(Api.USER + "/<EMAIL>").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andDo(print());

        String jsonResponse = result.andReturn().getResponse().getContentAsString();

        assertTrue(jsonResponse.contains("<EMAIL>"));
    }

    /**
     * {@link UserController#findStatusByEmail(String)} should return user status and password flag.
     *
     * @throws Exception if request fails
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findStatusByEmail_shouldReturnUserStatusAndHasPassword() throws Exception {
        Role customerRole = roleRepository.findByName("CUSTOMER").orElseThrow();

        User user = new User();
        user.setEmail("<EMAIL>");
        user.setName("statusUser");
        user.setPassword("hashed-password");
        user.setRole(customerRole);
        user.setIsActive(true);
        user.setStatus(User.Status.COMPLETE);

        userRepository.save(user);

        mockMvc.perform(get(Api.USER + "/status/<EMAIL>").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("COMPLETE"))
                .andExpect(jsonPath("$.has_password").value(true))
                .andDo(print());
    }

    /**
     * {@link UserController#findStatusByEmail(String)} should return 404 if user is inactive.
     *
     * @throws Exception if request fails
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findStatusByEmail_shouldReturnNotFoundForInactiveUser() throws Exception {
        Role customerRole = roleRepository.findByName("CUSTOMER").orElseThrow();

        User user = new User();
        user.setEmail("<EMAIL>");
        user.setName("inactiveUser");
        user.setPassword("secret");
        user.setRole(customerRole);
        user.setIsActive(false);
        user.setStatus(User.Status.COMPLETE);

        userRepository.save(user);

        mockMvc.perform(get(Api.USER + "/status/<EMAIL>").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound())
                .andDo(print());
    }

    /**
     * {@link UserController#update(Integer, UpdateUserDto)} should update user fields and return updated data.
     *
     * @throws Exception if request fails
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void update_shouldUpdateUserFields() throws Exception {
        Role adminRole = roleRepository.findByName("CUSTOMER").orElseThrow();

        User user = new User();
        user.setEmail("<EMAIL>");
        user.setName("originalName");
        user.setPassword("secret");
        user.setRole(adminRole);
        user.setIsActive(true);
        user.setStatus(User.Status.COMPLETE);

        User saved = userRepository.save(user);

        UpdateUserDto updateDto = new UpdateUserDto();
        updateDto.setEmail("<EMAIL>");
        updateDto.setName("updatedName");
        updateDto.setIsActive(false);

        String jsonRequest = objectMapper.writeValueAsString(updateDto);

        ResultActions result = mockMvc.perform(patch(Api.USER + "/" + saved.getId()).contentType(MediaType.APPLICATION_JSON)
                                                       .content(jsonRequest))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.name").value("updatedName"))
                .andExpect(jsonPath("$.is_active").value(false))
                .andExpect(jsonPath("$.password").doesNotExist())
                .andDo(print());

        String jsonResponse = result.andReturn().getResponse().getContentAsString();
        assertFalse(jsonResponse.contains("<EMAIL>"));
        assertFalse(jsonResponse.contains("originalName"));
    }

    /**
     * {@link UserController#updateByEmail(String, UpdateUserDto)} should update user fields by email and return updated data.
     *
     * @throws Exception if request fails
     */
    @Test
    @WithMockUser(roles = {TestRole.CUSTOMER})
    void updateByEmail_shouldUpdateUserFieldsByEmail() throws Exception {
        Role adminRole = roleRepository.findByName("CUSTOMER").orElseThrow();

        User user = new User();
        user.setEmail("<EMAIL>");
        user.setName("originalName");
        user.setPassword("secret");
        user.setRole(adminRole);
        user.setIsActive(true);
        user.setStatus(User.Status.COMPLETE);

        User saved = userRepository.save(user);

        UpdateUserDto dto = new UpdateUserDto();
        dto.setName("newName");
        dto.setIsActive(false);

        String json = objectMapper.writeValueAsString(dto);

        ResultActions result = mockMvc.perform(patch(Api.USER + "/by-email/" + saved.getEmail()).contentType(MediaType.APPLICATION_JSON)
                                                       .content(json))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.name").value("newName"))
                .andExpect(jsonPath("$.is_active").value(false))
                .andExpect(jsonPath("$.password").doesNotExist())
                .andDo(print());

        String jsonResponse = result.andReturn().getResponse().getContentAsString();
        assertFalse(jsonResponse.contains("originalName"));
    }

    /**
     * Test for {@link UserController#updatePassword(Integer, UpdatePasswordDto)}.
     * <p>
     * Verifies that a valid password change request correctly updates the user's password.
     */
    @Test
    @WithMockUser(roles = {TestRole.CUSTOMER})
    void updatePassword_shouldChangePasswordSuccessfully() throws Exception {
        Role role = roleRepository.findByName("CUSTOMER").orElseThrow();

        User user = new User();
        user.setEmail("<EMAIL>");
        user.setName("changeUser");
        user.setPassword(passwordEncoder.encode("oldPassword"));
        user.setRole(role);
        user.setIsActive(true);
        user.setStatus(User.Status.COMPLETE);

        User saved = userRepository.save(user);

        UpdatePasswordDto dto = new UpdatePasswordDto();
        dto.setOldPassword("oldPassword");
        dto.setNewPassword("newSecurePassword123!");

        String json = objectMapper.writeValueAsString(dto);

        mockMvc.perform(patch(Api.USER + "/change-password/" + saved.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(json))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.is_active").value(true))
                .andExpect(jsonPath("$.name").value("changeUser"))
                .andExpect(jsonPath("$.role_id").value(1))
                .andDo(print());
    }

    /**
     * Test for {@link UserController#updatePassword(Integer, UpdatePasswordDto)}.
     * <p>
     * Verifies that an incorrect old password results in 401 Unauthorized error.
     */
    @Test
    @WithMockUser(roles = {TestRole.CUSTOMER})
    void updatePassword_shouldFailIfOldPasswordIsWrong() throws Exception {
        Role role = roleRepository.findByName("CUSTOMER").orElseThrow();

        User user = new User();
        user.setEmail("<EMAIL>");
        user.setName("failUser");
        user.setPassword(passwordEncoder.encode("correctPassword"));
        user.setRole(role);
        user.setIsActive(true);
        user.setStatus(User.Status.COMPLETE);

        User saved = userRepository.save(user);

        UpdatePasswordDto dto = new UpdatePasswordDto();
        dto.setOldPassword("incorrectPassword");
        dto.setNewPassword("newPass");

        String json = objectMapper.writeValueAsString(dto);

        mockMvc.perform(patch(Api.USER + "/change-password/" + saved.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(json)).andExpect(status().isUnauthorized());
    }

    /**
     * Test for {@link UserController#updateEmail(Integer, UpdateEmailDto)}.
     * <p>
     * Verifies that a valid email update request stores a change request and sends a verification email.
     */
    @Test
    @WithMockUser(roles = {TestRole.CUSTOMER})
    void updateEmail_shouldSendEmailAndSaveRequest() throws Exception {
        Role adminRole = roleRepository.findByName("CUSTOMER").orElseThrow();

        User user = new User();
        user.setEmail("<EMAIL>");
        user.setName("emailChangeUser");
        user.setPassword(passwordEncoder.encode("securePass123"));
        user.setRole(adminRole);
        user.setIsActive(true);
        user.setStatus(User.Status.COMPLETE);

        User saved = userRepository.save(user);

        UpdateEmailDto dto = new UpdateEmailDto();
        dto.setNewEmail("<EMAIL>");
        dto.setPassword("securePass123");

        String json = objectMapper.writeValueAsString(dto);

        mockMvc.perform(patch(Api.USER + "/change-email/" + saved.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(json))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("Email exchange request made successfully"))
                .andDo(print());
    }

    /**
     * Test for {@link UserController#updateEmail(Integer, UpdateEmailDto)}.
     * <p>
     * Verifies that providing an incorrect current password returns 401 Unauthorized.
     */
    @Test
    @WithMockUser(roles = {TestRole.CUSTOMER})
    void updateEmail_shouldReturnUnauthorizedIfPasswordIncorrect() throws Exception {
        Role role = roleRepository.findByName("CUSTOMER").orElseThrow();

        User user = new User();
        user.setEmail("<EMAIL>");
        user.setName("wrongPassUser");
        user.setPassword(passwordEncoder.encode("correctPassword"));
        user.setRole(role);
        user.setIsActive(true);
        user.setStatus(User.Status.COMPLETE);

        userRepository.save(user);

        UpdateEmailDto dto = new UpdateEmailDto();
        dto.setNewEmail("<EMAIL>");
        dto.setPassword("wrongPassword");

        String json = objectMapper.writeValueAsString(dto);

        mockMvc.perform(patch(Api.USER + "/change-email/" + user.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(json)).andExpect(status().isUnauthorized());
    }

    /**
     * Test for {@link UserController#updateEmail(Integer, UpdateEmailDto)}.
     * <p>
     * Verifies that updating to an already existing email returns 409 Conflict.
     */
    @Test
    @WithMockUser(roles = {TestRole.CUSTOMER})
    void updateEmail_shouldReturnConflictIfEmailAlreadyExists() throws Exception {
        Role role = roleRepository.findByName("CUSTOMER").orElseThrow();

        User user1 = new User();
        user1.setEmail("<EMAIL>");
        user1.setName("ExistingUser");
        user1.setPassword("dummy");
        user1.setRole(role);
        user1.setIsActive(true);
        userRepository.save(user1);

        User user2 = new User();
        user2.setEmail("<EMAIL>");
        user2.setName("conflictUser");
        user2.setPassword(passwordEncoder.encode("validPass"));
        user2.setRole(role);
        user2.setIsActive(true);
        user2.setStatus(User.Status.COMPLETE);
        user2 = userRepository.save(user2);

        UpdateEmailDto dto = new UpdateEmailDto();
        dto.setNewEmail("<EMAIL>");
        dto.setPassword("validPass");

        String json = objectMapper.writeValueAsString(dto);

        mockMvc.perform(patch(Api.USER + "/change-email/" + user2.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(json)).andExpect(status().isConflict());
    }

    /**
     * Test for {@link UserController#updateVerifyEmail(Integer, UpdateEmailVerifyDto)}.
     * <p>
     * Verifies that a correct token successfully confirms the email change and updates user email.
     */
    @Test
    @WithMockUser(roles = {TestRole.CUSTOMER})
    void updateVerifyEmail_shouldConfirmAndChangeEmail() throws Exception {
        Role customerRole = roleRepository.findByName("CUSTOMER").orElseThrow();

        User user = new User();
        user.setEmail("<EMAIL>");
        user.setName("UserToVerify");
        user.setPassword(passwordEncoder.encode("oldPassword"));
        user.setRole(customerRole);
        user.setIsActive(true);
        user.setStatus(User.Status.COMPLETE);
        user = userRepository.save(user);

        Customer customer = new Customer();
        customer.setFirstName("Test");
        customer.setLastName("Customer");
        customer.setEmail("<EMAIL>");
        customer.setUserId(user.getId());
        customer.setIsActive(true);
        customer.setCreatedAt(Instant.now());
        customer.setUpdatedAt(Instant.now());
        customer.setCompanies(new ArrayList<>());
        customer.setPhones(new ArrayList<>());
        customer.setContracts(new ArrayList<>());
        customerRepository.save(customer);

        String rawToken = "token123";
        String hashedToken = passwordEncoder.encode(rawToken);

        ChangeUserEmail changeRequest = new ChangeUserEmail();
        changeRequest.setUser(user);
        changeRequest.setNewEmail("<EMAIL>");
        changeRequest.setToken(hashedToken);
        changeUserEmailRepository.save(changeRequest);

        UpdateEmailVerifyDto dto = new UpdateEmailVerifyDto();
        dto.setToken(rawToken);

        String json = objectMapper.writeValueAsString(dto);

        mockMvc.perform(patch(Api.USER + "/change-email/verify/" + user.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(json))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.id").value(user.getId()))
                .andDo(print());
    }

    /**
     * Test for {@link UserController#updateVerifyEmail(Integer, UpdateEmailVerifyDto)}.
     * <p>
     * Verifies that an email with an incorrect token fails with HTTP 401 Unauthorized.
     */
    @Test
    @WithMockUser(roles = {TestRole.CUSTOMER})
    void updateVerifyEmail_shouldFailIfTokenIncorrect() throws Exception {
        Role customerRole = roleRepository.findByName("CUSTOMER").orElseThrow();

        User user = new User();
        user.setEmail("<EMAIL>");
        user.setName("UserInvalidToken");
        user.setPassword(passwordEncoder.encode("testPass"));
        user.setRole(customerRole);
        user.setIsActive(true);
        user.setStatus(User.Status.COMPLETE);
        user = userRepository.save(user);

        ChangeUserEmail changeRequest = new ChangeUserEmail();
        changeRequest.setUser(user);
        changeRequest.setNewEmail("<EMAIL>");
        changeRequest.setToken(passwordEncoder.encode("correctToken"));
        changeUserEmailRepository.save(changeRequest);

        UpdateEmailVerifyDto dto = new UpdateEmailVerifyDto();
        dto.setToken("wrongToken");
        String json = objectMapper.writeValueAsString(dto);

        mockMvc.perform(patch(Api.USER + "/change-email/verify/" + user.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(json)).andExpect(status().isForbidden());
    }

    /**
     * Test for {@link UserController#updateVerifyEmail(Integer, UpdateEmailVerifyDto)}.
     * <p>
     * Verifies that an email fails with HTTP 409 Conflict if the new email already exists in the system.
     */
    @Test
    @WithMockUser(roles = {TestRole.CUSTOMER})
    void updateVerifyEmail_shouldFailIfEmailAlreadyExists() throws Exception {
        Role customerRole = roleRepository.findByName("CUSTOMER").orElseThrow();

        User existing = new User();
        existing.setEmail("<EMAIL>");
        existing.setName("Existing");
        existing.setPassword("x");
        existing.setRole(customerRole);
        existing.setIsActive(true);
        existing.setStatus(User.Status.COMPLETE);
        userRepository.save(existing);

        User user = new User();
        user.setEmail("<EMAIL>");
        user.setName("WillConflict");
        user.setPassword(passwordEncoder.encode("pass"));
        user.setRole(customerRole);
        user.setIsActive(true);
        user.setStatus(User.Status.COMPLETE);
        user = userRepository.save(user);

        ChangeUserEmail changeRequest = new ChangeUserEmail();
        changeRequest.setUser(user);
        changeRequest.setNewEmail("<EMAIL>");
        changeRequest.setToken(passwordEncoder.encode("tokenABC"));
        changeUserEmailRepository.save(changeRequest);

        UpdateEmailVerifyDto dto = new UpdateEmailVerifyDto();
        dto.setToken("tokenABC");
        String json = objectMapper.writeValueAsString(dto);

        mockMvc.perform(patch(Api.USER + "/change-email/verify/" + user.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(json)).andExpect(status().isConflict());
    }

    /**
     * Test for {@link UserController#updateVerifyEmail(Integer, UpdateEmailVerifyDto)}.
     * <p>
     * Verifies that an email fails with HTTP 404 Not Found if no change request is found for the user.
     */
    @Test
    @WithMockUser(roles = {TestRole.CUSTOMER})
    void updateVerifyEmail_shouldFailIfNoChangeRequest() throws Exception {
        Role customerRole = roleRepository.findByName("CUSTOMER").orElseThrow();

        User user = new User();
        user.setEmail("<EMAIL>");
        user.setName("NoChangeRequest");
        user.setPassword(passwordEncoder.encode("pass"));
        user.setRole(customerRole);
        user.setIsActive(true);
        user.setStatus(User.Status.COMPLETE);
        user = userRepository.save(user);


        UpdateEmailVerifyDto dto = new UpdateEmailVerifyDto();
        dto.setToken("anyToken");
        String json = objectMapper.writeValueAsString(dto);

        mockMvc.perform(patch(Api.USER + "/change-email/verify/" + user.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(json)).andExpect(status().isNotFound());
    }

    /**
     * Test for {@link UserController#remove(Integer)}.
     * <p>
     * Verifies that deleting an existing user returns HTTP 204 No Content.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void deleteUser_shouldReturnNoContentIfUserExists() throws Exception {
        Role adminRole = roleRepository.findByName("ADMIN").orElseThrow();

        User user = new User();
        user.setEmail("<EMAIL>");
        user.setName("ToDelete");
        user.setPassword("dummy");
        user.setRole(adminRole);
        user.setIsActive(true);
        user.setStatus(User.Status.COMPLETE);
        user = userRepository.save(user);

        mockMvc.perform(delete(Api.USER + "/" + user.getId())).andExpect(status().isNoContent());

        assertFalse(userRepository.existsById(user.getId()));
    }

    /**
     * Test for {@link UserController#requestPasswordReset(RequestPasswordResetDto)}.
     * <p>
     * Verifies that a valid email triggers password reset email and returns HTTP 200 with success response.
     */
    @Test
    @WithMockUser(roles = {TestRole.CUSTOMER})
    void requestPasswordReset_shouldSendResetEmailIfUserExists() throws Exception {
        Role role = roleRepository.findByName("CUSTOMER").orElseThrow();

        User user = new User();
        user.setEmail("<EMAIL>");
        user.setName("ResetMe");
        user.setPassword(passwordEncoder.encode("pass123"));
        user.setRole(role);
        user.setIsActive(true);
        user.setStatus(User.Status.COMPLETE);
        userRepository.save(user);

        RequestPasswordResetDto dto = new RequestPasswordResetDto();
        dto.setEmail("<EMAIL>");
        dto.setCallbackUrl("https://example.com/reset");

        String json = objectMapper.writeValueAsString(dto);

        mockMvc.perform(post(Api.USER + "/request/password").contentType(MediaType.APPLICATION_JSON).content(json))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Email sent successfully"));
    }

    /**
     * Test for {@link UserController#requestPasswordReset(RequestPasswordResetDto)}.
     * <p>
     * Verifies that requesting password reset for a non-existent email returns HTTP 404.
     */
    @Test
    @WithMockUser(roles = {TestRole.CUSTOMER})
    void requestPasswordReset_shouldReturnNotFoundIfEmailDoesNotExist() throws Exception {
        RequestPasswordResetDto dto = new RequestPasswordResetDto();
        dto.setEmail("<EMAIL>");
        dto.setCallbackUrl("https://example.com/reset");

        String json = objectMapper.writeValueAsString(dto);

        mockMvc.perform(post(Api.USER + "/request/password").contentType(MediaType.APPLICATION_JSON).content(json))
                .andExpect(status().isNotFound());
    }

    /**
     * Test for {@link UserController#resetPassword(ResetPasswordDto)}.
     * <p>
     * Verifies that a valid PASSWORD_RESET token resets the password and returns updated user without password.
     */
    @Test
    @WithMockUser(roles = {TestRole.CUSTOMER})
    void resetPassword_shouldResetPasswordSuccessfullyWithPasswordResetToken() throws Exception {
        Role role = roleRepository.findByName("CUSTOMER").orElseThrow();

        User user = new User();
        user.setEmail("<EMAIL>");
        user.setName("PasswordResetUser");
        user.setPassword(passwordEncoder.encode("oldPassword"));
        user.setRole(role);
        user.setIsActive(true);
        user.setStatus(User.Status.COMPLETE);
        user = userRepository.save(user);

        String rawToken = "reset-token-123";

        PasswordResetRequest request = new PasswordResetRequest();
        request.setUser(user);
        request.setToken(rawToken);
        request.setEmail(user.getEmail());
        request.setCallbackUrl("https://example.com/reset");
        request.setStatus("PENDING");
        request.setExpiresAt(Instant.now().plus(1, ChronoUnit.HOURS));
        passwordResetRequestRepository.save(request);

        ResetPasswordDto dto = new ResetPasswordDto();
        dto.setToken(rawToken);
        dto.setPassword("newStrongPassword");
        dto.setType("PASSWORD_RESET");

        String json = objectMapper.writeValueAsString(dto);

        mockMvc.perform(post(Api.USER + "/reset/password").contentType(MediaType.APPLICATION_JSON).content(json))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.password").doesNotExist());
    }

    /**
     * Test for {@link UserController#resetPassword(ResetPasswordDto)}.
     * <p>
     * Verifies that an expired PASSWORD_RESET token returns HTTP 400 Bad Request.
     */
    @Test
    @WithMockUser(roles = {TestRole.CUSTOMER})
    void resetPassword_shouldFailIfTokenExpired() throws Exception {
        Role role = roleRepository.findByName("CUSTOMER").orElseThrow();

        User user = new User();
        user.setEmail("<EMAIL>");
        user.setName("ExpiredTokenUser");
        user.setPassword(passwordEncoder.encode("expiredPassword"));
        user.setRole(role);
        user.setIsActive(true);
        user.setStatus(User.Status.COMPLETE);
        user = userRepository.save(user);

        PasswordResetRequest request = new PasswordResetRequest();
        request.setUser(user);
        request.setToken("expired-token");
        request.setEmail(user.getEmail());
        request.setCallbackUrl("https://example.com/reset");
        request.setStatus("PENDING");
        request.setExpiresAt(Instant.now().minus(1, ChronoUnit.HOURS)); // expired
        passwordResetRequestRepository.save(request);

        ResetPasswordDto dto = new ResetPasswordDto();
        dto.setToken("expired-token");
        dto.setPassword("newPassword");
        dto.setType("PASSWORD_RESET");

        String json = objectMapper.writeValueAsString(dto);

        mockMvc.perform(post(Api.USER + "/reset/password").contentType(MediaType.APPLICATION_JSON).content(json))
                .andExpect(status().isBadRequest());
    }

    /**
     * Test for {@link UserController#requestAccess(RequestAccessDto)}.
     * <p>
     * Verifies that a valid email triggers a successful access request with token and callback URL.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void requestAccess_shouldReturnTokenAndCallbackUrl_whenEmailIsProvided() throws Exception {
        Role role = roleRepository.findByName("CUSTOMER").orElseThrow();

        User user = new User();
        user.setEmail("<EMAIL>");
        user.setName("AccessRequestUser");
        user.setPassword(passwordEncoder.encode("somePassword"));
        user.setRole(role);
        user.setIsActive(true);
        user.setStatus(User.Status.COMPLETE);
        user = userRepository.save(user);

        RequestAccessDto dto = new RequestAccessDto();
        dto.setEmail(user.getEmail());
        dto.setCallbackUrl("https://callback.url/reset");

        String json = objectMapper.writeValueAsString(dto);

        mockMvc.perform(post(Api.USER + "/request/access").contentType(MediaType.APPLICATION_JSON).content(json))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Email sent"))
                .andExpect(jsonPath("$.callbackUrl").value(containsString("token=")))
                .andExpect(jsonPath("$.token").isNotEmpty());
    }

    /**
     * Test for {@link UserController#requestAccess(RequestAccessDto)}.
     * <p>
     * Verifies that the method returns HTTP 400 if both email and userId are missing.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void requestAccess_shouldFailIfNoEmailOrUserIdProvided() throws Exception {
        RequestAccessDto dto = new RequestAccessDto();
        dto.setCallbackUrl("https://callback.url/reset");

        String json = objectMapper.writeValueAsString(dto);

        mockMvc.perform(post(Api.USER + "/request/access").contentType(MediaType.APPLICATION_JSON).content(json))
                .andExpect(status().isBadRequest());
    }

    /**
     * Test for {@link UserController#requestAccess(RequestAccessDto)}.
     * <p>
     * Verifies that the method returns HTTP 404 if no user found for given email or userId.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void requestAccess_shouldFailIfUserNotFound() throws Exception {
        RequestAccessDto dto = new RequestAccessDto();
        dto.setEmail("<EMAIL>");
        dto.setCallbackUrl("https://callback.url/reset");

        String json = objectMapper.writeValueAsString(dto);

        mockMvc.perform(post(Api.USER + "/request/access").contentType(MediaType.APPLICATION_JSON).content(json))
                .andExpect(status().isNotFound());
    }

    /**
     * Test for {@link UserController#sendVerificationToken(EmailCallbackDto)}.
     * <p>
     * Verifies that a verification token is generated and sent via email for an active user,
     * and that a 200 OK response is returned with the expected success message.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void sendVerificationToken_shouldSendMagicLinkAndUpdateUser() throws Exception {
        Role customerRole = roleRepository.findByName("CUSTOMER").orElseThrow();

        User user = new User();
        user.setEmail("<EMAIL>");
        user.setName("UserToVerify");
        user.setPassword(passwordEncoder.encode("password"));
        user.setRole(customerRole);
        user.setIsActive(true);
        user.setStatus(User.Status.COMPLETE);
        user = userRepository.save(user);

        EmailCallbackDto dto = new EmailCallbackDto();
        dto.setEmail("<EMAIL>");
        dto.setCallbackUrl("https://callback.url/verify");

        String json = objectMapper.writeValueAsString(dto);

        mockMvc.perform(post(Api.USER + "/account/verify/sendToken").contentType(MediaType.APPLICATION_JSON)
                                .content(json))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("Token sent successfully"))
                .andDo(print());

        User updated = userRepository.findById(user.getId()).orElseThrow();
        assertThat(updated.getTokenVerify()).isNotNull();
        assertThat(updated.getTokenExpiration()).isAfter(LocalDateTime.now());
        assertThat(updated.getTokenMagicLink()).isNotNull();
        assertThat(updated.getStatus()).isEqualTo(User.Status.LOGIN);
    }

    /**
     * Test for {@link UserController#confirmVerificationToken(EmailTokenDto)}.
     * <p>
     * Verifies that a user with a correct token and unexpired token is activated,
     * status updated, and a login response is returned. Also checks that the token-related
     * fields are cleared and user is marked as VERIFIED_EMAIL.
     */
    @Test
    @WithMockUser(roles = "ADMIN")
    void confirmVerificationToken_shouldVerifyTokenAndActivateUser() throws Exception {
        Role customerRole = roleRepository.findByName("CUSTOMER").orElseThrow();

        User user = new User();
        user.setEmail("<EMAIL>");
        user.setName("Confirmed User");
        user.setPassword(passwordEncoder.encode("password"));
        user.setRole(customerRole);
        user.setIsActive(false);
        user.setStatus(User.Status.LOGIN);

        String rawToken = "123456";
        String encryptedToken = passwordEncoder.encode(rawToken);

        user.setTokenVerify(encryptedToken);
        user.setTokenExpiration(LocalDateTime.now().plusMinutes(30));
        user = userRepository.save(user);

        EmailTokenDto dto = new EmailTokenDto();
        dto.setEmail(user.getEmail());
        dto.setToken(rawToken);

        String json = objectMapper.writeValueAsString(dto);

        mockMvc.perform(post(Api.USER + "/account/verify/confirmToken").contentType(MediaType.APPLICATION_JSON)
                                .content(json)).andExpect(status().isOk()).andDo(print());

        User updated = userRepository.findById(user.getId()).orElseThrow();

        assertThat(updated.getIsActive()).isTrue();
        assertThat(updated.getStatus()).isEqualTo(User.Status.VERIFIED_EMAIL);
        assertThat(updated.getTokenVerify()).isNull();
        assertThat(updated.getTokenMagicLink()).isNull();
        assertThat(updated.getTokenExpiration()).isBeforeOrEqualTo(LocalDateTime.now());
    }

    /**
     * Test for {@link UserController#confirmVerificationMagicLink(MagicLinkDto)}.
     * <p>
     * Verifies that a valid magic link activates the user, updates token fields, and returns LoginResponse.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void confirmVerificationMagicLink_shouldActivateUserAndReturnToken() throws Exception {
        Role customerRole = roleRepository.findByName("CUSTOMER").orElseThrow();

        User user = new User();
        user.setEmail("<EMAIL>");
        user.setName("Magic User");
        user.setPassword(passwordEncoder.encode("password"));
        user.setRole(customerRole);
        user.setIsActive(false);
        user.setStatus(User.Status.LOGIN);
        user.setTokenExpiration(LocalDateTime.now().plusMinutes(30));
        user = userRepository.save(user);

        int userId = user.getId();
        String rawToken = "some-token";

        String baseLink = "https://callback.url/verify";
        String fullMagicLink = baseLink + "?magic=" + rawToken + "&id=" + userId;
        user.setTokenMagicLink(fullMagicLink);
        userRepository.save(user);

        // mock jwtDecoder.decode()
        Jwt mockJwt = Jwt.withTokenValue("jwt")
                .header("alg", "none")
                .claim("id", userId)
                .claim("token", rawToken)
                .issuedAt(Instant.now())
                .expiresAt(Instant.now().plusSeconds(60))
                .build();

        when(jwtDecoder.decode(rawToken)).thenReturn(mockJwt);

        LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo(
                user.getId(),
                user.getEmail(),
                user.getName(),
                true,
                User.Status.VERIFIED_EMAIL,
                user.getRole().getId(),
                user.getRole().getName(),
                true);

        LoginResponse mockResponse = new LoginResponse("access-token", 3600L, "refresh-token", userInfo);

        when(tokenService.login(any(User.class), eq(true))).thenReturn(mockResponse);

        MagicLinkDto dto = new MagicLinkDto();
        dto.setMagicLink(baseLink + "?magic=" + rawToken);

        String json = objectMapper.writeValueAsString(dto);

        mockMvc.perform(post(Api.USER + "/account/verify/magic-link").contentType(MediaType.APPLICATION_JSON)
                                .content(json))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.access_token").value("access-token"))
                .andDo(print());

        User updated = userRepository.findById(userId).orElseThrow();

        assertThat(updated.getIsActive()).isTrue();
        assertThat(updated.getStatus()).isEqualTo(User.Status.VERIFIED_EMAIL);
        assertThat(updated.getTokenVerify()).isNull();
        assertThat(updated.getTokenMagicLink()).isNull();
        assertThat(updated.getTokenExpiration()).isBeforeOrEqualTo(LocalDateTime.now());
    }

    /**
     * Test for {@link UserController#createPassword(CreatePasswordDto)}.
     * <p>
     * Verifies that a new password is created for a verified, active user who has no password,
     * and that a LoginResponse is returned.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void createPassword_shouldSetPasswordAndReturnToken() throws Exception {
        Role customerRole = roleRepository.findByName("CUSTOMER").orElseThrow();

        User user = new User();
        user.setEmail("<EMAIL>");
        user.setName("No Password User");
        user.setPassword(null); // важное условие!
        user.setRole(customerRole);
        user.setIsActive(true);
        user.setStatus(User.Status.VERIFIED_EMAIL);
        user = userRepository.save(user);

        CreatePasswordDto dto = new CreatePasswordDto();
        dto.setUserId(user.getId());
        dto.setPassword("newSecurePassword");

        LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo(
                user.getId(),
                user.getEmail(),
                user.getName(),
                true,
                User.Status.VERIFIED_EMAIL,
                customerRole.getId(),
                customerRole.getName(),
                true);

        LoginResponse mockResponse = new LoginResponse("access-token", 3600L, "refresh-token", userInfo);

        when(tokenService.login(any(User.class), eq(true))).thenReturn(mockResponse);

        String json = objectMapper.writeValueAsString(dto);

        mockMvc.perform(post(Api.USER + "/account/create-password").contentType(MediaType.APPLICATION_JSON)
                                .content(json))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.access_token").value("access-token"))
                .andDo(print());

        User updated = userRepository.findById(user.getId()).orElseThrow();

        assertThat(updated.getPassword()).isNotNull();
        assertThat(passwordEncoder.matches("newSecurePassword", updated.getPassword())).isTrue();
    }

    /**
     * Test for {@link UserController#resendVerificationToken(EmailResendTokenDto)}.
     * <p>
     * Verifies that a verification token is regenerated and email is sent to user,
     * updating their token and expiration fields.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void resendVerificationToken_shouldResendMagicLink() throws Exception {
        Role customerRole = roleRepository.findByName("CUSTOMER").orElseThrow();

        User user = new User();
        user.setEmail("<EMAIL>");
        user.setName("User Resend");
        user.setPassword(null);
        user.setRole(customerRole);
        user.setIsActive(false);
        user.setStatus(User.Status.NOT_VERIFIED);
        user = userRepository.save(user);

        String callbackUrl = "https://callback.url/create-account";

        EmailResendTokenDto dto = new EmailResendTokenDto();
        dto.setEmail(user.getEmail());
        dto.setTokenMagicLink(callbackUrl);


        String json = objectMapper.writeValueAsString(dto);

        mockMvc.perform(post(Api.USER + "/account/verify/resend-token").contentType(MediaType.APPLICATION_JSON)
                                .content(json))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("Token sent successfully"))
                .andDo(print());

        User updated = userRepository.findById(user.getId()).orElseThrow();
        assertThat(updated.getTokenVerify()).isNotNull();
        assertThat(updated.getTokenExpiration()).isAfter(LocalDateTime.now());
        assertThat(updated.getTokenMagicLink()).contains("?magic=");
    }

    /**
     * Tests that the endpoint /request/email returns 200 OK
     * and success message when a valid email exists.
     */
    @Test
    @WithMockUser(roles = "ADMIN")
    void getUserByEmail_shouldReturnSuccessMessage_whenEmailExists() throws Exception {
        // given
        Role customerRole = roleRepository.findByName("CUSTOMER").orElseThrow();

        User user = new User();
        user.setEmail("<EMAIL>");
        user.setName("Get Email User");
        user.setPassword(passwordEncoder.encode("password"));
        user.setRole(customerRole);
        user.setIsActive(true);
        user.setStatus(User.Status.VERIFIED_EMAIL);
        userRepository.save(user);

        EmailDto dto = new EmailDto();
        dto.setEmail(user.getEmail());

        String json = objectMapper.writeValueAsString(dto);

        // when & then
        mockMvc.perform(post(Api.USER + "/request/email").contentType(MediaType.APPLICATION_JSON).content(json))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("Request sent successfully"))
                .andDo(print());
    }

}
