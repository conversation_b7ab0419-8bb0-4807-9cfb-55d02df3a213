package de.interzero.oneepr.auth.auth;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.auth.auth.dto.LoginResponse;
import de.interzero.oneepr.auth.auth.dto.RefreshTokenRequest;
import de.interzero.oneepr.auth.user.Role;
import de.interzero.oneepr.auth.user.RoleRepository;
import de.interzero.oneepr.auth.user.User;
import de.interzero.oneepr.auth.user.UserRepository;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.TestRole;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.jwt.*;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.httpBasic;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest
@AutoConfigureMockMvc
class AuthControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private PasswordEncoder encoder;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private JpaUserDetailsService jpaUserDetailsService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private JwtDecoder jwtDecoder;

    @Autowired
    private JwtEncoder jwtEncoder;

    private static final String ADMIN_MAIL = "<EMAIL>";
    
    private static final String ADMIN_PASSWORD = "1epr2025";

    @BeforeEach
    void setup() {
        // add a test user to the repository
        Role superAdmin = roleRepository.findByName(TestRole.SUPER_ADMIN).orElseThrow();
        User user = new User();
        user.setEmail(ADMIN_MAIL);
        user.setName(ADMIN_MAIL);
        user.setRole(superAdmin);
        user.setPassword(encoder.encode(ADMIN_PASSWORD));
        user.setIsActive(true);
        user.setStatus(User.Status.COMPLETE);
        userRepository.save(user);
    }

    @AfterEach
    void tearDown() {
        userRepository.delete(userRepository.findByEmailIgnoreCase(ADMIN_MAIL).orElseThrow());
    }

    /**
     * {@link AuthController#login(Authentication)} should return a valid JWT token
     *
     * @throws Exception if request fails
     */
    @Test
    void login_success() throws Exception {
        MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(Api.AUTH + "/login")
                                                   .with(httpBasic(ADMIN_MAIL, ADMIN_PASSWORD)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.access_token").exists())
                .andExpect(jsonPath("$.access_token").isString())
                .andExpect(jsonPath("$.access_token").isNotEmpty())
                .andExpect(jsonPath("$.refresh_token").exists())
                .andExpect(jsonPath("$.refresh_token").isString())
                .andExpect(jsonPath("$.expires_in").exists())
                .andExpect(jsonPath("$.user").exists())
                .andReturn();

        // Extract the token from the response and validate it
        String responseJson = result.getResponse().getContentAsString();
        Map<String, Object> responseMap = objectMapper.readValue(
                responseJson, new TypeReference<>() {
                });
        String token = (String) responseMap.get("access_token");
        validateAccessToken(token);
    }

    /**
     * {@link AuthController#login(Authentication)} should return 403 Unauthorized
     *
     * @throws Exception if request fails
     */
    @Test
    void login_wrongPassword() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders.post(Api.AUTH + "/login").with(httpBasic(ADMIN_MAIL, "WRONG_PASSWORD")))
                .andExpect(status().isUnauthorized());
    }

    /**
     * {@link AuthController#refresh(RefreshTokenRequest)} should return a new valid JWT token
     */
    @Test
    void refresh_success() throws Exception {
        // request a new access token using the refresh token
        RefreshTokenRequest refreshTokenRequest = createTokens(ADMIN_MAIL);
        MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(Api.AUTH + "/refresh/token")
                                                   .contentType(MediaType.APPLICATION_JSON)
                                                   .content(objectMapper.writeValueAsString(refreshTokenRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.access_token").exists())
                .andExpect(jsonPath("$.access_token").isString())
                .andExpect(jsonPath("$.access_token").isNotEmpty())
                .andExpect(jsonPath("$.refresh_token").doesNotExist())
                .andExpect(jsonPath("$.expires_in").exists())
                .andExpect(jsonPath("$.user").exists())
                .andReturn();

        // Extract the token from the response and validate it
        String responseJson = result.getResponse().getContentAsString();
        Map<String, Object> responseMap = objectMapper.readValue(
                responseJson, new TypeReference<>() {
                });
        String newToken = (String) responseMap.get("access_token");
        validateAccessToken(newToken);
    }

    /**
     * {@link AuthController#refresh(RefreshTokenRequest)} should throw 403 Unauthorized
     */
    @Test
    void refresh_expired() throws Exception {
        // generate an expired refresh token
        UserDetails userDetails = jpaUserDetailsService.loadUserByUsername(ADMIN_MAIL);
        Instant expiredTime = Instant.now().minusSeconds(60);
        JwtClaimsSet claimsSet = JwtClaimsSet.builder()
                .subject(userDetails.getUsername())
                .expiresAt(expiredTime)
                .build();
        String expiredRefreshToken = jwtEncoder.encode(JwtEncoderParameters.from(claimsSet)).getTokenValue();

        // request a new access token using the expired refresh token
        RefreshTokenRequest refreshTokenRequest = new RefreshTokenRequest(expiredRefreshToken, "dummyAccessToken");
        mockMvc.perform(MockMvcRequestBuilders.post(Api.AUTH + "/refresh/token")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(refreshTokenRequest)))
                .andExpect(status().isUnauthorized());
    }

    /**
     * {@link AuthController#refresh(RefreshTokenRequest)} should reject non-whitelisted refresh tokens
     */
    @Test
    void refresh_nonWhitelistedToken() throws Exception {
        // Generate a valid refresh token but don't add it to whitelist
        UserDetails userDetails = jpaUserDetailsService.loadUserByUsername(ADMIN_MAIL);
        JwtClaimsSet claimsSet = JwtClaimsSet.builder()
                .subject(userDetails.getUsername())
                .expiresAt(Instant.now().plusSeconds(3600))
                .build();
        String nonWhitelistedRefreshToken = jwtEncoder.encode(JwtEncoderParameters.from(claimsSet)).getTokenValue();

        // Try to use the non-whitelisted refresh token
        RefreshTokenRequest refreshTokenRequest = new RefreshTokenRequest(
                nonWhitelistedRefreshToken,
                "dummyAccessToken");
        mockMvc.perform(MockMvcRequestBuilders.post(Api.AUTH + "/refresh/token")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(refreshTokenRequest)))
                .andExpect(status().isUnauthorized());
    }

    /**
     * {@link AuthController#logout(String)} should remove all refresh tokens for the user from the whitelist
     *
     * @throws Exception if request fails
     */
    @Test
    void logout_removesRefreshTokensFromWhitelist() throws Exception {
        // Create multiple refresh tokens for the same user
        RefreshTokenRequest tokens1 = createTokens(ADMIN_MAIL);
        RefreshTokenRequest tokens2 = createTokens(ADMIN_MAIL);

        // Verify both refresh tokens work before logout
        mockMvc.perform(MockMvcRequestBuilders.post(Api.AUTH + "/refresh/token")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(tokens1))).andExpect(status().isOk());

        mockMvc.perform(MockMvcRequestBuilders.post(Api.AUTH + "/refresh/token")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(tokens2))).andExpect(status().isOk());

        // Logout the user
        mockMvc.perform(MockMvcRequestBuilders.post(Api.AUTH + "/logout")
                                .header("Authorization", "Bearer " + tokens1.accessToken())).andExpect(status().isOk());

        // Verify both refresh tokens are now invalid
        mockMvc.perform(MockMvcRequestBuilders.post(Api.AUTH + "/refresh/token")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(tokens1)))
                .andExpect(status().isUnauthorized());

        mockMvc.perform(MockMvcRequestBuilders.post(Api.AUTH + "/refresh/token")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(tokens2)))
                .andExpect(status().isUnauthorized());
    }

    /**
     * {@link AuthController#logout(String)} should only affect the specific user's refresh tokens
     *
     * @throws Exception if request fails
     */
    @Test
    void logout_onlyAffectsSpecificUser() throws Exception {
        // Create a second test user
        Role superAdmin = roleRepository.findByName(TestRole.SUPER_ADMIN).orElseThrow();
        User user2 = new User();
        user2.setEmail("<EMAIL>");
        user2.setName("<EMAIL>");
        user2.setRole(superAdmin);
        user2.setPassword(encoder.encode(ADMIN_PASSWORD));
        user2.setIsActive(true);
        user2.setStatus(User.Status.COMPLETE);
        userRepository.save(user2);

        // Create tokens for both users using the helper method
        RefreshTokenRequest user1Tokens = createTokens(ADMIN_MAIL);
        RefreshTokenRequest user2Tokens = createTokens("<EMAIL>");

        // Logout user1
        mockMvc.perform(MockMvcRequestBuilders.post(Api.AUTH + "/logout")
                                .header("Authorization", "Bearer " + user1Tokens.accessToken()))
                .andExpect(status().isOk());

        // Verify user1's refresh token is invalid
        mockMvc.perform(MockMvcRequestBuilders.post(Api.AUTH + "/refresh/token")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(user1Tokens)))
                .andExpect(status().isUnauthorized());

        // Verify user2's refresh token still works
        mockMvc.perform(MockMvcRequestBuilders.post(Api.AUTH + "/refresh/token")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(user2Tokens))).andExpect(status().isOk());
    }

    /**
     * {@link AuthController#login(Authentication)} should properly whitelist refresh tokens during login
     *
     * @throws Exception if request fails
     */
    @Test
    void login_refreshTokenIsWhitelisted() throws Exception {
        // Login to get tokens
        MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(Api.AUTH + "/login")
                                                   .with(httpBasic(ADMIN_MAIL, ADMIN_PASSWORD)))
                .andExpect(status().isOk())
                .andReturn();

        // Extract tokens from response
        String responseJson = result.getResponse().getContentAsString();
        Map<String, Object> responseMap = objectMapper.readValue(
                responseJson, new TypeReference<>() {
                });
        String refreshToken = (String) responseMap.get("refresh_token");

        // Verify the refresh token can be used (proving it's whitelisted)
        RefreshTokenRequest refreshTokenRequest = new RefreshTokenRequest(refreshToken, "dummyAccessToken");
        mockMvc.perform(MockMvcRequestBuilders.post(Api.AUTH + "/refresh/token")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(refreshTokenRequest)))
                .andExpect(status().isOk());
    }

    /**
     * {@link AuthController#status(String)} should return true for a valid JWT token
     *
     * @throws Exception if request fails
     */
    @Test
    void status_validToken() throws Exception {
        RefreshTokenRequest tokens = createTokens(ADMIN_MAIL);
        mockMvc.perform(MockMvcRequestBuilders.get(Api.AUTH + "/status")
                                .header("Authorization", "Bearer " + tokens.accessToken()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").value(true));
    }

    /**
     * {@link AuthController#status(String)} should return false for an expired JWT token
     *
     * @throws Exception if request fails
     */
    @Test
    void status_expiredToken() throws Exception {
        // Generate an expired access token
        UserDetails userDetails = jpaUserDetailsService.loadUserByUsername(ADMIN_MAIL);
        Instant now = Instant.now();
        JwtClaimsSet claimsSet = JwtClaimsSet.builder()
                .subject(userDetails.getUsername())
                .claim(TokenService.CLAIM_ACCOUNT_MAIL, userDetails.getUsername())
                .claim(TokenService.CLAIM_ROLES, TestRole.SUPER_ADMIN)
                .issuer(TokenService.TOKEN_ISSUER)
                .issuedAt(now.minusSeconds(60))
                .expiresAt(now.minusSeconds(1))
                .build();
        String expiredToken = jwtEncoder.encode(JwtEncoderParameters.from(claimsSet)).getTokenValue();

        mockMvc.perform(MockMvcRequestBuilders.get(Api.AUTH + "/status")
                                .header("Authorization", "Bearer " + expiredToken))
                .andExpect(status().isUnauthorized());
    }


    /**
     * Validate the access token
     *
     * @param accessToken the access token to validate
     */
    private void validateAccessToken(String accessToken) {
        assertNotNull(accessToken);

        // Decode and validate the JWT token
        Jwt decodedToken = jwtDecoder.decode(accessToken);

        // Validate token claims
        assertEquals(ADMIN_MAIL, decodedToken.getSubject(), "Token subject should match user email");
        assertEquals(
                ADMIN_MAIL,
                decodedToken.getClaim(TokenService.CLAIM_ACCOUNT_MAIL),
                "Token account mail should match user email");

        // Validate authorities/roles
        String roles = decodedToken.getClaim(TokenService.CLAIM_ROLES);
        assertNotNull(roles, "Token should contain roles");
        assertTrue(roles.contains(TestRole.SUPER_ADMIN), "Token roles should contain SUPER_ADMIN");

        // Validate token expiration
        assertNotNull(decodedToken.getExpiresAt(), "Token should have expiration time");
        assertTrue(decodedToken.getExpiresAt().isAfter(Instant.now()), "Token should not be expired");

        // Validate issuer
        assertEquals(
                TokenService.TOKEN_ISSUER,
                decodedToken.getIssuer().toString(),
                "Token issuer should match the expected URL");
    }

    /**
     * Create new access and refresh tokens for the specified user, reusing the {@link RefreshTokenRequest} to return both tokens.
     *
     * @param username the username of the user to create tokens for
     * @return a {@link RefreshTokenRequest} containing the refresh and access tokens
     */
    private RefreshTokenRequest createTokens(String username) {
        UserDetails userDetails = jpaUserDetailsService.loadUserByUsername(username);
        LoginResponse generatedTokens = tokenService.login(userDetails, true);
        String accessToken = generatedTokens.accessToken();
        String refreshToken = generatedTokens.refreshToken();
        assertNotNull(accessToken, "Token should contain access token");
        assertNotNull(refreshToken, "Token should contain refresh token");
        return new RefreshTokenRequest(refreshToken, accessToken);
    }
}
