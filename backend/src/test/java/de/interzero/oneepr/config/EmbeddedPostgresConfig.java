package de.interzero.oneepr.config;

import io.zonky.test.db.postgres.embedded.EmbeddedPostgres;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;

import javax.sql.DataSource;
import java.io.IOException;

/**
 * Configuration class for embedded Postgres used in tests. Disabled when running in the pipeline.
 */
@Configuration
@Profile("!pipeline")
public class EmbeddedPostgresConfig implements DisposableBean {

    private EmbeddedPostgres postgres;

    @Bean
    @Primary
    public DataSource embeddedPostgresDataSource() throws IOException {
        // Store the instance so we can close it properly
        this.postgres = EmbeddedPostgres.start();
        return postgres.getPostgresDatabase();
    }

    @Override
    public void destroy() throws Exception {
        // Close the embedded Postgres instance when the Spring context is closed
        if (postgres != null) {
            postgres.close();
        }
    }
}