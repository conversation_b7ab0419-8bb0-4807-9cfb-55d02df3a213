package de.interzero.oneepr.common;

import org.flywaydb.test.annotation.FlywayTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * This is a simple test to make sure that zonky's embedded postgres DB is working as expected with FlywayTest
 */
@SpringBootTest
@AutoConfigureMockMvc
@FlywayTest
@Transactional
class PostgresTest {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * Confirm the expected postgres version
     */
    @Test
    void testPostgresVersion() {
        String version = jdbcTemplate.queryForObject("SELECT version()", String.class);
        assertTrue(Objects.requireNonNull(version).contains("17.3"), "PostgreSQL version should be 17.3");
    }
}
