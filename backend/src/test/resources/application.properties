spring.application.name=One EPR BE
#database
spring.datasource.driver-class-name=org.postgresql.Driver
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=validate
# some prisma schemes aren't following proper naming schemes, so this forces springboot to use the name as it appears in the column annotation
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
# Flyway configuration
spring.flyway.clean-disabled=false
spring.flyway.enabled=true
spring.flyway.out-of-order=true
spring.flyway.locations=classpath:db/migration
# Security
token.validity.access=28800000
token.validity.refresh=86400000
# this key pair is to sign and validate the JWT token.
rsa.private-key=MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQD4lm7520jxUnhak7bGpURT7kJrz5FUFGJExEh1i5CMdKN+Hd8PGAbp3U9/bR/Geu92cDBRPu8YT9OlvHtAicXKaX2vUwn5uZJYboSjCnZwziLAYRv4x/6nbBVHUIMuZYIUCCT/mdJ93R6HKHgBlfniSNT1vBcwuASdfHrOB0UTPgNDVyl+QuZjsUsT0zj59VQIbN31CuzLuiVwhT0idYyTSKm4lMlNUdoYNe1QWsvIWbpQyByMzuLWZ/GxCBvUp7/eO5N1luOsEH3smvsmVQz5C3LC+qcqq+QrUd2NXP9tXfZIwLFRXdaDuhnJ/vz4SlCg0+UtvDBUWtxA66WcPZchAgMBAAECggEAAlxKPfCBcfMCqo+zs+6N6VGSpXASGXCcSy7IVNCvgJDYP1JhBUVadIs8IOHc+1W8sb5bbxlXeuGqccny5XyVELuqhrqJ5/A0CYHAnArShzCDAMjuMKhxg+u/xrRysnHFfx8bxKZ9JPl1O532n5TeOixayPjxTuLORGnX9fWEKv3RMXUerKim06l1bZ09zn0xTjEXN1lGEZODVYzKioNRg3gq+RBfb0GURSLarEHKVohbQIHFflOkYTU8pfeN1rWLnYwmT/YBT0C7bzfd0ReBCJnam8WJcC54SmuFpTEnQvBQhRnUT23gR8saKmyEPaPmodldaoO62EUP2HG6O0oY6wKBgQD/MZr6KPGPmPAtffcSFgyLM0fmQkXGL9NRSQUiTKdNxQe1ZCf7YqGh/TxSn8Hbmp4udtY/R1QH3XGsUhaJoIHkA6mtQ4apgvVM3rdiKiJxMiB15PR355ppKFAick2t2sGK3aAsA5Unl7YYPKMN/82vsWbAXkCrE/aTH3UsGfdmswKBgQD5X3w4LNnheuNfExtil86d0VXTtcjjGDsyCCXPZJHbdG08vzNJ4+ny9qrs0tltZmIzS7cJ+fBKO9EjkuEfIzeoNT3evtgTpd0Svu7IlvtqUFwx/nGKFXaYIhEq6LHAkOWaf5uS+mys9rQMveZm/MQ+YqDiquxUbhw/1cdsK4JU2wKBgQCpshbYWvmm/5BW7/in8jmNJyCcoV08mPjwiJn9YPOv52Fk4aEkrUXGLls6pjmHCp49T5DQq2HZiQvaBwwtoKo/MaBXMxwNBKSR5VgU0ufsVeT4ALQAN+aaHtCK12+IOHuTGCU2n3tDxMK2Azx6RqxENLebHBHssW26D6UfGnVFuQKBgQC2KfxSBOF8hwqFLy34DbEPNBYgO1DuI4tKdw03vN2XlCsgDwPmXClQJMSjjzrpYbsmgY7KKNxRPfT+qZLt7goARDRX/M1XPPiLcxJ3UWODPAS9OIO0PQ2PAIPN9oGyi6ZOAB2jdm/gwa6xCB/A57Brahzgt78/xde5Ae9Vo0rntQKBgQCInwJgl+KHxQi8y+1EhEn+YYWOMhMhtFVW60eVuoySAOb6gaWzHqsyoKHwSuKgpdFphJgFQyzLCYUEztFqon+KZynoedM72iWxLSQuneBU063lMGu2GhaAddkT5W3F7aIgfMomEgr5iKskEuuHOczfVOu++iJFP/OAj57uzDgYgQ==
rsa.public-key=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA+JZu+dtI8VJ4WpO2xqVEU+5Ca8+RVBRiRMRIdYuQjHSjfh3fDxgG6d1Pf20fxnrvdnAwUT7vGE/Tpbx7QInFyml9r1MJ+bmSWG6Eowp2cM4iwGEb+Mf+p2wVR1CDLmWCFAgk/5nSfd0ehyh4AZX54kjU9bwXMLgEnXx6zgdFEz4DQ1cpfkLmY7FLE9M4+fVUCGzd9Qrsy7olcIU9InWMk0ipuJTJTVHaGDXtUFrLyFm6UMgcjM7i1mfxsQgb1Ke/3juTdZbjrBB97Jr7JlUM+QtywvqnKqvkK1HdjVz/bV32SMCxUV3Wg7oZyf78+EpQoNPlLbwwVFrcQOulnD2XIQIDAQAB
#logging
logging.level.org.hibernate.SQL=WARN
logging.level.org.hibernate.type.descriptor.sql=WARN
logging.level.org.hibernate=WARN
logging.level.interzero.de.c4rbe=DEBUG
# Hibernate specific
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.show_sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.type.descriptor.sql=ERROR
#placeholder for the real core-api-url
app.core-api-url=example.com
# Lambda request presigned url
file.lambda.request-presigned-url=https://3pdf2nhm7hl4n37hjo4upqgjme0pdvsd.lambda-url.us-east-2.on.aws
# Lambda download file url
file.lambda.download-file-url=https://3qthmn2fk2clgkiikb3dzy6o7e0fyttk.lambda-url.us-east-2.on.aws
# Email configuration
spring.mail.host=localhost
spring.mail.port=1025
spring.mail.properties.mail.smtp.auth=false
spring.mail.properties.mail.smtp.starttls.enable=true
# Enable sandbox gateway
mail.gateway=sandbox