#database
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.url=***********************************************
spring.datasource.username=test_user
spring.datasource.password=test_password
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=validate
# Flyway configuration
spring.flyway.clean-disabled=false
spring.flyway.enabled=true
file.lambda.request-presigned-url=https://3pdf2nhm7hl4n37hjo4upqgjme0pdvsd.lambda-url.us-east-2.on.aws
file.lambda.download-file-url=https://3qthmn2fk2clgkiikb3dzy6o7e0fyttk.lambda-url.us-east-2.on.aws
# Email configuration
spring.mail.host=k8s-entwdigi-mailpits-161ddc30dc-63b434d32b26d692.elb.eu-central-1.amazonaws.com
spring.mail.port=1025
spring.mail.properties.mail.smtp.auth=false
spring.mail.properties.mail.smtp.starttls.enable=true