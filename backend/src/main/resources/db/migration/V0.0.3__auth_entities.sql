--
-- Name: Status; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public."Status" AS ENUM (
    'NOT_VERIFIED',
    'VERIFIED_EMAIL',
    'LOGIN',
    'PENDING_PASSWORD',
    'COMPLETE'
    );

--
-- Name: one_epr_role; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.one_epr_role
(
    id           INTEGER NOT NULL,
    name         TEXT NOT NULL,
    display_name TEXT NOT NULL,
    is_active    BOOLEAN DEFAULT TRUE,
    created_at   TIMESTAMP WITHOUT TIME ZONE,
    updated_at   TIMESTAMP WITHOUT TIME ZONE,
    deleted_at   TIMESTAMP WITHOUT TIME ZONE
);

--
-- Name: one_epr_role_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.one_epr_role_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

--
-- Name: one_epr_role_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.one_epr_role_id_seq OWNED BY public.one_epr_role.id;



--
-- Name: one_epr_user; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.one_epr_user
(
    id                    INTEGER NOT NULL,
    first_name            TEXT,
    last_name             TEXT,
    name                  TEXT                                   NOT NULL,
    email                 VARCHAR(255)                           NOT NULL,
    password              TEXT,
    is_active             BOOLEAN         DEFAULT FALSE          NOT NULL,
    role_id               INTEGER,
    token_verify          TEXT,
    token_expiration      TIMESTAMP WITHOUT TIME ZONE,
    token_magic_link      TEXT,
    token_create_password TEXT,
    token_attempts        INTEGER         DEFAULT 0              NOT NULL,
    block_time            TIMESTAMP WITHOUT TIME ZONE,
    status                public."Status" DEFAULT 'NOT_VERIFIED' NOT NULL,
    created_at            TIMESTAMP WITHOUT TIME ZONE,
    updated_at            TIMESTAMP WITHOUT TIME ZONE,
    deleted_at            TIMESTAMP WITHOUT TIME ZONE
);

--
-- Name: one_epr_user_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.one_epr_user_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

--
-- Name: one_epr_user_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.one_epr_user_id_seq OWNED BY public.one_epr_user.id;



--
-- Name: password_reset_request; Type: TABLE; Schema: public
--

CREATE TABLE public.password_reset_request
(
    id           INTEGER NOT NULL,
    email        TEXT    NOT NULL,
    user_id      INTEGER NOT NULL,
    token        TEXT    NOT NULL,
    status       VARCHAR(255) DEFAULT 'PENDING',
    callback_url TEXT,
    expires_at   TIMESTAMP WITHOUT TIME ZONE
);

--
-- Name: password_reset_request_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.password_reset_request_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

--
-- Name: password_reset_request_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.password_reset_request_id_seq OWNED BY public.password_reset_request.id;


--
-- Name: user_access_request; Type: TABLE; Schema: public
--

CREATE TABLE public.user_access_request
(
    id           INTEGER NOT NULL,
    user_id      INTEGER,
    user_email   VARCHAR(255),
    requester_id INTEGER                NOT NULL,
    status       VARCHAR(255) DEFAULT 'PENDING',
    token        TEXT                   NOT NULL,
    callback_url TEXT,
    is_active    BOOLEAN                NOT NULL,
    created_at   TIMESTAMP WITHOUT TIME ZONE,
    expires_at   TIMESTAMP WITHOUT TIME ZONE,
    updated_at   TIMESTAMP WITHOUT TIME ZONE,
    wrong_count  INTEGER      DEFAULT 0 NOT NULL
);

--
-- Name: user_access_request_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.user_access_request_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

--
-- Name: user_access_request_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.user_access_request_id_seq OWNED BY public.user_access_request.id;



--
-- Name: change_user_email; Type: TABLE; Schema: public
--

CREATE TABLE public.change_user_email
(
    id         UUID NOT NULL,
    new_email  TEXT    NOT NULL,
    token      TEXT    NOT NULL,
    user_id    INTEGER NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE,
    updated_at TIMESTAMP WITHOUT TIME ZONE,
    deleted_at TIMESTAMP WITHOUT TIME ZONE
);

--
-- Name: change_user_email_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--
CREATE SEQUENCE public.change_user_email_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

--
-- Name: change_user_email_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--
ALTER SEQUENCE public.change_user_email_id_seq OWNED BY public.change_user_email.id;

--
-- Name: refresh_token; Type: TABLE; Schema: public
--

CREATE TABLE public.refresh_token
(
    id         INTEGER NOT NULL,
    user_id    INTEGER UNIQUE NOT NULL,
    token      TEXT UNIQUE    NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE
);

--
-- Name: refresh_token_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--
CREATE SEQUENCE public.refresh_token_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

--
-- Name: refresh_token_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--
ALTER SEQUENCE public.refresh_token_id_seq OWNED BY public.refresh_token.id;
