--
-- Name: Status; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public."Status" AS ENUM (
    'NOT_VERIFIED',
    'VERIFIED_EMAIL',
    'LOGIN',
    'PENDING_PASSWORD',
    'COMPLETE'
    );



--
-- Name: one_epr_role; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.one_epr_role
(
    id           SERIAL PRIMARY KEY,
    name         VARCHAR(255) NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    is_active    BOOLEAN DEFAULT TRUE,
    created_at   TIMESTAMP WITHOUT TIME ZONE,
    updated_at   TIMESTAMP WITHOUT TIME ZONE,
    deleted_at   TIMESTAMP WITHOUT TIME ZONE
);



--
-- Name: one_epr_user; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.one_epr_user
(
    id                    SERIAL PRIMARY KEY,
    first_name            <PERSON><PERSON><PERSON><PERSON>(255),
    last_name             <PERSON><PERSON><PERSON><PERSON>(255),
    name                  <PERSON><PERSON><PERSON><PERSON>(255)                           NOT NULL,
    email                 VA<PERSON>HAR(255)                           NOT NULL,
    password              VARCHAR(255),
    is_active             BOOLEAN         DEFAULT FALSE          NOT NULL,
    role_id               INTEGER REFERENCES public.one_epr_role (id),
    token_verify          VARCHAR(255),
    token_expiration      TIMESTAMP WITHOUT TIME ZONE,
    token_magic_link      VARCHAR(255),
    token_create_password VARCHAR(255),
    token_attempts        INTEGER         DEFAULT 0              NOT NULL,
    block_time            TIMESTAMP WITHOUT TIME ZONE,
    status                public."Status" DEFAULT 'NOT_VERIFIED' NOT NULL,
    created_at            TIMESTAMP WITHOUT TIME ZONE,
    updated_at            TIMESTAMP WITHOUT TIME ZONE,
    deleted_at            TIMESTAMP WITHOUT TIME ZONE
);
