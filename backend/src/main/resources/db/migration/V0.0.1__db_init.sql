--
-- Name: ClusterStatus; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public."ClusterStatus" AS ENUM (
    'ACTIVE',
    'INACTIVE'
);




--
-- Name: CouponDiscountType; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public."CouponDiscountType" AS ENUM (
    'PERCENTAGE',
    'ABSOLUTE',
    'BUY_X_PRODUCTS_GET_Y_PRODUCTS',
    'BUY_X_PRODUCTS_GET_Y_DISCOUNT'
);




--
-- Name: CouponMode; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public."CouponMode" AS ENUM (
    'GENERAL',
    'INDIVIDUAL',
    'GROUP_SEGMENT'
);




--
-- Name: CouponType; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public."CouponType" AS ENUM (
    'SYSTEM',
    'CUSTOMER'
);




--
-- Name: DeclineReasonType; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public."DeclineReasonType" AS ENUM (
    'LICENSE_INFORMATION',
    'LICENSE_VOLUME_REPORT'
);




--
-- Name: LicenseThirdPartyInvoiceIssuer; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public."LicenseThirdPartyInvoiceIssuer" AS ENUM (
    'THIRD_PARTY_DUAL_SYSTEM',
    'OTHER_THIRD_PARTY'
);




--
-- Name: PartnerContractChangeType; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public."PartnerContractChangeType" AS ENUM (
    'EMAIL',
    'NOTE',
    'FILE'
);




--
-- Name: PartnerContractStatus; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public."PartnerContractStatus" AS ENUM (
    'DRAFT',
    'ACTIVE',
    'EXPIRED',
    'TERMINATED',
    'TO_BE_SIGNED',
    'DENIED'
);




--
-- Name: PartnerStatus; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public."PartnerStatus" AS ENUM (
    'NO_UPDATES',
    'IMPROVED_CONTRACT',
    'DENIED_CONTRACT',
    'REQUESTED_COMMISSION',
    'CHANGED_INFORMATION'
);




--
-- Name: enum_certificate_status; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.enum_certificate_status AS ENUM (
    'AVAILABLE',
    'NOT_AVAILABLE'
);




--
-- Name: enum_commission_service_type; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.enum_commission_service_type AS ENUM (
    'EU_LICENSE',
    'DIRECT_LICENSE',
    'ACTION_GUIDE'
);




--
-- Name: enum_commission_type; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.enum_commission_type AS ENUM (
    'AFFILIATE_LINK',
    'COUPON'
);




--
-- Name: enum_commission_user_type; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.enum_commission_user_type AS ENUM (
    'CUSTOMER',
    'PARTNER'
);




--
-- Name: enum_consent_type; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.enum_consent_type AS ENUM (
    'ACCOUNT',
    'PURCHASE'
);




--
-- Name: enum_contract_status; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.enum_contract_status AS ENUM (
    'ACTIVE',
    'TERMINATION_PROCESS',
    'TERMINATED'
);




--
-- Name: enum_contract_type; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.enum_contract_type AS ENUM (
    'EU_LICENSE',
    'DIRECT_LICENSE',
    'ACTION_GUIDE'
);




--
-- Name: enum_customer_type; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.enum_customer_type AS ENUM (
    'REGULAR',
    'PREMIUM'
);




--
-- Name: enum_file_type; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.enum_file_type AS ENUM (
    'GENERAL_INFORMATION',
    'REQUIRED_INFORMATION',
    'CONTRACT',
    'CONTRACT_TERMINATION',
    'LICENSE_CONTRACT',
    'CERTIFICATE',
    'INVOICE',
    'PAYMENT',
    'LICENSE_PROOF_OF_REGISTRATION',
    'PROOF_OF_TERMINATION',
    'THIRD_PARTY_INVOICE',
    'MARKETING_MATERIAL',
    'PARTNER_CONTRACT'
);




--
-- Name: enum_general_information_status; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.enum_general_information_status AS ENUM (
    'NEW',
    'DONE',
    'DECLINED',
    'APPROVED',
    'OPEN'
);




--
-- Name: enum_general_information_type; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.enum_general_information_type AS ENUM (
    'TEXT',
    'NUMBER',
    'DOCUMENT',
    'FILE',
    'IMAGE'
);




--
-- Name: enum_license_clerk_control_status; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.enum_license_clerk_control_status AS ENUM (
    'PENDING',
    'DONE'
);




--
-- Name: enum_license_contract_status; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.enum_license_contract_status AS ENUM (
    'ACTIVE',
    'TERMINATION_PROCESS',
    'TERMINATED'
);




--
-- Name: enum_license_registration_status; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.enum_license_registration_status AS ENUM (
    'PENDING',
    'IN_REVIEW',
    'REGISTRATION',
    'DONE'
);




--
-- Name: enum_license_report_set_rhythm; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.enum_license_report_set_rhythm AS ENUM (
    'ANNUALLY',
    'MONTHLY',
    'QUARTERLY'
);




--
-- Name: enum_license_required_information_kind; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.enum_license_required_information_kind AS ENUM (
    'REQUIRED_INFORMATION',
    'GENERAL_INFORMATION'
);




--
-- Name: enum_license_required_information_status; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.enum_license_required_information_status AS ENUM (
    'NEW',
    'DONE',
    'DECLINED',
    'APPROVED',
    'OPEN'
);




--
-- Name: enum_license_required_information_type; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.enum_license_required_information_type AS ENUM (
    'TEXT',
    'NUMBER',
    'DOCUMENT',
    'FILE',
    'IMAGE'
);




--
-- Name: enum_license_third_party_invoice_status; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.enum_license_third_party_invoice_status AS ENUM (
    'OPEN',
    'PAYED',
    'UNPROCESSED',
    'CANCELLED'
);




--
-- Name: enum_license_volume_report_status; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.enum_license_volume_report_status AS ENUM (
    'NEW',
    'DONE',
    'DECLINED',
    'APPROVED',
    'OPEN'
);




--
-- Name: enum_marketing_material_category; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.enum_marketing_material_category AS ENUM (
    'STANDARD',
    'SPECIFIC_MATERIAL'
);




--
-- Name: enum_marketing_material_partner_restriction; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.enum_marketing_material_partner_restriction AS ENUM (
    'ALL',
    'CLUSTER',
    'SPECIFIC'
);




--
-- Name: enum_service_step_type; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.enum_service_step_type AS ENUM (
    'LICENSE',
    'ACTION_GUIDE'
);




--
-- Name: enum_termination_status; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.enum_termination_status AS ENUM (
    'REQUESTED',
    'COMPLETED',
    'PENDING'
);




--
-- Name: shopping_cart_journey; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.shopping_cart_journey AS ENUM (
    'LONG',
    'DIRECT_LICENSE',
    'QUICK_LICENSE',
    'QUICK_ACTION_GUIDE'
);




--
-- Name: shopping_cart_status; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.shopping_cart_status AS ENUM (
    'OPEN',
    'PURCHASED'
);




--
-- Name: type_affiliate; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.type_affiliate AS ENUM (
    'AFFILIATE_LINK',
    'COUPON'
);




--
-- Name: type_use_coupon; Type: TYPE; Schema: public; Owner: epr-local-user
--

CREATE TYPE public.type_use_coupon AS ENUM (
    'LINK',
    'WRITTEN'
);

CREATE TYPE public.shopping_cart_item_specification_type AS ENUM (
    'PURCHASE',
    'VOLUME_CHANGE'
);




SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: action_guide; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.action_guide (
                                     id integer NOT NULL,
                                     contract_id integer NOT NULL,
                                     country_id integer NOT NULL,
                                     country_code text NOT NULL,
                                     country_name text NOT NULL,
                                     country_flag text NOT NULL,
                                     created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                     updated_at timestamp(3) without time zone NOT NULL,
                                     deleted_at date,
                                     contract_status public.enum_contract_status DEFAULT 'ACTIVE'::public.enum_contract_status NOT NULL,
                                     termination_id integer
);




--
-- Name: action_guide_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.action_guide_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: action_guide_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.action_guide_id_seq OWNED BY public.action_guide.id;


--
-- Name: action_guide_price_list; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.action_guide_price_list (
                                                id integer NOT NULL,
                                                setup_price_list_id integer NOT NULL,
                                                action_guide_id integer NOT NULL,
                                                name text NOT NULL,
                                                description text NOT NULL,
                                                price integer NOT NULL,
                                                created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                                updated_at timestamp(3) without time zone NOT NULL,
                                                deleted_at timestamp(3) without time zone
);

--
-- Name: reason_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--
CREATE SEQUENCE IF NOT EXISTS public.reason_id_seq START WITH 1 INCREMENT BY 1;

CREATE TABLE public.reason
(
    id         INTEGER NOT NULL,
    title      TEXT    NOT NULL,
    value      TEXT    NOT NULL,
    type       TEXT    NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    deleted_at date,
    CONSTRAINT pk_reason PRIMARY KEY (id)
);


--
-- Name: action_guide_price_list_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.action_guide_price_list_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: action_guide_price_list_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.action_guide_price_list_id_seq OWNED BY public.action_guide_price_list.id;


--
-- Name: certificate; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.certificate (
                                    id integer NOT NULL,
                                    license_id integer NOT NULL,
                                    name text NOT NULL,
                                    status public.enum_certificate_status DEFAULT 'NOT_AVAILABLE'::public.enum_certificate_status NOT NULL,
                                    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                    updated_at timestamp(3) without time zone NOT NULL,
                                    deleted_at date
);




--
-- Name: certificate_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.certificate_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: certificate_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.certificate_id_seq OWNED BY public.certificate.id;


--
-- Name: cluster; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.cluster (
                                id integer NOT NULL,
                                name text NOT NULL,
                                registration_start_date timestamp(3) without time zone NOT NULL,
                                registration_end_date timestamp(3) without time zone NOT NULL,
                                status public."ClusterStatus" NOT NULL,
                                min_household_packaging integer NOT NULL,
                                max_household_packaging integer NOT NULL,
                                type_of_services jsonb NOT NULL,
                                participating_countries jsonb NOT NULL,
                                created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                updated_at timestamp(3) without time zone NOT NULL,
                                deleted_at timestamp(3) without time zone
);




--
-- Name: cluster_customers; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.cluster_customers (
                                          id integer NOT NULL,
                                          cluster_id integer NOT NULL,
                                          customer_id integer NOT NULL
);




--
-- Name: cluster_customers_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.cluster_customers_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: cluster_customers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.cluster_customers_id_seq OWNED BY public.cluster_customers.id;


--
-- Name: cluster_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.cluster_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: cluster_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.cluster_id_seq OWNED BY public.cluster.id;


--
-- Name: commission; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.commission (
                                   id integer NOT NULL,
                                   user_id integer NOT NULL,
                                   user_type public.enum_commission_user_type NOT NULL,
                                   commission_percentage integer NOT NULL,
                                   commission_value integer NOT NULL,
                                   net_turnover integer DEFAULT 0 NOT NULL,
                                   type public.enum_commission_type NOT NULL,
                                   coupon_id integer,
                                   coupon_code text,
                                   affiliate_link text,
                                   service_type public.enum_commission_service_type NOT NULL,
                                   order_id text NOT NULL,
                                   order_customer_id integer,
                                   created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                   used    BOOLEAN DEFAULT FALSE NOT NULL
);




--
-- Name: commission_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.commission_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: commission_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.commission_id_seq OWNED BY public.commission.id;


--
-- Name: company; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.company (
                                id integer NOT NULL,
                                name text NOT NULL,
                                description text,
                                vat text,
                                tin text,
                                lucid text,
                                customer_id integer,
                                starting timestamp(3) without time zone,
                                website text,
                                partner_id integer,
                                address_id integer,
                                created_at timestamp(3) without time zone NOT NULL,
                                updated_at timestamp(3) without time zone NOT NULL,
                                deleted_at date,
                                industry_sector text,
                                owner_name text
);




--
-- Name: company_address; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.company_address (
                                        id integer NOT NULL,
                                        country_code text NOT NULL,
                                        address_line text NOT NULL,
                                        city text NOT NULL,
                                        zip_code text NOT NULL,
                                        street_and_number text NOT NULL,
                                        additional_address text NOT NULL,
                                        created_at timestamp(3) without time zone NOT NULL,
                                        updated_at timestamp(3) without time zone NOT NULL,
                                        deleted_at date
);




--
-- Name: company_address_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.company_address_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: company_address_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.company_address_id_seq OWNED BY public.company_address.id;


--
-- Name: company_contact; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.company_contact (
                                        id integer NOT NULL,
                                        company_id integer,
                                        name text,
                                        email text,
                                        phone_mobile text,
                                        created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                        updated_at timestamp(3) without time zone NOT NULL,
                                        deleted_at date
);




--
-- Name: company_contact_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.company_contact_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: company_contact_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.company_contact_id_seq OWNED BY public.company_contact.id;


--
-- Name: company_email; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.company_email (
                                      id integer NOT NULL,
                                      email text NOT NULL,
                                      company_id integer,
                                      created_at timestamp(3) without time zone NOT NULL,
                                      updated_at timestamp(3) without time zone NOT NULL,
                                      deleted_at date
);




--
-- Name: company_email_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.company_email_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: company_email_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.company_email_id_seq OWNED BY public.company_email.id;


--
-- Name: company_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.company_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: company_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.company_id_seq OWNED BY public.company.id;


--
-- Name: consent; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.consent (
                                id integer NOT NULL,
                                name text NOT NULL,
                                type public.enum_consent_type NOT NULL,
                                description text NOT NULL,
                                version integer NOT NULL,
                                created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                updated_at timestamp(3) without time zone NOT NULL,
                                deleted_at date
);




--
-- Name: consent_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.consent_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: consent_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.consent_id_seq OWNED BY public.consent.id;


--
-- Name: contract; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.contract (
                                 id integer NOT NULL,
                                 customer_id integer NOT NULL,
                                 type public.enum_contract_type NOT NULL,
                                 status public.enum_contract_status DEFAULT 'ACTIVE'::public.enum_contract_status NOT NULL,
                                 title text NOT NULL,
                                 start_date timestamp(3) without time zone NOT NULL,
                                 termination_id integer,
                                 created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                 updated_at timestamp(3) without time zone NOT NULL,
                                 deleted_at date,
                                 end_date timestamp(3) without time zone NOT NULL
);




--
-- Name: contract_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.contract_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: contract_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.contract_id_seq OWNED BY public.contract.id;


--
-- Name: coupon; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.coupon (
                               id integer NOT NULL,
                               buy_x_get_y jsonb,
                               code text NOT NULL,
                               commission_percentage integer,
                               description text,
                               discount_type public."CouponDiscountType" NOT NULL,
                               elegible_products jsonb,
                               end_date timestamp(3) without time zone NOT NULL,
                               is_active boolean DEFAULT true NOT NULL,
                               link text,
                               max_amount integer,
                               max_uses integer,
                               max_uses_per_customer integer,
                               min_amount integer,
                               min_products integer,
                               mode public."CouponMode" NOT NULL,
                               note text,
                               start_date timestamp(3) without time zone NOT NULL,
                               type public."CouponType" NOT NULL,
                               value integer NOT NULL,
                               created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                               updated_at timestamp(3) without time zone NOT NULL,
                               deleted_at date,
                               redeemable_by_new_customers boolean DEFAULT false NOT NULL,
                               for_commission boolean DEFAULT false NOT NULL,
                               used_at timestamp(3) without time zone NOT NULL
);




--
-- Name: coupon_customer; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.coupon_customer (
                                        id integer NOT NULL,
                                        coupon_id integer NOT NULL,
                                        customer_id integer NOT NULL
);




--
-- Name: coupon_customer_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.coupon_customer_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: coupon_customer_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.coupon_customer_id_seq OWNED BY public.coupon_customer.id;


--
-- Name: coupon_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.coupon_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: coupon_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.coupon_id_seq OWNED BY public.coupon.id;


--
-- Name: coupon_partners; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.coupon_partners (
                                        id integer NOT NULL,
                                        coupon_id integer NOT NULL,
                                        partner_id integer NOT NULL
);




--
-- Name: coupon_partners_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.coupon_partners_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: coupon_partners_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.coupon_partners_id_seq OWNED BY public.coupon_partners.id;


--
-- Name: coupon_uses; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.coupon_uses (
                                    id integer NOT NULL,
                                    coupon_id integer NOT NULL,
                                    shopping_cart_id text NOT NULL,
                                    order_id text,
                                    customer_id integer NOT NULL,
                                    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                    is_first_purchase boolean DEFAULT false NOT NULL
);




--
-- Name: coupon_uses_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.coupon_uses_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: coupon_uses_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.coupon_uses_id_seq OWNED BY public.coupon_uses.id;


--
-- Name: customer; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.customer (
                                 id integer NOT NULL,
                                 type public.enum_customer_type DEFAULT 'REGULAR'::public.enum_customer_type NOT NULL,
                                 first_name text NOT NULL,
                                 last_name text NOT NULL,
                                 salutation text,
                                 email text NOT NULL,
                                 user_id integer NOT NULL,
                                 is_active boolean,
                                 document_id integer,
                                 id_stripe text,
                                 created_at timestamp(3) without time zone NOT NULL,
                                 updated_at timestamp(3) without time zone NOT NULL,
                                 deleted_at date,
                                 company_name text
);




--
-- Name: customer_commitment; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.customer_commitment (
                                            id integer NOT NULL,
                                            customer_email text,
                                            country_code text NOT NULL,
                                            year integer NOT NULL,
                                            commitment jsonb NOT NULL,
                                            service_setup jsonb,
                                            is_license_required boolean DEFAULT false NOT NULL,
                                            blame jsonb NOT NULL,
                                            created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                            updated_at timestamp(3) without time zone NOT NULL,
                                            deleted_at date
);




--
-- Name: customer_commitment_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.customer_commitment_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: customer_commitment_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.customer_commitment_id_seq OWNED BY public.customer_commitment.id;


--
-- Name: customer_consent; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.customer_consent (
                                         id integer NOT NULL,
                                         customer_id integer NOT NULL,
                                         consent_id integer NOT NULL,
                                         given boolean NOT NULL,
                                         "givenAt" timestamp(3) without time zone,
                                         "revokedAt" timestamp(3) without time zone,
                                         created_at timestamp(3) without time zone NOT NULL,
                                         updated_at timestamp(3) without time zone NOT NULL
);




--
-- Name: customer_consent_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.customer_consent_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: customer_consent_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.customer_consent_id_seq OWNED BY public.customer_consent.id;


--
-- Name: customer_document; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.customer_document (
                                          id integer NOT NULL,
                                          customer_id integer NOT NULL,
                                          document_url text NOT NULL,
                                          status text NOT NULL
);




--
-- Name: customer_document_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.customer_document_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: customer_document_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.customer_document_id_seq OWNED BY public.customer_document.id;


--
-- Name: customer_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.customer_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: customer_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.customer_id_seq OWNED BY public.customer.id;


--
-- Name: customer_invitation; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.customer_invitation (
                                            id integer NOT NULL,
                                            comission_date timestamp(3) without time zone NOT NULL,
                                            product text NOT NULL,
                                            comission numeric(65,30) NOT NULL,
                                            order_number text NOT NULL,
                                            lead_source text NOT NULL,
                                            customer_id integer NOT NULL,
                                            invited_customer_id integer
);




--
-- Name: customer_invitation_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.customer_invitation_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: customer_invitation_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.customer_invitation_id_seq OWNED BY public.customer_invitation.id;


--
-- Name: customer_invite_token; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.customer_invite_token (
                                              id integer NOT NULL,
                                              token text NOT NULL,
                                              share_link text NOT NULL,
                                              customer_id integer,
                                              created_at timestamp(3) without time zone NOT NULL,
                                              updated_at timestamp(3) without time zone NOT NULL,
                                              deleted_at date,
                                              expiration_date date
);




--
-- Name: customer_invite_token_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.customer_invite_token_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: customer_invite_token_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.customer_invite_token_id_seq OWNED BY public.customer_invite_token.id;


--
-- Name: customer_phone; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.customer_phone (
                                       id integer NOT NULL,
                                       phone_number text NOT NULL,
                                       customer_id integer,
                                       created_at timestamp(3) without time zone NOT NULL,
                                       updated_at timestamp(3) without time zone NOT NULL,
                                       deleted_at date,
                                       phone_type text DEFAULT 'PHONE'::text NOT NULL
);




--
-- Name: customer_phone_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.customer_phone_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: customer_phone_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.customer_phone_id_seq OWNED BY public.customer_phone.id;


--
-- Name: customer_service_step; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.customer_service_step (
                                              id integer NOT NULL,
                                              service_step_id integer NOT NULL,
                                              customer_id integer NOT NULL,
                                              done_at timestamp(3) without time zone,
                                              created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                              updated_at timestamp(3) without time zone NOT NULL,
                                              deleted_at date
);




--
-- Name: customer_service_step_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.customer_service_step_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: customer_service_step_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.customer_service_step_id_seq OWNED BY public.customer_service_step.id;


--
-- Name: customer_tutorial; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.customer_tutorial (
                                          id integer NOT NULL,
                                          customer_id integer NOT NULL,
                                          service_type public.enum_contract_type NOT NULL,
                                          is_finished boolean DEFAULT false NOT NULL,
                                          created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                          updated_at timestamp(3) without time zone NOT NULL,
                                          deleted_at date
);




--
-- Name: customer_tutorial_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.customer_tutorial_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: customer_tutorial_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.customer_tutorial_id_seq OWNED BY public.customer_tutorial.id;


--
-- Name: decline; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.decline (
                                id integer NOT NULL,
                                title text NOT NULL,
                                license_required_information_id integer,
                                license_volume_report_id integer,
                                license_volume_report_item_id integer,
                                created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                updated_at timestamp(3) without time zone NOT NULL,
                                deleted_at date,
                                CONSTRAINT pk_decline PRIMARY KEY (id)
);




--
-- Name: decline_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.decline_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: decline_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.decline_id_seq OWNED BY public.decline.id;


--
-- Name: decline_reason; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.decline_reason (
    id         integer NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date,
    decline_id INT     NOT NULL,
    reason_id  INT     NOT NULL,

    CONSTRAINT pk_decline_reason PRIMARY KEY (id),
    FOREIGN KEY (decline_id) REFERENCES decline (id),
    CONSTRAINT fk_decline FOREIGN KEY (decline_id) REFERENCES decline (id),
    CONSTRAINT fk_reason FOREIGN KEY (reason_id) REFERENCES reason (id)

);




--
-- Name: decline_reason_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.decline_reason_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: decline_reason_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.decline_reason_id_seq OWNED BY public.decline_reason.id;


--
-- Name: decline_to_decline_reason; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.decline_to_decline_reason (
    id integer NOT NULL,
    decline_id integer NOT NULL,
    reason_id integer NOT NULL, -- CORRECTED COLUMN NAME
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date
);




--
-- Name: decline_to_decline_reason_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.decline_to_decline_reason_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: decline_to_decline_reason_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.decline_to_decline_reason_id_seq OWNED BY public.decline_to_decline_reason.id;


--
-- Name: file; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.file (
                             id text NOT NULL,
                             user_id text NOT NULL,
                             name text NOT NULL,
                             original_name text NOT NULL,
                             extension text NOT NULL,
                             size text NOT NULL,
                             type public.enum_file_type NOT NULL,
                             created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                             updated_at timestamp(3) without time zone NOT NULL,
                             deleted_at date,
                             required_information_id integer,
                             contract_id integer,
                             certificate_id integer,
                             license_id integer,
                             termination_id integer,
                             general_information_id integer,
                             third_party_invoice_id integer,
                             marketing_material_id integer,
                             partner_contract_id integer,
                             order_id integer
);




--
-- Name: general_information; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.general_information (
                                            id integer NOT NULL,
                                            setup_general_information_id integer NOT NULL,
                                            contract_id integer NOT NULL,
                                            type public.enum_general_information_type NOT NULL,
                                            status public.enum_general_information_status DEFAULT 'OPEN'::public.enum_general_information_status NOT NULL,
                                            name text NOT NULL,
                                            description text NOT NULL,
                                            created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                            updated_at timestamp(3) without time zone NOT NULL,
                                            deleted_at timestamp(3) without time zone,
                                            question text,
                                            file_id text,
                                            answer text
);




--
-- Name: general_information_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.general_information_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: general_information_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.general_information_id_seq OWNED BY public.general_information.id;


--
-- Name: license; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.license (
                                id integer NOT NULL,
                                contract_id integer NOT NULL,
                                registration_number text NOT NULL,
                                registration_status public.enum_license_registration_status DEFAULT 'PENDING'::public.enum_license_registration_status NOT NULL,
                                clerk_control_status public.enum_license_clerk_control_status DEFAULT 'PENDING'::public.enum_license_clerk_control_status NOT NULL,
                                contract_status public.enum_license_contract_status DEFAULT 'ACTIVE'::public.enum_license_contract_status NOT NULL,
                                country_id integer NOT NULL,
                                country_code text NOT NULL,
                                country_name text NOT NULL,
                                country_flag text NOT NULL,
                                year integer NOT NULL,
                                start_date timestamp(3) without time zone NOT NULL,
                                end_date timestamp(3) without time zone,
                                termination_id integer,
                                created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                updated_at timestamp(3) without time zone NOT NULL,
                                deleted_at date,
                                registration_and_termination_monday_ref integer
);




--
-- Name: license_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.license_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: license_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.license_id_seq OWNED BY public.license.id;


--
-- Name: license_other_cost; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.license_other_cost (
                                           id integer NOT NULL,
                                           setup_other_cost_id integer NOT NULL,
                                           license_id integer NOT NULL,
                                           name text NOT NULL,
                                           price integer NOT NULL,
                                           created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                           updated_at timestamp(3) without time zone NOT NULL,
                                           deleted_at date
);




--
-- Name: license_other_cost_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.license_other_cost_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: license_other_cost_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.license_other_cost_id_seq OWNED BY public.license_other_cost.id;


--
-- Name: license_packaging_service; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.license_packaging_service (
                                                  id integer NOT NULL,
                                                  setup_packaging_service_id integer NOT NULL,
                                                  license_id integer NOT NULL,
                                                  name text NOT NULL,
                                                  description text NOT NULL,
                                                  created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                                  updated_at timestamp(3) without time zone NOT NULL,
                                                  deleted_at date,
                                                  obliged boolean DEFAULT false NOT NULL
);




--
-- Name: license_packaging_service_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.license_packaging_service_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: license_packaging_service_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.license_packaging_service_id_seq OWNED BY public.license_packaging_service.id;


--
-- Name: license_price_list; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.license_price_list (
                                           id integer NOT NULL,
                                           setup_price_list_id integer NOT NULL,
                                           license_id integer NOT NULL,
                                           name text NOT NULL,
                                           description text NOT NULL,
                                           condition_type text NOT NULL,
                                           condition_type_value text NOT NULL,
                                           start_date timestamp(3) without time zone NOT NULL,
                                           end_date timestamp(3) without time zone NOT NULL,
                                           basic_price integer,
                                           minimum_price integer,
                                           registration_fee integer,
                                           handling_fee integer,
                                           variable_handling_fee double precision,
                                           thresholds jsonb,
                                           created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                           updated_at timestamp(3) without time zone NOT NULL,
                                           deleted_at timestamp(3) without time zone
);




--
-- Name: license_price_list_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.license_price_list_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: license_price_list_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.license_price_list_id_seq OWNED BY public.license_price_list.id;


--
-- Name: license_report_set; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.license_report_set (
                                           id integer NOT NULL,
                                           setup_report_set_id integer NOT NULL,
                                           license_packaging_service_id integer NOT NULL,
                                           created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                           updated_at timestamp(3) without time zone NOT NULL,
                                           deleted_at date
);




--
-- Name: license_report_set_frequency; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.license_report_set_frequency (
                                                     id integer NOT NULL,
                                                     setup_report_set_frequency_id integer NOT NULL,
                                                     license_packaging_service_id integer NOT NULL,
                                                     rhythm public.enum_license_report_set_rhythm NOT NULL,
                                                     frequency jsonb NOT NULL,
                                                     created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                                     updated_at timestamp(3) without time zone NOT NULL,
                                                     deleted_at date
);




--
-- Name: license_report_set_frequency_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.license_report_set_frequency_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: license_report_set_frequency_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.license_report_set_frequency_id_seq OWNED BY public.license_report_set_frequency.id;


--
-- Name: license_report_set_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.license_report_set_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: license_report_set_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.license_report_set_id_seq OWNED BY public.license_report_set.id;


--
-- Name: license_representative_tier; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.license_representative_tier (
                                                    id integer NOT NULL,
                                                    setup_representative_tier_id integer NOT NULL,
                                                    license_id integer NOT NULL,
                                                    name text NOT NULL,
                                                    price integer NOT NULL,
                                                    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                                    updated_at timestamp(3) without time zone NOT NULL,
                                                    deleted_at date
);




--
-- Name: license_representative_tier_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.license_representative_tier_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: license_representative_tier_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.license_representative_tier_id_seq OWNED BY public.license_representative_tier.id;


--
-- Name: license_required_information; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.license_required_information (
                                                     id integer NOT NULL,
                                                     setup_required_information_id integer,
                                                     license_id integer,
                                                     type public.enum_license_required_information_type NOT NULL,
                                                     status public.enum_license_required_information_status DEFAULT 'OPEN'::public.enum_license_required_information_status NOT NULL,
                                                     name text NOT NULL,
                                                     description text NOT NULL,
                                                     created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                                     updated_at timestamp(3) without time zone NOT NULL,
                                                     deleted_at timestamp(3) without time zone,
                                                     question text,
                                                     file_id text,
                                                     answer text,
                                                     kind public.enum_license_required_information_kind DEFAULT 'REQUIRED_INFORMATION'::public.enum_license_required_information_kind NOT NULL,
                                                     contract_id integer,
                                                     setup_general_information_id integer
);




--
-- Name: license_required_information_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.license_required_information_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: license_required_information_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.license_required_information_id_seq OWNED BY public.license_required_information.id;


--
-- Name: license_third_party_invoice; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.license_third_party_invoice (
                                                    id integer NOT NULL,
                                                    title text NOT NULL,
                                                    price integer NOT NULL,
                                                    issued_at timestamp(3) without time zone NOT NULL,
                                                    status public.enum_license_third_party_invoice_status DEFAULT 'OPEN'::public.enum_license_third_party_invoice_status NOT NULL,
                                                    license_id integer,
                                                    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                                    updated_at timestamp(3) without time zone NOT NULL,
                                                    deleted_at date,
                                                    due_date timestamp(3) without time zone NOT NULL,
                                                    issuer public."LicenseThirdPartyInvoiceIssuer" NOT NULL,
                                                    third_party_invoice_monday_ref integer
);




--
-- Name: license_third_party_invoice_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.license_third_party_invoice_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: license_third_party_invoice_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.license_third_party_invoice_id_seq OWNED BY public.license_third_party_invoice.id;


--
-- Name: license_volume_report; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.license_volume_report (
                                              id integer NOT NULL,
                                              license_packaging_service_id integer NOT NULL,
                                              status public.enum_license_volume_report_status DEFAULT 'OPEN'::public.enum_license_volume_report_status NOT NULL,
                                              year integer NOT NULL,
                                              "interval" text NOT NULL,
                                              report_table jsonb NOT NULL,
                                              created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                              updated_at timestamp(3) without time zone NOT NULL,
                                              deleted_at date,
                                              volume_report_monday_ref integer,
                                              stage text
);




--
-- Name: license_volume_report_decline_reason; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.license_volume_report_decline_reason (
                                                             id integer NOT NULL,
                                                             license_volume_report_error_id integer NOT NULL,
                                                             report_decline_reason_id integer NOT NULL,
                                                             created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                                             updated_at timestamp(3) without time zone NOT NULL,
                                                             deleted_at date
);




--
-- Name: license_volume_report_decline_reason_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.license_volume_report_decline_reason_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: license_volume_report_decline_reason_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.license_volume_report_decline_reason_id_seq OWNED BY public.license_volume_report_decline_reason.id;


--
-- Name: license_volume_report_error; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.license_volume_report_error (
                                                    id integer NOT NULL,
                                                    license_volume_report_id integer,
                                                    license_volume_report_item_id integer,
                                                    description text NOT NULL,
                                                    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                                    updated_at timestamp(3) without time zone NOT NULL,
                                                    deleted_at date
);




--
-- Name: license_volume_report_error_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.license_volume_report_error_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: license_volume_report_error_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.license_volume_report_error_id_seq OWNED BY public.license_volume_report_error.id;


--
-- Name: license_volume_report_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.license_volume_report_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: license_volume_report_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.license_volume_report_id_seq OWNED BY public.license_volume_report.id;


--
-- Name: license_volume_report_item; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.license_volume_report_item (
                                                   id integer NOT NULL,
                                                   license_volume_report_id integer NOT NULL,
                                                   setup_fraction_id integer NOT NULL,
                                                   setup_column_id integer DEFAULT 0 NOT NULL,
                                                   value integer NOT NULL,
                                                   price integer DEFAULT 0 NOT NULL,
                                                   created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                                   updated_at timestamp(3) without time zone NOT NULL,
                                                   deleted_at date,
                                                   setup_column_code text,
                                                   setup_fraction_code text
);




--
-- Name: license_volume_report_item_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.license_volume_report_item_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: license_volume_report_item_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.license_volume_report_item_id_seq OWNED BY public.license_volume_report_item.id;


--
-- Name: marketing_material; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.marketing_material (
                                           id integer NOT NULL,
                                           name text NOT NULL,
                                           start_date date,
                                           end_date date,
                                           category public.enum_marketing_material_category NOT NULL,
                                           partner_restriction public.enum_marketing_material_partner_restriction NOT NULL,
                                           created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                           updated_at timestamp(3) without time zone NOT NULL,
                                           deleted_at date
);




--
-- Name: marketing_material_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.marketing_material_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: marketing_material_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.marketing_material_id_seq OWNED BY public.marketing_material.id;


--
-- Name: marketing_material_partner; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.marketing_material_partner (
                                                   id integer NOT NULL,
                                                   marketing_material_id integer NOT NULL,
                                                   partner_id integer NOT NULL
);




--
-- Name: marketing_material_partner_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.marketing_material_partner_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: marketing_material_partner_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.marketing_material_partner_id_seq OWNED BY public.marketing_material_partner.id;


--
-- Name: partner; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.partner (
                                id integer NOT NULL,
                                first_name text NOT NULL,
                                last_name text NOT NULL,
                                email text NOT NULL,
                                user_id integer NOT NULL,
                                created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                updated_at timestamp(3) without time zone NOT NULL,
                                deleted_at date,
                                commission_mode text,
                                commission_percentage integer,
                                no_provision_negotiated boolean DEFAULT false,
                                payout_cycle text,
                                status public."PartnerStatus" DEFAULT 'NO_UPDATES'::public."PartnerStatus"
);




--
-- Name: partner_banking; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.partner_banking (
                                        id integer NOT NULL,
                                        partner_id integer,
                                        business_identifier_code text,
                                        international_account_number text,
                                        created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                        updated_at timestamp(3) without time zone NOT NULL,
                                        deleted_at date
);




--
-- Name: partner_banking_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.partner_banking_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: partner_banking_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.partner_banking_id_seq OWNED BY public.partner_banking.id;


--
-- Name: partner_contract; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.partner_contract (
                                         id integer NOT NULL,
                                         partner_id integer NOT NULL,
                                         status public."PartnerContractStatus" DEFAULT 'DRAFT'::public."PartnerContractStatus" NOT NULL,
                                         agreed_on timestamp(3) without time zone,
                                         start_date timestamp(3) without time zone NOT NULL,
                                         end_date timestamp(3) without time zone,
                                         created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                         updated_at timestamp(3) without time zone NOT NULL,
                                         deleted_at date
);




--
-- Name: partner_contract_change; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.partner_contract_change (
                                                id integer NOT NULL,
                                                partner_contract_id integer NOT NULL,
                                                change_type public."PartnerContractChangeType" NOT NULL,
                                                change_description text,
                                                created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);




--
-- Name: partner_contract_change_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.partner_contract_change_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: partner_contract_change_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.partner_contract_change_id_seq OWNED BY public.partner_contract_change.id;


--
-- Name: partner_contract_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.partner_contract_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: partner_contract_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.partner_contract_id_seq OWNED BY public.partner_contract.id;


--
-- Name: partner_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.partner_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: partner_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.partner_id_seq OWNED BY public.partner.id;


--
-- Name: recommended_country; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.recommended_country (
                                            id integer NOT NULL,
                                            name text NOT NULL,
                                            customer_id integer,
                                            created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                            updated_at timestamp(3) without time zone NOT NULL,
                                            deleted_at date
);




--
-- Name: recommended_country_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.recommended_country_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: recommended_country_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.recommended_country_id_seq OWNED BY public.recommended_country.id;


--
-- Name: report_decline_reason; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.report_decline_reason (
                                              id integer NOT NULL,
                                              title text NOT NULL,
                                              created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                              updated_at timestamp(3) without time zone NOT NULL,
                                              deleted_at date
);




--
-- Name: report_decline_reason_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.report_decline_reason_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: report_decline_reason_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.report_decline_reason_id_seq OWNED BY public.report_decline_reason.id;


--
-- Name: service_next_step; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.service_next_step (
                                          id integer NOT NULL,
                                          license_id integer,
                                          action_guide_id integer,
                                          title text NOT NULL,
                                          available_date timestamp(3) without time zone NOT NULL,
                                          deadline_date timestamp(3) without time zone NOT NULL,
                                          done_at timestamp(3) without time zone,
                                          created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                          updated_at timestamp(3) without time zone NOT NULL,
                                          deleted_at date
);




--
-- Name: service_next_step_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.service_next_step_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: service_next_step_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.service_next_step_id_seq OWNED BY public.service_next_step.id;


--
-- Name: service_step; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.service_step (
                                     id integer NOT NULL,
                                     type public.enum_service_step_type NOT NULL,
                                     country_code text,
                                     title text NOT NULL,
                                     available_at timestamp(3) without time zone NOT NULL,
                                     deadline_at timestamp(3) without time zone NOT NULL,
                                     is_active boolean NOT NULL,
                                     created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                     updated_at timestamp(3) without time zone NOT NULL,
                                     deleted_at date
);




--
-- Name: service_step_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.service_step_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: service_step_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.service_step_id_seq OWNED BY public.service_step.id;


--
-- Name: shopping_cart; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.shopping_cart (
                                      id text NOT NULL,
                                      cart_json jsonb,
                                      email text,
                                      created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                      updated_at timestamp(3) without time zone NOT NULL,
                                      deleted_at date,
                                      invoice_id integer,
                                      is_churned boolean DEFAULT false NOT NULL,
                                      journey public.shopping_cart_journey NOT NULL,
                                      journey_step text,
                                      payment jsonb,
                                      status public.shopping_cart_status DEFAULT 'OPEN'::public.shopping_cart_status NOT NULL,
                                      total integer DEFAULT 0 NOT NULL,
                                      vat_percentage integer DEFAULT 0 NOT NULL,
                                      vat_value integer DEFAULT 0 NOT NULL,
                                      coupon_id integer,
                                      coupon_type public.type_use_coupon,
                                      coupon_url_link text,
                                      affiliate_link text,
                                      affiliate_type public.type_affiliate,
                                      affiliate_customer_id integer,
                                      affiliate_partner_id integer
);




--
-- Name: shopping_cart_item; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.shopping_cart_item (
                                           id integer NOT NULL,
                                           shopping_cart_id text NOT NULL,
                                           service_type public.enum_contract_type NOT NULL,
                                           specification_type public.shopping_cart_item_specification_type NOT NULL,
                                           country_id integer NOT NULL,
                                           country_code text NOT NULL,
                                           country_name text NOT NULL,
                                           country_flag text NOT NULL,
                                           year integer NOT NULL,
                                           price_list jsonb NOT NULL,
                                           packaging_services jsonb,
                                           customer_commitment_id integer,
                                           created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                           updated_at timestamp(3) without time zone NOT NULL,
                                           deleted_at date,
                                           calculator jsonb
);




--
-- Name: shopping_cart_item_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.shopping_cart_item_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;




--
-- Name: shopping_cart_item_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.shopping_cart_item_id_seq OWNED BY public.shopping_cart_item.id;


--
-- Name: termination; Type: TABLE; Schema: public; Owner: epr-local-user
--

CREATE TABLE public.termination (
                                    id integer NOT NULL,
                                    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                    updated_at timestamp(3) without time zone NOT NULL,
                                    deleted_at date,
                                    completed_at timestamp(3) without time zone,
                                    requested_at timestamp(3) without time zone NOT NULL,
                                    status public.enum_termination_status DEFAULT 'REQUESTED'::public.enum_termination_status NOT NULL,

                                    CONSTRAINT pk_termination PRIMARY KEY (id)

);




--
-- Name: termination_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE public.termination_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;



--
-- Name: termination_reason_id_seq; Type: SEQUENCE; Schema: public; Owner: epr-local-user
--

CREATE SEQUENCE IF NOT EXISTS public.termination_reason_id_seq START WITH 1 INCREMENT BY 1;

CREATE TABLE public.termination_reason(
    id          INTEGER NOT NULL,
    created_at  TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at  TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    deleted_at  DATE,
    termination INTEGER NOT NULL,
    reason      INTEGER NOT NULL,

    CONSTRAINT pk_termination_reason PRIMARY KEY (id),

    CONSTRAINT fk_termination_reason_reason
        FOREIGN KEY (reason)
            REFERENCES reason (id),
    CONSTRAINT fk_termination_reason_termination
        FOREIGN KEY (termination)
            REFERENCES termination (id)
);





--
-- Name: termination_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: epr-local-user
--

ALTER SEQUENCE public.termination_id_seq OWNED BY public.termination.id;


--
-- Name: action_guide id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.action_guide ALTER COLUMN id SET DEFAULT nextval('public.action_guide_id_seq'::regclass);


--
-- Name: action_guide_price_list id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.action_guide_price_list ALTER COLUMN id SET DEFAULT nextval('public.action_guide_price_list_id_seq'::regclass);


--
-- Name: certificate id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.certificate ALTER COLUMN id SET DEFAULT nextval('public.certificate_id_seq'::regclass);


--
-- Name: cluster id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.cluster ALTER COLUMN id SET DEFAULT nextval('public.cluster_id_seq'::regclass);


--
-- Name: cluster_customers id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.cluster_customers ALTER COLUMN id SET DEFAULT nextval('public.cluster_customers_id_seq'::regclass);


--
-- Name: commission id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.commission ALTER COLUMN id SET DEFAULT nextval('public.commission_id_seq'::regclass);


--
-- Name: company id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.company ALTER COLUMN id SET DEFAULT nextval('public.company_id_seq'::regclass);


--
-- Name: company_address id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.company_address ALTER COLUMN id SET DEFAULT nextval('public.company_address_id_seq'::regclass);


--
-- Name: company_contact id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.company_contact ALTER COLUMN id SET DEFAULT nextval('public.company_contact_id_seq'::regclass);


--
-- Name: company_email id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.company_email ALTER COLUMN id SET DEFAULT nextval('public.company_email_id_seq'::regclass);


--
-- Name: consent id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.consent ALTER COLUMN id SET DEFAULT nextval('public.consent_id_seq'::regclass);


--
-- Name: contract id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.contract ALTER COLUMN id SET DEFAULT nextval('public.contract_id_seq'::regclass);


--
-- Name: coupon id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.coupon ALTER COLUMN id SET DEFAULT nextval('public.coupon_id_seq'::regclass);


--
-- Name: coupon_customer id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.coupon_customer ALTER COLUMN id SET DEFAULT nextval('public.coupon_customer_id_seq'::regclass);


--
-- Name: coupon_partners id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.coupon_partners ALTER COLUMN id SET DEFAULT nextval('public.coupon_partners_id_seq'::regclass);


--
-- Name: coupon_uses id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.coupon_uses ALTER COLUMN id SET DEFAULT nextval('public.coupon_uses_id_seq'::regclass);


--
-- Name: customer id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.customer ALTER COLUMN id SET DEFAULT nextval('public.customer_id_seq'::regclass);


--
-- Name: customer_commitment id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.customer_commitment ALTER COLUMN id SET DEFAULT nextval('public.customer_commitment_id_seq'::regclass);


--
-- Name: customer_consent id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.customer_consent ALTER COLUMN id SET DEFAULT nextval('public.customer_consent_id_seq'::regclass);


--
-- Name: customer_document id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.customer_document ALTER COLUMN id SET DEFAULT nextval('public.customer_document_id_seq'::regclass);


--
-- Name: customer_invitation id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.customer_invitation ALTER COLUMN id SET DEFAULT nextval('public.customer_invitation_id_seq'::regclass);


--
-- Name: customer_invite_token id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.customer_invite_token ALTER COLUMN id SET DEFAULT nextval('public.customer_invite_token_id_seq'::regclass);


--
-- Name: customer_phone id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.customer_phone ALTER COLUMN id SET DEFAULT nextval('public.customer_phone_id_seq'::regclass);


--
-- Name: customer_service_step id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.customer_service_step ALTER COLUMN id SET DEFAULT nextval('public.customer_service_step_id_seq'::regclass);


--
-- Name: customer_tutorial id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.customer_tutorial ALTER COLUMN id SET DEFAULT nextval('public.customer_tutorial_id_seq'::regclass);


--
-- Name: decline id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.decline ALTER COLUMN id SET DEFAULT nextval('public.decline_id_seq'::regclass);


--
-- Name: decline_reason id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.decline_reason ALTER COLUMN id SET DEFAULT nextval('public.decline_reason_id_seq'::regclass);


--
-- Name: decline_to_decline_reason id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.decline_to_decline_reason ALTER COLUMN id SET DEFAULT nextval('public.decline_to_decline_reason_id_seq'::regclass);


--
-- Name: general_information id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.general_information ALTER COLUMN id SET DEFAULT nextval('public.general_information_id_seq'::regclass);


--
-- Name: license id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license ALTER COLUMN id SET DEFAULT nextval('public.license_id_seq'::regclass);


--
-- Name: license_other_cost id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_other_cost ALTER COLUMN id SET DEFAULT nextval('public.license_other_cost_id_seq'::regclass);


--
-- Name: license_packaging_service id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_packaging_service ALTER COLUMN id SET DEFAULT nextval('public.license_packaging_service_id_seq'::regclass);


--
-- Name: license_price_list id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_price_list ALTER COLUMN id SET DEFAULT nextval('public.license_price_list_id_seq'::regclass);


--
-- Name: license_report_set id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_report_set ALTER COLUMN id SET DEFAULT nextval('public.license_report_set_id_seq'::regclass);


--
-- Name: license_report_set_frequency id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_report_set_frequency ALTER COLUMN id SET DEFAULT nextval('public.license_report_set_frequency_id_seq'::regclass);


--
-- Name: license_representative_tier id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_representative_tier ALTER COLUMN id SET DEFAULT nextval('public.license_representative_tier_id_seq'::regclass);


--
-- Name: license_required_information id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_required_information ALTER COLUMN id SET DEFAULT nextval('public.license_required_information_id_seq'::regclass);


--
-- Name: license_third_party_invoice id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_third_party_invoice ALTER COLUMN id SET DEFAULT nextval('public.license_third_party_invoice_id_seq'::regclass);


--
-- Name: license_volume_report id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_volume_report ALTER COLUMN id SET DEFAULT nextval('public.license_volume_report_id_seq'::regclass);


--
-- Name: license_volume_report_decline_reason id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_volume_report_decline_reason ALTER COLUMN id SET DEFAULT nextval('public.license_volume_report_decline_reason_id_seq'::regclass);


--
-- Name: license_volume_report_error id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_volume_report_error ALTER COLUMN id SET DEFAULT nextval('public.license_volume_report_error_id_seq'::regclass);


--
-- Name: license_volume_report_item id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_volume_report_item ALTER COLUMN id SET DEFAULT nextval('public.license_volume_report_item_id_seq'::regclass);


--
-- Name: marketing_material id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.marketing_material ALTER COLUMN id SET DEFAULT nextval('public.marketing_material_id_seq'::regclass);


--
-- Name: marketing_material_partner id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.marketing_material_partner ALTER COLUMN id SET DEFAULT nextval('public.marketing_material_partner_id_seq'::regclass);


--
-- Name: partner id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.partner ALTER COLUMN id SET DEFAULT nextval('public.partner_id_seq'::regclass);


--
-- Name: partner_banking id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.partner_banking ALTER COLUMN id SET DEFAULT nextval('public.partner_banking_id_seq'::regclass);


--
-- Name: partner_contract id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.partner_contract ALTER COLUMN id SET DEFAULT nextval('public.partner_contract_id_seq'::regclass);


--
-- Name: partner_contract_change id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.partner_contract_change ALTER COLUMN id SET DEFAULT nextval('public.partner_contract_change_id_seq'::regclass);


--
-- Name: recommended_country id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.recommended_country ALTER COLUMN id SET DEFAULT nextval('public.recommended_country_id_seq'::regclass);


--
-- Name: report_decline_reason id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.report_decline_reason ALTER COLUMN id SET DEFAULT nextval('public.report_decline_reason_id_seq'::regclass);


--
-- Name: service_next_step id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.service_next_step ALTER COLUMN id SET DEFAULT nextval('public.service_next_step_id_seq'::regclass);


--
-- Name: service_step id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.service_step ALTER COLUMN id SET DEFAULT nextval('public.service_step_id_seq'::regclass);


--
-- Name: shopping_cart_item id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.shopping_cart_item ALTER COLUMN id SET DEFAULT nextval('public.shopping_cart_item_id_seq'::regclass);


--
-- Name: termination id; Type: DEFAULT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.termination ALTER COLUMN id SET DEFAULT nextval('public.termination_id_seq'::regclass);


--
-- Name: action_guide action_guide_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.action_guide
    ADD CONSTRAINT action_guide_pkey PRIMARY KEY (id);


--
-- Name: action_guide_price_list action_guide_price_list_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.action_guide_price_list
    ADD CONSTRAINT action_guide_price_list_pkey PRIMARY KEY (id);


--
-- Name: certificate certificate_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.certificate
    ADD CONSTRAINT certificate_pkey PRIMARY KEY (id);


--
-- Name: cluster_customers cluster_customers_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.cluster_customers
    ADD CONSTRAINT cluster_customers_pkey PRIMARY KEY (id);


--
-- Name: cluster cluster_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.cluster
    ADD CONSTRAINT cluster_pkey PRIMARY KEY (id);


--
-- Name: commission commission_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.commission
    ADD CONSTRAINT commission_pkey PRIMARY KEY (id);


--
-- Name: company_address company_address_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.company_address
    ADD CONSTRAINT company_address_pkey PRIMARY KEY (id);


--
-- Name: company_contact company_contact_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.company_contact
    ADD CONSTRAINT company_contact_pkey PRIMARY KEY (id);


--
-- Name: company_email company_email_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.company_email
    ADD CONSTRAINT company_email_pkey PRIMARY KEY (id);


--
-- Name: company company_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.company
    ADD CONSTRAINT company_pkey PRIMARY KEY (id);


--
-- Name: consent consent_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.consent
    ADD CONSTRAINT consent_pkey PRIMARY KEY (id);


--
-- Name: contract contract_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.contract
    ADD CONSTRAINT contract_pkey PRIMARY KEY (id);


--
-- Name: coupon_customer coupon_customer_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.coupon_customer
    ADD CONSTRAINT coupon_customer_pkey PRIMARY KEY (id);


--
-- Name: coupon_partners coupon_partners_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.coupon_partners
    ADD CONSTRAINT coupon_partners_pkey PRIMARY KEY (id);


--
-- Name: coupon coupon_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.coupon
    ADD CONSTRAINT coupon_pkey PRIMARY KEY (id);


--
-- Name: coupon_uses coupon_uses_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.coupon_uses
    ADD CONSTRAINT coupon_uses_pkey PRIMARY KEY (id);


--
-- Name: customer_commitment customer_commitment_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.customer_commitment
    ADD CONSTRAINT customer_commitment_pkey PRIMARY KEY (id);


--
-- Name: customer_consent customer_consent_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.customer_consent
    ADD CONSTRAINT customer_consent_pkey PRIMARY KEY (id);


--
-- Name: customer_document customer_document_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.customer_document
    ADD CONSTRAINT customer_document_pkey PRIMARY KEY (id);


--
-- Name: customer_invitation customer_invitation_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.customer_invitation
    ADD CONSTRAINT customer_invitation_pkey PRIMARY KEY (id);


--
-- Name: customer_invite_token customer_invite_token_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.customer_invite_token
    ADD CONSTRAINT customer_invite_token_pkey PRIMARY KEY (id);


--
-- Name: customer_phone customer_phone_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.customer_phone
    ADD CONSTRAINT customer_phone_pkey PRIMARY KEY (id);


--
-- Name: customer customer_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.customer
    ADD CONSTRAINT customer_pkey PRIMARY KEY (id);


--
-- Name: customer_service_step customer_service_step_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.customer_service_step
    ADD CONSTRAINT customer_service_step_pkey PRIMARY KEY (id);


--
-- Name: customer_tutorial customer_tutorial_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.customer_tutorial
    ADD CONSTRAINT customer_tutorial_pkey PRIMARY KEY (id);

--
-- Name: decline_to_decline_reason decline_to_decline_reason_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.decline_to_decline_reason
    ADD CONSTRAINT decline_to_decline_reason_pkey PRIMARY KEY (id);


--
-- Name: file file_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.file
    ADD CONSTRAINT file_pkey PRIMARY KEY (id);


--
-- Name: general_information general_information_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.general_information
    ADD CONSTRAINT general_information_pkey PRIMARY KEY (id);


--
-- Name: license_other_cost license_other_cost_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_other_cost
    ADD CONSTRAINT license_other_cost_pkey PRIMARY KEY (id);


--
-- Name: license_packaging_service license_packaging_service_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_packaging_service
    ADD CONSTRAINT license_packaging_service_pkey PRIMARY KEY (id);


--
-- Name: license license_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license
    ADD CONSTRAINT license_pkey PRIMARY KEY (id);


--
-- Name: license_price_list license_price_list_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_price_list
    ADD CONSTRAINT license_price_list_pkey PRIMARY KEY (id);


--
-- Name: license_report_set_frequency license_report_set_frequency_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_report_set_frequency
    ADD CONSTRAINT license_report_set_frequency_pkey PRIMARY KEY (id);


--
-- Name: license_report_set license_report_set_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_report_set
    ADD CONSTRAINT license_report_set_pkey PRIMARY KEY (id);


--
-- Name: license_representative_tier license_representative_tier_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_representative_tier
    ADD CONSTRAINT license_representative_tier_pkey PRIMARY KEY (id);


--
-- Name: license_required_information license_required_information_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_required_information
    ADD CONSTRAINT license_required_information_pkey PRIMARY KEY (id);


--
-- Name: license_third_party_invoice license_third_party_invoice_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_third_party_invoice
    ADD CONSTRAINT license_third_party_invoice_pkey PRIMARY KEY (id);


--
-- Name: license_volume_report_decline_reason license_volume_report_decline_reason_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_volume_report_decline_reason
    ADD CONSTRAINT license_volume_report_decline_reason_pkey PRIMARY KEY (id);


--
-- Name: license_volume_report_error license_volume_report_error_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_volume_report_error
    ADD CONSTRAINT license_volume_report_error_pkey PRIMARY KEY (id);


--
-- Name: license_volume_report_item license_volume_report_item_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_volume_report_item
    ADD CONSTRAINT license_volume_report_item_pkey PRIMARY KEY (id);


--
-- Name: license_volume_report license_volume_report_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_volume_report
    ADD CONSTRAINT license_volume_report_pkey PRIMARY KEY (id);


--
-- Name: marketing_material_partner marketing_material_partner_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.marketing_material_partner
    ADD CONSTRAINT marketing_material_partner_pkey PRIMARY KEY (id);


--
-- Name: marketing_material marketing_material_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.marketing_material
    ADD CONSTRAINT marketing_material_pkey PRIMARY KEY (id);


--
-- Name: partner_banking partner_banking_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.partner_banking
    ADD CONSTRAINT partner_banking_pkey PRIMARY KEY (id);


--
-- Name: partner_contract_change partner_contract_change_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.partner_contract_change
    ADD CONSTRAINT partner_contract_change_pkey PRIMARY KEY (id);


--
-- Name: partner_contract partner_contract_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.partner_contract
    ADD CONSTRAINT partner_contract_pkey PRIMARY KEY (id);


--
-- Name: partner partner_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.partner
    ADD CONSTRAINT partner_pkey PRIMARY KEY (id);


--
-- Name: recommended_country recommended_country_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.recommended_country
    ADD CONSTRAINT recommended_country_pkey PRIMARY KEY (id);


--
-- Name: report_decline_reason report_decline_reason_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.report_decline_reason
    ADD CONSTRAINT report_decline_reason_pkey PRIMARY KEY (id);


--
-- Name: service_next_step service_next_step_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.service_next_step
    ADD CONSTRAINT service_next_step_pkey PRIMARY KEY (id);


--
-- Name: service_step service_step_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.service_step
    ADD CONSTRAINT service_step_pkey PRIMARY KEY (id);


--
-- Name: shopping_cart_item shopping_cart_item_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.shopping_cart_item
    ADD CONSTRAINT shopping_cart_item_pkey PRIMARY KEY (id);


--
-- Name: shopping_cart shopping_cart_pkey; Type: CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.shopping_cart
    ADD CONSTRAINT shopping_cart_pkey PRIMARY KEY (id);

--
-- Name: company_contact_company_id_key; Type: INDEX; Schema: public; Owner: epr-local-user
--

CREATE UNIQUE INDEX company_contact_company_id_key ON public.company_contact USING btree (company_id);


--
-- Name: coupon_code_key; Type: INDEX; Schema: public; Owner: epr-local-user
--

CREATE UNIQUE INDEX coupon_code_key ON public.coupon USING btree (code);


--
-- Name: coupon_link_key; Type: INDEX; Schema: public; Owner: epr-local-user
--

CREATE UNIQUE INDEX coupon_link_key ON public.coupon USING btree (link);


--
-- Name: customer_commitment_customer_email_country_code_year_key; Type: INDEX; Schema: public; Owner: epr-local-user
--

CREATE UNIQUE INDEX customer_commitment_customer_email_country_code_year_key ON public.customer_commitment USING btree (customer_email, country_code, year);


--
-- Name: customer_consent_customer_id_consent_id_key; Type: INDEX; Schema: public; Owner: epr-local-user
--

CREATE UNIQUE INDEX customer_consent_customer_id_consent_id_key ON public.customer_consent USING btree (customer_id, consent_id);


--
-- Name: customer_email_key; Type: INDEX; Schema: public; Owner: epr-local-user
--

CREATE UNIQUE INDEX customer_email_key ON public.customer USING btree (email);


--
-- Name: customer_invite_token_customer_id_key; Type: INDEX; Schema: public; Owner: epr-local-user
--

CREATE UNIQUE INDEX customer_invite_token_customer_id_key ON public.customer_invite_token USING btree (customer_id);


--
-- Name: customer_tutorial_customer_id_service_type_key; Type: INDEX; Schema: public; Owner: epr-local-user
--

CREATE UNIQUE INDEX customer_tutorial_customer_id_service_type_key ON public.customer_tutorial USING btree (customer_id, service_type);


--
-- Name: customer_unique; Type: INDEX; Schema: public; Owner: epr-local-user
--

CREATE UNIQUE INDEX customer_unique ON public.customer USING btree (user_id);


--
-- Name: decline_license_required_information_id_key; Type: INDEX; Schema: public; Owner: epr-local-user
--

CREATE UNIQUE INDEX decline_license_required_information_id_key ON public.decline USING btree (license_required_information_id);


--
-- Name: decline_license_volume_report_id_key; Type: INDEX; Schema: public; Owner: epr-local-user
--

CREATE UNIQUE INDEX decline_license_volume_report_id_key ON public.decline USING btree (license_volume_report_id);


--
-- Name: decline_license_volume_report_item_id_key; Type: INDEX; Schema: public; Owner: epr-local-user
--

CREATE UNIQUE INDEX decline_license_volume_report_item_id_key ON public.decline USING btree (license_volume_report_item_id);


--
-- Name: license_registration_and_termination_monday_ref_unique; Type: INDEX; Schema: public; Owner: epr-local-user
--

CREATE UNIQUE INDEX license_registration_and_termination_monday_ref_unique ON public.license USING btree (registration_and_termination_monday_ref);


--
-- Name: license_report_set_frequency_license_packaging_service_id_key; Type: INDEX; Schema: public; Owner: epr-local-user
--

CREATE UNIQUE INDEX license_report_set_frequency_license_packaging_service_id_key ON public.license_report_set_frequency USING btree (license_packaging_service_id);


--
-- Name: license_report_set_license_packaging_service_id_key; Type: INDEX; Schema: public; Owner: epr-local-user
--

CREATE UNIQUE INDEX license_report_set_license_packaging_service_id_key ON public.license_report_set USING btree (license_packaging_service_id);


--
-- Name: license_third_party_invoice_unique; Type: INDEX; Schema: public; Owner: epr-local-user
--

CREATE UNIQUE INDEX license_third_party_invoice_unique ON public.license_third_party_invoice USING btree (third_party_invoice_monday_ref);


--
-- Name: partner_banking_partner_id_key; Type: INDEX; Schema: public; Owner: epr-local-user
--

CREATE UNIQUE INDEX partner_banking_partner_id_key ON public.partner_banking USING btree (partner_id);


--
-- Name: partner_contract_partner_id_key; Type: INDEX; Schema: public; Owner: epr-local-user
--

CREATE UNIQUE INDEX partner_contract_partner_id_key ON public.partner_contract USING btree (partner_id);


--
-- Name: shopping_cart_item_customer_commitment_id_key; Type: INDEX; Schema: public; Owner: epr-local-user
--

CREATE UNIQUE INDEX shopping_cart_item_customer_commitment_id_key ON public.shopping_cart_item USING btree (customer_commitment_id);


--
-- Name: action_guide action_guide_contract_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.action_guide
    ADD CONSTRAINT action_guide_contract_id_fkey FOREIGN KEY (contract_id) REFERENCES public.contract(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: action_guide_price_list action_guide_price_list_action_guide_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.action_guide_price_list
    ADD CONSTRAINT action_guide_price_list_action_guide_id_fkey FOREIGN KEY (action_guide_id) REFERENCES public.action_guide(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: action_guide action_guide_termination_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.action_guide
    ADD CONSTRAINT action_guide_termination_id_fkey FOREIGN KEY (termination_id) REFERENCES public.termination(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: certificate certificate_license_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.certificate
    ADD CONSTRAINT certificate_license_id_fkey FOREIGN KEY (license_id) REFERENCES public.license(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: cluster_customers cluster_customers_cluster_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.cluster_customers
    ADD CONSTRAINT cluster_customers_cluster_id_fkey FOREIGN KEY (cluster_id) REFERENCES public.cluster(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: cluster_customers cluster_customers_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.cluster_customers
    ADD CONSTRAINT cluster_customers_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customer(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: commission commission_coupon_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.commission
    ADD CONSTRAINT commission_coupon_id_fkey FOREIGN KEY (coupon_id) REFERENCES public.coupon(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: commission commission_order_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.commission
    ADD CONSTRAINT commission_order_customer_id_fkey FOREIGN KEY (order_customer_id) REFERENCES public.customer(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: company company_address_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.company
    ADD CONSTRAINT company_address_id_fkey FOREIGN KEY (address_id) REFERENCES public.company_address(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: company_contact company_contact_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.company_contact
    ADD CONSTRAINT company_contact_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.company(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: company company_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.company
    ADD CONSTRAINT company_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customer(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: company_email company_email_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.company_email
    ADD CONSTRAINT company_email_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.company(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: company company_partner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.company
    ADD CONSTRAINT company_partner_id_fkey FOREIGN KEY (partner_id) REFERENCES public.partner(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: contract contract_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.contract
    ADD CONSTRAINT contract_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customer(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: contract contract_termination_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.contract
    ADD CONSTRAINT contract_termination_id_fkey FOREIGN KEY (termination_id) REFERENCES public.termination(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: coupon_customer coupon_customer_coupon_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.coupon_customer
    ADD CONSTRAINT coupon_customer_coupon_id_fkey FOREIGN KEY (coupon_id) REFERENCES public.coupon(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: coupon_customer coupon_customer_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.coupon_customer
    ADD CONSTRAINT coupon_customer_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customer(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: coupon_partners coupon_partners_coupon_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.coupon_partners
    ADD CONSTRAINT coupon_partners_coupon_id_fkey FOREIGN KEY (coupon_id) REFERENCES public.coupon(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: coupon_partners coupon_partners_partner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.coupon_partners
    ADD CONSTRAINT coupon_partners_partner_id_fkey FOREIGN KEY (partner_id) REFERENCES public.partner(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: coupon_uses coupon_uses_coupon_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.coupon_uses
    ADD CONSTRAINT coupon_uses_coupon_id_fkey FOREIGN KEY (coupon_id) REFERENCES public.coupon(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: coupon_uses coupon_uses_shopping_cart_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.coupon_uses
    ADD CONSTRAINT coupon_uses_shopping_cart_id_fkey FOREIGN KEY (shopping_cart_id) REFERENCES public.shopping_cart(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: customer_commitment customer_commitment_customer_email_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.customer_commitment
    ADD CONSTRAINT customer_commitment_customer_email_fkey FOREIGN KEY (customer_email) REFERENCES public.customer(email) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: customer_consent customer_consent_consent_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.customer_consent
    ADD CONSTRAINT customer_consent_consent_id_fkey FOREIGN KEY (consent_id) REFERENCES public.consent(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: customer_consent customer_consent_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.customer_consent
    ADD CONSTRAINT customer_consent_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customer(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: customer_document customer_document_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.customer_document
    ADD CONSTRAINT customer_document_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customer(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: customer_invitation customer_invitation_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.customer_invitation
    ADD CONSTRAINT customer_invitation_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customer(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: customer_invitation customer_invitation_invited_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.customer_invitation
    ADD CONSTRAINT customer_invitation_invited_customer_id_fkey FOREIGN KEY (invited_customer_id) REFERENCES public.customer(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: customer_invite_token customer_invite_token_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.customer_invite_token
    ADD CONSTRAINT customer_invite_token_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customer(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: customer_phone customer_phone_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.customer_phone
    ADD CONSTRAINT customer_phone_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customer(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: customer_service_step customer_service_step_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.customer_service_step
    ADD CONSTRAINT customer_service_step_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customer(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: customer_service_step customer_service_step_service_step_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.customer_service_step
    ADD CONSTRAINT customer_service_step_service_step_id_fkey FOREIGN KEY (service_step_id) REFERENCES public.service_step(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: customer_tutorial customer_tutorial_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.customer_tutorial
    ADD CONSTRAINT customer_tutorial_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customer(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: decline decline_license_required_information_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.decline
    ADD CONSTRAINT decline_license_required_information_id_fkey FOREIGN KEY (license_required_information_id) REFERENCES public.license_required_information(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: decline decline_license_volume_report_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.decline
    ADD CONSTRAINT decline_license_volume_report_id_fkey FOREIGN KEY (license_volume_report_id) REFERENCES public.license_volume_report(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: decline decline_license_volume_report_item_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.decline
    ADD CONSTRAINT decline_license_volume_report_item_id_fkey FOREIGN KEY (license_volume_report_item_id) REFERENCES public.license_volume_report_item(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: decline_to_decline_reason decline_to_decline_reason_decline_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.decline_to_decline_reason
    ADD CONSTRAINT decline_to_decline_reason_decline_id_fkey FOREIGN KEY (decline_id) REFERENCES public.decline(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: decline_to_decline_reason decline_to_decline_reason_decline_reason_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

--ALTER TABLE ONLY public.decline_to_decline_reason
--    ADD CONSTRAINT decline_to_decline_reason_decline_reason_id_fkey FOREIGN KEY (decline_reason_id) REFERENCES public.decline_reason(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: file file_certificate_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.file
    ADD CONSTRAINT file_certificate_id_fkey FOREIGN KEY (certificate_id) REFERENCES public.certificate(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: file file_contract_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.file
    ADD CONSTRAINT file_contract_id_fkey FOREIGN KEY (contract_id) REFERENCES public.contract(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: file file_general_information_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.file
    ADD CONSTRAINT file_general_information_id_fkey FOREIGN KEY (general_information_id) REFERENCES public.general_information(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: file file_license_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.file
    ADD CONSTRAINT file_license_id_fkey FOREIGN KEY (license_id) REFERENCES public.license(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: file file_marketing_material_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.file
    ADD CONSTRAINT file_marketing_material_id_fkey FOREIGN KEY (marketing_material_id) REFERENCES public.marketing_material(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: file file_partner_contract_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.file
    ADD CONSTRAINT file_partner_contract_id_fkey FOREIGN KEY (partner_contract_id) REFERENCES public.partner_contract(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: file file_required_information_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.file
    ADD CONSTRAINT file_required_information_id_fkey FOREIGN KEY (required_information_id) REFERENCES public.license_required_information(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: file file_termination_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.file
    ADD CONSTRAINT file_termination_id_fkey FOREIGN KEY (termination_id) REFERENCES public.termination(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: file file_third_party_invoice_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.file
    ADD CONSTRAINT file_third_party_invoice_id_fkey FOREIGN KEY (third_party_invoice_id) REFERENCES public.license_third_party_invoice(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: license license_contract_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license
    ADD CONSTRAINT license_contract_id_fkey FOREIGN KEY (contract_id) REFERENCES public.contract(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: license_other_cost license_other_cost_license_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_other_cost
    ADD CONSTRAINT license_other_cost_license_id_fkey FOREIGN KEY (license_id) REFERENCES public.license(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: license_packaging_service license_packaging_service_license_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_packaging_service
    ADD CONSTRAINT license_packaging_service_license_id_fkey FOREIGN KEY (license_id) REFERENCES public.license(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: license_price_list license_price_list_license_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_price_list
    ADD CONSTRAINT license_price_list_license_id_fkey FOREIGN KEY (license_id) REFERENCES public.license(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: license_report_set_frequency license_report_set_frequency_license_packaging_service_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_report_set_frequency
    ADD CONSTRAINT license_report_set_frequency_license_packaging_service_id_fkey FOREIGN KEY (license_packaging_service_id) REFERENCES public.license_packaging_service(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: license_report_set license_report_set_license_packaging_service_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_report_set
    ADD CONSTRAINT license_report_set_license_packaging_service_id_fkey FOREIGN KEY (license_packaging_service_id) REFERENCES public.license_packaging_service(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: license_representative_tier license_representative_tier_license_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_representative_tier
    ADD CONSTRAINT license_representative_tier_license_id_fkey FOREIGN KEY (license_id) REFERENCES public.license(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: license_required_information license_required_information_contract_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_required_information
    ADD CONSTRAINT license_required_information_contract_id_fkey FOREIGN KEY (contract_id) REFERENCES public.contract(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: license_required_information license_required_information_license_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_required_information
    ADD CONSTRAINT license_required_information_license_id_fkey FOREIGN KEY (license_id) REFERENCES public.license(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: license license_termination_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license
    ADD CONSTRAINT license_termination_id_fkey FOREIGN KEY (termination_id) REFERENCES public.termination(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: license_third_party_invoice license_third_party_invoice_license_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_third_party_invoice
    ADD CONSTRAINT license_third_party_invoice_license_id_fkey FOREIGN KEY (license_id) REFERENCES public.license(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: license_volume_report_decline_reason license_volume_report_decline_reason_license_volume_report_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_volume_report_decline_reason
    ADD CONSTRAINT license_volume_report_decline_reason_license_volume_report_fkey FOREIGN KEY (license_volume_report_error_id) REFERENCES public.license_volume_report_error(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: license_volume_report_decline_reason license_volume_report_decline_reason_report_decline_reason_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_volume_report_decline_reason
    ADD CONSTRAINT license_volume_report_decline_reason_report_decline_reason_fkey FOREIGN KEY (report_decline_reason_id) REFERENCES public.report_decline_reason(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: license_volume_report_error license_volume_report_error_license_volume_report_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_volume_report_error
    ADD CONSTRAINT license_volume_report_error_license_volume_report_id_fkey FOREIGN KEY (license_volume_report_id) REFERENCES public.license_volume_report(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: license_volume_report_error license_volume_report_error_license_volume_report_item_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_volume_report_error
    ADD CONSTRAINT license_volume_report_error_license_volume_report_item_id_fkey FOREIGN KEY (license_volume_report_item_id) REFERENCES public.license_volume_report_item(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: license_volume_report_item license_volume_report_item_license_volume_report_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_volume_report_item
    ADD CONSTRAINT license_volume_report_item_license_volume_report_id_fkey FOREIGN KEY (license_volume_report_id) REFERENCES public.license_volume_report(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: license_volume_report license_volume_report_license_packaging_service_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.license_volume_report
    ADD CONSTRAINT license_volume_report_license_packaging_service_id_fkey FOREIGN KEY (license_packaging_service_id) REFERENCES public.license_packaging_service(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: marketing_material_partner marketing_material_partner_marketing_material_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.marketing_material_partner
    ADD CONSTRAINT marketing_material_partner_marketing_material_id_fkey FOREIGN KEY (marketing_material_id) REFERENCES public.marketing_material(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: marketing_material_partner marketing_material_partner_partner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.marketing_material_partner
    ADD CONSTRAINT marketing_material_partner_partner_id_fkey FOREIGN KEY (partner_id) REFERENCES public.partner(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: partner_banking partner_banking_partner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.partner_banking
    ADD CONSTRAINT partner_banking_partner_id_fkey FOREIGN KEY (partner_id) REFERENCES public.partner(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: partner_contract_change partner_contract_change_partner_contract_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.partner_contract_change
    ADD CONSTRAINT partner_contract_change_partner_contract_id_fkey FOREIGN KEY (partner_contract_id) REFERENCES public.partner_contract(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: partner_contract partner_contract_partner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.partner_contract
    ADD CONSTRAINT partner_contract_partner_id_fkey FOREIGN KEY (partner_id) REFERENCES public.partner(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: recommended_country recommended_country_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.recommended_country
    ADD CONSTRAINT recommended_country_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customer(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: service_next_step service_next_step_action_guide_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.service_next_step
    ADD CONSTRAINT service_next_step_action_guide_id_fkey FOREIGN KEY (action_guide_id) REFERENCES public.action_guide(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: service_next_step service_next_step_license_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.service_next_step
    ADD CONSTRAINT service_next_step_license_id_fkey FOREIGN KEY (license_id) REFERENCES public.license(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: shopping_cart shopping_cart_affiliate_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.shopping_cart
    ADD CONSTRAINT shopping_cart_affiliate_customer_id_fkey FOREIGN KEY (affiliate_customer_id) REFERENCES public.customer(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: shopping_cart shopping_cart_affiliate_partner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.shopping_cart
    ADD CONSTRAINT shopping_cart_affiliate_partner_id_fkey FOREIGN KEY (affiliate_partner_id) REFERENCES public.partner(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: shopping_cart shopping_cart_coupon_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.shopping_cart
    ADD CONSTRAINT shopping_cart_coupon_id_fkey FOREIGN KEY (coupon_id) REFERENCES public.coupon(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: shopping_cart_item shopping_cart_item_customer_commitment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.shopping_cart_item
    ADD CONSTRAINT shopping_cart_item_customer_commitment_id_fkey FOREIGN KEY (customer_commitment_id) REFERENCES public.customer_commitment(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: shopping_cart_item shopping_cart_item_shopping_cart_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: epr-local-user
--

ALTER TABLE ONLY public.shopping_cart_item
    ADD CONSTRAINT shopping_cart_item_shopping_cart_id_fkey FOREIGN KEY (shopping_cart_id) REFERENCES public.shopping_cart(id) ON UPDATE CASCADE ON DELETE RESTRICT;

CREATE TYPE customer_activity_type_enum AS ENUM (
            'ACCOUNT_LOGIN',
            'ACCOUNT_UPDATE_PASSWORD',
            'ACCOUNT_UPDATE_EMAIL',
            'ACCOUNT_UPDATE_ADDRESS',
            'ACCOUNT_UPDATE_PAYMENT',
            'CONTRACT_ADD_SERVICE',
            'CONTRACT_UPDATE_SERVICE',
            'CONTRACT_TERMINATION',
            'CONTRACT_SERVICE_TERMINATION',
            'REPORT_ADD',
            'REPORT_UPDATE',
            'DOCUMENT_UPLOAD',
            'DOCUMENT_ANSWER',
            'DOCUMENT_DOWNLOAD'
        );

-- Create the customer_activities table
CREATE TABLE public.customer_activities
(
    id          SERIAL PRIMARY KEY,
    type        customer_activity_type_enum NOT NULL,
    metadata    JSONB,
    created_at  TIMESTAMPTZ                 NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at  TIMESTAMPTZ                 NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at  TIMESTAMPTZ,
    customer_id INTEGER                     NOT NULL,
    CONSTRAINT fk_customer_activities_customer
        FOREIGN KEY (customer_id)
            REFERENCES public.customer (id)
            ON DELETE RESTRICT
);


CREATE INDEX IF NOT EXISTS idx_customer_activities_customer_id ON public.customer_activities (customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_activities_type ON public.customer_activities (type);
CREATE INDEX IF NOT EXISTS idx_customer_activities_created_at ON public.customer_activities (created_at);

CREATE INDEX IF NOT EXISTS idx_customer_activities_metadata_gin ON public.customer_activities USING GIN (metadata);

COMMENT
ON TABLE public.customer_activities IS 'Stores records of various activities performed by or related to customers.';
COMMENT
ON COLUMN public.customer_activities.id IS 'Primary key for the customer activity.';
COMMENT
ON COLUMN public.customer_activities.type IS 'The type of activity that occurred.';
COMMENT
ON COLUMN public.customer_activities.metadata IS 'JSONB column to store arbitrary key-value data related to the activity.';
COMMENT
ON COLUMN public.customer_activities.created_at IS 'Timestamp of when the activity was created.';
COMMENT
ON COLUMN public.customer_activities.updated_at IS 'Timestamp of when the activity was last updated.';
COMMENT
ON COLUMN public.customer_activities.deleted_at IS 'Timestamp of when the activity was soft-deleted.';
COMMENT
ON COLUMN public.customer_activities.customer_id IS 'Foreign key referencing the customer associated with this activity.';
--
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_reason_type') THEN
        CREATE TYPE public.enum_reason_type AS ENUM ('LICENSE_INFORMATION', 'LICENSE_VOLUME_REPORT', 'TERMINATION');
    END IF;
END$$;

CREATE TABLE IF NOT EXISTS public.reason (
    id SERIAL PRIMARY KEY,
    title VARCHAR NOT NULL,
    value VARCHAR NOT NULL,
    type public.enum_reason_type NOT NULL,
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    deleted_at DATE
);

-- Create the 'termination' table.
CREATE TABLE IF NOT EXISTS public.termination (
    id SERIAL PRIMARY KEY,
    status public.enum_termination_status DEFAULT 'REQUESTED'::public.enum_termination_status NOT NULL,
    requested_at TIMESTAMPTZ NOT NULL,
    completed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    deleted_at DATE
);

-- Creates the 'decline' table, which records the event of a decline
-- for various information requests or reports.
CREATE TABLE IF NOT EXISTS public.decline (
    id SERIAL PRIMARY KEY,
    title VARCHAR NOT NULL,
    license_required_information_id INTEGER UNIQUE,
    license_volume_report_id INTEGER UNIQUE,
    license_volume_report_item_id INTEGER UNIQUE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL,
    deleted_at DATE
);

-- Add comments for clarity
COMMENT ON TABLE public.decline IS 'Stores decline events for various items like required information or volume reports.';
COMMENT ON COLUMN public.decline.title IS 'A title or summary for the decline event.';
COMMENT ON COLUMN public.decline.license_required_information_id IS 'Foreign key linking to a declined required information request.';
COMMENT ON COLUMN public.decline.license_volume_report_id IS 'Foreign key linking to a declined volume report.';
COMMENT ON COLUMN public.decline.license_volume_report_item_id IS 'Foreign key linking to a specific declined item within a volume report.';


CREATE TABLE IF NOT EXISTS public.decline_to_decline_reason (
    id SERIAL PRIMARY KEY,
    decline_id INTEGER NOT NULL,
    reason_id INTEGER NOT NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    deleted_at DATE
);

-- Step 4: Add all foreign key constraints now that all tables exist.
ALTER TABLE public.decline
    ADD CONSTRAINT fk_decline_to_required_information
    FOREIGN KEY (license_required_information_id) REFERENCES public.license_required_information (id) ON DELETE SET NULL;

ALTER TABLE public.decline
    ADD CONSTRAINT fk_decline_to_volume_report
    FOREIGN KEY (license_volume_report_id) REFERENCES public.license_volume_report (id) ON DELETE SET NULL;

ALTER TABLE public.decline
    ADD CONSTRAINT fk_decline_to_volume_report_item
    FOREIGN KEY (license_volume_report_item_id) REFERENCES public.license_volume_report_item (id) ON DELETE SET NULL;

ALTER TABLE public.decline_to_decline_reason
    ADD CONSTRAINT fk_join_to_decline
    FOREIGN KEY (decline_id) REFERENCES public.decline(id) ON DELETE CASCADE;

ALTER TABLE public.decline_to_decline_reason
    ADD CONSTRAINT fk_join_to_reason
    FOREIGN KEY (reason_id) REFERENCES public.reason(id) ON DELETE RESTRICT;


-- Create the 'termination_termination_reason' join table correctly.
CREATE TABLE IF NOT EXISTS public.termination_termination_reason (
    id SERIAL PRIMARY KEY,
    termination_id INTEGER NOT NULL
        CONSTRAINT fk_termination_reason_to_termination REFERENCES public.termination(id) ON DELETE CASCADE,
    reason_id INTEGER NOT NULL
        CONSTRAINT fk_termination_reason_to_reason REFERENCES public.reason(id) ON DELETE RESTRICT,
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    deleted_at DATE
);

ALTER TABLE ONLY public.decline_to_decline_reason
    ADD CONSTRAINT decline_to_decline_reason_reason_id_fkey FOREIGN KEY (reason_id) REFERENCES public.reason(id) ON UPDATE CASCADE ON DELETE RESTRICT;
