-- This script seeds the database with initial data

-- Roles
INSERT INTO public.one_epr_role (name, display_name, is_active, created_at, updated_at)
VALUES ('SUPER_ADMIN', 'Super Administrator', true, NOW(), NOW()),
       ('ADMI<PERSON>', 'Administrator', true, NOW(), NOW()),
       ('CLERK', 'Clerk', true, NOW(), NOW()),
       ('CUSTOMER', 'Customer', true, NOW(), NOW());

-- Users
INSERT INTO public.one_epr_user (first_name, last_name, name, email, password, is_active, role_id, status, created_at, updated_at)
VALUES
    ('Super', 'Admin', 'superadmin', '<EMAIL>', '$2a$10$hJJzOfhFS8OnObePdHJ88OThEmUngZCURydMdFSkdw1VzQwbkkNqi', true, (SELECT id FROM public.one_epr_role WHERE name = 'SUPER_ADMIN'), 'COMPLETE', NOW(), NOW()),
    ('Admin', 'User', 'admin', '<EMAIL>', '$2a$10$hJJzOfhFS8OnObePdHJ88OThEmUngZCURydMdFSkdw1VzQwbkkNqi', true, (SELECT id FROM public.one_epr_role WHERE name = 'ADMIN'), 'COMPLETE', NOW(), NOW()),
    ('Clerk', 'User', 'clerk', '<EMAIL>', '$2a$10$hJJzOfhFS8OnObePdHJ88OThEmUngZCURydMdFSkdw1VzQwbkkNqi', true, (SELECT id FROM public.one_epr_role WHERE name = 'CLERK'), 'COMPLETE', NOW(), NOW()),
    ('Customer', 'User', 'customer', '<EMAIL>', '$2a$10$hJJzOfhFS8OnObePdHJ88OThEmUngZCURydMdFSkdw1VzQwbkkNqi', true, (SELECT id FROM public.one_epr_role WHERE name = 'CUSTOMER'), 'COMPLETE', NOW(), NOW());
