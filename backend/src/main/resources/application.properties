spring.application.name=One EPR BE
#database
spring.jpa.hibernate.ddl-auto=validate
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.url=
spring.datasource.username=
spring.datasource.password=
# some prisma schemes aren't following proper naming schemes, so this forces springboot to use the name as it appears in the column annotation
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
# Flyway configuration
spring.flyway.enabled=true
spring.flyway.out-of-order=true
spring.flyway.locations=classpath:db/migration
#logging
logging.level.de.interzero.oneepr=DEBUG
# this key pair is to sign and validate the JWT token.
rsa.public-key=\
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA0qZjTgK3D7ve/JDE3rku\
Xz6v2YuESO1oMuA9u4i58s2LX0toa7k5poPLlj2LeWPmGt16V0lqQ/Lo1bb9fegU\
VVlL1yJ3rAecw44bIuCU7Xxh5ZVvXTUuBpzF6s0T3tVRmDyfGUId8kSXFpGCne6R\
vwUnNi+IJGkMgyhPFHB8CPAlVVyhY9TN7CCBGCVnPUNEtspVJ5Xry5k7HhIHkBli\
9BGOLCWpNVpuCsqg12udyyzewyFpdnE01msOBnoY99S+46bEjW1qPBWb6BNbpd6g\
knR2p/Lyc7oI8QurnUFlphBxSMRfUcxwnjKkUWRwZqsTJgAZ0A2MqR7KueX5zLNq\
hQIDAQAB
rsa.private-key=MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIB\
AQDSpmNOArcPu978kMTeuS5fPq/Zi4RI7Wgy4D27iLnyzYtfS2hruTmmg8uWPY\
t5Y+Ya3XpXSWpD8ujVtv196BRVWUvXInesB5zDjhsi4JTtfGHllW9dNS4GnMXq\
zRPe1VGYPJ8ZQh3yRJcWkYKd7pG/BSc2L4gkaQyDKE8UcHwI8CVVXKFj1M3sII\
EYJWc9Q0S2ylUnlevLmTseEgeQGWL0EY4sJak1Wm4KyqDXa53LLN7DIWl2cTTW\
aw4Gehj31L7jpsSNbWo8FZvoE1ul3qCSdHan8vJzugjxC6udQWWmEHFIxF9RzH\
CeMqRRZHBmqxMmABnQDYypHsq55fnMs2qFAgMBAAECggEBAMJDjjtIR7ir4sX7\
YIOiNcDCl/vBLbrvrnOU13mWax4fLtQwP9lwlVkaHGuu0Gyfk3rdG9FZtDV70/\
wb6QTvPalEU6vQbpro3kp+SEpO4Slu1v2aZXyI6axRcjjK7fNHuRrzdSofDmz5\
knjOsq2hcSm/v9y50wz5D//48Y0A+3JlD/L9LYczyTVnw1q/EAUcUPYHfJ5CjX\
HTR+ITmJCwzqJYx0wjMIx1YWLIDX5DTdYbb5ayKHTcwOazL+sdlTiC9K4sF2U3\
ieQnWv0xwHgK2+zv49AhKB+/SpW7OOBUwkt3r2TItc/9vvinV8xPPtVOnrUjJ1\
aAij3hz0DsSwiRKbUCgYEA+z84e8mwHOHFTL9R6+CwsfG7C9aDnxsmeryV5yjh\
JNH9SIZBmXrX0fxyLVnAi0ARpNsSfTnwnISw5iJfYsKnD6L2/Xlfr9KbVPfja9\
6G5kZHrFcW07ovEmPHrOS7qZFqsoyNC82n61FJYKSYJxs8NC5t84Lrx2h6lGIW\
uvbQ1wcCgYEA1qKOuzIz54TW5Dbc2RNAZ7eGW2IGswWF4HXV07X57RMIRh5Xy0\
xoiRIwjMbLN3BI70X38ZCSNTtvNzOjxcX/6+lHuuhuQSiciN5RarYpUUvIJcBV\
oLWoLeukOhta3wlm3ODntKmkmWa42UJktfIkpAQWgQW/o7RERyN6zZ7moxMCgY\
EAkgsiuxn2zCMUe9MuPG1kaFs6PdxYsBCpImkivbbOgIersg+QbDWy5DXY7LGi\
MY9xciGBstcOHP0oku1iAtdhIr640YhO7bX/YrmhMZ+tTIeHvA73juVH3WmwtL\
hUuXF00wrOJhFKTYf3njyu0PSM30wpz4XHpib+Meh+i/lSVdsCgYAYZvTpTYTf\
Lpgr7BzhxTGOWSw6WYO/eMJGyb7cyhS3InHPXwXGRFKiOuIwLy3NiMOSCJdu7G\
vjB3XWhdV519AH50bTdjeKyu45FWWWR02dQ4tm114k3mJXjjHmtVN4hh4ewKc3\
QA5JrocXlFfU48nMp/ZTBu9J0mXs3JjKpeU85wKBgC/XwAWODFkOllyYTzk1wM\
LfTXVhGcKkLbL87jRZkIXKRvzIC7jPebiv8fysH51oKQsthuaQbjR1KSAXiuVX\
xWv821WE8hQvbv+J6ElKi0J+ixtSGn7EeA2aLtFhvv7tusyd4aoE9lzCzayEwY\
QNPbZL6s3Z0vb3eTSi5QrP7H48
#placeholder for the real core-api-url
app.core-api-url=example.com
# Lambda request presigned url
file.lambda.request-presigned-url=https://3pdf2nhm7hl4n37hjo4upqgjme0pdvsd.lambda-url.us-east-2.on.aws
# Lambda download file url
file.lambda.download-file-url=https://3qthmn2fk2clgkiikb3dzy6o7e0fyttk.lambda-url.us-east-2.on.aws
# Email configuration
spring.mail.host=k8s-entwdigi-mailpits-161ddc30dc-63b434d32b26d692.elb.eu-central-1.amazonaws.com
spring.mail.port=1025
spring.mail.properties.mail.smtp.auth=false
spring.mail.properties.mail.smtp.starttls.enable=true

# Enable sandbox gateway
mail.gateway=sandbox

services.crm.base-url=https://