package de.interzero.oneepr.customer.license_volume_report.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DeclineLicenseVolumeReportDto {

    @NotEmpty
    @JsonProperty("reason_ids")
    @Schema(
            description = "The decline reason IDs",
            example = "[1, 2]"
    )
    private List<Integer> reasonIds;

    @NotBlank
    @JsonProperty("title")
    @Schema(
            description = "The title of the decline",
            example = "Decline reason title"
    )
    private String title;
}
