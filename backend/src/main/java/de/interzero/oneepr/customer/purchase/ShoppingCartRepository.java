package de.interzero.oneepr.customer.purchase;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Repository interface for managing {@link ShoppingCart} entities.
 */
@Repository
public interface ShoppingCartRepository extends JpaRepository<ShoppingCart, String> {

    /**
     * Finds a shopping cart by ID where it has not been soft-deleted.
     *
     * @param shoppingCartId the ID of the shopping cart
     * @return an optional containing the shopping cart if found and not deleted
     */
    Optional<ShoppingCart> findByIdAndDeletedAtIsNull(String shoppingCartId);

    /**
     * Updates the email associated with all shopping carts matching the old email,
     * only if the cart has not been soft-deleted.
     *
     * @param oldEmail the current email address stored in the shopping cart
     * @param newEmail the new email address to update
     */
    @Modifying
    @Query(
            """
                        UPDATE ShoppingCart sc 
                        SET sc.email = :newEmail 
                        WHERE sc.email = :oldEmail AND sc.deletedAt IS NULL
                    """
    )
    void updateEmailByOldEmailAndDeletedAtIsNull(@Param("oldEmail") String oldEmail,
                                                 @Param("newEmail") String newEmail);

    @Modifying
    @Query("UPDATE ShoppingCart s SET s.email = :newEmail WHERE s.email = :oldEmail AND s.deletedAt IS NULL")
    void updateEmailForActiveCarts(@Param("oldEmail") String oldEmail,
                                   @Param("newEmail") String newEmail);
}
