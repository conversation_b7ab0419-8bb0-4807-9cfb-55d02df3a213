package de.interzero.oneepr.customer.file.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.file.File;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CreateFileDto {

    @Schema(
            description = "The kind of the file",
            example = "CONTRACT"
    )
    @JsonProperty("type")
    private File.Type type;

    @Schema(
            description = "The required information ID",
            example = "1"
    )
    @JsonProperty("required_information_id")
    private Integer requiredInformationId;

    @Schema(
            description = "The contract ID",
            example = "1"
    )
    @JsonProperty("contract_id")
    private Integer contractId;

    @Schema(
            description = "The certificate ID",
            example = "1"
    )
    @JsonProperty("certificate_id")
    private Integer certificateId;

    @Schema(
            description = "The license ID",
            example = "1"
    )
    @JsonProperty("license_id")
    private Integer licenseId;

    @Schema(
            description = "The termination ID",
            example = "1"
    )
    @JsonProperty("termination_id")
    private Integer terminationId;

    @Schema(
            description = "The general information ID",
            example = "1"
    )
    @JsonProperty("general_information_id")
    private Integer generalInformationId;

    @Schema(
            description = "The marketing material ID",
            example = "1"
    )
    @JsonProperty("marketing_material_id")
    private Integer marketingMaterialId;

    @Schema(
            description = "The partner contract ID",
            example = "1"
    )
    @JsonProperty("partner_contract_id")
    private Integer partnerContractId;
}
