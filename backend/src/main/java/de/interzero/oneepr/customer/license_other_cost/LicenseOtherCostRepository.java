package de.interzero.oneepr.customer.license_other_cost;

import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for managing {@link LicenseOtherCost} entities.
 */
@Repository
public interface LicenseOtherCostRepository extends JpaRepository<LicenseOtherCost, Integer> {

    /**
     * Finds all costs that have not been soft-deleted, ordered by ID descending.
     *
     * @return A list of license other costs.
     */
    List<LicenseOtherCost> findAllByDeletedAtIsNullOrderByIdDesc();

    /**
     * Finds all costs for a specific license that have not been soft-deleted, ordered by ID descending.
     *
     * @param licenseId The ID of the license to filter by.
     * @return A list of license other costs for the given license.
     */
    List<LicenseOtherCost> findAllByLicense_IdAndDeletedAtIsNullOrderByIdDesc(Integer licenseId);

    /**
     * Finds a single cost by its ID, ensuring it has not been soft-deleted.
     * It uses an entity graph to eagerly fetch the full chain of related entities
     * (license -> contract -> customer) required for permission validation.
     *
     * @param id The ID of the license other cost.
     * @return An optional containing the found cost with its relations loaded.
     * @ts-legacy This method's @EntityGraph is the direct translation of the nested `include` statements
     * in the original Prisma queries (`findOne`, `validatingUserPermissionLicenseOtherCost`).
     */
    @EntityGraph(attributePaths = {"license.contract.customer"})
    Optional<LicenseOtherCost> findByIdAndDeletedAtIsNull(Integer id);
}