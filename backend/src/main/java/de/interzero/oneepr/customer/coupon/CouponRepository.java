package de.interzero.oneepr.customer.coupon;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for managing {@link Coupon} entities.
 */
@Repository
public interface CouponRepository extends JpaRepository<Coupon, Integer>, JpaSpecificationExecutor<Coupon> {

    /**
     * Retrieves all coupons that have not been soft-deleted.
     *
     * @return list of active (non-deleted) coupons
     */
    List<Coupon> findAllByDeletedAtIsNull();

    /**
     * Retrieves a coupon by its ID, only if it has not been soft-deleted.
     *
     * @param id the ID of the coupon
     * @return an Optional containing the coupon if found and not deleted, or empty otherwise
     */
    Optional<Coupon> findByIdAndDeletedAtIsNull(Integer id);

    /**
     * Retrieves a coupon by its code, only if it has not been soft-deleted.
     *
     * @param code the code of the coupon
     * @return an Optional containing the coupon if found and not deleted, or empty otherwise
     */
    Optional<Coupon> findByCodeAndDeletedAtIsNull(String code);

    /**
     * Retrieves a coupon by its code, only if it is active and not soft-deleted.
     *
     * @param code the code of the coupon
     * @return an Optional containing the coupon if found, active, and not deleted, or empty otherwise
     */
    Optional<Coupon> findByCodeAndIsActiveTrueAndDeletedAtIsNull(String code);

    /**
     * Checks whether there exists another coupon with the same code and not soft-deleted,
     * excluding the coupon with the specified ID.
     *
     * @param code the code to check for uniqueness
     * @param id   the ID to exclude from the search
     * @return true if such a coupon exists, false otherwise
     */
    boolean existsByCodeAndDeletedAtIsNullAndIdNot(String code,
                                                   Integer id);


    /**
     * Find coupon by code
     *
     * @param code Coupon code
     * @return Optional coupon
     */
    Optional<Coupon> findByCode(String code);

    /**
     * Find coupon by ID with all relationships (customers, partners, uses)
     *
     * @param id Coupon ID
     * @return Optional coupon with relationships
     */
    @Query(
            """
                    SELECT c FROM Coupon c
                    LEFT JOIN FETCH c.customers cc
                    LEFT JOIN FETCH cc.customer
                    LEFT JOIN FETCH c.partners pc
                    LEFT JOIN FETCH pc.partner
                    WHERE c.id = :id AND c.deletedAt IS NULL
                    """
    )
    Optional<Coupon> findByIdWithRelations(@Param("id") Integer id);

}
