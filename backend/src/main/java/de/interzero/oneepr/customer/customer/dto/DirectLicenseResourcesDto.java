package de.interzero.oneepr.customer.customer.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * Top-level Data Transfer Object for a customer's direct license resource savings report.
 * <p>
 * This DTO is a direct translation of the root {@code resources} object
 * constructed and returned by the {@code getDirectLicenseResources} method
 * from {@code customer.service.ts}. It serves as the main container for both
 * the aggregated total and the year-by-year breakdown of resource savings.
 */
@Data
public class DirectLicenseResourcesDto {

    @JsonProperty("total")
    private TotalResourcesDto total = new TotalResourcesDto();

    @JsonProperty("years")
    private Map<Integer, YearlyResourcesDto> years = new HashMap<>();
}