package de.interzero.oneepr.customer.license_required_information;

import de.interzero.oneepr.common.config.ModelMapperConfig;
import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.contract.ContractRepository;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer_io.CustomerIoService;
import de.interzero.oneepr.customer.decline.Decline;
import de.interzero.oneepr.customer.decline.DeclineRepository;
import de.interzero.oneepr.customer.license.License;
import de.interzero.oneepr.customer.license.LicenseRepository;
import de.interzero.oneepr.customer.license_required_information.dto.*;
import de.interzero.oneepr.customer.reason.DeclineReason;
import de.interzero.oneepr.customer.reason.DeclineReasonRepository;
import de.interzero.oneepr.customer.reason.Reason;
import de.interzero.oneepr.customer.reason.ReasonRepository;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import de.interzero.oneepr.customer.shared.auth.Role;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.util.List;

/**
 * Service for managing license required information.
 */
@Service
@RequiredArgsConstructor
public class LicenseRequiredInformationService {

    private final LicenseRequiredInformationRepository licenseRequiredInformationRepository;

    private final DeclineRepository declineRepository;

    private final DeclineReasonRepository declineReasonRepository;

    private final ReasonRepository reasonRepository;

    private final LicenseRepository licenseRepository;

    private final ContractRepository contractRepository;

    private final CustomerIoService customerIoService;

    private static final String LICENSE_REQUIRED_INFORMATION_NOT_FOUND = "License Required Information not found";

    /**
     * Retrieves all non-deleted required information records, filtered by either license or contract ID.
     *
     * @param data DTO containing the filter criteria (licenseId or contractId).
     * @return A list of specialized DTOs representing the found records.
     */
    @Transactional(readOnly = true)
    public List<LicenseRequiredInformationResponseDto> findAll(FindLicenseRequiredInformationsDto data) {
        if (data.getLicenseId() == null && data.getContractId() == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "License ID or Contract ID is required");
        }

        List<LicenseRequiredInformation> results;
        if (data.getLicenseId() != null) {
            results = licenseRequiredInformationRepository.findAllByLicense_IdAndDeletedAtIsNullOrderByIdAsc(data.getLicenseId());
        } else {
            results = licenseRequiredInformationRepository.findAllByContract_IdAndDeletedAtIsNullOrderByIdAsc(data.getContractId());
        }

        return results.stream().map(LicenseRequiredInformationResponseDto::fromEntity).toList();
    }

    /**
     * Retrieves a single required information record by its ID after validating user permissions.
     *
     * @param id   The ID of the record to retrieve.
     * @param user The authenticated user making the request.
     * @return A specialized DTO representing the found record.
     */
    @Transactional(readOnly = true)
    public LicenseRequiredInformationResponseDto findOne(Integer id,
                                                         AuthenticatedUser user) {
        validateUserPermission(id, user);
        LicenseRequiredInformation entity = licenseRequiredInformationRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(
                        HttpStatus.NOT_FOUND,
                        LICENSE_REQUIRED_INFORMATION_NOT_FOUND));
        return LicenseRequiredInformationResponseDto.fromEntity(entity);
    }

    /**
     * Creates a new required information record and triggers an update to the customer's integration service.
     *
     * @param dto The DTO containing the data for the new record.
     * @return The newly created and persisted {@link LicenseRequiredInformation} entity.
     */
    @Transactional
    public LicenseRequiredInformation create(CreateLicenseRequiredInformationDto dto) {
        if (dto.getKind() == LicenseRequiredInformation.Kind.REQUIRED_INFORMATION && (dto.getLicenseId() == null || dto.getSetupRequiredInformationId() == null)) {
            throw new ResponseStatusException(
                    HttpStatus.BAD_REQUEST,
                    "License ID and Setup Required Information ID are required for required information");
        }
        if (dto.getKind() == LicenseRequiredInformation.Kind.GENERAL_INFORMATION && (dto.getContractId() == null || dto.getSetupGeneralInformationId() == null)) {
            throw new ResponseStatusException(
                    HttpStatus.BAD_REQUEST,
                    "Contract ID and Setup General Information ID are required for general information");
        }

        LicenseRequiredInformation info = ModelMapperConfig.mapPresentFields(dto, LicenseRequiredInformation.class);

        if (dto.getLicenseId() != null) {
            License license = licenseRepository.findById(dto.getLicenseId()).orElseThrow();
            info.setLicense(license);
        }
        if (dto.getContractId() != null) {
            Contract contract = contractRepository.findById(dto.getContractId()).orElseThrow();
            info.setContract(contract);
        }

        info.setCreatedAt(Instant.now());
        info.setUpdatedAt(Instant.now());

        LicenseRequiredInformation savedInfo = licenseRequiredInformationRepository.save(info);

        if (savedInfo.getContract() != null && savedInfo.getContract().getCustomer() != null) {
            customerIoService.processRequiredInformation(savedInfo.getContract().getCustomer().getId());
        }

        return savedInfo;
    }

    /**
     * Partially updates an existing required information record. If the status is changed back to OPEN or DONE,
     * any existing decline records are removed.
     *
     * @param id   The ID of the record to update.
     * @param dto  The DTO containing the fields to update.
     * @param user The authenticated user making the request.
     * @return The updated and persisted {@link LicenseRequiredInformation} entity.
     */
    @Transactional
    public LicenseRequiredInformation update(Integer id,
                                             UpdateLicenseRequiredInformationDto dto,
                                             AuthenticatedUser user) {
        validateUserPermission(id, user);
        LicenseRequiredInformation info = licenseRequiredInformationRepository.findWithDeclineAndContractByIdAndDeletedAtIsNull(
                        id)
                .orElseThrow(() -> new ResponseStatusException(
                        HttpStatus.NOT_FOUND,
                        LICENSE_REQUIRED_INFORMATION_NOT_FOUND));

        ModelMapperConfig.mapPresentFields(dto, info);
        info.setUpdatedAt(Instant.now());

        if ((info.getStatus() == LicenseRequiredInformation.Status.OPEN || info.getStatus() == LicenseRequiredInformation.Status.DONE) && info.getDecline() != null) {
            Decline decline = info.getDecline();
            declineReasonRepository.deleteAllByDeclineId(decline.getId());
            declineRepository.delete(decline);
            info.setDecline(null);
        }

        if (info.getContract() != null && info.getContract().getCustomer() != null) {
            customerIoService.processRequiredInformation(info.getContract().getCustomer().getId());
        }

        return licenseRequiredInformationRepository.save(info);
    }

    /**
     * Soft-deletes a required information record by setting its `deletedAt` timestamp.
     *
     * @param id   The ID of the record to remove.
     * @param user The authenticated user making the request.
     * @return The updated entity, now marked as deleted.
     */
    @Transactional
    public LicenseRequiredInformation remove(Integer id,
                                             AuthenticatedUser user) {
        validateUserPermission(id, user);
        LicenseRequiredInformation info = licenseRequiredInformationRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(
                        HttpStatus.NOT_FOUND,
                        LICENSE_REQUIRED_INFORMATION_NOT_FOUND));
        info.setDeletedAt(Instant.now());
        return licenseRequiredInformationRepository.save(info);
    }

    /**
     * Updates a required information record to a DECLINED status and attaches decline reasons.
     *
     * @param id  The ID of the record to decline.
     * @param dto The DTO containing the decline reasons and title.
     * @return The updated entity, now with a DECLINED status and associated decline record.
     */
    @Transactional
    public LicenseRequiredInformation decline(Integer id,
                                              DeclineLicenseRequiredInformationDto dto) {
        if (dto.getReasonIds() == null || dto.getReasonIds().isEmpty()) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "At least one decline reason ID is required");
        }
        LicenseRequiredInformation info = licenseRequiredInformationRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(
                        HttpStatus.NOT_FOUND,
                        LICENSE_REQUIRED_INFORMATION_NOT_FOUND));

        info.setStatus(LicenseRequiredInformation.Status.DECLINED);
        info.setUpdatedAt(Instant.now());

        Decline decline = new Decline();
        decline.setTitle(dto.getTitle() != null ? dto.getTitle() : "");
        decline.setLicenseRequiredInformation(info);
        decline.setCreatedAt(Instant.now());
        decline.setUpdatedAt(Instant.now());
        List<Reason> reasons = reasonRepository.findAllById(dto.getReasonIds());
        for (Reason reason : reasons) {
            decline.addDeclineReason(new DeclineReason(decline, reason));
        }

        info.setDecline(decline);
        return licenseRequiredInformationRepository.save(info);
    }

    /**
     * Validates if the authenticated user has permission to access a specific required information record.
     *
     * @param id   The ID of the record to check.
     * @param user The authenticated user.
     * @throws ResponseStatusException if the record is not found or the user is not authorized.
     */
    private void validateUserPermission(Integer id,
                                        AuthenticatedUser user) {
        LicenseRequiredInformation info = licenseRequiredInformationRepository.findWithPermissionsByIdAndDeletedAtIsNull(
                        id)
                .orElseThrow(() -> new ResponseStatusException(
                        HttpStatus.NOT_FOUND,
                        LICENSE_REQUIRED_INFORMATION_NOT_FOUND));

        Contract contract = info.getContract();
        if (contract == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Contract not found");
        }
        Customer customer = contract.getCustomer();
        if (customer == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Customer not found");
        }

        if (user.getRole() == Role.CUSTOMER && !customer.getUserId().equals(Integer.parseInt(user.getId()))) {
            throw new ResponseStatusException(
                    HttpStatus.FORBIDDEN,
                                              "You do not have permission to access this license required information");
        }
    }
}
