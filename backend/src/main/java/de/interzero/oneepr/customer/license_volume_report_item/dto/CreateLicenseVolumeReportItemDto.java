package de.interzero.oneepr.customer.license_volume_report_item.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Data Transfer Object for creating a new license volume report item.
 * Converted from TypeScript create-license-volume-report-item.dto.ts
 * Maintains exact same structure, variable names, and properties.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CreateLicenseVolumeReportItemDto {

    @NotNull
    @JsonProperty("license_volume_report_id")
    @Schema(
            description = "The license volume report ID",
            example = "1",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer licenseVolumeReportId;

    @NotNull
    @JsonProperty("setup_fraction_id")
    @Schema(
            description = "The setup fraction ID",
            example = "1",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer setupFractionId;

    @NotNull
    @JsonProperty("setup_fraction_code")
    @Schema(
            description = "The setup fraction code",
            example = "DEOK34",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String setupFractionCode;

    @NotNull
    @JsonProperty("setup_column_id")
    @Schema(
            description = "The setup column ID",
            example = "1",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer setupColumnId;

    @NotNull
    @JsonProperty("setup_column_code")
    @Schema(
            description = "The setup column code",
            example = "DEOK34",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String setupColumnCode;

    @NotNull
    @JsonProperty("value")
    @Schema(
            description = "The value of the license volume report item",
            example = "1",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer value;
}
