package de.interzero.oneepr.customer.coupon.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import de.interzero.oneepr.customer.coupon.Coupon;
import de.interzero.oneepr.customer.coupon.Coupon.DiscountType;
import de.interzero.oneepr.customer.coupon.Coupon.Mode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;
import java.util.List;
import java.util.Map;

@Schema(description = "Fields required to create a new coupon")
@Data
@EqualsAndHashCode(callSuper = true)
public class CreateCouponDto extends BaseDto {

    @JsonProperty("buy_x_get_y")
    @Schema(
            description = "Buy X get Y products or discount json object",
            example = """
                    [
                      {
                        "buyProduct": "direct_license | eu_license | action_guide",
                        "buyAtLeast": 1,
                        "receiveProduct": "workshop | action_guide",
                        "receiveAtLeast": 1
                      },
                      {
                        "or": "or"
                      },
                      {
                        "buyProduct": "direct_license | eu_license | action_guide",
                        "buyAtLeast": 1,
                        "discountType": "PERCENTAGE | ABSOLUTE",
                        "discountValue": 1000
                      }
                    ]
                    """
    )

    private Map<String, Object> buyXGetY;

    @JsonProperty("code")
    @Schema(
            description = "Unique code for the coupon",
            example = "OFF100"
    )
    private String code;

    @JsonProperty("description")
    @Schema(
            description = "Description of the coupon",
            example = "10% discount on all products"
    )
    private String description;

    @JsonProperty("discount_type")
    @Schema(
            description = "Type of discount",
            example = "PERCENTAGE"
    )
    private DiscountType discountType;

    @JsonProperty("elegible_products")
    @Schema(
            description = "Eligible products JSON",
            example = "{\"direct_license\":{...}}"
    )
    private Map<String, Object> elegibleProducts;

    @JsonProperty("end_date")
    @Schema(
            description = "End date of the coupon validity",
            example = "2023-12-31T23:59:59.000Z"
    )
    private Instant endDate;

    @JsonProperty("is_active")
    @Schema(
            description = "Is the coupon active",
            example = "true"
    )
    private Boolean isActive;

    @JsonProperty("link")
    @Schema(
            description = "Unique link for the coupon",
            example = "http://example.com/coupon"
    )
    private String link;

    @JsonProperty("max_amount")
    @Schema(
            description = "Maximum amount for the coupon (in cents)",
            example = "1000"
    )
    private Integer maxAmount;

    @JsonProperty("max_uses")
    @Schema(
            description = "Maximum uses",
            example = "5"
    )
    private Integer maxUses;

    @JsonProperty("max_uses_per_customer")
    @Schema(
            description = "Max uses per customer",
            example = "1"
    )
    private Integer maxUsesPerCustomer;

    @JsonProperty("min_amount")
    @Schema(
            description = "Minimum amount for the coupon (in cents)",
            example = "100"
    )
    private Integer minAmount;

    @JsonProperty("min_products")
    @Schema(
            description = "Minimum products required",
            example = "2"
    )
    private Integer minProducts;

    @JsonProperty("mode")
    @Schema(
            description = "Coupon mode",
            example = "GENERAL"
    )
    private Mode mode;

    @JsonProperty("note")
    @Schema(
            description = "Note about the coupon",
            example = "Internal note"
    )
    private String note;

    @JsonProperty("redeemable_by_new_customers")
    @Schema(
            description = "Is redeemable by new customers",
            example = "false"
    )
    private Boolean redeemableByNewCustomers;

    @JsonProperty("shopping_cart_id")
    @Schema(
            description = "ID of the shopping cart",
            example = "uuid"
    )
    private String shoppingCartId;

    @JsonProperty("start_date")
    @Schema(
            description = "Start date of the coupon validity",
            example = "2023-01-01T00:00:00.000Z"
    )
    private Instant startDate;

    @JsonProperty("type")
    @Schema(
            description = "Type of coupon",
            example = "SYSTEM"
    )
    private Coupon.Type type;

    @JsonProperty("value")
    @Schema(
            description = "Value of the coupon (always in cents)",
            example = "10000"
    )
    private Integer value;

    @JsonProperty("customers")
    @Schema(
            description = "Array of customer IDs",
            example = "[1, 2, 3]"
    )
    private List<Integer> customers;
}