package de.interzero.oneepr.customer.customer_commitment.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * Originally found in src/customer-commitment/dto/customer-commitment.dto.ts
 */
@Data
public class ReportSet {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("name")
    private String name;

    @JsonProperty("mode")
    private String mode;

    @JsonProperty("type")
    private String type;

    @JsonProperty("fractions")
    private List<Fraction> fractions;

    @JsonProperty("columns")
    private List<Column> columns;

    @Data
    public static class Fraction {

        @JsonProperty("id")
        private Integer id;

        @JsonProperty("name")
        private String name;

        @JsonProperty("description")
        private String description;

        @JsonProperty("icon")
        private String icon;

        @JsonProperty("is_active")
        private Boolean isActive;

        @JsonProperty("report_set_id")
        private Integer reportSetId;

        @JsonProperty("parent_id")
        private Integer parentId;

        @JsonProperty("children")
        private List<Fraction> children;
    }

    @Data
    public static class Column {

        @JsonProperty("id")
        private Integer id;

        @JsonProperty("name")
        private String name;

        @JsonProperty("description")
        private String description;

        @JsonProperty("unit_type")
        private String unitType;

        @JsonProperty("report_set_id")
        private Integer reportSetId;

        @JsonProperty("parent_id")
        private Integer parentId;

        @JsonProperty("children")
        private List<Column> children;
    }
}
