package de.interzero.oneepr.customer.service_step;

import de.interzero.oneepr.customer.service_step.dto.CreateServiceStepDto;
import de.interzero.oneepr.customer.service_step.dto.UpdateServiceStepDto;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


/**
 * ServiceStepService
 * Service layer for managing service steps.
 * Handles business logic related to creating, retrieving, updating, and deleting service steps.
 */
@Service
@RequiredArgsConstructor
public class ServiceStepService {

    private final ServiceStepRepository serviceStepRepository;

    private final EntityManager entityManager;

    private static final String NOT_FOUND = "Service step not found with ID: ";

    private Instant parseInstant(String dateString,
                                 String fieldName) {
        if (dateString == null || dateString.trim().isEmpty()) {
            return null;
        }
        try {
            return Instant.parse(dateString);
        } catch (DateTimeParseException e) {
            throw new ResponseStatusException(
                    HttpStatus.BAD_REQUEST,
                                              "Invalid format for " + fieldName + ": " + dateString + ". Expected ISO 8601 format (e.g., yyyy-MM-ddTHH:mm:ssZ).");
        }
    }

    /**
     * Finds all service steps based on optional filter parameters.
     *
     * @param type        The type of service step (e.g., "LICENSE", "ACTION_GUIDE"). Must be provided and valid.
     * @param countryCode Optional country code to filter by.
     * @param isActive    Optional active status to filter by.
     * @return A list of {@link ServiceStep} entities matching the criteria.
     * @throws ResponseStatusException if type is invalid or other parameters are malformed.
     * @ts-legacy The original TS checked `typeof params.countryCode !== "string"` which is implicitly
     * handled by Java's static typing if the controller binds to `String`.
     * @ts-legacy The TS check `!["false", "true"].includes(String(params.isActive)))` for boolean
     * is handled if the controller binds `isActive` to a `Boolean`.
     */
    @Transactional(readOnly = true)
    public List<ServiceStep> findAll(String type,
                                     String countryCode,
                                     Boolean isActive) {
        if (type == null || !Arrays.asList("LICENSE", "ACTION_GUIDE").contains(type.toUpperCase())) {
            throw new ResponseStatusException(
                    HttpStatus.BAD_REQUEST,
                                              "Type is invalid. Must be LICENSE or ACTION_GUIDE.");
        }
        // Original TS countryCode type check is implicit in Java's typing.
        // Original TS isActive string value check is handled if Spring binds "true"/"false" to Boolean.

        ServiceStep.Type serviceStepTypeEnum;
        try {
            serviceStepTypeEnum = ServiceStep.Type.valueOf(type.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new ResponseStatusException(
                    HttpStatus.BAD_REQUEST,
                                              "Type is invalid. Must be one of: " + Arrays.toString(ServiceStep.Type.values()));
        }

        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<ServiceStep> cq = cb.createQuery(ServiceStep.class);
        Root<ServiceStep> serviceStepRoot = cq.from(ServiceStep.class);
        List<Predicate> predicates = new ArrayList<>();

        predicates.add(cb.isNull(serviceStepRoot.get("deletedAt")));
        predicates.add(cb.equal(serviceStepRoot.get("type"), serviceStepTypeEnum));

        if (countryCode != null && !countryCode.trim().isEmpty()) {
            predicates.add(cb.equal(serviceStepRoot.get("countryCode"), countryCode));
        }
        if (isActive != null) {
            predicates.add(cb.equal(serviceStepRoot.get("isActive"), isActive));
        }

        cq.where(cb.and(predicates.toArray(new Predicate[0])));
        TypedQuery<ServiceStep> query = entityManager.createQuery(cq);
        return query.getResultList();
    }

    /**
     * Finds a single service step by its ID.
     *
     * @param id The ID of the service step to find.
     * @return The found {@link ServiceStep} entity.
     * @throws ResponseStatusException if the ID is invalid or the service step is not found.
     * @ts-legacy ID validation `!id || Number.isNaN(Number(id))` is translated to null check and `id <= 0`.
     * @ts-legacy Prisma's `findUnique` with a where clause on `id` and `deleted_at: null` is replicated.
     */
    @Transactional(readOnly = true)
    public ServiceStep findOne(Integer id) {
        if (id == null || id <= 0) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Service step ID is invalid.");
        }
        return serviceStepRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, NOT_FOUND + id));
    }

    /**
     * Creates a new service step.
     *
     * @param data The DTO containing data for the new service step.
     * @return The created {@link ServiceStep} entity.
     * @ts-legacy missing type validation
     */
    @Transactional
    public ServiceStep create(CreateServiceStepDto data) {
        ServiceStep serviceStep = new ServiceStep();
        serviceStep.setType(ServiceStep.Type.valueOf(data.getType()));
        serviceStep.setCountryCode(data.getCountryCode());
        serviceStep.setTitle(data.getTitle());
        serviceStep.setAvailableAt(parseInstant(data.getAvailableAt(), "availableAt"));
        serviceStep.setDeadlineAt(parseInstant(data.getDeadlineAt(), "deadlineAt"));
        serviceStep.setIsActive(data.isActive());

        Instant now = Instant.now();
        serviceStep.setCreatedAt(now);
        serviceStep.setUpdatedAt(now);
        serviceStep.setDeletedAt(null);

        return serviceStepRepository.save(serviceStep);
    }

    /**
     * Updates an existing service step.
     *
     * @param id   The ID of the service step to update.
     * @param data The DTO containing update data.
     * @return The updated {@link ServiceStep} entity.
     * @throws ResponseStatusException if the ID is invalid or the service step is not found.
     * @ts-legacy If a field in 'data' is null (for object types), it might set the entity field to null.
     */
    @Transactional
    public ServiceStep update(Integer id,
                              UpdateServiceStepDto data) {
        if (id == null || id <= 0) { // Original TS: !id || Number.isNaN(Number(id))
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Service step ID is invalid.");
        }

        ServiceStep serviceStep = serviceStepRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, NOT_FOUND + id));

        // For primitives like boolean isActive, it will take the value from 'data'.
        if (data.getType() != null) {
            try {
                serviceStep.setType(ServiceStep.Type.valueOf(data.getType().toUpperCase()));
            } catch (IllegalArgumentException e) {
                throw new ResponseStatusException(
                        HttpStatus.BAD_REQUEST,
                                                  "Type is invalid in DTO. Must be one of: " + Arrays.toString(
                                                          ServiceStep.Type.values()));
            }
        }
        if (data.getCountryCode() != null) {
            serviceStep.setCountryCode(data.getCountryCode());
        }
        if (data.getTitle() != null) {
            serviceStep.setTitle(data.getTitle());
        }
        if (data.getAvailableAt() != null) {
            serviceStep.setAvailableAt(parseInstant(data.getAvailableAt(), "availableAt"));
        }

        if (data.getDeadlineAt() != null) {
            serviceStep.setDeadlineAt(parseInstant(data.getDeadlineAt(), "deadlineAt"));
        }
        if (data.getIsActive() != null) {
            serviceStep.setIsActive(data.getIsActive());
        }
        serviceStep.setUpdatedAt(Instant.now());

        return serviceStepRepository.save(serviceStep);
    }

    /**
     * Soft deletes a service step by setting its 'deleted_at' timestamp.
     *
     * @param id The ID of the service step to remove.
     * @return The "removed" (soft-deleted) {@link ServiceStep} entity.
     * @throws ResponseStatusException if the service step is not found or ID is invalid.
     * @ts-legacy The original TS code did not explicitly check if the entity existed before attempting the update for soft delete.
     */
    @Transactional
    public ServiceStep remove(Integer id) {
        serviceStepRepository.softDeleteAndSetTimestamps(id, LocalDate.now());
        return serviceStepRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, NOT_FOUND + id));
    }
}
