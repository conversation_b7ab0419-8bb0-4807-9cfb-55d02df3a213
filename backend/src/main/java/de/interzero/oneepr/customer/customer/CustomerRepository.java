package de.interzero.oneepr.customer.customer;

import de.interzero.oneepr.customer.customer.dto.LicenseCountryInfo;
import de.interzero.oneepr.customer.license.License;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Repository
public interface CustomerRepository extends JpaRepository<Customer, Integer>, JpaSpecificationExecutor<Customer> {

    Optional<Customer> findByEmail(String email);

    /**
     * Finds a single customer by their ID, but only if they have not been soft-deleted.
     *
     * @param customerId The ID of the customer to find.
     * @return An Optional containing the customer if found and not deleted, otherwise an empty Optional.
     */
    Optional<Customer> findByIdAndDeletedAtIsNull(Integer customerId);

    /**
     * Finds all license and country details associated with customers who have at least one active contract.
     *
     * @return A list of {@link LicenseCountryInfo} objects containing flattened data for processing.
     * @ts-legacy This query emulates Prisma's nested include with select. It retrieves
     * a flattened list of license and country information for all customers with at
     * least one active contract. This data is then processed in the service layer,
     * which is more efficient than fetching the entire entity graph.
     */
    @Query(
            "SELECT new de.interzero.oneepr.customer.customer.dto.LicenseCountryInfo(c.id, l.countryId, l.countryCode, l.countryName, l.countryFlag) " + "FROM Customer c JOIN c.contracts ct JOIN ct.licenses l " + "WHERE c.deletedAt IS NULL " + "AND ct.deletedAt IS NULL " + "AND ct.status = 'ACTIVE'"
    )
    List<LicenseCountryInfo> findLicenseCountryInfoForActiveCustomers();

    /**
     * Counts the number of unique customers who have at least one active, non-deleted contract.
     *
     * @return The total count of distinct customers with active contracts.
     * @ts-legacy This query calculates the total number of unique customers with active
     * contracts. This corresponds to the `customers.length` value used in the original
     * NestJS method's `reduce` function to populate the `unlicensed_customer_count` field.
     */
    @Query(
            "SELECT count(DISTINCT c.id) " + "FROM Customer c JOIN c.contracts ct " + "WHERE c.deletedAt IS NULL " + "AND ct.deletedAt IS NULL " + "AND ct.status = 'ACTIVE'"
    )
    int countDistinctCustomersWithActiveContracts();

    /**
     * Finds the first customer associated with a given user ID, ensuring the customer has not been soft-deleted.
     *
     * @param userId
     * @return
     */
    Optional<Customer> findFirstByUserIdAndDeletedAtIsNull(Integer userId);

    boolean existsByEmail(String email);

    Optional<Customer> findByEmailAndDeletedAtIsNull(String email);

    Optional<Customer> findByUserIdAndDeletedAtIsNull(Integer userId);

    @Query("SELECT count(c) FROM Customer c JOIN c.contracts con WHERE c.deletedAt IS NULL AND con.deletedAt IS NULL AND con.status IN ('ACTIVE', 'TERMINATION_PROCESS') AND c.createdAt < :endDate")
    long countActiveContractsBeforeDate(@Param("endDate") Instant endDate);

    @Query("SELECT count(c) FROM Customer c JOIN c.contracts con WHERE con.status = 'TERMINATED' AND con.updatedAt BETWEEN :startDate AND :endDate")
    long countTerminatedContractsInDateRange(@Param("startDate") Instant startDate,
                                             @Param("endDate") Instant endDate);

    long countByCreatedAtBetween(Instant start,
                                 Instant end);

    /**
     * Counts the total number of unique customers who have at least one active contract.
     */
    @Query(
            "SELECT count(DISTINCT c.id) FROM Customer c " + "JOIN c.contracts con " + "WHERE c.deletedAt IS NULL " + "AND con.deletedAt IS NULL " + "AND con.status = 'ACTIVE'"
    )
    long countTotalActiveCustomers();

    /**
     * Fetches all licenses associated with active, non-deleted customers and contracts.
     */
    @Query(
            "SELECT l FROM License l " + "WHERE l.contract.customer.deletedAt IS NULL " + "AND l.contract.deletedAt IS NULL " + "AND l.contract.status = 'ACTIVE' " + "AND l.deletedAt IS NULL"
    )
    List<License> findAllLicensesForActiveCustomers();
}
