package de.interzero.oneepr.customer.purchase;

import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.customer_commitment.CustomerCommitment;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.time.LocalDate;
import java.util.Map;

@Getter
@Setter
@Entity
@Table(
        name = "shopping_cart_item",
        schema = "public"
)
public class ShoppingCartItem {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "shopping_cart_item_id_gen"
    )
    @SequenceGenerator(
            name = "shopping_cart_item_id_gen",
            sequenceName = "shopping_cart_item_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    private Integer id;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "shopping_cart_id",
            nullable = false
    )
    private ShoppingCart shoppingCart;

    @NotNull
    @Column(
            name = "country_id",
            nullable = false
    )
    private Integer countryId;

    @NotNull
    @Column(
            name = "country_code",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String countryCode;

    @NotNull
    @Column(
            name = "country_name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String countryName;

    @NotNull
    @Column(
            name = "country_flag",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String countryFlag;

    @NotNull
    @Column(
            name = "year",
            nullable = false
    )
    private Integer year;

    @NotNull
    @Column(
            name = "price_list",
            nullable = false
    )
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<String, Object> priceList;

    @Column(name = "packaging_services")
    @JdbcTypeCode(SqlTypes.JSON)
    private Object packagingServices;

    @OneToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "customer_commitment_id")
    private CustomerCommitment customerCommitment;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    private Instant updatedAt;

    @Column(name = "deleted_at")
    private LocalDate deletedAt;

    @Column(name = "calculator")
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<String, Object> calculator;

    @ColumnDefault("'PURCHASE'")
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(name = "service_type")
    private Contract.Type serviceType;

    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(name = "specification_type")
    private SpecificationType specificationType;

    public enum SpecificationType {
        PURCHASE,
        VOLUME_CHANGE
    }
}