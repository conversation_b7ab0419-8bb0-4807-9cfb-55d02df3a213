package de.interzero.oneepr.customer.license_packaging_service.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * Get License Packaging Service Performance DTO
 * Converted from TypeScript license-packaging-service-performance-dto.ts
 * Maintains exact same structure, variable names, and property names.
 */
@Data
public class GetLicensePackagingServicePerformanceDto {

    /**
     * The start date for performance filtering
     * Equivalent to TypeScript: start_date: Date;
     */
    @JsonProperty("start_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(
            description = "The start date",
            example = "2024-01-01",
            type = "string",
            format = "date",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private Date startDate;

    /**
     * The end date for performance filtering
     * Equivalent to TypeScript: end_date: Date;
     */
    @JsonProperty("end_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(
            description = "The end date",
            example = "2024-01-01",
            type = "string",
            format = "date",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private Date endDate;
}
