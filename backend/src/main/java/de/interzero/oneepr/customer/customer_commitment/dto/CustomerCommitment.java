package de.interzero.oneepr.customer.customer_commitment.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * Describes the structure of a stored customer commitment.
 * Originally found in src/customer-commitment/dto/customer-commitment.dto.ts
 */
@Data
public class CustomerCommitment {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("year")
    private Integer year;

    @JsonProperty("commitment")
    private List<AnsweredCommitment> commitment;

    @JsonProperty("setup")
    private CustomerServiceSetup setup;
}