package de.interzero.oneepr.customer.license_third_party_invoice.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * DTO for providing filter criteria when finding third-party license invoices.
 * All fields are optional and used to narrow down the search results.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class FindAllThirdPartyInvoiceDto {

    @Schema(
            description = "Optional: The license ID to filter invoices for.",
            example = "789",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("license_id")
    private Integer licenseId;


    @Schema(
            description = "Optional: The start date (inclusive) for filtering invoices (e.g., '2025-01-01').",
            example = "2025-01-01",
            format = "date",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("from_date")
    private String fromDate;

    @Schema(
            description = "Optional: The end date (inclusive) for filtering invoices (e.g., '2025-12-31').",
            example = "2025-12-31",
            format = "date",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("to_date")
    private String toDate;
}