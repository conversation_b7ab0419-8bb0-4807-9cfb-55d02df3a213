package de.interzero.oneepr.customer.license_packaging_service;

import de.interzero.oneepr.customer.entity.LicenseVolumeReportItem;
import de.interzero.oneepr.customer.license.License;
import de.interzero.oneepr.customer.license_packaging_service.dto.CreateLicensePackagingServiceDto;
import de.interzero.oneepr.customer.license_packaging_service.dto.GetLicensePackagingServicePerformanceDto;
import de.interzero.oneepr.customer.license_packaging_service.dto.GetLicensePackagingServiceTurnoverDto;
import de.interzero.oneepr.customer.license_packaging_service.dto.UpdateLicensePackagingServiceDto;
import de.interzero.oneepr.customer.license_volume_report_item.LicenseVolumeReportItemRepository;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import de.interzero.oneepr.customer.shared.auth.Role;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import jakarta.persistence.EntityManager;
import jakarta.persistence.criteria.Predicate;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.TextStyle;
import java.util.*;
import java.util.stream.Collectors;

/**
 * License Packaging Service for managing license packaging service operations.
 * Converted from TypeScript license-packaging-service.service.ts
 * Maintains exact same structure, variable names, and function names.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class LicensePackagingServiceService {

    private final LicensePackagingServiceRepository licensePackagingServiceRepository;
    private final LicenseVolumeReportItemRepository licenseVolumeReportItemRepository;
    private final EntityManager entityManager;

    /**
     * Find all license packaging services with optional license_id filter
     * Equivalent to TypeScript: async findAll(license_id?: number)
     *
     * @param license_id Optional license ID to filter by (maintaining exact same parameter name)
     * @return List of LicensePackagingService entities with nested relations
     */
    public List<LicensePackagingService> findAll(Integer license_id) {
        // Create specification for filtering - maintaining exact same logic as TypeScript
        Specification<LicensePackagingService> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // where: { deleted_at: null } - maintaining exact same logic as TypeScript
            predicates.add(criteriaBuilder.isNull(root.get("deletedAt")));

            // ...(license_id && { license_id: license_id }) - maintaining exact same conditional logic
            if (license_id != null) {
                predicates.add(criteriaBuilder.equal(root.get("license").get("id"), license_id));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        // orderBy: { id: "asc" } - maintaining exact same ordering as TypeScript
        Sort sort = Sort.by(Sort.Direction.ASC, "id");

        // Return with nested includes - maintaining exact same structure as TypeScript
        return licensePackagingServiceRepository.findAll(spec, sort);
    }

    /**
     * Find one license packaging service by ID with user permission validation
     * Equivalent to TypeScript: async findOne(id: number, user: AuthenticatedUser)
     *
     * @param id The ID of the license packaging service (maintaining exact same parameter name)
     * @param user The authenticated user (maintaining exact same parameter name)
     * @return The found LicensePackagingService with nested relations
     */
    public LicensePackagingService findOne(Integer id, AuthenticatedUser user) {
        // Validate user permission first - maintaining exact same logic as TypeScript
        validatingUserPermissionLicensePackagingService(id, user);

        // const licensePackagingService = await this.databaseService.licensePackagingService.findUnique
        // where: { id: Number(id), deleted_at: null } - maintaining exact same logic as TypeScript
        Optional<LicensePackagingService> licensePackagingServiceOpt = licensePackagingServiceRepository
                .findByIdAndDeletedAtIsNull(id);

        if (licensePackagingServiceOpt.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "License Packaging Service not found");
        }

        // return licensePackagingService; - maintaining exact same return logic as TypeScript
        return licensePackagingServiceOpt.get();
    }

    /**
     * Get performance data for a setup packaging service
     * Equivalent to TypeScript: async getPerformance(setup_packaging_service_id: number, params: GetLicensePackagingServicePerformanceDto)
     *
     * @param setup_packaging_service_id The setup packaging service ID (maintaining exact same parameter name)
     * @param params The performance parameters (maintaining exact same parameter name)
     * @return Performance data map with exact same structure as TypeScript
     */
    public Map<String, Object> getPerformance(Integer setup_packaging_service_id, GetLicensePackagingServicePerformanceDto params) {
        // if (!setup_packaging_service_id || Number.isNaN(Number(setup_packaging_service_id))) - maintaining exact same validation
        if (setup_packaging_service_id == null || setup_packaging_service_id <= 0) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "License Packaging Service ID is invalid");
        }

        // Create specification for filtering - maintaining exact same logic as TypeScript
        Specification<LicensePackagingService> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // where: { setup_packaging_service_id: Number(setup_packaging_service_id), deleted_at: null }
            predicates.add(criteriaBuilder.equal(root.get("setupPackagingServiceId"), setup_packaging_service_id));
            predicates.add(criteriaBuilder.isNull(root.get("deletedAt")));

            // ...(params.start_date && { created_at: { gte: params.start_date } })
            if (params.getStart_date() != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("createdAt"), params.getStart_date().toInstant()));
            }

            // ...(params.end_date && { created_at: { lte: params.end_date } })
            if (params.getEnd_date() != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("createdAt"), params.getEnd_date().toInstant()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        // orderBy: { created_at: "desc" } - maintaining exact same ordering as TypeScript
        Sort sort = Sort.by(Sort.Direction.DESC, "createdAt");

        // const setupPackagingServices = await this.databaseService.licensePackagingService.findMany
        List<LicensePackagingService> setupPackagingServices = licensePackagingServiceRepository.findAll(spec, sort);

        // let result = { ... } - maintaining exact same result structure as TypeScript
        Map<String, Object> result = new HashMap<>();
        result.put("setup_packaging_service_id", setup_packaging_service_id);
        result.put("name", null);
        result.put("customers_total", 0);
        result.put("revenue_total", 0);
        result.put("handling_total", 0);
        result.put("third_party_total", 0);

        // if (!setupPackagingServices.length) return result; - maintaining exact same early return logic
        if (setupPackagingServices.isEmpty()) {
            return result;
        }

        // result.name = setupPackagingServices[0].name; - maintaining exact same logic as TypeScript
        result.put("name", setupPackagingServices.get(0).getName());

        // result = setupPackagingServices.reduce(...) - maintaining exact same reduce logic as TypeScript
        for (LicensePackagingService curr : setupPackagingServices) {
            result.put("setup_packaging_service_id", curr.getSetupPackagingServiceId());
            result.put("name", curr.getName());
            result.put("customers_total", (Integer) result.get("customers_total") + 1);

            // Access price_list through license relationship - maintaining exact same logic as TypeScript
            License license = curr.getLicense();
            if (license != null && license.getPriceList() != null && !license.getPriceList().isEmpty()) {
                Integer registrationFee = license.getPriceList().get(0).getRegistrationFee();
                Integer handlingFee = license.getPriceList().get(0).getHandlingFee();

                result.put("revenue_total", (Integer) result.get("revenue_total") + (registrationFee != null ? registrationFee : 0));
                result.put("handling_total", (Integer) result.get("handling_total") + (handlingFee != null ? handlingFee : 0));
            }

            // third_party_total: acc.third_party_total + 0 - maintaining exact same logic as TypeScript
            result.put("third_party_total", (Integer) result.get("third_party_total") + 0);
        }

        return result;
    }

    /**
     * Get turnover data for a setup packaging service
     * Equivalent to TypeScript: async getTurnover(setup_packaging_service_id: number, params: GetLicensePackagingServiceTurnoverDto)
     *
     * @param setup_packaging_service_id The setup packaging service ID (maintaining exact same parameter name)
     * @param params The turnover parameters (maintaining exact same parameter name)
     * @return Turnover data list with exact same structure as TypeScript
     */
    public List<Map<String, Object>> getTurnover(Integer setup_packaging_service_id, GetLicensePackagingServiceTurnoverDto params) {
        // if (!setup_packaging_service_id || Number.isNaN(Number(setup_packaging_service_id))) - maintaining exact same validation
        if (setup_packaging_service_id == null || setup_packaging_service_id <= 0) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "License Packaging Service ID is invalid");
        }

        // const volumeReportItems = await this.databaseService.licenseVolumeReportItem.findMany
        List<LicenseVolumeReportItem> volumeReportItems = licenseVolumeReportItemRepository.findAll((root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // where: { deleted_at: null, volume_report: { packaging_service: { setup_packaging_service_id } } }
            predicates.add(criteriaBuilder.isNull(root.get("deletedAt")));
            predicates.add(criteriaBuilder.equal(
                root.get("licenseVolumeReport").get("packagingService").get("setupPackagingServiceId"),
                setup_packaging_service_id
            ));

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        });

        // const groupedData = volumeReportItems.reduce(...) - maintaining exact same grouping logic as TypeScript
        Map<String, Map<String, Object>> groupedData = new HashMap<>();

        for (LicenseVolumeReportItem item : volumeReportItems) {
            // Get the date from volume report - maintaining exact same logic as TypeScript
            Instant createdAt = item.getLicenseVolumeReport().getCreatedAt();
            LocalDate date = createdAt.atZone(ZoneId.systemDefault()).toLocalDate();

            // Generate key based on group_by parameter - maintaining exact same logic as TypeScript
            String key;
            switch (params.getGroup_by()) {
                case "MONTH":
                    key = date.getYear() + "-" + String.format("%02d", date.getMonthValue());
                    break;
                case "QUARTER":
                    int quarter = (date.getMonthValue() - 1) / 3 + 1;
                    key = date.getYear() + "-Q" + quarter;
                    break;
                case "YEAR":
                    key = String.valueOf(date.getYear());
                    break;
                default:
                    key = date.toString();
            }

            // Initialize or update grouped data - maintaining exact same logic as TypeScript
            groupedData.computeIfAbsent(key, k -> {
                Map<String, Object> data = new HashMap<>();
                data.put("period", k);
                data.put("total_value", 0);
                data.put("total_price", 0);
                data.put("items_count", 0);
                return data;
            });

            Map<String, Object> group = groupedData.get(key);
            group.put("total_value", (Integer) group.get("total_value") + (item.getValue() != null ? item.getValue() : 0));
            group.put("total_price", (Integer) group.get("total_price") + (item.getPrice() != null ? item.getPrice() : 0));
            group.put("items_count", (Integer) group.get("items_count") + 1);
        }

        // return Object.values(groupedData).sort(...) - maintaining exact same sorting logic as TypeScript
        return groupedData.values().stream()
                .sorted((a, b) -> ((String) a.get("period")).compareTo((String) b.get("period")))
                .collect(Collectors.toList());
    }

    /**
     * Create a new license packaging service
     * Equivalent to TypeScript: async create(createLicensePackagingServiceDto: CreateLicensePackagingServiceDto)
     *
     * @param createLicensePackagingServiceDto The DTO containing the data to create the service (maintaining exact same parameter name)
     * @return The created LicensePackagingService
     */
    @Transactional
    public LicensePackagingService create(CreateLicensePackagingServiceDto createLicensePackagingServiceDto) {
        // return await this.databaseService.licensePackagingService.create - maintaining exact same logic as TypeScript
        LicensePackagingService licensePackagingService = new LicensePackagingService();

        // Set license relationship - maintaining exact same logic as TypeScript
        License license = new License();
        license.setId(createLicensePackagingServiceDto.getLicense_id());
        licensePackagingService.setLicense(license);

        // Set other fields - maintaining exact same field mapping as TypeScript
        licensePackagingService.setSetupPackagingServiceId(createLicensePackagingServiceDto.getSetup_packaging_service_id());
        licensePackagingService.setName(createLicensePackagingServiceDto.getName());
        licensePackagingService.setDescription(createLicensePackagingServiceDto.getDescription());
        licensePackagingService.setCreatedAt(Instant.now());
        licensePackagingService.setUpdatedAt(Instant.now());

        return licensePackagingServiceRepository.save(licensePackagingService);
    }

    /**
     * Update a license packaging service
     * Equivalent to TypeScript: async update(id: number, data: UpdateLicensePackagingServiceDto, user: AuthenticatedUser)
     *
     * @param id The ID of the license packaging service to update (maintaining exact same parameter name)
     * @param data The DTO containing the update data (maintaining exact same parameter name)
     * @param user The authenticated user (maintaining exact same parameter name)
     * @return The updated LicensePackagingService
     */
    @Transactional
    public LicensePackagingService update(Integer id, UpdateLicensePackagingServiceDto data, AuthenticatedUser user) {
        // await this.validatingUserPermissionLicensePackagingService(id, user); - maintaining exact same logic as TypeScript
        validatingUserPermissionLicensePackagingService(id, user);

        // Find the existing license packaging service
        Optional<LicensePackagingService> licensePackagingServiceOpt = licensePackagingServiceRepository.findByIdAndDeletedAtIsNull(id);

        if (licensePackagingServiceOpt.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "License Packaging Service not found");
        }

        LicensePackagingService licensePackagingService = licensePackagingServiceOpt.get();

        // return await this.databaseService.licensePackagingService.update - maintaining exact same update logic as TypeScript
        if (data.getLicense_id() != null) {
            License license = new License();
            license.setId(data.getLicense_id());
            licensePackagingService.setLicense(license);
        }
        if (data.getSetup_packaging_service_id() != null) {
            licensePackagingService.setSetupPackagingServiceId(data.getSetup_packaging_service_id());
        }
        if (data.getName() != null) {
            licensePackagingService.setName(data.getName());
        }
        if (data.getDescription() != null) {
            licensePackagingService.setDescription(data.getDescription());
        }

        // updated_at: new Date() - maintaining exact same logic as TypeScript
        licensePackagingService.setUpdatedAt(Instant.now());

        return licensePackagingServiceRepository.save(licensePackagingService);
    }

    /**
     * Remove (soft delete) a license packaging service
     * Equivalent to TypeScript: async remove(id: number, user: AuthenticatedUser)
     *
     * @param id The ID of the license packaging service to remove (maintaining exact same parameter name)
     * @param user The authenticated user (maintaining exact same parameter name)
     * @return The soft-deleted LicensePackagingService
     */
    @Transactional
    public LicensePackagingService remove(Integer id, AuthenticatedUser user) {
        // await this.validatingUserPermissionLicensePackagingService(id, user); - maintaining exact same logic as TypeScript
        validatingUserPermissionLicensePackagingService(id, user);

        // Find the existing license packaging service
        Optional<LicensePackagingService> licensePackagingServiceOpt = licensePackagingServiceRepository.findByIdAndDeletedAtIsNull(id);

        if (licensePackagingServiceOpt.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "License Packaging Service not found");
        }

        LicensePackagingService licensePackagingService = licensePackagingServiceOpt.get();

        // return await this.databaseService.licensePackagingService.update - maintaining exact same soft delete logic as TypeScript
        // where: { id: Number(id) }, data: { deleted_at: new Date(), updated_at: new Date() }
        licensePackagingService.setDeletedAt(LocalDate.now());
        licensePackagingService.setUpdatedAt(Instant.now());

        return licensePackagingServiceRepository.save(licensePackagingService);
    }

    /**
     * Validate user permission for accessing a license packaging service
     * Equivalent to TypeScript: async validatingUserPermissionLicensePackagingService(id: number, user: AuthenticatedUser)
     *
     * @param id The ID of the license packaging service (maintaining exact same parameter name)
     * @param user The authenticated user (maintaining exact same parameter name)
     * @throws ResponseStatusException if user doesn't have permission
     */
    private void validatingUserPermissionLicensePackagingService(Integer id, AuthenticatedUser user) {
        // if (user.role === Role.ADMIN || user.role === Role.SUPER_ADMIN || user.role === Role.CLERK) return;
        // maintaining exact same role-based access logic as TypeScript
        if (user.getRole() == Role.ADMIN || user.getRole() == Role.SUPER_ADMIN || user.getRole() == Role.CLERK) {
            return;
        }

        // if (user.role === Role.CUSTOMER) - maintaining exact same customer validation logic as TypeScript
        if (user.getRole() == Role.CUSTOMER) {
            // const licensePackagingService = await this.databaseService.licensePackagingService.findUnique
            Optional<LicensePackagingService> licensePackagingServiceOpt = licensePackagingServiceRepository
                    .findByIdAndDeletedAtIsNull(id);

            if (licensePackagingServiceOpt.isEmpty()) {
                throw new ResponseStatusException(HttpStatus.NOT_FOUND, "License Packaging Service not found");
            }

            LicensePackagingService licensePackagingService = licensePackagingServiceOpt.get();

            // Check if the user owns this license packaging service through the customer relationship
            // maintaining exact same ownership validation logic as TypeScript
            Integer customerId = licensePackagingService.getLicense().getContract().getCustomer().getId();

            // if (licensePackagingService.license.contract.customer_id !== Number(user.id))
            if (!user.getId().equals(customerId.toString())) {
                throw new ResponseStatusException(HttpStatus.FORBIDDEN, "You do not have permission to access this resource");
            }
        } else {
            // throw new ForbiddenException("You do not have permission to access this resource");
            throw new ResponseStatusException(HttpStatus.FORBIDDEN, "You do not have permission to access this resource");
        }
    }
}
