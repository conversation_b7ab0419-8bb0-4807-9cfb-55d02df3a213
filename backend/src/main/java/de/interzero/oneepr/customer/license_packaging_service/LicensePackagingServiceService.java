package de.interzero.oneepr.customer.license_packaging_service;

import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.entity.LicenseVolumeReportItem;
import de.interzero.oneepr.customer.license.License;
import de.interzero.oneepr.customer.license_packaging_service.dto.*;
import de.interzero.oneepr.customer.license_volume_report_item.LicenseVolumeReportItemRepository;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import de.interzero.oneepr.customer.shared.auth.Role;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import jakarta.persistence.criteria.Predicate;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

/**
 * License Packaging Service for managing license packaging service operations.
 * Converted from TypeScript license-packaging-service.service.ts
 * Maintains exact same structure, variable names, and function names.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class LicensePackagingServiceService {

    private final LicensePackagingServiceRepository licensePackagingServiceRepository;

    private final LicenseVolumeReportItemRepository licenseVolumeReportItemRepository;

    /**
     * Find all license packaging services with optional license_id filter
     * Equivalent to TypeScript: async findAll(license_id?: number)
     *
     * @param licenseId Optional license ID to filter by (maintaining exact same parameter name)
     * @return List of LicensePackagingService entities with nested relations
     */
    @SuppressWarnings("java:S1192")
    public List<LicensePackagingService> findAll(Integer licenseId) {
        // Create specification for filtering - maintaining exact same logic as TypeScript
        Specification<LicensePackagingService> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // where: { deleted_at: null } - maintaining exact same logic as TypeScript
            predicates.add(criteriaBuilder.isNull(root.get("deletedAt")));

            // ...(license_id && { license_id: license_id }) - maintaining exact same conditional logic
            if (licenseId != null) {
                predicates.add(criteriaBuilder.equal(root.get("license").get("id"), licenseId));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        // orderBy: { id: "asc" } - maintaining exact same ordering as TypeScript
        Sort sort = Sort.by(Sort.Direction.ASC, "id");

        // Return with nested includes - maintaining exact same structure as TypeScript
        return licensePackagingServiceRepository.findAll(spec, sort);
    }

    /**
     * Find one license packaging service by ID with user permission validation
     * Equivalent to TypeScript: async findOne(id: number, user: AuthenticatedUser)
     *
     * @param id   The ID of the license packaging service (maintaining exact same parameter name)
     * @param user The authenticated user (maintaining exact same parameter name)
     * @return The found LicensePackagingService with nested relations
     */
    public LicensePackagingService findOne(Integer id,
                                           AuthenticatedUser user) {
        // Validate user permission first - maintaining exact same logic as TypeScript
        validatingUserPermissionLicensePackagingService(id, user);

        // const licensePackagingService = await this.databaseService.licensePackagingService.findUnique
        // where: { id: Number(id), deleted_at: null } - maintaining exact same logic as TypeScript
        Optional<LicensePackagingService> licensePackagingServiceOpt = licensePackagingServiceRepository.findByIdAndDeletedAtIsNull(
                id);

        if (licensePackagingServiceOpt.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "License Packaging Service not found");
        }

        // return licensePackagingService; - maintaining exact same return logic as TypeScript
        return licensePackagingServiceOpt.get();
    }

    /**
     * Get performance data for a setup packaging service
     * Equivalent to TypeScript: async getPerformance(setupPackagingServiceId: number, params: GetLicensePackagingServicePerformanceDto)
     *
     * @param setupPackagingServiceId The setup packaging service ID (maintaining exact same parameter name)
     * @param params                  The performance parameters (maintaining exact same parameter name)
     * @return Performance data map with exact same structure as TypeScript
     */
    @SuppressWarnings("all")
    public PackagingServicePerformanceDto getPerformance(Integer setupPackagingServiceId,
                                                         GetLicensePackagingServicePerformanceDto params) {
        if (setupPackagingServiceId == null || setupPackagingServiceId <= 0) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "License Packaging Service ID is invalid");
        }

        // Create specification for filtering - maintaining exact same logic as TypeScript
        Specification<LicensePackagingService> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            predicates.add(criteriaBuilder.equal(root.get("setupPackagingServiceId"), setupPackagingServiceId));
            predicates.add(criteriaBuilder.isNull(root.get("deletedAt")));

            if (params.getStartDate() != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(
                        root.get("createdAt"),
                        params.getStartDate().toInstant()));
            }

            if (params.getEndDate() != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(
                        root.get("createdAt"),
                        params.getStartDate().toInstant()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        // orderBy: { created_at: "desc" } - maintaining exact same ordering as TypeScript
        Sort sort = Sort.by(Sort.Direction.DESC, "createdAt");

        // const setupPackagingServices = await this.databaseService.licensePackagingService.findMany
        List<LicensePackagingService> setupPackagingServices = licensePackagingServiceRepository.findAll(spec, sort);

        // let result = { ... } - maintaining exact same result structure as TypeScript
        PackagingServicePerformanceDto result = new PackagingServicePerformanceDto();
        result.setSetupPackagingServiceId(setupPackagingServiceId);
        result.setCustomersTotal(0);
        result.setRevenueTotal(0);
        result.setHandlingTotal(0);
        result.setThirdPartyTotal(0);

        if (setupPackagingServices.isEmpty()) {
            return result;
        }

        // result.name = setupPackagingServices[0].name; - maintaining exact same logic as TypeScript
        result.setName(setupPackagingServices.getFirst().getName());

        // result = setupPackagingServices.reduce(...) - maintaining exact same reduce logic as TypeScript
        for (LicensePackagingService curr : setupPackagingServices) {
            result.setSetupPackagingServiceId(curr.getSetupPackagingServiceId());
            result.setName(curr.getName());
            result.setCustomersTotal(result.getCustomersTotal() + 1);

            // Access price_list through license relationship - maintaining exact same logic as TypeScript
            License license = curr.getLicense();
            if (license != null && license.getPriceList() != null && !license.getPriceList().isEmpty()) {
                Integer registrationFee = license.getPriceList().getFirst().getRegistrationFee();
                Integer handlingFee = license.getPriceList().getFirst().getHandlingFee();

                result.setRevenueTotal(result.getRevenueTotal() + (registrationFee != null ? registrationFee : 0));
                result.setHandlingTotal(result.getHandlingTotal() + (handlingFee != null ? handlingFee : 0));
            }

            // third_party_total: acc.third_party_total + 0 - maintaining exact same logic as TypeScript
            result.setThirdPartyTotal(result.getThirdPartyTotal());
        }

        return result;
    }

    /**
     * Get turnover data for a setup packaging service
     * Equivalent to TypeScript: async getTurnover(setupPackagingServiceId: number, params: GetLicensePackagingServiceTurnoverDto)
     *
     * @param setupPackagingServiceId The setup packaging service ID (maintaining exact same parameter name)
     * @param params                  The turnover parameters (maintaining exact same parameter name)
     * @return Turnover data list with exact same structure as TypeScript
     */
    @SuppressWarnings({"java:S3776","java:S1192"})
    public List<PackagingServiceTurnoverDto> getTurnover(Integer setupPackagingServiceId,
                                                         GetLicensePackagingServiceTurnoverDto params) {
        if (setupPackagingServiceId == null || setupPackagingServiceId <= 0) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "License Packaging Service ID is invalid");
        }

        // const setupPackagingServices = await this.databaseService.licensePackagingService.findMany
        List<LicensePackagingService> setupPackagingServices = licensePackagingServiceRepository.findAll(
                (root, query, criteriaBuilder) -> {
                    List<Predicate> predicates = new ArrayList<>();

                    predicates.add(criteriaBuilder.equal(root.get("setupPackagingServiceId"), setupPackagingServiceId));
                    predicates.add(criteriaBuilder.isNull(root.get("deletedAt")));

                    return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
                }, Sort.by(Sort.Direction.DESC, "createdAt"));

        if (setupPackagingServices.isEmpty()) {
            return new ArrayList<>();
        }

        // const currentDate = new Date(); - maintaining exact same logic as TypeScript
        LocalDate currentDate = LocalDate.now();
        List<LocalDate> periods = new ArrayList<>();

        // Generate periods based on group_by parameter - maintaining exact same logic as TypeScript
        if ("MONTH".equals(params.getGroupBy())) {
            for (int i = 5; i >= 0; i--) {
                periods.add(currentDate.minusMonths(i).withDayOfMonth(1));
            }
        } else if ("QUARTER".equals(params.getGroupBy())) {
            int currentQuarter = (currentDate.getMonthValue() - 1) / 3;
            for (int i = 3; i >= 0; i--) {
                int quarterStartMonth = (currentQuarter - i) * 3 + 1;
                if (quarterStartMonth > 0) {
                    periods.add(LocalDate.of(currentDate.getYear(), quarterStartMonth, 1));
                } else {
                    periods.add(LocalDate.of(currentDate.getYear() - 1, quarterStartMonth + 12, 1));
                }
            }
        } else {
            periods.add(LocalDate.of(currentDate.getYear() - 1, 1, 1));
            periods.add(LocalDate.of(currentDate.getYear(), 1, 1));
        }

        return periods.stream().map(period -> {
            List<LicensePackagingService> periodServices = setupPackagingServices.stream().filter(service -> {
                LocalDate serviceDate = service.getCreatedAt().atZone(ZoneId.systemDefault()).toLocalDate();

                if ("MONTH".equals(params.getGroupBy())) {
                    return serviceDate.getMonthValue() == period.getMonthValue() && serviceDate.getYear() == period.getYear();
                } else if ("QUARTER".equals(params.getGroupBy())) {
                    return ((serviceDate.getMonthValue() - 1) / 3) == ((period.getMonthValue() - 1) / 3) && serviceDate.getYear() == period.getYear();
                } else {
                    return serviceDate.getYear() == period.getYear();
                }
            }).toList();

            int totalRevenue = periodServices.stream().mapToInt(service -> {
                License license = service.getLicense();
                if (license != null && license.getPriceList() != null && !license.getPriceList().isEmpty()) {
                    Integer registrationFee = license.getPriceList().getFirst().getRegistrationFee();
                    return registrationFee != null ? registrationFee : 0;
                }
                return 0;
            }).sum();

            // Generate period label - maintaining exact same logic as TypeScript
            String periodLabel;
            if ("MONTH".equals(params.getGroupBy())) {
                periodLabel = period.getMonth().getDisplayName(java.time.format.TextStyle.FULL, Locale.getDefault());
            } else if ("QUARTER".equals(params.getGroupBy())) {
                int quarter = (period.getMonthValue() - 1) / 3 + 1;
                periodLabel = "Q" + quarter + " " + period.getYear();
            } else {
                periodLabel = String.valueOf(period.getYear());
            }

            PackagingServiceTurnoverDto result = new PackagingServiceTurnoverDto();
            result.setPeriod(period.atStartOfDay(ZoneId.systemDefault()).toInstant().toString());
            result.setPeriodLabel(periodLabel);
            result.setRevenue(totalRevenue);
            return result;
        }).toList();
    }

    /**
     * Get weight reported data for a setup packaging service
     * Equivalent to TypeScript: async getWeightReported(setupPackagingServiceId: number)
     *
     * @param setupPackagingServiceId The setup packaging service ID (maintaining exact same parameter name)
     * @return List of weight report data with exact same structure as TypeScript
     */
    @SuppressWarnings("java:S1192")
    public List<PackagingServiceWeightReportedDto> getWeightReported(Integer setupPackagingServiceId) {
        if (setupPackagingServiceId == null || setupPackagingServiceId <= 0) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "License Packaging Service ID is invalid");
        }

        // const volumeReportItems = await this.databaseService.licenseVolumeReportItem.findMany
        List<LicenseVolumeReportItem> volumeReportItems = licenseVolumeReportItemRepository.findAll(
                (root, query, criteriaBuilder) -> {
                    List<Predicate> predicates = new ArrayList<>();

                    // where: { setup_fraction_code: { not: null } } - maintaining exact same logic as TypeScript
                    predicates.add(criteriaBuilder.isNotNull(root.get("setupFractionCode")));

                    predicates.add(criteriaBuilder.equal(
                            root.get("licenseVolumeReport").get("packagingService").get("setupPackagingServiceId"),
                            setupPackagingServiceId));
                    predicates.add(criteriaBuilder.isNull(root.get("licenseVolumeReport")
                                                                  .get("packagingService")
                                                                  .get("deletedAt")));
                    predicates.add(criteriaBuilder.isNull(root.get("licenseVolumeReport").get("deletedAt")));

                    return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
                }, Sort.by(Sort.Direction.DESC, "createdAt"));

        Map<String, Integer> groupedWeights = new HashMap<>();

        for (LicenseVolumeReportItem item : volumeReportItems) {
            // const code = item.setup_fraction_code; - maintaining exact same logic as TypeScript
            String code = item.getSetupFractionCode();

            if (!groupedWeights.containsKey(code)) {
                groupedWeights.put(code, 0);
            } else {
                // acc[code] += item.value || 0; - maintaining exact same logic as TypeScript
                groupedWeights.put(code, groupedWeights.get(code) + (item.getValue() != null ? item.getValue() : 0));
            }

        }

        // const result = Object.entries(groupedWeights).map(...).sort(...) - maintaining exact same transformation and sorting logic as TypeScript
        return groupedWeights.entrySet().stream().map(entry -> {
            PackagingServiceWeightReportedDto result = new PackagingServiceWeightReportedDto();
            result.setSetupFractionCode(entry.getKey());
            result.setTotalWeight(entry.getValue());
            return result;
        }).sorted((a, b) -> Integer.compare(b.getTotalWeight(), a.getTotalWeight())).toList();
    }

    /**
     * Create a new license packaging service
     * Equivalent to TypeScript: async create(createLicensePackagingServiceDto: CreateLicensePackagingServiceDto)
     *
     * @param createLicensePackagingServiceDto The DTO containing the data to create the service (maintaining exact same parameter name)
     * @return The created LicensePackagingService
     */
    @Transactional
    public LicensePackagingService create(CreateLicensePackagingServiceDto createLicensePackagingServiceDto) {
        // return await this.databaseService.licensePackagingService.create - maintaining exact same logic as TypeScript
        LicensePackagingService licensePackagingService = new LicensePackagingService();

        // Set license relationship - maintaining exact same logic as TypeScript
        License license = new License();
        license.setId(createLicensePackagingServiceDto.getLicenseId());
        licensePackagingService.setLicense(license);

        // Set other fields - maintaining exact same field mapping as TypeScript
        licensePackagingService.setSetupPackagingServiceId(createLicensePackagingServiceDto.getSetupPackagingServiceId());
        licensePackagingService.setName(createLicensePackagingServiceDto.getName());
        licensePackagingService.setDescription(createLicensePackagingServiceDto.getDescription());
        licensePackagingService.setCreatedAt(Instant.now());
        licensePackagingService.setUpdatedAt(Instant.now());

        return licensePackagingServiceRepository.save(licensePackagingService);
    }

    /**
     * Update a license packaging service
     * Equivalent to TypeScript: async update(id: number, data: UpdateLicensePackagingServiceDto, user: AuthenticatedUser)
     *
     * @param id   The ID of the license packaging service to update (maintaining exact same parameter name)
     * @param data The DTO containing the update data (maintaining exact same parameter name)
     * @param user The authenticated user (maintaining exact same parameter name)
     * @return The updated LicensePackagingService
     */
    @Transactional
    public LicensePackagingService update(Integer id,
                                          UpdateLicensePackagingServiceDto data,
                                          AuthenticatedUser user) {
        // await this.validatingUserPermissionLicensePackagingService(id, user); - maintaining exact same logic as TypeScript
        validatingUserPermissionLicensePackagingService(id, user);

        // Find the existing license packaging service
        Optional<LicensePackagingService> licensePackagingServiceOpt = licensePackagingServiceRepository.findByIdAndDeletedAtIsNull(
                id);

        if (licensePackagingServiceOpt.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "License Packaging Service not found");
        }

        LicensePackagingService licensePackagingService = licensePackagingServiceOpt.get();

        // return await this.databaseService.licensePackagingService.update - maintaining exact same update logic as TypeScript
        if (data.getLicenseId() != null) {
            License license = new License();
            license.setId(data.getLicenseId());
            licensePackagingService.setLicense(license);
        }
        if (data.getSetupPackagingServiceId() != null) {
            licensePackagingService.setSetupPackagingServiceId(data.getSetupPackagingServiceId());
        }
        if (data.getName() != null) {
            licensePackagingService.setName(data.getName());
        }
        if (data.getDescription() != null) {
            licensePackagingService.setDescription(data.getDescription());
        }

        // updated_at: new Date() - maintaining exact same logic as TypeScript
        licensePackagingService.setUpdatedAt(Instant.now());

        return licensePackagingServiceRepository.save(licensePackagingService);
    }

    /**
     * Remove (soft delete) a license packaging service
     * Equivalent to TypeScript: async remove(id: number, user: AuthenticatedUser)
     *
     * @param id   The ID of the license packaging service to remove (maintaining exact same parameter name)
     * @param user The authenticated user (maintaining exact same parameter name)
     * @return The soft-deleted LicensePackagingService
     */
    @Transactional
    public LicensePackagingService remove(Integer id,
                                          AuthenticatedUser user) {
        // await this.validatingUserPermissionLicensePackagingService(id, user); - maintaining exact same logic as TypeScript
        validatingUserPermissionLicensePackagingService(id, user);

        // Find the existing license packaging service
        Optional<LicensePackagingService> licensePackagingServiceOpt = licensePackagingServiceRepository.findByIdAndDeletedAtIsNull(
                id);

        if (licensePackagingServiceOpt.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "License Packaging Service not found");
        }

        LicensePackagingService licensePackagingService = licensePackagingServiceOpt.get();

        licensePackagingService.setDeletedAt(LocalDate.now());
        licensePackagingService.setUpdatedAt(Instant.now());

        return licensePackagingServiceRepository.save(licensePackagingService);
    }

    /**
     * Validate user permission for accessing a license packaging service
     * Equivalent to TypeScript: async validatingUserPermissionLicensePackagingService(id: number, user: AuthenticatedUser)
     *
     * @param id   The ID of the license packaging service (maintaining exact same parameter name)
     * @param user The authenticated user (maintaining exact same parameter name)
     * @throws ResponseStatusException if user doesn't have permission
     */
    private void validatingUserPermissionLicensePackagingService(Integer id,
                                                                 AuthenticatedUser user) {
        // const licensePackagingService = await this.databaseService.licensePackagingService.findUnique
        Optional<LicensePackagingService> licensePackagingServiceOpt = licensePackagingServiceRepository.findByIdAndDeletedAtIsNull(
                id);

        LicensePackagingService licensePackagingService = licensePackagingServiceOpt.orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
                                                                                                                                   "License Packaging Service not found"));

        License license = licensePackagingService.getLicense();
        if (license == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "License not found");
        }

        Contract contract = license.getContract();
        if (contract == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Contract not found");
        }

        Customer customer = contract.getCustomer();
        if (customer == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Customer not found");
        }

        if (user.getRole() == Role.CUSTOMER && !customer.getUserId().equals(Integer.valueOf(user.getId()))) {
            throw new ResponseStatusException(
                    HttpStatus.FORBIDDEN,
                                              "You do not have permission to access this license packaging service");
        }


    }
}
