package de.interzero.oneepr.customer.termination;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.action_guide.ActionGuide;
import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.file.File;
import de.interzero.oneepr.customer.license.License;
import de.interzero.oneepr.customer.reason.TerminationReason;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(
        name = "termination",
        schema = "public"
)
public class Termination {

    public enum Status {
        REQUESTED,
        COMPLETED,
        PENDING
    }

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "termination_id_gen"
    )
    @SequenceGenerator(
            name = "termination_id_gen",
            sequenceName = "termination_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private LocalDate deletedAt;

    @Column(name = "completed_at")
    @JsonProperty("completed_at")
    private Instant completedAt;

    @NotNull
    @Column(
            name = "requested_at",
            nullable = false
    )
    @JsonProperty("requested_at")
    private Instant requestedAt;

    @NotNull
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @ColumnDefault("'REQUESTED'")
    @Column(
            name = "status",
            nullable = false,
            columnDefinition = "enum_termination_status"
    )
    @JsonProperty("status")
    private Status status;

    @OneToMany(
            mappedBy = "termination",
            fetch = FetchType.LAZY
    )
    @JsonIgnore
    @JsonProperty("action_guides")
    private List<ActionGuide> actionGuides = new ArrayList<>();

    @OneToMany(
            mappedBy = "termination",
            fetch = FetchType.LAZY
    )
    @JsonIgnore
    @JsonProperty("contracts")
    private List<Contract> contracts = new ArrayList<>();

    @OneToMany(
            mappedBy = "termination",
            fetch = FetchType.LAZY
    )
    @JsonIgnore
    @JsonProperty("files")
    private List<File> files = new ArrayList<>();

    @OneToMany(
            mappedBy = "termination",
            fetch = FetchType.LAZY
    )
    @JsonIgnore
    @JsonProperty("licenses")
    private List<License> licenses = new ArrayList<>();

    @OneToMany(
            mappedBy = "termination",
            fetch = FetchType.LAZY
    )
    @JsonIgnore
    @JsonProperty("reasons")
    private List<TerminationReason> reasons = new ArrayList<>();

}
