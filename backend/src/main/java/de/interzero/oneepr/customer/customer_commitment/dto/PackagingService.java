package de.interzero.oneepr.customer.customer_commitment.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Originally found in src/customer-commitment/dto/customer-commitment.dto.ts
 */
@Data
public class PackagingService {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("name")
    private String name;

    @JsonProperty("description")
    private String description;

    @JsonProperty("country_id")
    private Integer countryId;

    @JsonProperty("obliged")
    private Boolean obliged;
}
