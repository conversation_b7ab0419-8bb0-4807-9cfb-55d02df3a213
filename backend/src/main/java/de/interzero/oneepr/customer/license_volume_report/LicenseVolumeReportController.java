package de.interzero.oneepr.customer.license_volume_report;

import de.interzero.oneepr.customer.license_volume_report.dto.CreateLicenseVolumeReportDto;
import de.interzero.oneepr.customer.license_volume_report.dto.DeclineLicenseVolumeReportDto;
import de.interzero.oneepr.customer.license_volume_report.dto.UpdateLicenseVolumeReportDto;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import de.interzero.oneepr.common.AuthUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

import java.util.List;

import static de.interzero.oneepr.common.string.Api.LICENSE_VOLUME_REPORT;
import static de.interzero.oneepr.common.string.Role.*;
import static de.interzero.oneepr.common.string.Role.CUSTOMER;

/**
 * License Volume Report Controller for managing license volume report operations.
 * Converted from TypeScript license-volume-report.controller.ts
 * Maintains exact same structure, variable names, and function names.
 */
@Tag(
        name = "License Volume Report",
        description = "License Volume Report management operations"
)
@SecurityRequirement(name = "bearerAuth")
@RestController
@RequestMapping(LICENSE_VOLUME_REPORT)
@RequiredArgsConstructor
@Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER})
public class LicenseVolumeReportController {

    private final LicenseVolumeReportService licenseVolumeReportService;

    /**
     * Create a new license volume report
     * Equivalent to TypeScript: @Post() create(@Body() createLicenseVolumeReportDto: CreateLicenseVolumeReportDto)
     *
     * @param createLicenseVolumeReportDto The DTO containing the data to create the report
     * @return The created LicenseVolumeReport
     */
    @PostMapping
    public LicenseVolumeReport create(@Valid @RequestBody CreateLicenseVolumeReportDto createLicenseVolumeReportDto) {
        // Maintaining exact same logic as TypeScript: return this.licenseVolumeReportService.create(createLicenseVolumeReportDto);
        return licenseVolumeReportService.create(createLicenseVolumeReportDto);
    }

    /**
     * Find all license volume reports
     * Equivalent to TypeScript: @Get() findAll()
     *
     * @return List of all LicenseVolumeReport entities
     */
    @GetMapping
    public List<LicenseVolumeReport> findAll() {
        // Maintaining exact same logic as TypeScript: return this.licenseVolumeReportService.findAll();
        return licenseVolumeReportService.findAll();
    }

    /**
     * Find one license volume report by ID
     * Equivalent to TypeScript: @Get(":id") findOne(@Param("id") id: string, @User() user: AuthenticatedUser)
     *
     * @param id The ID of the license volume report (as string, converted to number like TypeScript +id)
     * @return The found LicenseVolumeReport or empty response
     */
    @GetMapping("/{id}")
    public LicenseVolumeReport findOne(@PathVariable("id") Integer id) {
        // Get authenticated user - maintaining exact same logic as TypeScript @User() decorator
        AuthenticatedUser user = AuthUtil.getRelevantUserDetails();
        return licenseVolumeReportService.findOne(id, user);
    }

    /**
     * Update a license volume report
     * Equivalent to TypeScript: @Put(":id") update(@Param("id") id: string, @Body() updateLicenseVolumeReportDto: UpdateLicenseVolumeReportDto, @User() user: AuthenticatedUser)
     *
     * @param id                           The ID of the license volume report (as string, converted to number like TypeScript +id)
     * @param updateLicenseVolumeReportDto The DTO containing the update data
     * @return The updated LicenseVolumeReport
     */
    @PutMapping("/{id}")
    public LicenseVolumeReport update(@PathVariable("id") Integer id,
                                      @Valid @RequestBody UpdateLicenseVolumeReportDto updateLicenseVolumeReportDto) {
        // Get authenticated user - maintaining exact same logic as TypeScript @User() decorator
        AuthenticatedUser user = AuthUtil.getRelevantUserDetails();
        // Maintaining exact same logic as TypeScript: return this.licenseVolumeReportService.update(+id, updateLicenseVolumeReportDto, user);
        return licenseVolumeReportService.update(id, updateLicenseVolumeReportDto, user);
    }

    /**
     * Decline a license volume report
     * Equivalent to TypeScript: @Post(":id/decline") decline(@Param("id") id: number, @Body() data: DeclineLicenseVolumeReportDto, @User() user: AuthenticatedUser)
     *
     * @param id   The ID of the license volume report (as number, maintaining exact same type as TypeScript)
     * @param data The DTO containing decline reasons and title (maintaining exact same parameter name as TypeScript)
     * @return The declined LicenseVolumeReport
     */
    @PostMapping("/{id}/decline")
    @Operation(summary = "Decline a license volume report")
    public LicenseVolumeReport decline(@PathVariable("id") Integer id,
                                       @Valid @RequestBody DeclineLicenseVolumeReportDto data) {
        // Get authenticated user - maintaining exact same logic as TypeScript @User() decorator
        AuthenticatedUser user = AuthUtil.getRelevantUserDetails();

        // Maintaining exact same logic as TypeScript: return this.licenseVolumeReportService.decline(id, data, user);
        return licenseVolumeReportService.decline(id, data, user);
    }

    /**
     * Remove (soft delete) a license volume report
     * Equivalent to TypeScript: @Delete(":id") remove(@Param("id") id: string, @User() user: AuthenticatedUser)
     *
     * @param id The ID of the license volume report (as string, converted to number like TypeScript +id)
     * @return The soft-deleted LicenseVolumeReport
     */
    @DeleteMapping("/{id}")
    public LicenseVolumeReport remove(@PathVariable("id") Integer id) {
        // Get authenticated user - maintaining exact same logic as TypeScript @User() decorator
        AuthenticatedUser user = AuthUtil.getRelevantUserDetails();

        // Maintaining exact same logic as TypeScript: return this.licenseVolumeReportService.remove(+id, user);
        return licenseVolumeReportService.remove(id, user);
    }
}
