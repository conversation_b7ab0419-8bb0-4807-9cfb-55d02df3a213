package de.interzero.oneepr.customer.customer;

import de.interzero.oneepr.common.AuthUtil;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.customer.customer.dto.*;
import de.interzero.oneepr.customer.entity.CustomerTutorial;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.Map;

import static de.interzero.oneepr.common.string.Role.*;

@RestController
@RequestMapping(Api.CUSTOMERS)
@Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER, PARTNER})
@RequiredArgsConstructor
public class CustomerController {

    private final CustomerService customerService;

    @PreAuthorize("permitAll()")
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public Customer create(@RequestBody CreateCustomerDto data) {
        return customerService.create(data);
    }

    @GetMapping
    public Page<Customer> findAll(FindAllCustomersDto params) {
        return customerService.findAll(params);
    }

    @GetMapping("/tutorials")
    public List<CustomerTutorial> findTutorialStatus(FindTutorialDto params) {
        return customerService.findTutorialStatus(params, AuthUtil.getRelevantUserDetails());
    }

    @PostMapping("/tutorials")
    public CustomerTutorial upsertTutorialStatus(@RequestBody UpsertTutorialDto body) {
        return customerService.upsertTutorialStatus(body);
    }

    @PostMapping("/customer-with-countries")
    @ResponseStatus(HttpStatus.CREATED)
    public Map<String, Object> createWithCountries(@RequestBody CreateCustomerCountriesDto body) {
        return customerService.createWithCountries(body);
    }

    @GetMapping("/by-country")
    public Map<String, GroupByCountryDto> groupByCountry() {
        return customerService.groupByCountry();
    }

    @GetMapping("/{id}")
    public Customer findOne(@PathVariable String id) {
        try {
            Integer customerId = Integer.valueOf(id);
            return customerService.findById(customerId, AuthUtil.getRelevantUserDetails());
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "ID must be a valid number");
        }
    }

    @GetMapping("/user/{userId}")
    public CustomerProfileDto findByUserId(@PathVariable String userId) {
        try {
            Integer parsedUserId = Integer.valueOf(userId);
            return customerService.findByUserId(parsedUserId, AuthUtil.getRelevantUserDetails());
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "User ID must be a valid number");
        }
    }

    @GetMapping("/by-email/{email}")
    public Customer findOneByEmail(@PathVariable String email) {
        return customerService.findOneByEmail(email, AuthUtil.getRelevantUserDetails());
    }

    @PutMapping("/{id}")
    public Customer update(@PathVariable String id,
                           @RequestBody UpdateCustomerDto data) {
        try {
            Integer customerId = Integer.valueOf(id);
            return customerService.update(customerId, data, AuthUtil.getRelevantUserDetails());
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "ID must be a valid number");
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> remove(@PathVariable String id) {
        try {
            Integer customerId = Integer.valueOf(id);
            customerService.remove(customerId, AuthUtil.getRelevantUserDetails());
            return ResponseEntity.ok(Map.of("statusCode", 200, "message", "Customer deleted successfully"));
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "ID must be a valid number");
        }
    }

    @GetMapping("/{id}/details")
    public CustomerDetailsDto details(@PathVariable String id) {
        try {
            Integer customerId = Integer.valueOf(id);
            return customerService.details(customerId, AuthUtil.getRelevantUserDetails());
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "ID must be a valid number");
        }
    }

    @GetMapping("/summary/monthly")
    public Map<String, Object> getSummary() {
        return customerService.getSummary();
    }

    @GetMapping("/{id}/direct-license-resources")
    public DirectLicenseResourcesDto getDirectLicenseResources(@PathVariable String id) {
        try {
            Integer customerId = Integer.valueOf(id);
            return customerService.getDirectLicenseResources(customerId);
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "ID must be a valid number");
        }
    }
}