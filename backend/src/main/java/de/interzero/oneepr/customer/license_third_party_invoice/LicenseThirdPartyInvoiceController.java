package de.interzero.oneepr.customer.license_third_party_invoice;

import de.interzero.oneepr.common.AuthUtil;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.customer.license_third_party_invoice.dto.CreateLicenseThirdPartyInvoiceDto;
import de.interzero.oneepr.customer.license_third_party_invoice.dto.FindAllThirdPartyInvoiceDto;
import de.interzero.oneepr.customer.license_third_party_invoice.dto.UpdateLicenseThirdPartyInvoiceDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Controller for managing third-party license invoices.
 * Exposes endpoints for creating, retrieving, updating, and deleting these invoices.
 */
@Tag(
        name = "third-party-invoices",
        description = "Operations related to third-party license invoices"
)
@RestController
@RequestMapping(Api.THIRD_PARTY_INVOICES)
@RequiredArgsConstructor
public class LicenseThirdPartyInvoiceController {

    private final LicenseThirdPartyInvoiceService licenseThirdPartyInvoiceService;

    /**
     * Retrieves all third-party invoices, with optional filtering by license ID,
     * from date, and to date, provided as query parameters.
     *
     * @param licenseId Optional ID of the license to filter invoices by.
     * @param fromDate  Optional start date (inclusive, e.g., "YYYY-MM-DD") for filtering invoices by their issued date.
     * @param toDate    Optional end date (inclusive, e.g., "YYYY-MM-DD") for filtering invoices by their issued date.
     * @return A list of third-party invoices matching the specified criteria.
     * @ts-legacy Corresponds to the NestJS GET endpoint that accepted filter criteria via a query DTO.
     * This Java implementation uses individual @RequestParam annotations and then constructs
     * the FindAllThirdPartyInvoiceDto internally to pass to the service layer.
     */
    @GetMapping("")
    @Operation(
            summary = "Get all third party invoices for a specific license",
            description = "Retrieves invoices linked to the given license ID, with optional date range filtering.",
            responses = @ApiResponse(
                    responseCode = "200",
                    description = "A list of third-party invoices for the specified license.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(
                                    implementation = List.class,
                                    subTypes = {LicenseThirdPartyInvoice.class}
                            )
                    )
            )
    )
    public List<LicenseThirdPartyInvoice> findAll(@RequestParam(
                                                          name = "license_id",
                                                          required = false
                                                  ) Integer licenseId,
                                                  @RequestParam(
                                                          name = "from_date",
                                                          required = false
                                                  ) String fromDate,
                                                  @RequestParam(
                                                          name = "to_date",
                                                          required = false
                                                  ) String toDate) {
        FindAllThirdPartyInvoiceDto data = new FindAllThirdPartyInvoiceDto();
        data.setLicenseId(licenseId);
        data.setFromDate(fromDate);
        data.setToDate(toDate);
        return this.licenseThirdPartyInvoiceService.findAll(data);
    }

    /**
     * Retrieves a specific third-party invoice by its ID.
     * User details are obtained internally via AuthUtil for permission checks in the service.
     *
     * @param id The ID of the third-party invoice.
     * @return The third-party invoice details.
     */
    @GetMapping("/{id}")
    @Operation(
            summary = "Get a third party invoice by id",
            responses = @ApiResponse(
                    responseCode = "200",
                    description = "The third party invoice details.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = LicenseThirdPartyInvoice.class)
                    )
            )
    )
    public LicenseThirdPartyInvoice findOne(@Parameter(description = "ID of the third-party invoice to retrieve.") @PathVariable("id") Integer id) {
        return this.licenseThirdPartyInvoiceService.findOne(id, AuthUtil.getRelevantUserDetails());
    }

    /**
     * Creates a new third-party invoice.
     *
     * @param data DTO containing information for the new invoice.
     * @return The created third-party invoice.
     */
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    @Operation(
            summary = "Create a third party invoice",
            responses = @ApiResponse(
                    responseCode = "201",
                    description = "The third party invoice has been successfully created.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = LicenseThirdPartyInvoice.class)
                    )
            )
    )
    public LicenseThirdPartyInvoice create(@io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "Data to create a new third-party invoice.",
            required = true,
            content = @Content(schema = @Schema(implementation = CreateLicenseThirdPartyInvoiceDto.class))
    ) @RequestBody CreateLicenseThirdPartyInvoiceDto data) {
        return this.licenseThirdPartyInvoiceService.create(data);
    }

    /**
     * Updates an existing third-party invoice by its ID.
     * User details are obtained internally via AuthUtil for permission checks in the service.
     *
     * @param id   The ID of the third-party invoice to update.
     * @param data DTO containing updated data.
     * @return The updated third-party invoice.
     */
    @PutMapping("/{id}")
    @Operation(
            summary = "Update a third party invoice",
            responses = @ApiResponse(
                    responseCode = "200",
                    description = "The third party invoice has been successfully updated.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = LicenseThirdPartyInvoice.class)
                    )
            )
    )
    public LicenseThirdPartyInvoice update(@Parameter(description = "ID of the third-party invoice to update.") @PathVariable("id") Integer id,
                                           @io.swagger.v3.oas.annotations.parameters.RequestBody(
                                                   description = "Data to update the third-party invoice.",
                                                   required = true,
                                                   content = @Content(schema = @Schema(implementation = UpdateLicenseThirdPartyInvoiceDto.class))
                                           ) @RequestBody UpdateLicenseThirdPartyInvoiceDto data) {
        return this.licenseThirdPartyInvoiceService.update(id, data, AuthUtil.getRelevantUserDetails());
    }

    /**
     * Deletes (soft delete) a third-party invoice by its ID.
     * User details are obtained internally via AuthUtil for permission checks in the service.
     *
     * @param id The ID of the third-party invoice to delete.
     * @return The soft-deleted third-party invoice.
     */
    @DeleteMapping("/{id}")
    @Operation(
            summary = "Delete a third party invoice",
            responses = @ApiResponse(
                    responseCode = "200",
                    description = "The third party invoice has been successfully marked as deleted.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = LicenseThirdPartyInvoice.class)
                    )
            )
    )
    public LicenseThirdPartyInvoice remove(@Parameter(description = "ID of the third-party invoice to delete.") @PathVariable("id") Integer id) {
        return this.licenseThirdPartyInvoiceService.remove(id, AuthUtil.getRelevantUserDetails());
    }
}