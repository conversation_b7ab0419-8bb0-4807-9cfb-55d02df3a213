package de.interzero.oneepr.customer.cluster;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ClusterRepository extends JpaRepository<Cluster, Integer>, JpaSpecificationExecutor<Cluster> {


    /**
     * Find all, that are not "deleted" (have "delete"-date)
     *
     * @return List of Clusters, that are not "deleted"
     */
    List<Cluster> findAllByDeletedAtIsNull();

    /**
     * Find by id, that is not "deleted" (has not "deleted_at"-date)
     *
     * @return cluster with id, that is not "deleted"
     */
    Cluster findByIdAndDeletedAtIsNull(Integer id);

}
