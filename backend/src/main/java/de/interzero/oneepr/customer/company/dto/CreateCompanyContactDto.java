package de.interzero.oneepr.customer.company.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CreateCompanyContactDto extends BaseDto {

    @Schema(description = "ID of the associated company")
    @JsonProperty("company_id")
    private Integer companyId;

    @Schema(description = "Name of the contact person")
    @JsonProperty("name")
    private String name;

    @Schema(description = "Email address of the contact person")
    @JsonProperty("email")
    private String email;

    @Schema(description = "Mobile phone number of the contact person")
    @JsonProperty("phone_mobile")
    private String phoneMobile;
}