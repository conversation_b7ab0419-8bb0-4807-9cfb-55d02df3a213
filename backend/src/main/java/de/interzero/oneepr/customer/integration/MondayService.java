package de.interzero.oneepr.customer.integration;

import de.interzero.oneepr.customer.company.Company;
import de.interzero.oneepr.customer.customer.dto.CreateCustomerDto;
import de.interzero.oneepr.customer.customer.dto.UpdateCustomerDto;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.Map;

@Service
public class MondayService {

    public void sendDataBoardCustomer(CreateCustomerDto data,
                                      Integer id) {
    }

    public void updateCustomerEmailMonday(Map<String,? extends Serializable> email) {
    }

    public void updateBoardAccountsMonday(UpdateCustomerDto data,
                                          Integer id) {
    }

    public void updateBoardAccountsMonday(Company updatedCompany,
                                          Integer integer) {
    }
}
