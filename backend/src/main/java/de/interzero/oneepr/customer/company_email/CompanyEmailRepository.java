package de.interzero.oneepr.customer.company_email;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CompanyEmailRepository extends JpaRepository<CompanyEmail, Integer> {

    List<CompanyEmail> findAllByDeletedAtIsNull();

    Optional<CompanyEmail> findByIdAndDeletedAtIsNull(Integer id);

    /**
     * Check if a company email exists by company ID and email address.
     * Used for duplicate checking when creating company emails.
     *
     * @param companyId Company ID
     * @param email     Email address
     * @return true if exists, false otherwise
     */
    boolean existsCompanyEmailsByCompany_IdAndEmail(Integer companyId,
                                                    String email);

    /**
     * Delete all company emails by company ID.
     * Used in update operations to replace all emails.
     * Equivalent to TypeScript: deleteMany({ where: { company_id: companyId } })
     *
     * @param companyId Company ID
     */
    @Modifying
    @Query("DELETE FROM CompanyEmail ce WHERE ce.company.id = :companyId")
    void deleteCompanyEmailByCompany_Id(Integer companyId);

    /**
     * find all company emails by company ID
     * @param id company ID
     * @return list of company emails
     */
    List<CompanyEmail> findAllByCompany_Id(Integer id);
}
