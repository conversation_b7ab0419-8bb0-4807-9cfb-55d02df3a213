package de.interzero.oneepr.customer.company_email;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CompanyEmailRepository extends JpaRepository<CompanyEmail, Integer> {

    List<CompanyEmail> findAllByDeletedAtIsNull();

    Optional<CompanyEmail> findByIdAndDeletedAtIsNull(Integer id);

}
