package de.interzero.oneepr.customer.customer_io;

import com.fasterxml.jackson.annotation.JsonInclude;
import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.contract.ContractRepository;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import de.interzero.oneepr.customer.http.AdminInterface;
import de.interzero.oneepr.customer.license.License;
import de.interzero.oneepr.customer.license_required_information.LicenseRequiredInformation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * TODO: Implement the full functionality based on the original TypeScript service:
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CustomerIoService {

    private final CustomerRepository customerRepository;

    private final ContractRepository contractRepository;

    private static final List<String> ATTRIBUTES_TO_FILTER = List.of(
            "version",
            "timestamp",
            "customer_id",
            "company.contacts",
            "company.created_at",
            "company.deleted_at",
            "company.updated_at",
            "company.address.id",
            "company.address.deleted_at",
            "company.address.updated_at",
            "company.address.created_at");

    /**
     * Processes required information for a customer and updates their attributes in an external system.
     *
     * @param customerId The ID of the customer to process.
     */
    @Transactional
    public void processRequiredInformation(Integer customerId) {
        try {
            // Check if customer exists and is not deleted.
            boolean customerExists = customerRepository.findByIdAndDeletedAtIsNull(customerId).isPresent();
            if (!customerExists) {
                log.warn(
                        "Attempted to process required information for non-existent or deleted customer ID: {}",
                        customerId);
                return;
            }

            RequiredInformationPayloadDto payload = this.dataProcessingRequiredInformation(customerId);
            updateAttributesByCustomerId(customerId, Map.of("required_information", this.validityAttributes(payload)));

        } catch (Exception e) {
            log.error("Failed to process required information for customer ID {}: {}", customerId, e.getMessage());
        }
    }

    /**
     * Updates customer attributes in an external system (Customer.io).
     *
     * @param customerId The ID of the customer to update.
     * @param attributes A map of attributes to send to the external system.
     */
    public void updateAttributesByCustomerId(Integer customerId,
                                             Map<String, Object> attributes) {
        if (customerId == null || attributes == null || attributes.isEmpty()) {
            return;
        }

        Customer customer = customerRepository.findByIdAndDeletedAtIsNull(customerId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Customer not found"));

        // Create a mutable copy to filter attributes.
        Map<String, Object> filteredAttributes = new HashMap<>(attributes);
        filterAttributes(filteredAttributes);

        // Prepare the parameters for the AdminInterface call
        Map<String, Object> apiParams = new HashMap<>(filteredAttributes);
        apiParams.put("id", customer.getId());
        apiParams.put("email", customer.getEmail());

        String url = String.format("/integrations/customer-io/%s", customer.getEmail());

        try {
            AdminInterface.admin(url, apiParams, HttpMethod.POST);
        } catch (Exception e) {
            log.error("Failed to update attributes in Customer.io for customer ID {}: {}", customerId, e.getMessage());
            //@ts-legacy The original code catches and logs the error but does not re-throw
        }
    }

    /**
     * Fetches and processes contract and license data into a structured payload for an external service.
     *
     * @param customerId The ID of the customer.
     * @return A DTO containing structured lists of general and country-specific information.
     * @ts-legacy Refactor this method to reduce its Cognitive Complexity from 17 to the 15 allowed.
     */
    @SuppressWarnings("java:S3776")
    private RequiredInformationPayloadDto dataProcessingRequiredInformation(Integer customerId) {
        List<Contract> contracts = contractRepository.findAllWithRequiredInformationByCustomerIdAndDeletedAtIsNull(
                customerId);

        RequiredInformationPayloadDto payload = new RequiredInformationPayloadDto();
        if (contracts.isEmpty()) {
            return payload;
        }

        List<GeneralInformationDto> generalInformationList = new ArrayList<>();
        List<CountryInformationDto> countryInformationList = new ArrayList<>();

        for (Contract contract : contracts) {
            // Process general information attached to the contract
            if (contract.getGeneralInformations() != null) {
                for (LicenseRequiredInformation generalInfo : contract.getGeneralInformations()) {
                    generalInformationList.add(new GeneralInformationDto(
                            generalInfo.getName(),
                                                                         generalInfo.getStatus()));
                }
            }

            // Process country-specific information from licenses
            if (contract.getLicenses() != null) {
                for (License license : contract.getLicenses()) {
                    if (license.getRequiredInformations() != null && !license.getRequiredInformations().isEmpty()) {
                        List<InformationDetailDto> details = license.getRequiredInformations()
                                .stream()
                                .map(info -> new InformationDetailDto(info.getName(), info.getStatus()))
                                .toList();
                        countryInformationList.add(new CountryInformationDto(license.getCountryName(), details));
                    }
                }
            }
        }

        payload.setGeneralInformation(generalInformationList);
        payload.setCountryInformation(countryInformationList);

        return payload;
    }

    /**
     * Checks if the required information payload is effectively empty. If it is, returns an empty string
     * to signal deletion to Customer.io. Otherwise, returns the original payload.
     *
     * @param payload The payload DTO containing processed information.
     * @return The payload itself, or an empty string if the payload is empty.
     */
    private Object validityAttributes(RequiredInformationPayloadDto payload) {
        final String DELETE_ATTRIBUTE_SIGNAL = "";

        if (payload == null) {
            return DELETE_ATTRIBUTE_SIGNAL;
        }

        // The payload is considered empty if both of its lists are null or empty.
        boolean isGeneralInfoEmpty = payload.getGeneralInformation() == null || payload.getGeneralInformation()
                .isEmpty();
        boolean isCountryInfoEmpty = payload.getCountryInformation() == null || payload.getCountryInformation()
                .isEmpty();

        if (isGeneralInfoEmpty && isCountryInfoEmpty) {
            return DELETE_ATTRIBUTE_SIGNAL;
        }

        return payload;
    }

    /**
     * Helper method to remove filtered attributes from the map.
     *
     * @param attributes The map of attributes to filter.
     * @ts-legacy This replaces the `attributesFilter.forEach((attributeFilter) => this.deleteAttribute(attributes, attributeFilter));` logic.
     */
    private void filterAttributes(Map<String, Object> attributes) {
        for (String attributeToFilter : ATTRIBUTES_TO_FILTER) {
            attributes.remove(attributeToFilter);
        }
    }

    /**
     * DTO to hold the most granular piece of information: its name and status.
     * Used within CountryInformationDto.
     */
    @Data
    @AllArgsConstructor
    private static class InformationDetailDto {

        private String name;

        private LicenseRequiredInformation.Status status;
    }

    /**
     * DTO to hold a list of required information, grouped by country.
     * Used within the main payload.
     */
    @Data
    @AllArgsConstructor
    private static class CountryInformationDto {

        private String country;

        private List<InformationDetailDto> information;
    }

    /**
     * DTO to hold a single piece of general, non-country-specific information.
     * Used within the main payload.
     */
    @Data
    @AllArgsConstructor
    private static class GeneralInformationDto {

        private String name;

        private LicenseRequiredInformation.Status status;
    }

    /**
     * The top-level DTO for constructing the 'required_information' attribute payload.
     * It separates information into general and country-specific lists.
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private static class RequiredInformationPayloadDto {

        private List<GeneralInformationDto> generalInformation = new ArrayList<>();

        private List<CountryInformationDto> countryInformation = new ArrayList<>();
    }
}
