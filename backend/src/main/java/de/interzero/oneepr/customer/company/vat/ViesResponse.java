package de.interzero.oneepr.customer.company.vat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Response from VIES (VAT Information Exchange System) API.
 * Equivalent to TypeScript ViesResponse interface.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ViesResponse {

    @JsonProperty("valid")
    private Boolean valid;

    @JsonProperty("countryCode")
    private String countryCode;

    @JsonProperty("vatNumber")
    private String vatNumber;

    @JsonProperty("name")
    private String name;

    @JsonProperty("address")
    private String address;

    @JsonProperty("requestDate")
    private String requestDate;

    @JsonProperty("requestIdentifier")
    private String requestIdentifier;

    @JsonProperty("traderName")
    private String traderName;

    @JsonProperty("traderStreet")
    private String traderStreet;

    @JsonProperty("traderPostalCode")
    private String traderPostalCode;

    @JsonProperty("traderCity")
    private String traderCity;

    @JsonProperty("traderCompanyType")
    private String traderCompanyType;

    @JsonProperty("traderNameMatch")
    private String traderNameMatch;

    @JsonProperty("traderStreetMatch")
    private String traderStreetMatch;

    @JsonProperty("traderPostalCodeMatch")
    private String traderPostalCodeMatch;

    @JsonProperty("traderCityMatch")
    private String traderCityMatch;

    @JsonProperty("traderCompanyTypeMatch")
    private String traderCompanyTypeMatch;
}
