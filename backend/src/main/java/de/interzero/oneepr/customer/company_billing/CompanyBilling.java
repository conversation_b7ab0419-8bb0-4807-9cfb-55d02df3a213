package de.interzero.oneepr.customer.company_billing;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.company.Company;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.time.Instant;
import java.time.LocalDate;

@Getter
@Setter
@Entity
@Table(
        name = "company_billing",
        schema = "public"
)
public class CompanyBilling {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "company_billing_id_gen"
    )
    @SequenceGenerator(
            name = "company_billing_id_gen",
            sequenceName = "company_billing_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    private Integer id;


    @Column(name = "is_custom")
    private Boolean isCustom = false;

    @NotNull
    @Column(
            name = "full_name",
            length = Integer.MAX_VALUE,
            nullable = false
    )
    private String fullName;

    @NotNull
    @Column(
            name = "country_code",
            length = Integer.MAX_VALUE,
            nullable = false
    )
    private String countryCode;

    @NotNull
    @Column(
            name = "country_name",
            length = Integer.MAX_VALUE,
            nullable = false
    )
    private String countryName;

    @NotNull
    @Column(
            name = "company_name",
            length = Integer.MAX_VALUE,
            nullable = false
    )
    private String companyName;

    @NotNull
    @Column(
            name = "street_and_number",
            length = Integer.MAX_VALUE,
            nullable = false
    )
    private String streetAndNumber;

    @NotNull
    @Column(
            name = "city",
            length = Integer.MAX_VALUE,
            nullable = false
    )
    private String city;

    @NotNull
    @Column(
            name = "zip_code",
            length = Integer.MAX_VALUE,
            nullable = false
    )
    private String zipCode;

    @OneToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "company_id")
    private Company company;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    private Instant updatedAt;

    @Column(name = "deleted_at")
    private LocalDate deletedAt;


    @Transient
    @JsonProperty("company_id")
    public Integer getCompanyId() {
        return company != null ? company.getId() : null;
    }

}
