package de.interzero.oneepr.customer.coupon;

import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.customer.coupon.dto.CreateCouponDto;
import de.interzero.oneepr.customer.coupon.dto.FindAllCouponsPaginatedDto;
import de.interzero.oneepr.customer.coupon.dto.UpdateCouponDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static de.interzero.oneepr.common.string.Role.*;

/**
 * REST Controller for managing coupons.
 * Provides endpoints to create, retrieve, update, and delete coupon entities.
 */
@Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER, MARKETING_MANAGER})
@Tag(
        name = "coupon",
        description = "APIs for managing coupons"
)
@RestController
@RequestMapping(Api.COUPON)
@RequiredArgsConstructor
public class CouponController {

    private final CouponService couponService;

    /**
     * Creates a new coupon based on the provided DTO.
     *
     * @param dto DTO containing the coupon details
     * @return ResponseEntity with created coupon
     */
    @PostMapping
    @Operation(summary = "Create a new coupon")
    @ApiResponse(
            responseCode = "201",
            description = "The coupon has been successfully created."
    )
    public ResponseEntity<Coupon> create(@Valid @RequestBody CreateCouponDto dto) {
        Coupon coupon = couponService.create(dto);
        return new ResponseEntity<>(coupon, HttpStatus.CREATED);
    }

    /**
     * Retrieves all coupons.
     *
     * @return ResponseEntity containing a list of all coupons
     */
    @GetMapping
    @Operation(summary = "Get all coupons")
    @ApiResponse(
            responseCode = "200",
            description = "List of coupons"
    )
    public ResponseEntity<List<Coupon>> findAll() {
        return ResponseEntity.ok(couponService.findAll());
    }

    /**
     * Retrieves a paginated list of coupons with optional filters.
     *
     * @param query DTO containing pagination and filter parameters
     * @return ResponseEntity containing filtered paginated coupons
     */
    @GetMapping("/list/paginated")
    @Operation(summary = "Get paginated coupons list with filters")
    @ApiResponse(
            responseCode = "200",
            description = "Returns paginated coupons with filters"
    )
    public ResponseEntity<Object> findAllPaginated(@Valid FindAllCouponsPaginatedDto query) {
        return ResponseEntity.ok(couponService.findAllPaginated(query));
    }

    /**
     * Retrieves a specific coupon by its ID.
     *
     * @param id The ID of the coupon to retrieve
     * @return ResponseEntity with the coupon or 200 OK with null if not found
     */
    @GetMapping("/{id}")
    @Operation(summary = "Find coupon by ID")
    @ApiResponse(
            responseCode = "200",
            description = "The coupon details"
    )
    public ResponseEntity<Coupon> findOne(@Parameter(description = "ID of coupon to get") @PathVariable("id") Integer id) {
        return ResponseEntity.ok(couponService.findOne(id));
    }

    /**
     * Finds a coupon by its unique code.
     *
     * @param code The coupon code to search for
     * @return ResponseEntity with the found coupon
     */
    @GetMapping("/code/{code}")
    @Operation(summary = "Find coupon by code")
    @ApiResponse(
            responseCode = "200",
            description = "Returns the coupon if found"
    )
    public ResponseEntity<Coupon> findByCode(@Parameter(description = "Code of coupon to find") @PathVariable("code") String code) {
        return ResponseEntity.ok(couponService.findByCode(code));
    }

    /**
     * Updates a coupon identified by its ID using the provided DTO.
     *
     * @param id  ID of the coupon to update
     * @param dto DTO containing updated coupon information
     * @return ResponseEntity with the updated coupon
     */
    @PatchMapping("/{id}")
    @Operation(summary = "Update coupon by ID")
    @ApiResponse(
            responseCode = "200",
            description = "The coupon has been successfully updated."
    )
    public ResponseEntity<Coupon> update(@Parameter(description = "ID of coupon to update") @PathVariable("id") Integer id,
                                         @io.swagger.v3.oas.annotations.parameters.RequestBody(
                                                 required = true,
                                                 content = @Content(
                                                         mediaType = "application/json",
                                                         schema = @Schema(implementation = UpdateCouponDto.class)
                                                 )
                                         ) @Valid @org.springframework.web.bind.annotation.RequestBody UpdateCouponDto dto) {
        return ResponseEntity.ok(couponService.update(id, dto));
    }

    /**
     * Deletes a coupon by its ID (soft delete).
     *
     * @param id The ID of the coupon to delete
     * @return ResponseEntity with the deleted coupon
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete coupon by ID")
    @ApiResponse(
            responseCode = "200",
            description = "The coupon has been successfully deleted."
    )
    public ResponseEntity<Coupon> remove(@Parameter(description = "ID of coupon to delete") @PathVariable("id") Integer id) {
        return ResponseEntity.ok(couponService.remove(id));
    }
}
