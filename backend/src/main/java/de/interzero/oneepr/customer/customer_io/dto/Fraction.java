package de.interzero.oneepr.customer.customer_io.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class Fraction {

    private Long id;

    private String icon;

    private String name;

    private List<Fraction> children;

    private Boolean isActive;

    private Long parentId;

    private Date createdAt;

    private Date deletedAt;

    private Date updatedAt;

    private String description;

    private Long reportSetId;


}
