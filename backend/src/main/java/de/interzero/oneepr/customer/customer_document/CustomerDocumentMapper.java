package de.interzero.oneepr.customer.customer_document;

import de.interzero.oneepr.customer.customer_document.dto.CreateCustomerDocumentDto;
import de.interzero.oneepr.customer.customer_document.dto.UpdateCustomerDocumentDto;
import org.mapstruct.*;

@Mapper(componentModel = "spring")
public interface CustomerDocumentMapper {

    /**
     * Updates an existing CustomerDocument entity from a CreateCustomerDocumentDto.
     * Ignores 'id' and 'customer' fields on the entity as 'id' is auto-generated
     * and 'customer' relationship is set manually after fetching the Customer entity.
     * The 'customerId' from the DTO is not directly mapped by this method.
     *
     * @param dto    The source DTO.
     * @param entity The target entity to update.
     */
    @Mapping(
            target = "id",
            ignore = true
    )
    @Mapping(
            target = "customer",
            ignore = true
    )
    void updateEntityFromDto(CreateCustomerDocumentDto dto,
                             @MappingTarget CustomerDocument entity);

    /**
     * Partially updates an existing CustomerDocument entity from an UpdateCustomerDocumentDto.
     * Only non-null properties from the DTO will be mapped to the entity.
     * 'id' and 'customer' fields on the entity are ignored by this mapping;
     * 'customer' relationship update is handled separately in the service if customerId is provided in the DTO.
     *
     * @param dto    The source DTO with optional update values.
     * @param entity The target entity to update.
     */
    @Mapping(
            target = "id",
            ignore = true
    )
    @Mapping(
            target = "customer",
            ignore = true
    )
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void partialUpdateEntityFromDto(UpdateCustomerDocumentDto dto,
                                    @MappingTarget CustomerDocument entity);
}