package de.interzero.oneepr.customer.company.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Data Transfer Object for updating an existing company.
 * Converted from TypeScript UpdateCompanyDto with exact same structure and field names.
 * Equivalent to PartialType(CreateCompanyDto) - all fields are optional for partial updates.
 */
@Schema(description = "Fields for updating a company - all fields are optional")
@Data
@EqualsAndHashCode(callSuper = true)
public class UpdateCompanyDto extends CreateCompanyDto {
    // All fields inherited from CreateCompanyDto are optional for updates
    // This matches the TypeScript PartialType(CreateCompanyDto) pattern
}
