package de.interzero.oneepr.customer.commission;

import de.interzero.oneepr.customer.commission.dto.*;
import de.interzero.oneepr.customer.coupon.Coupon;
import de.interzero.oneepr.customer.coupon.CouponRepository;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import de.interzero.oneepr.customer.entity.*;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import de.interzero.oneepr.customer.shared.auth.Role;
import de.interzero.oneepr.customer.utils.UtilsService;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * Commission Service - converted from TypeScript commission.service.ts
 * Maintains exact same structure and variable/function names
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CommissionService {

    private final CommissionRepository commissionRepository;

    private final CustomerRepository customerRepository;

    private final PartnerRepository partnerRepository;

    private final CouponRepository couponRepository;

    private final CouponCustomerRepository couponCustomerRepository;

    private final UtilsService utils;

    private final ModelMapper modelMapper;

    /**
     * Generate coupon - exact same logic as TypeScript generateCoupon method
     *
     * @param userId The user ID
     * @return Generated coupon
     */
    @Transactional
    public Coupon generateCoupon(Integer userId) {
        // Get all commissions for user - equivalent to this.findAll({ user_id: `${userId}` })
        FindAllCommissionsDto query = new FindAllCommissionsDto();
        query.setUserId(String.valueOf(userId));
        List<Commission> allCommission = findAll(query);

        // Calculate commission price - exact same logic as TypeScript
        int commissionPrice = allCommission.stream()
                .filter(item -> !Boolean.TRUE.equals(item.getUsed())) // equivalent to !item.used
                .mapToInt(item -> Optional.ofNullable(item.getCommissionValue()).orElse(0))
                .sum();

        // Generate code - exact same format as TypeScript
        String code = String.format("C%d%d%s", userId, commissionPrice, utils.getRandomLetters());

        Instant today = Instant.now();

        // Use commission - equivalent to await this.useCommission(userId)
        commissionRepository.markAsUsedByUserId(userId);

        // Find customer - exact same logic as TypeScript
        Optional<Customer> customerOpt = customerRepository.findFirstByUserIdAndDeletedAtIsNull(userId);

        // Create or update coupon - equivalent to databaseService.coupon.upsert
        Coupon coupon = couponRepository.findByCode(code).map(existingCoupon -> {
            // Update existing coupon
            existingCoupon.setValue(commissionPrice);
            existingCoupon.setForCommission(true);
            existingCoupon.setIsActive(true);
            return couponRepository.save(existingCoupon);
        }).orElseGet(() -> {
            // Create new coupon
            Coupon newCoupon = new Coupon();
            newCoupon.setCode(code);
            newCoupon.setDiscountType(Coupon.DiscountType.ABSOLUTE);
            newCoupon.setIsActive(true);
            newCoupon.setType(Coupon.Type.SYSTEM);
            newCoupon.setValue(commissionPrice);
            newCoupon.setMode(Coupon.Mode.GENERAL);
            newCoupon.setEndDate(today.plusSeconds(365 * 24 * 3600L)); // +1 year
            newCoupon.setStartDate(today);
            newCoupon.setForCommission(true);
            return couponRepository.save(newCoupon);
        });

        // Create coupon-customer relationship if customer exists
        if (customerOpt.isPresent()) {
            Customer customer = customerOpt.get();

            // Check if relationship already exists
            CouponCustomer couponCustomer = new CouponCustomer();
            couponCustomer.setCoupon(coupon);
            couponCustomer.setCustomer(customer);
            couponCustomerRepository.save(couponCustomer);
        }

        return coupon;
    }

    /**
     * Create commission - exact same logic as TypeScript create method
     *
     * @param createCommissionDto The commission data
     * @return Created commission(s)
     */
    @Transactional(timeout = 45)
    public List<Commission> create(CreateCommissionDto createCommissionDto) {
        Integer priceTotal = createCommissionDto.getPriceTotal();

        // Find user (customer or partner) - exact same logic as TypeScript
        Integer userId = createCommissionDto.getUserId();
        UserInfoDto user = null;
        Optional<Customer> customerOpt = customerRepository.findFirstByUserIdAndDeletedAtIsNull(userId);
        if (customerOpt.isPresent()) {
            user = new UserInfoDto(customerOpt.get().getUserId(), Commission.UserType.CUSTOMER);
        } else {
            Optional<Partner> partnerOpt = partnerRepository.findFirstByUserIdAndDeletedAtIsNull(userId);
            user =  partnerOpt.map(partner -> new UserInfoDto(partner.getUserId(), Commission.UserType.PARTNER))
                    .orElse(null);
        }

        if (user == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "User not found");
        }

        // Calculate commission value - exact same formula as TypeScript
        int commissionValue = priceTotal * (createCommissionDto.getCommissionPercentage() / 100);

        List<Commission> resultList = new ArrayList<>();

        // Handle coupon-based commission - exact same logic as TypeScript
        if (createCommissionDto.getCouponId() != null) {
            resultList.addAll(handleCouponCommission(createCommissionDto, commissionValue));
        }

        // Create affiliate link commission - exact same logic as TypeScript
        if (resultList.isEmpty()) {
            resultList.add(createAffiliateCommission(createCommissionDto, commissionValue, user));
        }
        return resultList;
    }

    /**
     * Handle coupon-based commission - exact same logic as TypeScript
     *
     * @param restCommissionDto Commission data
     * @param commissionValue   Calculated commission value
     * @return List of created commissions
     */
    private List<Commission> handleCouponCommission(CreateCommissionDto restCommissionDto,
                                                    int commissionValue) {
        // Find coupon with relationships - exact same logic as TypeScript
        Optional<Coupon> couponOpt = couponRepository.findByIdWithRelations(restCommissionDto.getCouponId());

        if (couponOpt.isEmpty()) {
            return Collections.emptyList();
        }

        Coupon coupon = couponOpt.get();
        List<Commission> commissions = new ArrayList<>();

        // Create commissions for all customers associated with coupon
        for (CouponCustomer couponCustomer : coupon.getCustomers()) {
            Customer customer = couponCustomer.getCustomer();
            Commission commission = createCommissionEntity(
                    restCommissionDto,
                    commissionValue,
                    customer.getUserId(),
                    Commission.UserType.CUSTOMER,
                    Commission.Type.COUPON,
                    coupon);
            commissions.add(commission);
        }

        // Create commissions for all partners associated with coupon
        for (CouponPartner couponPartner : coupon.getPartners()) {
            Partner partner = couponPartner.getPartner();
            Commission commission = createCommissionEntity(
                    restCommissionDto,
                    commissionValue,
                    partner.getUserId(),
                    Commission.UserType.PARTNER,
                    Commission.Type.COUPON,
                    coupon);
            commissions.add(commission);
        }
        commissionRepository.saveAll(commissions);
        return commissions;
    }

    /**
     * Create affiliate commission - exact same logic as TypeScript
     *
     * @param restCommissionDto Commission data
     * @param commissionValue   Calculated commission value
     * @param user              User information
     * @return Created commission
     */
    private Commission createAffiliateCommission(CreateCommissionDto restCommissionDto,
                                                 int commissionValue,
                                                 UserInfoDto user) {
        Commission commission = createCommissionEntity(
                restCommissionDto,
                commissionValue,
                user.getUserId(),
                user.getUserType(),
                Commission.Type.AFFILIATE_LINK,
                null);
        return commissionRepository.save(commission);
    }

    /**
     * Helper method to create commission entity - consolidates commission creation logic
     *
     * @param dto             Commission data
     * @param commissionValue Calculated commission value
     * @param userId          User ID
     * @param userType        User type
     * @param type            Commission type
     * @param coupon          Associated coupon (can be null)
     * @return Commission entity
     */
    private Commission createCommissionEntity(CreateCommissionDto dto,
                                              int commissionValue,
                                              Integer userId,
                                              Commission.UserType userType,
                                              Commission.Type type,
                                              Coupon coupon) {
        Commission commission = new Commission();
        commission.setUserId(userId);
        commission.setCommissionPercentage(dto.getCommissionPercentage());
        commission.setCommissionValue(commissionValue);
        commission.setNetTurnover(dto.getPriceTotal()); // Assuming this maps to price_total
        commission.setOrderId(dto.getOrderId());
        commission.setAffiliateLink(dto.getAffiliateLink());
        commission.setCreatedAt(Instant.now());

        // Set service type
        if (dto.getServiceType() != null) {
            commission.setServiceType(Commission.ServiceType.valueOf(dto.getServiceType().name()));
        }

        // Set coupon information if provided
        if (coupon != null) {
            commission.setCoupon(coupon);
            commission.setCouponCode(coupon.getCode());
        }

        // Set order customer if provided
        if (dto.getOrderCustomerId() != null) {
            customerRepository.findById(dto.getOrderCustomerId()).ifPresent(commission::setOrderCustomer);
        }

        // Set user type and commission type
        commission.setUserType(userType);
        commission.setType(type);

        return commission;
    }

    /**
     * Find all commissions - exact same logic as TypeScript findAll method
     *
     * @param dto Query parameters
     * @return List of commissions
     */
    public List<Commission> findAll(FindAllCommissionsDto dto) {
        return commissionRepository.findAll((root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (dto.getUserId() != null) {
                predicates.add(cb.equal(root.get("userId"), dto.getUserId()));
            }

            if (dto.getServiceType() != null) {
                predicates.add(cb.equal(root.get("serviceType"), dto.getServiceType()));
            }

            if (dto.getType() != null) {
                predicates.add(cb.equal(root.get("type"), dto.getType()));
            }

            if (dto.getClientName() != null && !dto.getClientName().isEmpty()) {
                Join<Commission, Customer> customer = root.join("customer", JoinType.LEFT);
                predicates.add(cb.like(cb.lower(customer.get("firstName")), dto.getClientName().toLowerCase() + "%"));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        });
    }

    /**
     * Find one commission - exact same logic as TypeScript findOne method
     *
     * @param id   Commission ID
     * @param user Authenticated user
     * @return Commission
     */
    public Optional<Commission> findOne(Integer id,
                                        AuthenticatedUser user) {
        validatingUserPermissionCommision(id, user);
        return commissionRepository.findById(id);
    }

    /**
     * Use commission - exact same logic as TypeScript useCommission method
     *
     * @param userId User ID
     * @return Number of updated records
     */
    @Transactional
    public int useCommission(Integer userId) {
        return commissionRepository.markAsUsedByUserId(userId);
    }

    /**
     * Update commission - exact same logic as TypeScript update method
     *
     * @param id                  Commission ID
     * @param updateCommissionDto Update data
     * @param user                Authenticated user
     * @return Updated commission
     */
    @Transactional
    public Commission update(Integer id,
                             UpdateCommissionDto updateCommissionDto,
                             AuthenticatedUser user) {
        validatingUserPermissionCommision(id, user);

        Commission commission = commissionRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Commission not found"));

        // Update fields if provided - exact same logic as TypeScript
        modelMapper.map(updateCommissionDto, commission);

        return commissionRepository.save(commission);
    }

    /**
     * Remove commission - exact same logic as TypeScript remove method
     *
     * @param id   Commission ID
     * @param user Authenticated user
     */
    @Transactional
    public void remove(Integer id,
                       AuthenticatedUser user) {
        validatingUserPermissionCommision(id, user);
        commissionRepository.deleteById(id);
    }

    /**
     * Validate user permission for commission - exact same logic as TypeScript validatingUserPermissionCommission method
     *
     * @param id   Commission ID
     * @param user Authenticated user
     */
    private void validatingUserPermissionCommision(Integer id,
                                                   AuthenticatedUser user) {
        Commission commission = commissionRepository.findByIdWithCustomer(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Commission not found"));

        Customer customer = commission.getOrderCustomer();
        if (customer == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Customer not found");
        }

        // Check permission - exact same logic as TypeScript
        if (user.getRole() == Role.CUSTOMER && !customer.getUserId().equals(Integer.parseInt(user.getId()))) {
            throw new ResponseStatusException(
                    HttpStatus.FORBIDDEN,
                                              "You do not have permission to access this commission");
        }
    }
}
