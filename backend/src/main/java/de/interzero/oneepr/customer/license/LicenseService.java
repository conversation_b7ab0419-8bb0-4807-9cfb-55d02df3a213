package de.interzero.oneepr.customer.license;

import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.license.dto.*;
import de.interzero.oneepr.customer.license_required_information.LicenseRequiredInformation;
import de.interzero.oneepr.customer.license_third_party_invoice.LicenseThirdPartyInvoice;
import de.interzero.oneepr.customer.license_volume_report.LicenseVolumeReport;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import de.interzero.oneepr.customer.shared.auth.Role;
import de.interzero.oneepr.customer.termination.Termination;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import jakarta.persistence.criteria.Predicate;

import java.time.Instant;
import java.time.LocalDate;
import java.util.*;

/**
 * License Service for managing license operations.
 * Converted from TypeScript license.service.ts
 * Maintains exact same structure, variable names, and function names.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class LicenseService {

    private final LicenseRepository licenseRepository;

    /**
     * Get license pendencies - maintaining exact same logic as TypeScript
     * Equivalent to TypeScript: export function getLicensePendencies(license: FullLicense)
     *
     * @param license The license with all nested relations
     * @return LicensePendencies object with pendencies and status
     */
    public static LicensePendenciesDto getLicensePendencies(License license) {
        List<LicensePendencyDto> pendencies = new ArrayList<>();

        if (license.getTermination() != null) {
            if (!Termination.Status.COMPLETED.equals(license.getTermination().getStatus())) {
                pendencies.add(new LicensePendencyDto(
                        LicensePendencyType.TERMINATION_REQUESTED,
                                                      "Termination requested"));

                return new LicensePendenciesDto(pendencies, "IN_REVIEW");
            }

            pendencies.add(new LicensePendencyDto(LicensePendencyType.TERMINATED, "Terminated"));

            return new LicensePendenciesDto(pendencies, "IN_REVIEW");
        }

        boolean isRequiredInformationsPending = license.getRequiredInformations() != null && license.getRequiredInformations()
                .stream()
                .anyMatch(requiredInformation -> LicenseRequiredInformation.Status.OPEN.equals(requiredInformation.getStatus()) || LicenseRequiredInformation.Status.DECLINED.equals(
                        requiredInformation.getStatus()));
        if (isRequiredInformationsPending) {
            pendencies.add(new LicensePendencyDto(LicensePendencyType.REQUIRED_INFORMATIONS, "Required informations"));
        }

        // const isVolumeReportsPending = license.packaging_services.some(...)
        boolean isVolumeReportsPending = license.getPackagingServices() != null && license.getPackagingServices()
                .stream()
                .anyMatch(packagingService -> packagingService.getVolumeReports() != null && packagingService.getVolumeReports()
                        .stream()
                        .anyMatch(volumeReport -> LicenseVolumeReport.Status.OPEN.equals(volumeReport.getStatus()) || LicenseVolumeReport.Status.DECLINED.equals(
                                volumeReport.getStatus())));
        if (isVolumeReportsPending) {
            pendencies.add(new LicensePendencyDto(LicensePendencyType.VOLUME_REPORTS, "Volume reports"));
        }

        // const isThirdPartyInvoicesPending = license.third_party_invoices.some(...)
        boolean isThirdPartyInvoicesPending = license.getThirdPartyInvoices() != null && license.getThirdPartyInvoices()
                .stream()
                .anyMatch(invoice -> LicenseThirdPartyInvoice.Status.OPEN.equals(invoice.getStatus()));
        if (isThirdPartyInvoicesPending) {
            pendencies.add(new LicensePendencyDto(LicensePendencyType.INVOICES, "Invoices"));
        }

        return new LicensePendenciesDto(pendencies, !pendencies.isEmpty() ? "OPEN_TO_DOS" : "DONE");
    }

    /**
     * Find all licenses with optional contract_id filter
     * Equivalent to TypeScript: async findAll(contract_id?: number)
     *
     * @param contractId Optional contract ID to filter by (maintaining exact same parameter name)
     * @return List of License entities with nested relations and pendencies
     */
    @SuppressWarnings("java:S1192")
    public List<LicensePendenciesResultDto> findAll(Integer contractId) {
        if (contractId == null) {
            return licenseRepository.findAll((root, query, criteriaBuilder) -> {
                List<Predicate> predicates = new ArrayList<>();
                predicates.add(criteriaBuilder.isNull(root.get("deletedAt")));
                return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
            }).stream().map(license -> {
                LicensePendenciesResultDto result = new LicensePendenciesResultDto();
                result.setLicense(license);
                return result;
            }).toList();
        }

        List<License> licenses = licenseRepository.findAll(
                (root, query, criteriaBuilder) -> {
                    List<Predicate> predicates = new ArrayList<>();

                    predicates.add(criteriaBuilder.isNull(root.get("deletedAt")));
                    predicates.add(criteriaBuilder.equal(root.get("contract").get("id"), contractId));

                    return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
                }, Sort.by(Sort.Direction.DESC, "id"));

        return licenses.stream().map(license -> {
            // Add pendencies - maintaining exact same logic as TypeScript
            LicensePendenciesDto pendencies = getLicensePendencies(license);

            LicensePendenciesResultDto result = new LicensePendenciesResultDto();
            result.setLicense(license);
            result.setPendencies(pendencies.getPendencies());
            result.setPendenciesStatus(pendencies.getPendenciesStatus());
            return result;
        }).toList();
    }

    /**
     * Find one license by ID with user permission validation
     * Equivalent to TypeScript: async findOne(id: number, user: AuthenticatedUser)
     *
     * @param id   The ID of the license (maintaining exact same parameter name)
     * @param user The authenticated user (maintaining exact same parameter name)
     * @return The found License with nested relations and pendencies
     */
    public LicensePendenciesResultDto findOne(Integer id,
                                       AuthenticatedUser user) {
        validatingUserPermissionLicenseOtherCost(id, user);

        // const license = await this.databaseService.license.findUnique({ ... }) - maintaining exact same logic as TypeScript
        Optional<License> licenseOpt = licenseRepository.findOne((root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.isNull(root.get("deletedAt")));
            predicates.add(criteriaBuilder.equal(root.get("id"), id));
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        });

        if (licenseOpt.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "License not found");
        }

        License license = licenseOpt.get();

        // return { ...license, ...getLicensePendencies(license) }; - maintaining exact same logic as TypeScript
        LicensePendenciesResultDto result = new LicensePendenciesResultDto();
        LicensePendenciesDto pendencies = getLicensePendencies(license);
        result.setLicense(license);
        result.setPendencies(pendencies.getPendencies());
        result.setPendenciesStatus(pendencies.getPendenciesStatus());
        return result;
    }

    /**
     * Create a new license
     * Equivalent to TypeScript: async create(data: CreateLicenseDto)
     *
     * @param data The DTO containing the data to create the license (maintaining exact same parameter name)
     * @return The created License
     */
    @Transactional
    public License create(CreateLicenseDto data) {
        License license = new License();

        Contract contract = new Contract();
        contract.setId(data.getContractId());
        license.setContract(contract);

        license.setCountryId(data.getCountryId());
        license.setCountryCode(data.getCountryCode());
        license.setCountryName(data.getCountryName());
        license.setCountryFlag(data.getCountryFlag());

        license.setRegistrationNumber(UUID.randomUUID().toString().split("-")[0]);

        license.setRegistrationStatus(data.getRegistrationStatus());
        license.setClerkControlStatus(data.getClerkControlStatus());
        license.setContractStatus(data.getContractStatus());
        license.setYear(data.getYear());
        license.setStartDate(data.getStartDate());
        license.setEndDate(data.getEndDate());

        if (data.getTerminationId() != null) {
            Termination termination = new Termination();
            termination.setId(data.getTerminationId());
            license.setTermination(termination);
        }

        license.setCreatedAt(Instant.now());
        license.setUpdatedAt(Instant.now());

        return licenseRepository.save(license);
    }

    /**
     * Update a license
     * Equivalent to TypeScript: async update(id: number, data: UpdateLicenseDto, user: AuthenticatedUser)
     *
     * @param id   The ID of the license to update (maintaining exact same parameter name)
     * @param data The DTO containing the update data (maintaining exact same parameter name)
     * @param user The authenticated user (maintaining exact same parameter name)
     * @return The updated License
     */
    @Transactional
    public License update(Integer id,
                          UpdateLicenseDto data,
                          AuthenticatedUser user) {
        // await this.validatingUserPermissionLicenseOtherCost(id, user); - maintaining exact same logic as TypeScript
        validatingUserPermissionLicenseOtherCost(id, user);

        // Find the existing license
        Optional<License> licenseOpt = licenseRepository.findOne((root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.isNull(root.get("deletedAt")));
            predicates.add(criteriaBuilder.equal(root.get("id"), id));
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        });

        if (licenseOpt.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "License not found");
        }

        License license = licenseOpt.get();

        // return await this.databaseService.license.update({ ... }) - maintaining exact same update logic as TypeScript
        if (data.getRegistrationStatus() != null) {
            license.setRegistrationStatus(data.getRegistrationStatus());
        }
        if (data.getClerkControlStatus() != null) {
            license.setClerkControlStatus(data.getClerkControlStatus());
        }
        if (data.getContractStatus() != null) {
            license.setContractStatus(data.getContractStatus());
        }
        if (data.getTerminationId() != null) {
            Termination termination = new Termination();
            termination.setId(data.getTerminationId());
            license.setTermination(termination);
        }

        // updated_at: new Date() - maintaining exact same logic as TypeScript
        license.setUpdatedAt(Instant.now());

        return licenseRepository.save(license);
    }

    /**
     * Remove (soft delete) a license
     * Equivalent to TypeScript: async remove(id: number, user: AuthenticatedUser)
     *
     * @param id   The ID of the license to remove (maintaining exact same parameter name)
     * @param user The authenticated user (maintaining exact same parameter name)
     * @return The soft-deleted License
     */
    @Transactional
    public License remove(Integer id,
                          AuthenticatedUser user) {
        validatingUserPermissionLicenseOtherCost(id, user);

        // Find the existing license
        Optional<License> licenseOpt = licenseRepository.findOne((root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.isNull(root.get("deletedAt")));
            predicates.add(criteriaBuilder.equal(root.get("id"), id));
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        });

        if (licenseOpt.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "License not found");
        }

        License license = licenseOpt.get();

        license.setDeletedAt(LocalDate.now());
        license.setUpdatedAt(Instant.now());

        return licenseRepository.save(license);
    }

    /**
     * Validate user permission for accessing a license
     * Equivalent to TypeScript: async validatingUserPermissionLicenseOtherCost(id: number, user: AuthenticatedUser)
     *
     * @param id   The ID of the license (maintaining exact same parameter name)
     * @param user The authenticated user (maintaining exact same parameter name)
     * @throws ResponseStatusException if user doesn't have permission
     */
    private void validatingUserPermissionLicenseOtherCost(Integer id,
                                                          AuthenticatedUser user) {
        // const license = await this.databaseService.license.findUnique({ ... })
        Optional<License> licenseOpt = licenseRepository.findOne((root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.isNull(root.get("deletedAt")));
            predicates.add(criteriaBuilder.equal(root.get("id"), id));
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        });

        if (licenseOpt.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "License not found");
        }
        License license = licenseOpt.get();
        Contract contract = license.getContract();
        if (contract == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Contract not found");
        }

        Customer customer = contract.getCustomer();
        if (customer == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Customer not found");
        }
        if (Role.CUSTOMER.equals(user.getRole()) && !customer.getUserId().equals(Integer.parseInt(user.getId()))) {
            throw new ResponseStatusException(
                    HttpStatus.FORBIDDEN,
                    "You do not have permission to access this company");
        }
    }
}
