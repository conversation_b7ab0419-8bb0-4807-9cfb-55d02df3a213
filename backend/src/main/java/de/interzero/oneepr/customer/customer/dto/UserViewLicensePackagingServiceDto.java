package de.interzero.oneepr.customer.customer.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.time.LocalDate;

/**
 * Data Transfer Object representing a packaging service for a license in the user profile view.
 * <p>
 * This DTO is a direct translation of the {@code LicensePackagingService} model, included
 * within the {@code licenses} relation of the {@code customer.service.ts#findByUserId} method.
 */
@Data
@NoArgsConstructor
public class UserViewLicensePackagingServiceDto {

    @Schema(description = "Unique identifier of the packaging service record.")
    @JsonProperty("id")
    private Integer id;

    @Schema(description = "ID of the corresponding packaging service setup item.")
    @JsonProperty("setup_packaging_service_id")
    private Integer setupPackagingServiceId;

    @Schema(description = "Name of the packaging service.")
    @JsonProperty("name")
    private String name;

    @Schema(description = "Description of the packaging service.")
    @JsonProperty("description")
    private String description;

    @Schema(description = "Flag indicating if the service is obliged.")
    @JsonProperty("obliged")
    private Boolean obliged;

    @Schema(description = "Timestamp of when the record was created.")
    @JsonProperty("created_at")
    private Instant createdAt;

    @Schema(description = "Timestamp of when the record was last updated.")
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Schema(description = "Timestamp of when the record was soft-deleted.")
    @JsonProperty("deleted_at")
    private LocalDate deletedAt;
}