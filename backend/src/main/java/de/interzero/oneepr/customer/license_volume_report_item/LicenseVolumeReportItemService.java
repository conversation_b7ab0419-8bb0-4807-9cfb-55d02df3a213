package de.interzero.oneepr.customer.license_volume_report_item;

import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer_io.CustomerIoService;
import de.interzero.oneepr.customer.decline.Decline;
import de.interzero.oneepr.customer.http.AdminInterface;
import de.interzero.oneepr.customer.license.License;
import de.interzero.oneepr.customer.license_packaging_service.LicensePackagingService;
import de.interzero.oneepr.customer.license_volume_report.LicenseVolumeReportRepository;
import de.interzero.oneepr.customer.license_volume_report_item.dto.CreateLicenseVolumeReportItemDto;
import de.interzero.oneepr.customer.license_volume_report_item.dto.UpdateLicenseVolumeReportItemDto;
import de.interzero.oneepr.customer.license_volume_report_item.dto.UpdateLicenseVolumeReportItemValueDto;
import de.interzero.oneepr.customer.license_volume_report_item.dto.DeclineLicenseVolumeReportItemDto;
import de.interzero.oneepr.customer.entity.LicenseVolumeReportItem;
import de.interzero.oneepr.customer.license_volume_report.LicenseVolumeReport;
import de.interzero.oneepr.customer.reason.DeclineReason;
import de.interzero.oneepr.customer.reason.Reason;
import de.interzero.oneepr.customer.reason.ReasonRepository;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import de.interzero.oneepr.customer.shared.auth.Role;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Service for managing license volume report items.
 * Converted from TypeScript license-volume-report-item.service.ts
 * Maintains exact same structure, variable names, and function names.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class LicenseVolumeReportItemService {

    private final LicenseVolumeReportItemRepository licenseVolumeReportItemRepository;

    private final LicenseVolumeReportRepository licenseVolumeReportRepository;

    // Note: These services would need to be implemented in Java
    private final CustomerIoService customerIoService;

    private final ReasonRepository reasonRepository;

    /**
     * Send data to CustomerIO service
     * Equivalent to TypeScript sendToCustomerIo method
     */
    public void sendToCustomerIo(Integer licenseVolumeReportId) {
        try {
            if (licenseVolumeReportId == null) {
                return;
            }

            // Find volume report with all necessary relations
            Optional<LicenseVolumeReport> resVolumeReport = licenseVolumeReportRepository.findById(licenseVolumeReportId);

            if (resVolumeReport.isPresent()) {
                // Extract customer_id from nested relations
                LicenseVolumeReport volumeReport = resVolumeReport.get();
                if (volumeReport.getPackagingService() != null && volumeReport.getPackagingService()
                        .getLicense() != null && volumeReport.getPackagingService()
                        .getLicense()
                        .getContract() != null && volumeReport.getPackagingService()
                        .getLicense()
                        .getContract()
                        .getCustomer() != null) {

                    Integer customerId = volumeReport.getPackagingService()
                            .getLicense()
                            .getContract()
                            .getCustomer()
                            .getId();

                    // Process purchase data - would need CustomerIoService implementation
                    customerIoService.processPurchaseData(customerId);
                }
            }
        } catch (Exception err) {
            log.error("Error in sendToCustomerIo: ", err);
        }
    }

    /**
     * Create a new license volume report item
     * Equivalent to TypeScript create method
     */
    public LicenseVolumeReportItem create(CreateLicenseVolumeReportItemDto createLicenseVolumeReportItemDto) {
        // Create new license volume report item
        LicenseVolumeReportItem licenseVolumeReportItem = new LicenseVolumeReportItem();


        // Map DTO fields to entity - maintaining exact field names
        if (createLicenseVolumeReportItemDto.getLicenseVolumeReportId() != null) {
            LicenseVolumeReport licenseVolumeReport = licenseVolumeReportRepository.findById(
                            createLicenseVolumeReportItemDto.getLicenseVolumeReportId())
                    .orElseThrow(() -> new ResponseStatusException(
                            HttpStatus.NOT_FOUND,
                            "License Volume Report not found"));
            licenseVolumeReportItem.setLicenseVolumeReport(licenseVolumeReport);
        }
        licenseVolumeReportItem.setSetupFractionId(createLicenseVolumeReportItemDto.getSetupFractionId());
        licenseVolumeReportItem.setSetupFractionCode(createLicenseVolumeReportItemDto.getSetupFractionCode());
        licenseVolumeReportItem.setSetupColumnId(createLicenseVolumeReportItemDto.getSetupColumnId());
        licenseVolumeReportItem.setSetupColumnCode(createLicenseVolumeReportItemDto.getSetupColumnCode());
        licenseVolumeReportItem.setValue(createLicenseVolumeReportItemDto.getValue());
        licenseVolumeReportItem.setCreatedAt(Instant.now());
        licenseVolumeReportItem.setUpdatedAt(Instant.now());

        // Save the entity
        LicenseVolumeReportItem savedItem = licenseVolumeReportItemRepository.save(licenseVolumeReportItem);

        // Send to CustomerIO - maintaining exact variable name
        sendToCustomerIo(createLicenseVolumeReportItemDto.getLicenseVolumeReportId());

        return savedItem;
    }

    /**
     * Find all license volume report items with filtering
     * Equivalent to TypeScript findAll method
     */
    public List<LicenseVolumeReportItem> findAll(Integer licenseVolumeReportId,
                                                 AuthenticatedUser user) {
        // Find license volume report with all necessary relations
        Optional<LicenseVolumeReport> licenseVolumeReport = licenseVolumeReportRepository.findFirstByIdAndDeletedAtIsNull(
                licenseVolumeReportId);

        if (licenseVolumeReport.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "License Volume Report Item not found");
        }
        LicenseVolumeReport volumeReport = licenseVolumeReport.get();

        // Extract packaging_service - maintaining exact variable name
        LicensePackagingService packagingService = volumeReport.getPackagingService();
        if (volumeReport.getPackagingService() == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Packaging service not found");
        }

        // Extract license - maintaining exact variable name
        License license = packagingService.getLicense();
        if (license == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "License not found");
        }

        // Extract contract - maintaining exact variable name
        Contract contract = license.getContract();
        if (contract == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Contract not found");
        }

        // Extract customer_id - maintaining exact variable name
        Customer customer = contract.getCustomer();
        if (customer == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Customer not found");
        }

        // Check user permissions - maintaining exact logic
        if (user.getRole() == Role.CUSTOMER && !customer.getId().equals(Integer.parseInt(user.getId()))) {
            throw new ResponseStatusException(
                    HttpStatus.FORBIDDEN,
                                              "You do not have permission to access this resource");
        }

        // Return filtered items - maintaining exact field names
        return licenseVolumeReportItemRepository.findByLicenseVolumeReportAndDeletedAtIsNull(volumeReport);

    }

    /**
     * Find one license volume report item by ID
     * Equivalent to TypeScript findOne method
     */
    public Optional<LicenseVolumeReportItem> findOne(Integer id,
                                                     AuthenticatedUser user) {
        // Validate user permissions first
        validatingUserPermissionVolumeReportItem(id, user);

        // Find and return the item
        return licenseVolumeReportItemRepository.findById(id);
    }

    /**
     * Update a license volume report item
     * Equivalent to TypeScript update method
     */
    public LicenseVolumeReportItem update(Integer id,
                                          UpdateLicenseVolumeReportItemDto updateLicenseVolumeReportItemDto,
                                          AuthenticatedUser user) {
        // Validate user permissions first
        validatingUserPermissionVolumeReportItem(id, user);

        // Find existing item
        LicenseVolumeReportItem existingItem = licenseVolumeReportItemRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(
                        HttpStatus.NOT_FOUND,
                        "License Volume Report Item not found"));

        // Update fields from DTO - maintaining exact field names
        if (updateLicenseVolumeReportItemDto.getLicenseVolumeReportId() != null) {
            LicenseVolumeReport licenseVolumeReport = licenseVolumeReportRepository.findById(
                            updateLicenseVolumeReportItemDto.getLicenseVolumeReportId())
                    .orElseThrow(() -> new ResponseStatusException(
                            HttpStatus.NOT_FOUND,
                            "License Volume Report not found"));
            existingItem.setLicenseVolumeReport(licenseVolumeReport);
        }
        if (updateLicenseVolumeReportItemDto.getSetupFractionId() != null) {
            existingItem.setSetupFractionId(updateLicenseVolumeReportItemDto.getSetupFractionId());
        }
        if (updateLicenseVolumeReportItemDto.getSetupFractionCode() != null) {
            existingItem.setSetupFractionCode(updateLicenseVolumeReportItemDto.getSetupFractionCode());
        }
        if (updateLicenseVolumeReportItemDto.getSetupColumnId() != null) {
            existingItem.setSetupColumnId(updateLicenseVolumeReportItemDto.getSetupColumnId());
        }
        if (updateLicenseVolumeReportItemDto.getSetupColumnCode() != null) {
            existingItem.setSetupColumnCode(updateLicenseVolumeReportItemDto.getSetupColumnCode());
        }
        if (updateLicenseVolumeReportItemDto.getValue() != null) {
            existingItem.setValue(updateLicenseVolumeReportItemDto.getValue());
        }
        existingItem.setUpdatedAt(Instant.now());

        // Save updated item - maintaining exact variable name
        LicenseVolumeReportItem resUpdate = licenseVolumeReportItemRepository.save(existingItem);

        // Send to CustomerIO
        sendToCustomerIo(updateLicenseVolumeReportItemDto.getLicenseVolumeReportId());

        return resUpdate;
    }

    /**
     * Remove (soft delete) a license volume report item
     * Equivalent to TypeScript remove method
     */
    public LicenseVolumeReportItem remove(Integer id,
                                          AuthenticatedUser user) {
        // Validate user permissions first
        validatingUserPermissionVolumeReportItem(id, user);

        LicenseVolumeReportItem existingItem = new LicenseVolumeReportItem();
        existingItem.setId(id);
        // Soft delete - maintaining exact field name
        existingItem.setDeletedAt(LocalDate.now());
        existingItem.setUpdatedAt(Instant.now());

        return licenseVolumeReportItemRepository.save(existingItem);
    }

    /**
     * Create many license volume report items in a transaction
     * Equivalent to TypeScript createMany method
     */
    @Transactional(timeout = 20)
    public void createMany(List<CreateLicenseVolumeReportItemDto> data) {
        Instant now = Instant.now();

        if (data.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Items are required");
        }

        // Extract volumeReportId - maintaining exact variable name
        Integer volumeReportId = data.getFirst().getLicenseVolumeReportId();
        LicenseVolumeReport licenseVolumeReport = licenseVolumeReportRepository.findById(volumeReportId).orElse(null);
        // Find existing volume report items
        List<LicenseVolumeReportItem> volumerReportItems = licenseVolumeReportItemRepository.findByLicenseVolumeReport(
                licenseVolumeReport);

        // Process each item in the data array
        for (CreateLicenseVolumeReportItemDto item : data) {
            // Check if item already exists - maintaining exact logic
            Optional<LicenseVolumeReportItem> alreadyExists = volumerReportItems.stream()
                    .filter(i -> i.getSetupColumnCode().equals(item.getSetupColumnCode()) && i.getSetupFractionCode()
                            .equals(item.getSetupFractionCode()))
                    .findFirst();

            if (alreadyExists.isPresent()) {
                // Update existing items - maintaining exact field names
                licenseVolumeReportItemRepository.updateValueBySetupColumnCodeAndSetupFractionCode(
                        item.getSetupColumnCode(),
                        item.getSetupFractionCode(),
                        item.getValue(),
                        now);
            } else {
                // Create new item - maintaining exact field names
                LicenseVolumeReportItem newItem = new LicenseVolumeReportItem();
                newItem.setLicenseVolumeReport(licenseVolumeReport);
                newItem.setSetupFractionId(item.getSetupFractionId());
                newItem.setSetupFractionCode(item.getSetupFractionCode());
                newItem.setSetupColumnId(item.getSetupColumnId());
                newItem.setSetupColumnCode(item.getSetupColumnCode());
                newItem.setValue(item.getValue());
                newItem.setCreatedAt(now);
                newItem.setUpdatedAt(now);

                licenseVolumeReportItemRepository.save(newItem);
            }
        }

        // Update license volume report status - maintaining exact logic
        if (licenseVolumeReport != null) {
            licenseVolumeReport.setStatus(LicenseVolumeReport.Status.DONE);
            licenseVolumeReportRepository.save(licenseVolumeReport);
            String emailToSend = null;
            if (licenseVolumeReport.getPackagingService() != null && licenseVolumeReport.getPackagingService()
                    .getLicense() != null && licenseVolumeReport.getPackagingService()
                    .getLicense()
                    .getContract() != null && licenseVolumeReport.getPackagingService()
                    .getLicense()
                    .getContract()
                    .getCustomer() != null) {

                emailToSend = licenseVolumeReport.getPackagingService()
                        .getLicense()
                        .getContract()
                        .getCustomer()
                        .getEmail();
            }
            // Send email if emailToSend exists - maintaining exact logic
            if (emailToSend != null) {
                try {
                    // Extract message data - maintaining exact variable names
                    String countryName = licenseVolumeReport.getPackagingService().getLicense().getCountryName();
                    Integer licenseYear = licenseVolumeReport.getPackagingService().getLicense().getYear();

                    // Email parameters would be sent here
                    Map<String, Object> param = Map.of(
                            "transactional_message_id",
                            "17",
                            "identifiers",
                            Map.of("email", emailToSend),
                            "message_data",
                            Map.of("country_name", countryName, "license_year", licenseYear),
                            "to",
                            emailToSend,
                            "from",
                            "Lizenzero <<EMAIL>>",
                            "subject",
                            "Volume change confirmation");

                    AdminInterface.admin("/emails/send-message", param, HttpMethod.POST);
                } catch (Exception error) {
                    log.error("Failed To Send Email", error);
                }
            }
        }
        // Send to CustomerIO
        sendToCustomerIo(volumeReportId);
    }

    /**
     * Update bulk values for license volume report items
     * Equivalent to TypeScript updateBulkValues method
     */
    @Transactional
    public void updateBulkValues(List<UpdateLicenseVolumeReportItemValueDto> updateLicenseVolumeReportItemsDto) {
        Instant now = Instant.now();

        if (updateLicenseVolumeReportItemsDto.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "No items to update");
        }

        // Validate volume_report_item_id - maintaining exact variable name and logic
        if (updateLicenseVolumeReportItemsDto.stream().anyMatch(item -> item.getVolumeReportItemId() == null)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Missing volume_report_item_id in some items");
        }

        // Validate values - maintaining exact logic
        if (updateLicenseVolumeReportItemsDto.stream()
                .anyMatch(item -> item.getValue() == null || item.getValue() < 0)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid value in some items");
        }

        // Update all items in transaction
        for (UpdateLicenseVolumeReportItemValueDto dto : updateLicenseVolumeReportItemsDto) {
            LicenseVolumeReportItem item = licenseVolumeReportItemRepository.findById(dto.getVolumeReportItemId())
                    .orElseThrow(() -> new ResponseStatusException(
                            HttpStatus.NOT_FOUND,
                            "Volume report item not found"));

            item.setValue(dto.getValue());
            item.setUpdatedAt(now);
            licenseVolumeReportItemRepository.save(item);
        }

        // Send to CustomerIO - maintaining exact array access logic
        sendToCustomerIo(updateLicenseVolumeReportItemsDto.getFirst().getLicenseVolumeReportId());
    }

    /**
     * Decline a license volume report item
     * Equivalent to TypeScript decline method
     */
    public LicenseVolumeReportItem decline(Integer id,
                                           DeclineLicenseVolumeReportItemDto data,
                                           AuthenticatedUser user) {
        // Validate ID - maintaining exact logic
        if (id == null || id <= 0) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "License Volume Report ID is invalid");
        }

        // Validate reason_ids - maintaining exact variable name and logic
        if (data.getReasonIds() == null || data.getReasonIds().isEmpty()) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "At least one reason ID is required");
        }

        // Find license volume report item - maintaining exact logic
        LicenseVolumeReportItem licenseVolumeReportItem = licenseVolumeReportItemRepository.findByIdAndDeletedAtIsNull(
                        id)
                .orElseThrow(() -> new ResponseStatusException(
                        HttpStatus.NOT_FOUND,
                        "License Volume Report Item not found"));

        // Validate user permissions
        validatingUserPermissionVolumeReportItem(licenseVolumeReportItem.getId(), user);

        // Update item with decline information - maintaining exact logic
        licenseVolumeReportItem.setUpdatedAt(Instant.now());

        Decline decline = new Decline();
        decline.setTitle(data.getTitle());
        decline.setLicenseVolumeReportItem(licenseVolumeReportItem);

        // Note: The decline relationship would need to be implemented in the entity
        // This would involve creating Decline entity and DeclineReason entities
        // For now, we'll save the basic update
        List<DeclineReason> reasons = data.getReasonIds().stream().map(reasonId -> {
            Reason reason = reasonRepository.findById(reasonId)
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Reason not found"));
            return new DeclineReason(decline, reason);
        }).collect(Collectors.toList());
        decline.setDeclineReasons(reasons);
        licenseVolumeReportItem.getLicenseVolumeReport().setDecline(decline);

        return licenseVolumeReportItemRepository.save(licenseVolumeReportItem);
    }

    /**
     * Validate user permission for volume report item access
     * Equivalent to TypeScript validatingUserPermissionVolumeReportItem method
     */
    public void validatingUserPermissionVolumeReportItem(Integer id,
                                                         AuthenticatedUser user) {
        // Find license volume report with all relations - maintaining exact variable name
        LicenseVolumeReportItem licenseVolumeReport = licenseVolumeReportItemRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(
                        HttpStatus.NOT_FOUND,
                        "License Volume Report not found"));

        // Extract volume_report - maintaining exact variable name
        LicenseVolumeReport volumeReport = licenseVolumeReport.getLicenseVolumeReport();
        if (volumeReport == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Volume Report not found");
        }

        // Extract packaging_service - maintaining exact variable name
        var packagingService = volumeReport.getPackagingService();
        if (packagingService == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Packaging service not found");
        }

        // Extract license - maintaining exact variable name
        var license = packagingService.getLicense();
        if (license == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "License not found");
        }

        // Extract contract - maintaining exact variable name
        var contract = license.getContract();
        if (contract == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Contract not found");
        }

        // Extract customer - maintaining exact variable name
        var customer = contract.getCustomer();
        if (customer == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Customer not found");
        }

        // Check user permissions - maintaining exact logic
        if (user.getRole() == Role.CUSTOMER && !customer.getUserId().equals(Integer.parseInt(user.getId()))) {
            throw new ResponseStatusException(
                    HttpStatus.FORBIDDEN,
                                              "You do not have permission to access this volume report item");
        }
    }
}
