package de.interzero.oneepr.customer.http;

import de.interzero.oneepr.customer.shared.auth.Role;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Map;

/**
 * HTTP Module Service for making external API calls.
 * Converted from TypeScript http.service.ts
 * Maintains exact same structure, variable names, and function names.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class HttpModuleService {

    private final RestTemplate restTemplate;

    @Value("${app.admin-api-url}")
    private String adminApiUrl;

    @Value("${app.payment-api-url}")
    private String paymentApiUrl;

    @Value("${app.crm-api-url}")
    private String crmApiUrl;

    @Value("${app.auth-api-url}")
    private String authApiUrl;

    @Value("${app.system-api-key}")
    private String systemApiKey;

    // Constants for headers - maintaining exact names from TypeScript
    private static final String HEADER_SYSTEM_API_KEY = "X-System-Api-Key";
    private static final String HEADER_USER_ROLE = "X-User-Role";

    /**
     * Interface for parameters - equivalent to TypeScript IParams
     */
    public interface IParams extends Map<String, Object> {
    }

    /**
     * Enum for HTTP methods - equivalent to TypeScript MethodsRequest
     */
    public enum MethodsRequest {
        GET, POST, PUT, DELETE, PATCH
    }

    /**
     * Admin API call method
     * Equivalent to TypeScript admin method
     */
    public ResponseEntity<Object> admin(AdminRequest request) {
        // Validate input - maintaining exact validation logic
        if (request.url == null || request.url.isEmpty() || request.method == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid input");
        }

        try {
            // Build full URL - maintaining exact URL construction
            String fullUrl = adminApiUrl + request.url;

            // Create headers - maintaining exact header names and values
            HttpHeaders headers = new HttpHeaders();
            headers.set(HEADER_SYSTEM_API_KEY, systemApiKey);
            headers.set(HEADER_USER_ROLE, Role.SYSTEM.toString());

            // Create HTTP entity with params and headers
            HttpEntity<Object> entity;

            // Determine if params go in body or as query params - maintaining exact logic
            if ("post".equals(request.method) || "put".equals(request.method) || "patch".equals(request.method)) {
                entity = new HttpEntity<>(request.params, headers);
            } else {
                entity = new HttpEntity<>(headers);
                // For GET/DELETE, params would be added as query parameters
                if (request.params != null && !request.params.isEmpty()) {
                    fullUrl = buildUrlWithParams(fullUrl, request.params);
                }
            }

            // Convert string method to HttpMethod
            HttpMethod httpMethod = HttpMethod.valueOf(request.method.toUpperCase());

            // Make the request
            return restTemplate.exchange(fullUrl, httpMethod, entity, Object.class);

        } catch (RestClientException error) {
            // Maintain exact error handling logic
            log.error("Error in admin API call: ", error);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "External API call failed");
        }
    }

    /**
     * Request class for admin method - maintaining exact parameter structure
     */
    public static class AdminRequest {
        public String url;
        public Map<String, Object> params;
        public String method; // "get" | "post" | "put" | "delete" | "patch"

        public AdminRequest(String url, Map<String, Object> params, String method) {
            this.url = url;
            this.params = params;
            this.method = method;
        }
    }
}
