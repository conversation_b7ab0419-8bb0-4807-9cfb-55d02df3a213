package de.interzero.oneepr.customer.license_third_party_invoice.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.license_third_party_invoice.LicenseThirdPartyInvoice;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * DTO for updating an existing third-party license invoice.
 * All fields are optional. 'license_id' is omitted as it's typically not updatable.
 * Only provided fields will be considered for update.
 *
 * @ts-legacy This DTO is based on NestJS's PartialType(OmitType(CreateLicenseThirdPartyInvoiceDto, ["license_id"])).
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UpdateLicenseThirdPartyInvoiceDto {


    @Schema(
            description = "Optional: The new title of the invoice (e.g., 'Annual License Fee 2025 - Revised').",
            example = "Annual License Fee 2025 - Revised",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("title")
    private String title;


    @Schema(
            description = "Optional: The new status of the invoice.",
            implementation = LicenseThirdPartyInvoice.Status.class,
            example = "PAID",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("status")
    private LicenseThirdPartyInvoice.Status status;

    /**
     * Optional: The new price of the invoice in the smallest currency unit (e.g., cents).
     * If provided, must be a non-negative value.
     */
    @Min(
            value = 0,
            message = "Price cannot be negative if provided."
    )
    @Schema(
            description = "Optional: The new price of the invoice (in cents).",
            example = "10500",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("price")
    private Integer price;


    @Schema(
            description = "Optional: The new issuer of the invoice.",
            implementation = LicenseThirdPartyInvoice.Issuer.class,
            example = "INTERNAL_SYSTEM",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("issuer")
    private LicenseThirdPartyInvoice.Issuer issuer;

    @Schema(
            description = "Optional: The new issued date of the invoice (e.g., '2025-06-05').",
            example = "2025-06-05T10:00:00Z",
            format = "date-time",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("issued_at")
    private String issuedAt;


    @Schema(
            description = "Optional: The new due date of the invoice (e.g., '2025-07-05').",
            example = "2025-07-05T10:00:00Z",
            format = "date-time",

            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("due_date")
    private String dueDate;

    @Schema(
            description = "Optional: The new file ID associated with the invoice document.",
            example = "file_def456",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("file_id")
    private String fileId;
}