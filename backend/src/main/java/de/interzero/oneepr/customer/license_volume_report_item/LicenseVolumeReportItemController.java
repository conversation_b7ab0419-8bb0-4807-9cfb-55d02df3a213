package de.interzero.oneepr.customer.license_volume_report_item;

import de.interzero.oneepr.common.AuthUtil;
import de.interzero.oneepr.customer.license_volume_report_item.dto.CreateLicenseVolumeReportItemDto;
import de.interzero.oneepr.customer.license_volume_report_item.dto.UpdateLicenseVolumeReportItemValueDto;
import de.interzero.oneepr.customer.license_volume_report_item.dto.UpdateLicenseVolumeReportItemDto;
import de.interzero.oneepr.customer.license_volume_report_item.dto.DeclineLicenseVolumeReportItemDto;
import de.interzero.oneepr.customer.entity.LicenseVolumeReportItem;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

import static de.interzero.oneepr.common.string.Api.LICENSE_VOLUME_REPORT_ITEM;

/**
 * Controller for managing license volume report items.
 * Converted from TypeScript license-volume-report-item.controller.ts
 * Maintains exact same structure, variable names, and function names.
 */
@RestController
@RequestMapping(LICENSE_VOLUME_REPORT_ITEM)
@RequiredArgsConstructor
public class LicenseVolumeReportItemController {

    private final LicenseVolumeReportItemService licenseVolumeReportItemService;

    /**
     * Create a new license volume report item
     * Equivalent to TypeScript @Post() create method
     */
    @PostMapping
    public LicenseVolumeReportItem create(@RequestBody CreateLicenseVolumeReportItemDto createLicenseVolumeReportItemDto) {
        return licenseVolumeReportItemService.create(createLicenseVolumeReportItemDto);
    }

    /**
     * Find all license volume report items with optional filtering
     * Equivalent to TypeScript @Get() findAll method
     */
    @GetMapping
    public List<LicenseVolumeReportItem> findAll(@RequestParam(
            value = "license_volume_report_id"
    ) Integer licenseVolumeReportId) {

        // Get authenticated user - equivalent to @User() user: AuthenticatedUser
        AuthenticatedUser user = AuthUtil.getRelevantUserDetails();
        return licenseVolumeReportItemService.findAll(licenseVolumeReportId, user);
    }

    /**
     * Find one license volume report item by ID
     * Equivalent to TypeScript @Get(":id") findOne method
     */
    @GetMapping("{id}")
    public Optional<LicenseVolumeReportItem> findOne(@PathVariable("id") Integer id) {
        // Get authenticated user - equivalent to @User() user: AuthenticatedUser
        AuthenticatedUser user = AuthUtil.getRelevantUserDetails();

        // Convert string to integer - equivalent to +id
        return licenseVolumeReportItemService.findOne(id, user);
    }

    /**
     * Update a license volume report item
     * Equivalent to TypeScript @Put(":id") update method
     */
    @PutMapping("{id}")
    public LicenseVolumeReportItem update(@PathVariable("id") Integer id,
                                          @RequestBody UpdateLicenseVolumeReportItemDto updateLicenseVolumeReportItemDto) {

        // Get authenticated user - equivalent to @User() user: AuthenticatedUser
        AuthenticatedUser user = AuthUtil.getRelevantUserDetails();

        // Convert string to integer - equivalent to +id
        return licenseVolumeReportItemService.update(id, updateLicenseVolumeReportItemDto, user);
    }

    /**
     * Decline a license volume report item
     * Equivalent to TypeScript @Post(":id/decline") decline method
     */
    @PostMapping("{id}/decline")
    @Operation(summary = "Decline a license volume report item")
    public LicenseVolumeReportItem decline(@PathVariable("id") Integer id,
                                           @RequestBody DeclineLicenseVolumeReportItemDto data) {

        // Get authenticated user - equivalent to @User() user: AuthenticatedUser
        AuthenticatedUser user = AuthUtil.getRelevantUserDetails();

        return licenseVolumeReportItemService.decline(id, data, user);
    }

    /**
     * Remove (soft delete) a license volume report item
     * Equivalent to TypeScript @Delete(":id") remove method
     */
    @DeleteMapping("{id}")
    public LicenseVolumeReportItem remove(@PathVariable("id") Integer id) {
        // Get authenticated user - equivalent to @User() user: AuthenticatedUser
        AuthenticatedUser user = AuthUtil.getRelevantUserDetails();

        // Convert string to integer - equivalent to +id
        return licenseVolumeReportItemService.remove(id, user);
    }

    /**
     * Create multiple license volume report items in bulk
     * Equivalent to TypeScript @Post("bulk") createBulk method
     */
    @PostMapping("bulk")
    public void createBulk(@RequestBody List<CreateLicenseVolumeReportItemDto> createLicenseVolumeReportItemDto) {
        // Note: Method name maintained as createMany to match service method
        licenseVolumeReportItemService.createMany(createLicenseVolumeReportItemDto);
    }

    /**
     * Update multiple license volume report item values in bulk
     * Equivalent to TypeScript @Post("bulk-update") updateBulkValues method
     */
    @PostMapping("bulk-update")
    public void updateBulkValues(@RequestBody List<UpdateLicenseVolumeReportItemValueDto> updateLicenseVolumeReportItemsDto) {
        licenseVolumeReportItemService.updateBulkValues(updateLicenseVolumeReportItemsDto);
    }
}
