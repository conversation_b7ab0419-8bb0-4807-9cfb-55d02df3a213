package de.interzero.oneepr.customer.customer_phone.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "Fields required to create a new customer phone")
public class UpdateCustomerPhoneDto {

    @JsonProperty("customer_id")
    @Schema(description = "The customer id")
    private Integer customerId;

    @JsonProperty("phone_number")
    @Schema(description = "the phone number")
    private String phoneNumber;
}
