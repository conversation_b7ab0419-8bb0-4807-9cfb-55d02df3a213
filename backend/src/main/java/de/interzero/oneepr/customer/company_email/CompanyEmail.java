package de.interzero.oneepr.customer.company_email;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.company.Company;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.time.Instant;
import java.time.LocalDate;

/**
 * Entity based on the database table "company_email", which was defined in the Senno prisma model
 */
@Getter
@Setter
@Entity
@Table(
        name = "company_email",
        schema = "public"
)
public class CompanyEmail {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "company_email_id_gen"
    )
    @SequenceGenerator(
            name = "company_email_id_gen",
            sequenceName = "company_email_id_seq",
            allocationSize = 1
    )

    @JsonProperty("id")
    @Column(
            name = "id",
            nullable = false
    )
    private Integer id;

    @JsonProperty("email")
    @NotNull
    @Column(
            name = "email",
            nullable = false
    )
    private String email;

    @JsonProperty("company")
    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "company_id")
    private Company company;

    @Transient
    @JsonProperty("company_id")
    public Integer getCompanyId() {
        return company != null ? company.getId() : null;
    }

    @JsonProperty("created_at")
    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    private Instant createdAt;

    @JsonProperty("updated_at")
    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    private Instant updatedAt;

    @JsonProperty("deleted_at")
    @Column(name = "deleted_at")
    private LocalDate deletedAt;

}