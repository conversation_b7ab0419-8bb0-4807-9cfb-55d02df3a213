package de.interzero.oneepr.customer.customer_commitment.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * Represents an item within a commitment, with the mode fixed to "COMMITMENT".
 *
 * @ts-legacy Corresponds to (Omit<Criteria, "mode"> & { mode: "COMMITMENT" })
 */
@Data
public class Commitment {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("mode")
    private static final Criteria.Mode mode = Criteria.Mode.COMMITMENT;

    @JsonProperty("type")
    private Criteria.Type type;

    @JsonProperty("title")
    private String title;

    @JsonProperty("help_text")
    private String helpText;

    @JsonProperty("input_type")
    private Criteria.InputType inputType;

    @JsonProperty("calculator_type")
    private String calculatorType;

    @JsonProperty("country_id")
    private Integer countryId;

    @JsonProperty("packaging_service_id")
    private Integer packagingServiceId;

    @JsonProperty("required_information_id")
    private Integer requiredInformationId;

    @JsonProperty("created_at")
    private String createdAt;

    @JsonProperty("updated_at")
    private String updatedAt;

    @JsonProperty("deleted_at")
    private String deletedAt;

    @JsonProperty("options")
    private List<Criteria.Option> options;
}