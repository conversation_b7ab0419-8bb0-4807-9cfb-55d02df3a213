package de.interzero.oneepr.customer.customer_commitment.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;

/**
 * DTO for updating an existing customer commitment.
 * All fields are optional, allowing for partial updates.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UpdateCustomerCommitmentDto {

    @Schema(
            description = "The customer email. If provided, it might be used to identify or update the related customer.",
            example = "<EMAIL>",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("customer_email")
    private String customerEmail;

    @Schema(
            description = "The commitment details to update. This should be a JSON object.",
            example = "{\"criteria_1_answer\": \"UPDATED_YES\", \"criteria_2_notes\": \"Some new notes\"}",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("commitment")
    private Map<String, Object> commitment;


    @Schema(
            description = "The service setup details to update. This should be a JSON object.",
            example = "{\"service_tier\": \"PREMIUM\", \"auto_renew\": true}",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("service_setup")
    private Map<String, Object> serviceSetup;
}
