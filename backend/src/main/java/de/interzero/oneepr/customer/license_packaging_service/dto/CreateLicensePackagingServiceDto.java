package de.interzero.oneepr.customer.license_packaging_service.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Create License Packaging Service DTO
 * Converted from TypeScript create-license-packaging-service.dto.ts
 * Maintains exact same structure, variable names, and property names.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CreateLicensePackagingServiceDto {

    /**
     * The license ID
     * Equivalent to TypeScript: license_id: number;
     */
    @NotNull
    @JsonProperty("license_id")
    @Schema(
            description = "The license ID",
            example = "1",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer license_id;

    /**
     * The setup packaging service ID
     * Equivalent to TypeScript: setup_packaging_service_id: number;
     */
    @NotNull
    @JsonProperty("setup_packaging_service_id")
    @Schema(
            description = "The setup packaging service ID",
            example = "1",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer setup_packaging_service_id;

    /**
     * The name of the packaging service
     * Equivalent to TypeScript: name: string;
     */
    @NotNull
    @JsonProperty("name")
    @Schema(
            description = "The name of the packaging service",
            example = "Standard Packaging",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String name;

    /**
     * The description of the packaging service
     * Equivalent to TypeScript: description: string;
     */
    @NotNull
    @JsonProperty("description")
    @Schema(
            description = "The description of the packaging service",
            example = "Basic packaging service with standard features",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String description;
}
