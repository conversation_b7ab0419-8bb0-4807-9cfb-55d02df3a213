package de.interzero.oneepr.customer.customer_invite_token;

import de.interzero.oneepr.common.string.Api;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static de.interzero.oneepr.common.string.Role.*;

/**
 * CustomerInviteTokenController
 * REST controller for managing customer invite tokens.
 * This is currently a shell and will need endpoint methods (e.g., for creating tokens)
 * to be added to expose the functionality of the {@link CustomerInviteTokenService}.
 */
@RestController
@RequestMapping(Api.CUSTOMER_INVITE_TOKEN)
@Tag(
        name = "Customer Invite Token",
        description = "APIs for managing customer invite tokens"
)
@Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER})
@RequiredArgsConstructor
public class CustomerInviteTokenController {

    private final CustomerInviteTokenService customerInviteTokenService;

}
