package de.interzero.oneepr.customer.license_volume_report;

import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for managing {@link LicenseVolumeReport} entities.
 * Converted from TypeScript Prisma queries to maintain exact same functionality.
 */
@Repository
public interface LicenseVolumeReportRepository extends JpaRepository<LicenseVolumeReport, Integer> {

    /**
     * Finds all license volume reports that have not been soft-deleted.
     * Equivalent to TypeScript: prisma.licenseVolumeReport.findMany({ where: { deleted_at: null } })
     *
     * @return A list of active LicenseVolumeReport entities.
     */
    List<LicenseVolumeReport> findAllByDeletedAtIsNull();

    /**
     * Finds a single license volume report by its ID, ensuring it has not been soft-deleted.
     * Equivalent to TypeScript: prisma.licenseVolumeReport.findUnique({ where: { id } })
     *
     * @param id The ID of the license volume report.
     * @return An optional containing the found report.
     */
    Optional<LicenseVolumeReport> findByIdAndDeletedAtIsNull(Integer id);

    /**
     * Finds a license volume report with its packaging service and related entities for permission validation.
     * Equivalent to TypeScript: prisma.licenseVolumeReport.findUnique with nested includes
     *
     * @param id The ID of the license volume report.
     * @return An optional containing the found report with its relations loaded.
     */
    @EntityGraph(attributePaths = {"packagingService.license.contract.customer"})
    Optional<LicenseVolumeReport> findWithPermissionsByIdAndDeletedAtIsNull(Integer id);
}
