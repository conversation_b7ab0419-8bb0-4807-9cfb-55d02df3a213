package de.interzero.oneepr.customer.recommend_country;

import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import de.interzero.oneepr.customer.recommend_country.dto.CreateRecommendCountryDto;
import de.interzero.oneepr.customer.recommend_country.dto.OriginalCountDto;
import de.interzero.oneepr.customer.recommend_country.dto.TargetCountFormatDto;
import lombok.*;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

/**
 * RecommendCountryService
 * Service layer for managing country recommendations.
 */
@Service
@RequiredArgsConstructor
public class RecommendCountryService {

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    // This inner DTO directly models the inline object used in the original TS axios call.
    public static class CountryValidationRequest {
        private String country;
    }

    private final RecommendCountryRepository recommendCountryRepository;

    private final CustomerRepository customerRepository;

    private final RestTemplate restTemplate;

    // URL is now hardcoded as per original TS axios call
    private static final String COUNTRIES_NOW_API_URL = "https://countriesnow.space/api/v0.1/countries/positions";

    /**
     * Creates a new recommended country record after validating the country name
     * against an external service and checking customer existence if a customer ID is provided.
     *
     * @param data DTO containing the recommended country name and optional customer ID.
     * @return The created {@link RecommendedCountry} entity.
     * @throws ResponseStatusException if country validation fails, customer not found, or database error occurs.
     */
    @Transactional
    public RecommendedCountry create(CreateRecommendCountryDto data) {
        try {
            CountryValidationRequest request = new CountryValidationRequest();
            request.setCountry(data.getRecommendedCountryName());
            restTemplate.postForEntity(COUNTRIES_NOW_API_URL, request, Object.class);
        } catch (HttpClientErrorException axiosError) {
            if (axiosError.getStatusCode().is4xxClientError() || axiosError.getStatusCode().is5xxServerError()) {
                throw new ResponseStatusException(
                        HttpStatus.NOT_FOUND,
                                                  "Country with name " + data.getRecommendedCountryName() + " does not exist (validated externally).");
            }
            throw new ResponseStatusException(
                    HttpStatus.INTERNAL_SERVER_ERROR,
                                              "Error validating country: " + axiosError.getMessage(),
                                              axiosError);
        } catch (Exception e) {
            throw new ResponseStatusException(
                    HttpStatus.INTERNAL_SERVER_ERROR,
                                              "Error during country validation: " + e.getMessage(),
                                              e);
        }

        try {
            Customer customer = null;
            if (data.getCustomerId() != null) {
                customer = customerRepository.findById(data.getCustomerId())
                        .orElseThrow(() -> new ResponseStatusException(
                                HttpStatus.NOT_FOUND,
                                "Customer with ID " + data.getCustomerId() + " does not exist."));
            }

            RecommendedCountry recommendedCountry = new RecommendedCountry();
            recommendedCountry.setCustomer(customer); // Will be null if customerId was null or customer not found (though latter throws)
            recommendedCountry.setName(data.getRecommendedCountryName());

            Instant now = Instant.now();
            recommendedCountry.setCreatedAt(now);
            recommendedCountry.setUpdatedAt(now);

            return recommendCountryRepository.save(recommendedCountry);
        } catch (ResponseStatusException rse) { // Re-throw ResponseStatusException from customer check
            throw rse;
        } catch (Exception dbError) {
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Internal server error", dbError);
        }
    }

    /**
     * Counts recommendations grouped by country name and formats the output
     * to match the specific JSON structure: [{ "_count": { "name": 1 }, "name": "Germany" }].
     *
     * @return A list of {@link TargetCountFormatDto} objects, ready for serialization.
     * The original TypeScript code used a Prisma groupBy which has a similar nested _count structure.
     * This Java implementation achieves the desired JSON output structure through specific DTO mapping.
     */
    @Transactional(readOnly = true)
    public List<TargetCountFormatDto> countRecommendations() {
        // Fetch the raw counts from the repository.
        // returns List<OriginalCountDto> where OriginalCountDto has getName() and getCount().
        List<OriginalCountDto> rawCounts = recommendCountryRepository.countRecommendationsGroupByName();

        // Transform the raw counts into the target DTO format.
        if (rawCounts == null) {
            return List.of();
        }

        return rawCounts.stream()
                .map(rawCount -> new TargetCountFormatDto(rawCount.getName(), rawCount.getCount()))
                .toList();
    }

    /**
     * Deletes a RecommendedCountry by its ID.
     *
     * @param id The ID of the RecommendedCountry to delete.
     * @return The RecommendedCountry entity that was deleted.
     * @throws ResponseStatusException with HttpStatus.NOT_FOUND if no RecommendedCountry with the given ID is found.
     */
    @Transactional
    public RecommendedCountry remove(Integer id) {
        Optional<RecommendedCountry> countryOptional = recommendCountryRepository.findById(id);

        if (countryOptional.isPresent()) {
            RecommendedCountry countryToDelete = countryOptional.get();
            recommendCountryRepository.delete(countryToDelete);
            return countryToDelete;
        } else {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "RecommendedCountry not found with id: " + id);
        }
    }

    /**
     * Retrieves all recommended countries for a specific customer ID.
     *
     * @param customerId The ID of the customer.
     * @return A list of {@link RecommendedCountry} entities associated with the customer.
     */
    @Transactional(readOnly = true)
    public List<RecommendedCountry> getByCustomer(Integer customerId) {
        if (customerId == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Customer ID cannot be null.");
        }
        return recommendCountryRepository.findByCustomer_Id(customerId);
    }

    /**
     * Retrieves all recommended country records.
     *
     * @return A list of all {@link RecommendedCountry} entities.
     */
    @Transactional(readOnly = true)
    public List<RecommendedCountry> getAll() {
        return recommendCountryRepository.findAll();
    }
}
