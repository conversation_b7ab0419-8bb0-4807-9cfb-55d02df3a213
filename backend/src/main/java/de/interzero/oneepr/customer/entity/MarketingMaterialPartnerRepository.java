package de.interzero.oneepr.customer.entity;

import de.interzero.oneepr.customer.market_material.MarketingMaterial;
import de.interzero.oneepr.customer.partner.Partner;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * Repository interface for managing associations between {@link MarketingMaterial} and {@link Partner}.
 */
@Repository
public interface MarketingMaterialPartnerRepository extends JpaRepository<MarketingMaterialPartner, Long> {

    /**
     * Deletes all {@link MarketingMaterialPartner} records associated with the given partner ID.
     *
     * @param partnerId the ID of the partner whose marketing material associations should be deleted
     */
    void deleteAllByPartner_Id(Integer partnerId);

}
