package de.interzero.oneepr.customer.recommend_country.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * The original TypeScript code used a Prisma groupBy which has a similar nested _count structure.
 * This Java implementation achieves the desired JSON output structure through specific DTO mapping.
 */
@Getter
@Setter
@NoArgsConstructor
public class OriginalCountDto {

    private String name;

    private long count;

    public OriginalCountDto(String name,
                            Long count) {
        this.name = name;
        this.count = (count != null) ? count : 0L;
    }
}