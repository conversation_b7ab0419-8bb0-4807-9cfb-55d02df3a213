package de.interzero.oneepr.customer.contract;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.action_guide.ActionGuide;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.file.File;
import de.interzero.oneepr.customer.license.License;
import de.interzero.oneepr.customer.license_required_information.LicenseRequiredInformation;
import de.interzero.oneepr.customer.termination.Termination;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@Entity
@Table(
        name = "contract",
        schema = "public"
)
public class Contract {

    @JsonProperty("id")
    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "contract_id_gen"
    )
    @SequenceGenerator(
            name = "contract_id_gen",
            sequenceName = "contract_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    private Integer id;

    @JsonIgnore
    @JsonProperty("customer")
    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "customer_id",
            nullable = false
    )
    private Customer customer;

    @JsonProperty("title")
    @NotNull
    @Column(
            name = "title",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String title;

    @JsonProperty("start_date")
    @NotNull
    @Column(
            name = "start_date",
            nullable = false
    )
    private Instant startDate;

    @JsonIgnore
    @JsonProperty("termination")
    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "termination_id")
    private Termination termination;

    @JsonProperty("created_at")
    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    private Instant createdAt;

    @Column(name = "deleted_at")
    private LocalDate deletedAt;

    @JsonProperty("updated_at")
    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    private Instant updatedAt;

    @JsonProperty("end_date")
    @NotNull
    @Column(
            name = "end_date",
            nullable = false
    )
    private Instant endDate;

    @JsonProperty("type")
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(name = "type")
    private Type type;

    @JsonProperty("status")
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @ColumnDefault("'ACTIVE'")
    @Column(name = "status")
    private Status status;

    @JsonIgnore
    @JsonProperty("general_informations")
    @OneToMany(
            mappedBy = "contract",
            fetch = FetchType.LAZY,
            cascade = CascadeType.PERSIST
    )
    private List<LicenseRequiredInformation> generalInformations;

    @JsonIgnore
    @JsonProperty("licenses")
    @OneToMany(
            mappedBy = "contract",
            fetch = FetchType.LAZY
    )
    private List<License> licenses;

    @JsonIgnore
    @JsonProperty("files")
    @OneToMany(
            mappedBy = "contract",
            fetch = FetchType.LAZY
    )
    private List<File> files;

    @JsonIgnore
    @JsonProperty("action_guides")
    @OneToMany(
            mappedBy = "contract",
            fetch = FetchType.LAZY
    )
    private List<ActionGuide> actionGuides;

    public enum Type {
        EU_LICENSE,
        DIRECT_LICENSE,
        ACTION_GUIDE
    }

    public enum Status {
        ACTIVE,
        TERMINATION_PROCESS,
        TERMINATED
    }
}