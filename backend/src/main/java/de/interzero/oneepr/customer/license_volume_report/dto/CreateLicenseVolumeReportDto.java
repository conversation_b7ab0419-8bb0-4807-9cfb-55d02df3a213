package de.interzero.oneepr.customer.license_volume_report.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.license_volume_report.LicenseVolumeReport;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;

/**
 * Create License Volume Report DTO
 * Converted from TypeScript create-license-volume-report.dto.ts
 * Maintains exact same structure, variable names, and property names.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CreateLicenseVolumeReportDto {

    /**
     * @ts-legacy This field is not found to be used on the backend
     * The setup report set fraction ID
     * Equivalent to TypeScript: setup_report_set_fraction_id: number;
     */
    @JsonProperty("setup_report_set_fraction_id")
    @Schema(
            description = "The setup report set fraction ID",
            example = "1",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer setupReportSetFractionId;

    /**
     * The license packaging service ID
     * Equivalent to TypeScript: license_packaging_service_id: number;
     */
    @JsonProperty("license_packaging_service_id")
    @Schema(
            description = "The license packaging service ID",
            example = "1",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer licensePackagingServiceId;

    /**
     * The status of the license volume report
     * Equivalent to TypeScript: status: LicenseVolumeReportStatus;
     */
    @JsonProperty("status")
    @Schema(
            description = "The status of the license volume report",
            example = "DECLINED",
            implementation = LicenseVolumeReport.Status.class,
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private LicenseVolumeReport.Status status;

    /**
     * The year of the license volume report
     * Equivalent to TypeScript: year: number;
     */
    @JsonProperty("year")
    @Schema(
            description = "The year of the license volume report",
            example = "2024",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer year;

    /**
     * The interval of the license volume report
     * Equivalent to TypeScript: interval: string;
     */
    @JsonProperty("interval")
    @Schema(
            description = "The interval of the license volume report",
            example = "Q1, Q2, JANUARY, FEBRUARY, 2023, 2024",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String interval;

    /**
     * The report table of the license volume report
     * Equivalent to TypeScript: report_table: Record<string, any>;
     */
    @JsonProperty("report_table")
    @Schema(
            description = "The report table of the license volume report",
            example = "{}",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Map<String, Object> reportTable;
}
