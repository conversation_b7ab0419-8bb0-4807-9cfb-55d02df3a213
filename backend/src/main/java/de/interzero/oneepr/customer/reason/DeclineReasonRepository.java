package de.interzero.oneepr.customer.reason;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface DeclineReasonRepository extends JpaRepository<DeclineReason, Integer> {

    /**
     * Deletes all DeclineReason entities associated with a specific decline ID in a single, efficient query.
     * This is used to clean up child records before deleting the parent Decline record.
     *
     * @param declineId The ID of the parent {@link de.interzero.oneepr.customer.decline.Decline} entity.
     */
    @Modifying
    @Query("DELETE FROM DeclineReason dr WHERE dr.decline.id = :declineId")
    void deleteAllByDeclineId(Integer declineId);
}
