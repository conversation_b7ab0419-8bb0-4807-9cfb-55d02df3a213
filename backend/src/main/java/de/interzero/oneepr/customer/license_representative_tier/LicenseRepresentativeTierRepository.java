package de.interzero.oneepr.customer.license_representative_tier;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for managing {@link LicenseRepresentativeTier} entities.
 */
@Repository
public interface LicenseRepresentativeTierRepository extends JpaRepository<LicenseRepresentativeTier, Integer> {

    /**
     * Finds all tiers that have not been soft-deleted, ordered by ID descending.
     *
     * @return A list of license representative tiers.
     */
    List<LicenseRepresentativeTier> findAllByDeletedAtIsNullOrderByIdDesc();

    /**
     * Finds all tiers for a specific license that have not been soft-deleted, ordered by ID descending.
     *
     * @param licenseId The ID of the license to filter by.
     * @return A list of representative tiers for the given license.
     */
    List<LicenseRepresentativeTier> findAllByLicense_IdAndDeletedAtIsNullOrderByIdDesc(Integer licenseId);

    /**
     * Finds a single tier by its ID, ensuring it has not been soft-deleted.
     *
     * @param id The ID of the representative tier.
     * @return An optional containing the found tier.
     */
    @NonNull
    Optional<LicenseRepresentativeTier> findByIdAndDeletedAtIsNull(@NonNull Integer id);

    /**
     * Finds a single tier by its ID, ensuring it has not been soft-deleted.
     * It uses an entity graph to eagerly fetch the full chain of related entities
     * (license -> contract -> customer) required for permission validation.
     *
     * @param id The ID of the representative tier.
     * @return An optional containing the found tier with its relations loaded.
     */
    Optional<LicenseRepresentativeTier> findWithRelationsByIdAndDeletedAtIsNull(Integer id);
}
