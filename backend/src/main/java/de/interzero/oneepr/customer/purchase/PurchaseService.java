package de.interzero.oneepr.customer.purchase;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.common.MessageResponse;
import de.interzero.oneepr.common.util.NumberConverter;
import de.interzero.oneepr.customer.certificate.Certificate;
import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.contract.ContractRepository;
import de.interzero.oneepr.customer.contract.ContractService;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import de.interzero.oneepr.customer.customer_commitment.CustomerCommitment;
import de.interzero.oneepr.customer.customer_commitment.dto.CustomerServiceSetup;
import de.interzero.oneepr.customer.customer_commitment.dto.OtherCost;
import de.interzero.oneepr.customer.customer_commitment.dto.RequiredInformation;
import de.interzero.oneepr.customer.entity.LicensePriceList;
import de.interzero.oneepr.customer.entity.LicenseReportSet;
import de.interzero.oneepr.customer.entity.LicenseReportSetFrequency;
import de.interzero.oneepr.customer.http.CrmInterface;
import de.interzero.oneepr.customer.license.License;
import de.interzero.oneepr.customer.license.LicenseRepository;
import de.interzero.oneepr.customer.license_other_cost.LicenseOtherCost;
import de.interzero.oneepr.customer.license_packaging_service.LicensePackagingService;
import de.interzero.oneepr.customer.license_representative_tier.LicenseRepresentativeTier;
import de.interzero.oneepr.customer.license_required_information.LicenseRequiredInformation;
import de.interzero.oneepr.customer.license_volume_report.LicenseVolumeReport;
import de.interzero.oneepr.customer.purchase.dto.PurchaseDto;
import de.interzero.oneepr.customer.service_next_step.ServiceNextStep;
import jakarta.persistence.EntityNotFoundException;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/**
 * Service for managing purchases
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PurchaseService {

    private final ShoppingCartRepository shoppingCartRepository;

    private final CustomerRepository customerRepository;

    private final ContractRepository contractRepository;

    private final LicenseRepository licenseRepository;

    private final ContractService contractService;

    private final CrmInterface crmInterface;

    private final ObjectMapper objectMapper;

    /**
     * Get the current month as a string
     *
     * @return the current month as a string
     * @ts-legacy There's definitely a better way to do this than hardcoding the month names.
     */
    private static String getMonthName() {
        final LocalDate date = LocalDate.now();
        List<String> months = List.of(
                "JANUARY",
                "FEBRUARY",
                "MARCH",
                "APRIL",
                "MAY",
                "JUNE",
                "JULY",
                "AUGUST",
                "SEPTEMBER",
                "OCTOBER",
                "NOVEMBER",
                "DECEMBER");

        return months.get(date.getMonthValue() - 1);
    }

    /**
     * Get the initial volume report interval based on the rhythm and license year
     *
     * @param rhythm      the rhythm of the license report set frequency
     * @param licenseYear the license year
     * @return the initial volume report interval as a string
     * @ts-legacy Huge if clauses that should be broken up into smaller methods.
     * @ts-legacy euLicenseContract finds first result without confirming that only one result exists.
     * @ts-legacy Bad Optional<T> patterns.
     */
    private static String getInitialVolumeReportInterval(@NonNull LicenseReportSetFrequency.Rhythm rhythm,
                                                         @NonNull Integer licenseYear) {
        return switch (rhythm) {
            case QUARTERLY -> "Q1";
            case MONTHLY -> getMonthName();
            default -> String.valueOf(licenseYear);
        };
    }

    /**
     * Create a purchase based on the provided DTO.
     *
     * @param dto the purchase data transfer object containing the shopping cart ID and other necessary information
     * @return a MessageResponse indicating the result of the purchase creation
     */
    @SuppressWarnings({"unchecked", "java:S3776", "java:S1199"})
    // unchecked: needed for JSON object castings
    // java:S3776: original code is structured this way
    // java:S1199: nested code block is for the await Promise.all block in the original code
    @Transactional(rollbackFor = Exception.class)
    public MessageResponse create(@NonNull PurchaseDto dto) {
        LocalDateTime nowDate = LocalDateTime.now();

        final ShoppingCart shoppingCart = shoppingCartRepository.findById(dto.getShoppingCartId())
                .orElseThrow(() -> new EntityNotFoundException("Shopping cart not found"));

        // shopping cart validation
        if ((shoppingCart.getItems().isEmpty())) {
            throw new EntityNotFoundException("Shopping cart is empty");
        }
        if (shoppingCart.getEmail() == null) {
            throw new EntityNotFoundException("Shopping cart is missing customer email");
        }

        // customer validation
        final Customer customer = customerRepository.findByEmail(shoppingCart.getEmail())
                .orElseThrow(() -> new EntityNotFoundException("Customer not found"));

        List<Contract> customerContracts = contractRepository.findByCustomerIdAndDeletedAtIsNull(customer.getId());

        // licenses
        final List<ShoppingCartItem> euLicenses = shoppingCart.getItems()
                .stream()
                .filter(item -> item.getServiceType() == Contract.Type.EU_LICENSE)
                .toList();

        // @ts-legacy This is a huge if statement that should be broken up into smaller methods.
        if (!euLicenses.isEmpty()) {
            // @ts-legacy returns null instead of using proper Optional<T> pattern
            Contract euLicenseContract = customerContracts.stream()
                    .filter(contract -> contract.getType() == Contract.Type.EU_LICENSE)
                    .findFirst()
                    .orElse(null);

            if (euLicenseContract == null) {
                final int biggestYear = euLicenses.stream().mapToInt(ShoppingCartItem::getYear).max().orElseThrow();

                euLicenseContract = new Contract();
                euLicenseContract.setCustomer(customer);
                euLicenseContract.setType(Contract.Type.EU_LICENSE);
                euLicenseContract.setStatus(Contract.Status.ACTIVE);
                euLicenseContract.setTitle("EU License");
                euLicenseContract.setStartDate(LocalDate.of(nowDate.getYear(), 1, 1)
                                                       .atStartOfDay(ZoneId.systemDefault())
                                                       .toInstant());
                // @ts-legacy this doesn't seem right... should be at end of day but source ends up with start of day
                euLicenseContract.setEndDate(LocalDate.of(biggestYear, 12, 31)
                                                     .atStartOfDay(ZoneId.systemDefault())
                                                     .toInstant());
                LicenseRequiredInformation generalInformation = new LicenseRequiredInformation();
                generalInformation.setKind(LicenseRequiredInformation.Kind.GENERAL_INFORMATION);
                generalInformation.setSetupGeneralInformationId(1);
                generalInformation.setType(LicenseRequiredInformation.Type.DOCUMENT);
                generalInformation.setStatus(LicenseRequiredInformation.Status.OPEN);
                generalInformation.setName("Power of Attorney");
                generalInformation.setDescription("Power of Attorney");
                euLicenseContract.setGeneralInformations(List.of(generalInformation));

                contractService.generateContractPdf(euLicenseContract.getId());
            }

            final List<License> currentLicenses = euLicenseContract.getLicenses();

            //@ts-legacy the following block was inside an await Promise.all block. Due to the nature of java
            // we need to do this sequentially. Perhaps it can be refactored later to make some calls async
            {
                final Contract finalEuLicenseContract = euLicenseContract; // used due to lambda finality requirement
                euLicenses.stream()
                        .filter(license -> currentLicenses.stream()
                                .noneMatch(l -> l.getCountryCode().equals(license.getCountryCode()) && l.getYear()
                                        .equals(license.getYear())))
                        .forEach(license -> {
                            List<License> countryLicenses = currentLicenses.stream()
                                    .filter(l -> l.getCountryCode().equals(license.getCountryCode()))
                                    .toList();

                            License firstCountryLicense = countryLicenses.isEmpty() ? null : countryLicenses.getFirst();

                            CustomerCommitment customerCommitment = license.getCustomerCommitment();
                            LicensePriceList priceList;
                            if (firstCountryLicense != null) {
                                priceList = firstCountryLicense.getPriceList().getFirst();
                            } else {
                                // Original code used the "any" keyword to get the license.price_list, so
                                // we need to map the pricelist from the JSON of the license
                                Map<String, Object> priceListJson = license.getPriceList();
                                priceList = objectMapper.convertValue(priceListJson, LicensePriceList.class);
                            }

                            if (customerCommitment == null) {
                                throw new ResponseStatusException(
                                        HttpStatus.BAD_REQUEST,
                                        "Customer commitment not found for " + license.getCountryCode());
                            }

                            Long mondayVolumeReports = crmInterface.volumeReports(
                                    license.getCountryName(),
                                    String.valueOf(customer.getId()),
                                    String.valueOf(license.getYear()));

                            Long mondayRegistrationAndTerminations = crmInterface.registrationsAndTerminations(
                                    license.getCountryName(),
                                    String.valueOf(customer.getId()),
                                    String.valueOf(license.getYear()));

                            final CustomerServiceSetup setup = objectMapper.convertValue(
                                    customerCommitment.getServiceSetup(),
                                    CustomerServiceSetup.class);

                            License licenseCreateData = new License();
                            licenseCreateData.setCountryId(license.getCountryId());
                            licenseCreateData.setCountryCode(license.getCountryCode());
                            licenseCreateData.setCountryName(license.getCountryName());
                            licenseCreateData.setCountryFlag(license.getCountryFlag());
                            licenseCreateData.setRegistrationNumber(UUID.randomUUID()
                                                                            .toString()
                                                                            .split("-")[0]);    //@ts-legacy why split by "-"?
                            licenseCreateData.setYear(license.getYear());
                            licenseCreateData.setStartDate(LocalDate.of(license.getYear(), 1, 1)
                                                                   .atStartOfDay(ZoneId.systemDefault())
                                                                   .toInstant());
                            licenseCreateData.setEndDate(LocalDate.of(
                                            license.getYear(),
                                            12,
                                            31)    // @ts-legacy this should be at end of day but source ends up with start of day
                                                                 .atStartOfDay(ZoneId.systemDefault()).toInstant());
                            licenseCreateData.setRegistrationAndTerminationMondayRef(NumberConverter.longToInteger(
                                    mondayRegistrationAndTerminations));

                            LicensePriceList priceListCreate = new LicensePriceList();
                            priceListCreate.setSetupPriceListId(priceList.getId());
                            priceListCreate.setName(priceList.getName());
                            priceListCreate.setDescription(priceList.getDescription());
                            priceListCreate.setConditionType(priceList.getConditionType());
                            priceListCreate.setConditionTypeValue(priceList.getConditionTypeValue());
                            priceListCreate.setStartDate(priceList.getStartDate());
                            priceListCreate.setEndDate(priceList.getEndDate());
                            priceListCreate.setRegistrationFee(priceList.getRegistrationFee());
                            priceListCreate.setHandlingFee(priceList.getHandlingFee());
                            priceListCreate.setVariableHandlingFee(priceList.getVariableHandlingFee());
                            priceListCreate.setBasicPrice(null);
                            priceListCreate.setMinimumPrice(null);
                            licenseCreateData.setPriceList(List.of(priceListCreate));

                            licenseCreateData.setContract(contractRepository.getReferenceById(finalEuLicenseContract.getId()));

                            List<LicensePackagingService> packagingServices = new ArrayList<>();
                            if (license.getPackagingServices() != null) {
                                // get the packaging services from the license JSON data
                                Object jsonData = license.getPackagingServices();
                                final List<Map<String, Object>> licensePackagingServices;
                                if (jsonData instanceof List) {
                                    licensePackagingServices = ((List<Object>) jsonData).stream()
                                            .filter(Map.class::isInstance)
                                            .map(item -> (Map<String, Object>) item)
                                            .toList();
                                } else {
                                    throw new IllegalArgumentException("Invalid packaging services data format");
                                }
                                // loop through each service as the map does in the original code
                                for (Map<String, Object> packagingServiceFromLicense : licensePackagingServices) {
                                    Integer packagingServiceId = (Integer) packagingServiceFromLicense.get("id");  // replacement for packagingService.id
                                    CustomerServiceSetup.PackagingServiceExtended setupPackagingService = setup.getPackagingServices()
                                            .stream()
                                            .filter(service -> service.getId().equals(packagingServiceId))
                                            .findFirst()
                                            .orElseThrow(() -> new ResponseStatusException(
                                                    HttpStatus.BAD_REQUEST, String.format(
                                                    "Packaging service not found for ID: %s. Packaging service id: %s",
                                                    license.getCountryCode(),
                                                    packagingServiceId)));

                                    final Map<String, Object> reportTable;
                                    // @ts-legacy typo in "PLATAFORM"?
                                    if (!setupPackagingService.getReportSet().getMode().equals("ON_PLATAFORM")) {
                                        reportTable = null;
                                    } else {
                                        reportTable = new HashMap<>();
                                        reportTable.put(
                                                "fractions",
                                                setupPackagingService.getReportSet().getFractions());
                                        reportTable.put("columns", setupPackagingService.getReportSet().getColumns());
                                    }

                                    LicensePackagingService packagingService = new LicensePackagingService();
                                    packagingService.setSetupPackagingServiceId(setupPackagingService.getId());
                                    packagingService.setName(setupPackagingService.getName());
                                    packagingService.setDescription(setupPackagingService.getDescription());
                                    LicenseReportSet reportSet = new LicenseReportSet();
                                    reportSet.setSetupReportSetId(setupPackagingService.getReportSet().getId());
                                    packagingService.setReportSet(reportSet);
                                    LicenseReportSetFrequency reportSetFrequency = new LicenseReportSetFrequency();
                                    reportSetFrequency.setSetupReportSetFrequencyId(setupPackagingService.getReportSetFrequency()
                                                                                            .getId());
                                    reportSetFrequency.setRhythm(setupPackagingService.getReportSetFrequency()
                                                                         .getRhythm());
                                    reportSetFrequency.setFrequency(setupPackagingService.getReportSetFrequency()
                                                                            .getFrequency());
                                    packagingService.setReportSetFrequency(reportSetFrequency);
                                    LicenseVolumeReport volumeReport = new LicenseVolumeReport();
                                    volumeReport.setYear(license.getYear());
                                    volumeReport.setInterval(getInitialVolumeReportInterval(
                                            setupPackagingService.getReportSetFrequency()
                                                    .getRhythm(),
                                            license.getYear()));
                                    volumeReport.setStatus(LicenseVolumeReport.Status.OPEN);
                                    volumeReport.setVolumeReportMondayRef(NumberConverter.longToInteger(
                                            mondayVolumeReports));
                                    volumeReport.setReportTable(reportTable);
                                    packagingService.setVolumeReports(List.of(volumeReport));
                                    packagingServices.add(packagingService);
                                }
                                licenseCreateData.setPackagingServices(packagingServices);
                            }

                            if (firstCountryLicense != null) {
                                // required_informations
                                List<LicenseRequiredInformation> requiredInformations = new ArrayList<>();
                                for (RequiredInformation requiredInformation : setup.getRequiredInformations()) {
                                    LicenseRequiredInformation licenseRequiredInformation = new LicenseRequiredInformation();
                                    licenseRequiredInformation.setSetupRequiredInformationId(requiredInformation.getId());
                                    licenseRequiredInformation.setName(requiredInformation.getName());
                                    licenseRequiredInformation.setType(LicenseRequiredInformation.Type.valueOf(
                                            requiredInformation.getType()));
                                    licenseRequiredInformation.setDescription(requiredInformation.getDescription());
                                    licenseRequiredInformation.setFileId(requiredInformation.getFileId());
                                    requiredInformations.add(licenseRequiredInformation);
                                }
                                licenseCreateData.setRequiredInformations(requiredInformations);
                                // certificates
                                Certificate certificate = new Certificate();
                                certificate.setName(license.getCountryName() + "Certificate");
                                Certificate specificCertificate = new Certificate();
                                specificCertificate.setName(license.getCountryName() + "specific certificate");
                                licenseCreateData.setCertificates(List.of(certificate, specificCertificate));
                                // representative_tiers
                                if (setup.getRepresentativeTier() != null) {
                                    LicenseRepresentativeTier representativeTier = new LicenseRepresentativeTier();
                                    representativeTier.setSetupRepresentativeTierId(setup.getRepresentativeTier()
                                                                                            .getId());
                                    representativeTier.setName(setup.getRepresentativeTier().getName());
                                    representativeTier.setPrice(setup.getRepresentativeTier().getPrice());
                                    licenseCreateData.setRepresentativeTiers(List.of(representativeTier));
                                }
                                // other_costs
                                List<LicenseOtherCost> licenseOtherCosts = new ArrayList<>();
                                for (OtherCost otherCost : setup.getOtherCosts()) {
                                    LicenseOtherCost licenseOtherCost = new LicenseOtherCost();
                                    licenseOtherCost.setSetupOtherCostId(otherCost.getId());
                                    licenseOtherCost.setName(otherCost.getName());
                                    licenseOtherCost.setPrice(otherCost.getPrice());
                                    licenseOtherCosts.add(licenseOtherCost);
                                }
                                licenseCreateData.setOtherCosts(licenseOtherCosts);
                                // next_steps
                                Instant nowDateInstant = nowDate.atZone(ZoneId.systemDefault()).toInstant();
                                Instant deadlineDate = LocalDate.of(license.getYear(), 12, 31)
                                        .atStartOfDay()
                                        .atZone(ZoneId.systemDefault())
                                        .toInstant();

                                ServiceNextStep stepAccount = new ServiceNextStep();
                                stepAccount.setTitle("Create an account");
                                stepAccount.setAvailableDate(nowDateInstant);
                                stepAccount.setDeadlineDate(deadlineDate);

                                ServiceNextStep stepInformation = new ServiceNextStep();
                                stepInformation.setTitle("Enter required information");
                                stepInformation.setAvailableDate(nowDateInstant);
                                stepInformation.setDeadlineDate(deadlineDate);

                                ServiceNextStep stepRegister = new ServiceNextStep();
                                stepRegister.setTitle("Register with CONAI");
                                stepRegister.setAvailableDate(nowDateInstant);
                                stepRegister.setDeadlineDate(deadlineDate);

                                ServiceNextStep stepQuantity = new ServiceNextStep();
                                stepQuantity.setTitle("Quantity declaration");
                                stepQuantity.setAvailableDate(nowDateInstant);
                                stepQuantity.setDeadlineDate(deadlineDate);

                                licenseCreateData.setNextSteps(List.of(
                                        stepAccount,
                                        stepInformation,
                                        stepRegister,
                                        stepQuantity));

                                // create license
                                licenseRepository.save(licenseCreateData);
                            }
                        });
            }   // end of the await Promise.all block
            contractService.generateServiceOverviewPdf(euLicenseContract.getId());
        }

        // final return
        return new MessageResponse("Services added to contract successfully");
    }
}
