package de.interzero.oneepr.customer.market_material;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.time.LocalDate;

@Getter
@Setter
@Entity
@Table(
        name = "marketing_material",
        schema = "public"
)
public class MarketingMaterial {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "marketing_material_id_gen"
    )
    @SequenceGenerator(
            name = "marketing_material_id_gen",
            sequenceName = "marketing_material_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    private Integer id;

    @NotNull
    @Column(
            name = "name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String name;

    @Column(name = "start_date")
    private LocalDate startDate;

    @Column(name = "end_date")
    private LocalDate endDate;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    private Instant updatedAt;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    private Instant createdAt;

    @Column(name = "deleted_at")
    private LocalDate deletedAt;

/*
 TODO [Reverse Engineering] create field to map the 'category' column
 Available actions: Define target Java type | Uncomment as is | Remove column mapping
    @Column(
    name = "category",
    columnDefinition = "enum_marketing_material_category not null"
    )
    private Object category;
*/
/*
 TODO [Reverse Engineering] create field to map the 'partner_restriction' column
 Available actions: Define target Java type | Uncomment as is | Remove column mapping
    @Column(
    name = "partner_restriction",
    columnDefinition = "enum_marketing_material_partner_restriction not null"
    )
    private Object partnerRestriction;
*/
}