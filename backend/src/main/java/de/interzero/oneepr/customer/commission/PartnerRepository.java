package de.interzero.oneepr.customer.commission;

import de.interzero.oneepr.customer.entity.Partner;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface PartnerRepository extends JpaRepository<Partner, Integer> {

    Optional<Partner> findFirstByUserIdAndDeletedAtIsNull(Integer userId);
}
