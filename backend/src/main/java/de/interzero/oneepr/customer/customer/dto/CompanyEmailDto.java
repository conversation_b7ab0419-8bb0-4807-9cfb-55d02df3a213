package de.interzero.oneepr.customer.customer.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * Data Transfer Object representing an email associated with a company.
 * <p>
 * This DTO is a direct translation of the {@code CompanyEmail} model included in
 * the {@code companies} relation of the {@code customer.service.ts#details} method.
 */
@Data
@NoArgsConstructor
public class CompanyEmailDto {

    @Schema(description = "Unique identifier of the company email record.")
    @JsonProperty("id")
    private Integer id;

    @Schema(description = "The email address.")
    @JsonProperty("email")
    private String email;

    @Schema(description = "Timestamp of when the record was created.")
    @JsonProperty("created_at")
    private Instant createdAt;

    @Schema(description = "Timestamp of when the record was last updated.")
    @JsonProperty("updated_at")
    private Instant updatedAt;
}