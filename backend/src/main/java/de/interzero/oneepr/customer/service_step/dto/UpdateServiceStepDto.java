package de.interzero.oneepr.customer.service_step.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;


/**
 * UpdateServiceStepDto
 */
@Getter
@Setter
public class UpdateServiceStepDto {

    @JsonProperty("type")
    @Schema(
            description = "The service type",
            example = "LICENSE",
            requiredMode = Schema.RequiredMode.REQUIRED,
            allowableValues = {"LICENSE", "ACTION_GUIDE"}
    )
    private String type;

    @JsonProperty("countryCode")
    @Schema(
            description = "The service country code",
            example = "FR",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private String countryCode;

    @JsonProperty("title")
    @Schema(
            description = "The value of the volume or title of the service step",
            example = "Complete registration",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String title;

    @JsonProperty("availableAt")
    @Schema(
            description = "The package identifier for the volume or availability date",
            example = "2024-01-01T00:00:00Z",
            type = "string",
            format = "date-time",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String availableAt;

    @JsonProperty("deadlineAt")
    @Schema(
            description = "The period of the volume or deadline date",
            example = "2024-01-01T00:00:00Z",
            type = "string",
            format = "date-time",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String deadlineAt;

    @JsonProperty("isActive")
    @Schema(
            description = "The active status of service step (appears or not to users)",
            example = "true",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Boolean isActive;
}