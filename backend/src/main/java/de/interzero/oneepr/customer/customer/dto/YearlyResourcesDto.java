package de.interzero.oneepr.customer.customer.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * Represents the aggregated resource savings data for a single year.
 * <p>
 * This DTO is a direct translation of the object created for each year within the
 * {@code resources.years} map in the {@code getDirectLicenseResources} method
 * from {@code customer.service.ts}.
 */
@Data
public class YearlyResourcesDto {

    @JsonProperty("year")
    private Integer year;

    @JsonProperty("reported_weight")
    private BigDecimal reportedWeight = BigDecimal.ZERO;

    @JsonProperty("rosources_saved")
    private BigDecimal resourcesSaved = BigDecimal.ZERO;

    @JsonProperty("greenhouse_gases")
    private BigDecimal greenhouseGases = BigDecimal.ZERO;

    @JsonProperty("fractions")
    private Map<String, FractionResourcesDto> fractions = new HashMap<>();
}