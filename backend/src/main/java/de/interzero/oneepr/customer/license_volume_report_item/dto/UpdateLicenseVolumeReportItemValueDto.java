package de.interzero.oneepr.customer.license_volume_report_item.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Data Transfer Object for updating a license volume report item value.
 * Converted from TypeScript update-license-volume-report-item-value.dto.ts
 * Maintains exact same structure, variable names, and properties.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UpdateLicenseVolumeReportItemValueDto {

    @NotNull
    @JsonProperty("license_volume_report_id")
    @Schema(
            description = "The license volume report ID",
            example = "1"
    )
    private Integer licenseVolumeReportId;

    @NotNull
    @JsonProperty("volume_report_item_id")
    @Schema(
            description = "The volume report item ID",
            example = "1"
    )
    private Integer volumeReportItemId;

    @NotNull
    @JsonProperty("value")
    @Schema(
            description = "The value to update",
            example = "100"
    )
    private Integer value;
}
