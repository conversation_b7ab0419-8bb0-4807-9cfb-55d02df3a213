package de.interzero.oneepr.customer.company;

import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.entity.CompanyAddress;
import de.interzero.oneepr.customer.entity.CompanyContact;
import de.interzero.oneepr.customer.partner.Partner;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.time.Instant;
import java.time.LocalDate;

@Getter
@Setter
@Entity
@Table(
        name = "company",
        schema = "public"
)
public class Company {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "company_id_gen"
    )
    @SequenceGenerator(
            name = "company_id_gen",
            sequenceName = "company_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    private Integer id;

    @NotNull
    @Column(
            name = "name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String name;

    @Column(
            name = "description",
            length = Integer.MAX_VALUE
    )
    private String description;

    @Column(
            name = "vat",
            length = Integer.MAX_VALUE
    )
    private String vat;

    @Column(
            name = "tin",
            length = Integer.MAX_VALUE
    )
    private String tin;

    @Column(
            name = "lucid",
            length = Integer.MAX_VALUE
    )
    private String lucid;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "customer_id")
    private Customer customer;

    @Column(name = "starting")
    private Instant starting;

    @Column(
            name = "website",
            length = Integer.MAX_VALUE
    )
    private String website;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "partner_id")
    private Partner partner;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "address_id")
    private CompanyAddress address;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    private Instant updatedAt;

    @Column(name = "deleted_at")
    private LocalDate deletedAt;

    @Column(
            name = "industry_sector",
            length = Integer.MAX_VALUE
    )
    private String industrySector;

    @Column(
            name = "owner_name",
            length = Integer.MAX_VALUE
    )
    private String ownerName;

    @OneToOne(
            mappedBy = "company",
            cascade = CascadeType.ALL
    )
    private CompanyContact contacts;

}