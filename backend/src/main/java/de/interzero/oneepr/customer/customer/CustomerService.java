package de.interzero.oneepr.customer.customer;


import de.interzero.oneepr.action_guide.ActionGuide;
import de.interzero.oneepr.customer.certificate.Certificate;
import de.interzero.oneepr.customer.company.Company;
import de.interzero.oneepr.customer.company.CompanyService;
import de.interzero.oneepr.customer.consent.Consent;
import de.interzero.oneepr.customer.consent.ConsentRepository;
import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.contract.ContractRepository;
import de.interzero.oneepr.customer.coupon.Coupon;
import de.interzero.oneepr.customer.coupon.CouponService;
import de.interzero.oneepr.customer.coupon.dto.CreateCouponDto;
import de.interzero.oneepr.customer.customer.dto.*;
import de.interzero.oneepr.customer.customer_consent.CustomerConsent;
import de.interzero.oneepr.customer.customer_consent.CustomerConsentRepository;
import de.interzero.oneepr.customer.customer_io.CustomerIoService;
import de.interzero.oneepr.customer.customer_phone.CustomerPhone;
import de.interzero.oneepr.customer.customer_phone.CustomerPhoneRepository;
import de.interzero.oneepr.customer.entity.CustomerTutorial;
import de.interzero.oneepr.customer.entity.LicenseVolumeReportItem;
import de.interzero.oneepr.customer.http.AuthInterface;
import de.interzero.oneepr.customer.license.License;
import de.interzero.oneepr.customer.license_packaging_service.LicensePackagingService;
import de.interzero.oneepr.customer.license_required_information.LicenseRequiredInformation;
import de.interzero.oneepr.customer.license_volume_report.LicenseVolumeReport;
import de.interzero.oneepr.customer.purchase.ShoppingCartRepository;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import de.interzero.oneepr.customer.shared.auth.Role;
import de.interzero.oneepr.customer.shared.consts.GermanyFractions;
import de.interzero.oneepr.customer.termination.Termination;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flywaydb.core.internal.util.StringUtils;
import org.modelmapper.ModelMapper;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpMethod;
import de.interzero.oneepr.admin.mail.EmailMessage;
import de.interzero.oneepr.admin.mail.EmailOutboxGateway;
import de.interzero.oneepr.auth.user.User;
import de.interzero.oneepr.auth.user.UserService;
import de.interzero.oneepr.auth.user.dto.CreateUserDto;
import de.interzero.oneepr.auth.user.dto.UpdateUserDto;
import de.interzero.oneepr.customer.customer.dto.CreateCustomerDto;
import de.interzero.oneepr.customer.customer.dto.GroupByCountryDto;
import de.interzero.oneepr.customer.customer.dto.LicenseCountryInfo;
import de.interzero.oneepr.customer.customer.dto.UpdateCustomerDto;
import de.interzero.oneepr.customer.http.CrmInterface;
import de.interzero.oneepr.customer.http.dto.UpdateCrmCompanyDto;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
@SuppressWarnings("java:S6539")
public class CustomerService {

    @Lazy
    private final UserService userService;

    private final CustomerRepository customerRepository;

    private final CustomerTutorialRepository customerTutorialRepository;

    private final CustomerPhoneRepository customerPhoneRepository;

    private final ShoppingCartRepository shoppingCartRepository;

    private final ConsentRepository consentRepository;

    private final CustomerConsentRepository customerConsentRepository;

    private final CustomerIoService customerIoService;

    private final CompanyService companyService;

    private final ContractRepository contractRepository;

    private final CouponService couponService;

    private final ModelMapper modelMapper;

    private static final String CURRENT = "current";

    private static final String GROWTH_RATE = "growthRate";

    private static final String EMAIL = "email";

    private static final String CUSTOMER_NOT_FOUND = "Customer not found";

    private final EmailOutboxGateway emailOutboxGateway;

    private final CrmInterface crmInterface;

    /**
     * Finds a customer by ID and eagerly fetches a deep graph of related entities.
     *
     * @param id The ID of the customer to search for.
     * @return The Customer entity with specified relations loaded.
     * @throws ResponseStatusException if the customer is not found.
     * @ts-legacy No null check
     */
    @Transactional(readOnly = true)
    public Customer findById(Integer id,
                             AuthenticatedUser user) {
        this.validateUserPermissionCustomer(id, user);
        Optional<Customer> customerOptional = customerRepository.findById(id);
        if (customerOptional.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, CUSTOMER_NOT_FOUND);
        }

        return customerOptional.get();
    }

    /**
     * Aggregates customer data by country based on licenses in active contracts, returning a map of the results.
     *
     * @return A map where the key is a country code and the value is a {@link GroupByCountryDto} containing aggregated license and customer counts.
     * @ts-legacy This method translates the original's data fetching and in-memory
     * reduction. It uses two separate, efficient queries to gather the necessary data:
     * one to get the total count of relevant customers (for the `unlicensed_customer_count`
     * field) and another to get a flat list of all licenses. The reduction logic
     * is then performed in Java using a Map, mirroring the original's `reduce` function.
     */
    @Transactional(readOnly = true)
    public Map<String, GroupByCountryDto> groupByCountry() {
        int totalActiveCustomers = customerRepository.countDistinctCustomersWithActiveContracts();
        List<LicenseCountryInfo> licenseInfos = customerRepository.findLicenseCountryInfoForActiveCustomers();

        Map<String, GroupByCountryDto> result = new HashMap<>();

        for (LicenseCountryInfo license : licenseInfos) {
            result.compute(
                    license.getCountryCode(), (key, dto) -> {
                        if (dto == null) {
                            dto = new GroupByCountryDto();
                            dto.setCountryId(license.getCountryId());
                            dto.setCountryCode(license.getCountryCode());
                            dto.setCountryName(license.getCountryName());
                            dto.setCountryFlag(license.getCountryFlag());
                            dto.setLicensedCustomerCount(1);
                            dto.setUnlicensedCustomerCount(totalActiveCustomers);
                        } else {
                            dto.setLicensedCustomerCount(dto.getLicensedCustomerCount() + 1);
                        }
                        return dto;
                    });
        }
        return result;
    }

    /**
     * Finds and paginates a list of customers based on a dynamic set of filter criteria.
     * <p>
     * This method is a direct translation of the {@code findAll} method in the original
     * NestJS service. It uses the JPA Specification API to dynamically build a query
     * that replicates the filtering logic for search terms, contract types, contract
     * status, and country codes. Sorting and pagination are also handled according to
     * the request parameters.
     *
     * @param params A DTO containing all optional filter, sorting, and pagination parameters.
     * @return A {@link Page} of {@link Customer} entities matching the criteria.
     * @throws ResponseStatusException if the provided page or limit parameters are invalid.
     * @ts-legacy The original NestJS {@code findAll} uses a deep, multi-level {@code include}
     * clause to fetch related data. In JPA, this would require complex EntityGraphs or DTO
     * projections. This translation returns the {@code Page<Customer>} entity directly,
     * relying on lazy loading for related entities as they are accessed, which is the
     * standard Spring Data JPA approach.
     */
    @SuppressWarnings("java:S3776")
    @Transactional(readOnly = true)
    public Page<Customer> findAll(FindAllCustomersDto params) {
        if (params.getPage() != null && params.getPage() < 1) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Page param is invalid");
        }
        if (params.getLimit() != null && params.getLimit() < 1) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Limit param is invalid");
        }

        int page = params.getPage() != null ? params.getPage() - 1 : 0;
        int limit = params.getLimit() != null ? params.getLimit() : 10;

        Sort sort = Sort.by(Sort.Direction.DESC, "id"); // Default order
        if (params.getOrder() != null) {
            sort = switch (params.getOrder()) {
                case ASC -> Sort.by(Sort.Direction.ASC, "id");
                case DESC -> Sort.by(Sort.Direction.DESC, "id");
                case LAST_MODIFIED -> Sort.by(Sort.Direction.DESC, "updatedAt");
                case FIRST_MODIFIED -> Sort.by(Sort.Direction.ASC, "updatedAt");
            };
        }

        Pageable pageable = PageRequest.of(page, limit, sort);
        Specification<Customer> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.isNull(root.get("deletedAt")));

            // Search filter
            if (StringUtils.hasText(params.getSearch())) {
                List<Predicate> searchPredicates = new ArrayList<>();
                searchPredicates.add(cb.like(cb.lower(root.get(EMAIL)), "%" + params.getSearch().toLowerCase() + "%"));

                Join<Customer, Company> companyJoin = root.join("companies", JoinType.LEFT);
                searchPredicates.add(cb.like(
                        cb.lower(companyJoin.get("name")),
                        "%" + params.getSearch().toLowerCase() + "%"));

                try {
                    searchPredicates.add(cb.equal(root.get("id"), Integer.valueOf(params.getSearch())));
                } catch (NumberFormatException e) {
                    // Ignore if search string is not a valid number
                }
                predicates.add(cb.or(searchPredicates.toArray(new Predicate[0])));
            }

            // Contracts subquery logic
            Join<Customer, Contract> contractJoin = root.join("contracts");
            predicates.add(cb.isNull(contractJoin.get("deletedAt")));

            if (params.getServiceType() != null) {
                predicates.add(cb.equal(
                        contractJoin.get("type"),
                        Contract.Type.valueOf(params.getServiceType().name())));
            }
            if (params.getStatus() != null) {
                predicates.add(cb.equal(
                        contractJoin.get("status"),
                        Contract.Status.valueOf(params.getStatus().name())));
            }

            // Country Filter logic
            if (params.getServiceType() == null && StringUtils.hasText(params.getCountryCode())) {
                Join<Contract, License> licenseJoin = contractJoin.join("licenses", JoinType.LEFT);
                Join<Contract, ActionGuide> actionGuideJoin = contractJoin.join("actionGuides", JoinType.LEFT);
                predicates.add(cb.or(
                        cb.equal(licenseJoin.get("countryCode"), params.getCountryCode()),
                        cb.equal(actionGuideJoin.get("countryCode"), params.getCountryCode())));
            }

            assert query != null;
            query.distinct(true);
            return cb.and(predicates.toArray(new Predicate[0]));
        };

        return customerRepository.findAll(spec, pageable);
    }

    /**
     * Finds a single, active customer by their email address, performing a permission check.
     *
     * @param email The email address to search for. Must not be null or empty.
     * @param user  The authenticated user principal, used for permission validation.
     * @return The {@link Customer} entity if found and the user has permission.
     * @throws ResponseStatusException with {@code HttpStatus.BAD_REQUEST} if the email is invalid,
     *                                 {@code HttpStatus.NOT_FOUND} if the customer does not exist,
     *                                 or {@code HttpStatus.FORBIDDEN} if the user lacks permission.
     * @ts-legacy The original NestJS method eagerly fetches the customer's companies and
     * their addresses using a Prisma {@code include}. This translation returns the base
     * {@code Customer} entity and relies on lazy loading for its relations.
     */
    @Transactional(readOnly = true)
    public Customer findOneByEmail(String email,
                                   AuthenticatedUser user) {
        if (email == null || email.trim().isEmpty()) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Bad Request");
        }
        Customer customer = customerRepository.findByEmailAndDeletedAtIsNull(email)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, CUSTOMER_NOT_FOUND));
        validateUserPermissionCustomer(customer.getId(), user);
        return customer;
    }

    /**
     * Finds a customer by their user_id and returns a detailed profile view.
     *
     * @param userId The user_id associated with the customer.
     * @param user   The authenticated user performing the request.
     * @return A DTO containing a deep and detailed graph of the customer's profile data.
     * @ts-legacy This method translates Prisma's complex `include` query by fetching the
     * root {@code Customer} entity and then manually traversing its relations in Java.
     * The `where: { deleted_at: null }` clauses on nested relations are replicated
     * using Java Streams. This approach was chosen for functional correctness but will
     * cause a significant N+1 query problem, as each collection access can trigger
     * a separate database query.
     */
    @Transactional(readOnly = true)
    public CustomerProfileDto findByUserId(Integer userId,
                                           AuthenticatedUser user) {
        Customer customer = customerRepository.findByUserIdAndDeletedAtIsNull(userId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Customer not found!"));

        if (user.getRole() == Role.CUSTOMER && !userId.equals(Integer.valueOf(user.getId()))) {
            throw new ResponseStatusException(
                    HttpStatus.FORBIDDEN,
                                              "You do not have permission to access this customer");
        }

        CustomerProfileDto dto = modelMapper.map(customer, CustomerProfileDto.class);

        // Manually map all top-level relations to trigger lazy-loading and apply logic
        dto.setCompanies(customer.getCompanies()
                                 .stream()
                                 .map(company -> modelMapper.map(company, CompanyDetailsDto.class))
                                 .toList());

        dto.setPhones(customer.getPhones()
                              .stream()
                              .map(phone -> modelMapper.map(phone, CustomerPhoneDto.class))
                              .toList());

        dto.setContracts(customer.getContracts().stream().map(this::mapContractToUserViewDto).toList());

        return dto;
    }

    /**
     * Helper method to map a Contract entity to its detailed DTO for the user profile view.
     * It replicates the nested, filtered includes from the original `findByUserId` query.
     *
     * @param contract The Contract entity to map.
     * @return A populated UserViewContractDto.
     */
    private UserViewContractDto mapContractToUserViewDto(Contract contract) {
        UserViewContractDto dto = modelMapper.map(contract, UserViewContractDto.class);

        dto.setActionGuides(contract.getActionGuides()
                                    .stream()
                                    .filter(ag -> ag.getDeletedAt() == null)
                                    .map(ag -> modelMapper.map(ag, UserViewActionGuideDto.class))
                                    .toList());

        dto.setLicenses(contract.getLicenses()
                                .stream()
                                .filter(l -> l.getDeletedAt() == null)
                                .map(this::mapLicenseToUserViewDto)
                                .toList());

        dto.setGeneralInformations(contract.getGeneralInformations()
                                           .stream()
                                           .filter(gi -> gi.getDeletedAt() == null)
                                           .map(this::mapGeneralInformationToUserViewDto)
                                           .toList());

        dto.setFiles(contract.getFiles()
                             .stream()
                             .filter(f -> f.getDeletedAt() == null)
                             .map(f -> modelMapper.map(f, FileDetailsDto.class))
                             .toList());

        if (contract.getTermination() != null) {
            // Re-use the helper from the 'details' method implementation
            dto.setTermination(mapTerminationToDetailsDto(contract.getTermination()));
        }

        return dto;
    }

    /**
     * Helper method to map a License entity to its detailed DTO for the user profile view.
     *
     * @param license The License entity to map.
     * @return A populated UserViewLicenseDto.
     */
    private UserViewLicenseDto mapLicenseToUserViewDto(License license) {
        UserViewLicenseDto dto = modelMapper.map(license, UserViewLicenseDto.class);

        // Replicate unconditional includes on the license
        dto.setFiles(license.getFiles().stream().map(f -> modelMapper.map(f, FileDetailsDto.class)).toList());

        dto.setPriceList(license.getPriceList()
                                 .stream()
                                 .map(pl -> modelMapper.map(pl, UserViewLicensePriceListDto.class))
                                 .toList());

        dto.setPackagingServices(license.getPackagingServices()
                                         .stream()
                                         .map(ps -> modelMapper.map(ps, UserViewLicensePackagingServiceDto.class))
                                         .toList());

        if (license.getTermination() != null) {
            dto.setTermination(mapTerminationToDetailsDto(license.getTermination()));
        }

        return dto;
    }

    /**
     * Helper method to map a LicenseRequiredInformation entity to its DTO for the user profile view.
     *
     * @param info The LicenseRequiredInformation entity to map.
     * @return A populated UserViewGeneralInformationDto.
     */
    private UserViewGeneralInformationDto mapGeneralInformationToUserViewDto(LicenseRequiredInformation info) {
        UserViewGeneralInformationDto dto = modelMapper.map(info, UserViewGeneralInformationDto.class);

        dto.setAnswerFiles(info.getAnswerFiles().stream().map(f -> modelMapper.map(f, FileDetailsDto.class)).toList());

        return dto;
    }

    /**
     * Retrieves the tutorial completion statuses for a specific customer, optionally filtering by service type.
     * <p>
     * This method first validates that the authenticated user has permission to access the
     * specified customer's data. It then queries for active (non-soft-deleted) tutorial
     * records matching the customer ID and, if provided, the service type.
     *
     * @param params A DTO containing the filter criteria, specifically the customer ID and an optional service type.
     * @param user   The authenticated user principal, used to validate permission to access the customer's data.
     * @return A list of active {@link CustomerTutorial} records matching the query.
     * @throws ResponseStatusException if the customer is not found or if the authenticated user
     *                                 does not have permission to view their data.
     */
    @Transactional(readOnly = true)
    public List<CustomerTutorial> findTutorialStatus(FindTutorialDto params,
                                                     AuthenticatedUser user) {
        Integer customerId = Integer.valueOf(params.getCustomerId());
        validateUserPermissionCustomer(customerId, user);
        return customerTutorialRepository.findByCustomerIdAndServiceTypeAndDeletedAtIsNull(
                customerId,
                params.getServiceType() != null ? Contract.Type.valueOf(
                        params.getServiceType()
                                .name()) : null);
    }

    /**
     * Performs an upsert operation (create or update) for a customer's tutorial status.
     * <p>
     * The method first validates that the customer exists. It then attempts to find an
     * existing {@link CustomerTutorial} based on the composite business key of
     * {@code customerId} and {@code serviceType}. If a record is found, it updates the
     * {@code isFinished} status. If no record is found, it creates a new one with the
     * provided details.
     *
     * @param dto The {@link UpsertTutorialDto} containing the data for the upsert operation.
     * @return The persisted {@link CustomerTutorial} entity, whether newly created or updated.
     * @throws ResponseStatusException with {@code HttpStatus.NOT_FOUND} if the customer
     *                                 specified in the DTO does not exist.
     * @ts-legacy This method translates the behavior of Prisma's {@code upsert} operation.
     * It uses a "find-then-save" pattern in Java to replicate the atomic upsert, which
     * in the original implementation was based on the composite business key of customer
     * and service type, not the primary key.
     */
    @Transactional
    public CustomerTutorial upsertTutorialStatus(UpsertTutorialDto dto) {
        Customer customer = customerRepository.findById(dto.getCustomerId())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, CUSTOMER_NOT_FOUND));

        CustomerTutorial tutorial = customerTutorialRepository.findByCustomerIdAndServiceType(
                        dto.getCustomerId(),
                        dto.getServiceType())
                .orElse(new CustomerTutorial());

        // This logic replicates Prisma's upsert where the ID isn't the sole unique key
        if (tutorial.getId() == null) { // Create
            tutorial.setCustomer(customer);
            tutorial.setServiceType(dto.getServiceType());
        }
        tutorial.setIsFinished(dto.getIsFinished());

        return customerTutorialRepository.save(tutorial);
    }

    /**
     * Creates a new customer and an associated company from an admin-like context.
     * <p>
     * This method orchestrates a multi-step creation process: it first creates a user
     * in an external authentication service, then uses the returned user ID to create a
     * local {@link Customer} entity, and finally creates an associated {@link Company} entity.
     *
     * @param dto The DTO containing the nested customer and company data required for creation.
     * @return A map containing the newly created {@link Customer} and {@link Company} entities,
     * keyed by "customer" and "company" respectively.
     * @throws ResponseStatusException with {@code HttpStatus.BAD_REQUEST} if the external user
     *                                 creation fails or does not return a valid user ID.
     * @ts-legacy This method calls another {@code @Transactional} method (`create`) within the
     * same class. To ensure Spring's transactional proxy intercepts the call correctly, this
     * implementation relies on a self-injected proxy (`self.create(...)`) instead of a direct
     * `this.create(...)` call. The original `TODO` from the source implementation is also preserved.
     * TODO: add integration with purchase
     */
    public Map<String, Object> createWithCountries(CreateCustomerCountriesDto dto) {
        Map<String, Object> userParams = new HashMap<>();
        userParams.put("name", dto.getCustomer().getFirstName() + " " + dto.getCustomer().getLastName());
        userParams.put(EMAIL, dto.getCustomer().getEmail());
        userParams.put("is_active", true);
        userParams.put("role_id", 1);
        Object userResponse = AuthInterface.auth("/user?create_by_admin=true", userParams, HttpMethod.POST);
        @SuppressWarnings("unchecked") Integer userId = (Integer) ((Map<String, Object>) userResponse).get("id");
        if (userId == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Error when creating a user");
        }

        dto.getCustomer().setUserId(userId);
        Customer savedCustomer = this.create(dto.getCustomer());

        dto.getCompany().setCustomerId(savedCustomer.getId());
        Company savedCompany = companyService.create(dto.getCompany());

        Map<String, Object> result = new HashMap<>();
        result.put("customer", savedCustomer);
        result.put("company", savedCompany);
        return result;
    }

    /**
     * Performs a soft delete on a customer by setting their {@code deletedAt} field.
     * <p>
     * This method first validates that the authenticated user has the necessary permissions
     * to modify the specified customer. It then fetches the customer, checks if they have
     * already been soft-deleted to prevent re-deleting, and if not, sets the
     * {@code deletedAt} field to the current date before persisting the change.
     *
     * @param id   The unique identifier of the customer to be soft-deleted.
     * @param user The authenticated user principal, used for permission validation.
     * @throws ResponseStatusException with {@code HttpStatus.NOT_FOUND} if the customer does not exist,
     *                                 {@code HttpStatus.BAD_REQUEST} if the customer has already been deleted,
     *                                 or {@code HttpStatus.FORBIDDEN} if the user lacks permission.
     */
    @Transactional
    public void remove(Integer id,
                       AuthenticatedUser user) {
        validateUserPermissionCustomer(id, user);
        Customer customer = customerRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, CUSTOMER_NOT_FOUND));

        if (customer.getDeletedAt() != null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Customer already deleted");
        }
        customer.setDeletedAt(LocalDate.now());
        customerRepository.save(customer);
    }


    /**
     * Retrieves a detailed, shaped view of a customer by fetching the root entity and
     * manually mapping its relations to DTOs.
     *
     * @param id   The ID of the customer to retrieve.
     * @param user The authenticated user performing the request.
     * @return A DTO containing the specifically shaped customer data.
     * @throws ResponseStatusException if the customer is not found or the user lacks permission.
     * @ts-legacy This method translates Prisma's complex `include` query by fetching the
     * root {@code Customer} entity and then manually traversing its relations in Java.
     * The `select`, `where`, and `orderBy` clauses on nested relations are replicated
     * using Java Streams for filtering and sorting. This approach was chosen to achieve
     * functional correctness first, but it will cause a significant N+1 query problem,
     * as Hibernate will lazily load each collection in separate database queries.
     */
    @Transactional(readOnly = true)
    public CustomerDetailsDto details(Integer id,
                                      AuthenticatedUser user) {
        validateUserPermissionCustomer(id, user);

        Customer customer = customerRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, CUSTOMER_NOT_FOUND));

        // 1. Map base Customer and included Phones/Companies
        CustomerDetailsDto customerDto = modelMapper.map(customer, CustomerDetailsDto.class);

        // 2. Manually trigger loading and map Companies
        List<CompanyDetailsDto> companyDtos = customer.getCompanies()
                .stream()
                .map(company -> modelMapper.map(company, CompanyDetailsDto.class))
                .toList();
        customerDto.setCompanies(companyDtos);

        // 3. Manually trigger loading and map Phones
        List<CustomerPhoneDto> phoneDtos = customer.getPhones()
                .stream()
                .map(phone -> modelMapper.map(phone, CustomerPhoneDto.class))
                .toList();
        customerDto.setPhones(phoneDtos);

        // 4. Manually map Contracts (this part was already correct)
        List<ContractDetailsDto> contractDtos = customer.getContracts()
                .stream()
                .map(this::mapContractToDetailsDto)
                .toList();
        customerDto.setContracts(contractDtos);

        return customerDto;
    }

    /**
     * Maps a {@link Contract} entity to its detailed DTO representation, {@link ContractDetailsDto}.
     * <p>
     * This helper method orchestrates the mapping of a single contract and its complex,
     * nested relations. It ensures that child collections like licenses and action guides
     * are correctly projected, filtered, and sorted according to the logic defined in the
     * original Prisma query.
     *
     * @param contract The {@link Contract} entity to map.
     * @return A fully populated {@link ContractDetailsDto} object.
     */
    private ContractDetailsDto mapContractToDetailsDto(Contract contract) {
        ContractDetailsDto contractDto = modelMapper.map(contract, ContractDetailsDto.class);

        // Map Licenses with select, orderBy, and nested filtered includes
        List<LicenseDetailsDto> licenseDtos = contract.getLicenses()
                .stream()
                .sorted(Comparator.comparing(License::getCreatedAt))
                .map(this::mapLicenseToDetailsDto)
                .toList();
        contractDto.setLicenses(licenseDtos);

        // Map ActionGuides with select and nested filtered includes
        List<ActionGuideDetailsDto> actionGuideDtos = contract.getActionGuides()
                .stream()
                .map(this::mapActionGuideToDetailsDto)
                .toList();
        contractDto.setActionGuides(actionGuideDtos);

        // Map Termination with where clause on the relation
        Termination termination = contract.getTermination();
        if (termination != null && termination.getDeletedAt() == null) {
            contractDto.setTermination(mapTerminationToDetailsDto(termination));
        }

        return contractDto;
    }

    /**
     * Maps a {@link License} entity to its projected DTO, {@link LicenseDetailsDto}.
     * <p>
     * This helper method manually replicates the {@code select} clause from the original
     * Prisma query. It copies only the specified fields from the source entity to the DTO.
     * It also handles the mapping of nested relations like {@code termination}, {@code files},
     * and {@code certificates}, ensuring that the collections are filtered to exclude
     * soft-deleted records, just as the original query's {@code where: { deleted_at: null }}
     * clause required.
     *
     * @param license The {@link License} entity to map.
     * @return A populated {@link LicenseDetailsDto} containing only the selected fields.
     */
    private LicenseDetailsDto mapLicenseToDetailsDto(License license) {
        // This method manually replicates the `select` clause
        LicenseDetailsDto licenseDto = new LicenseDetailsDto();
        licenseDto.setId(license.getId());
        licenseDto.setCountryId(license.getCountryId());
        licenseDto.setCountryCode(license.getCountryCode());
        licenseDto.setCountryName(license.getCountryName());
        licenseDto.setCountryFlag(license.getCountryFlag());
        licenseDto.setYear(license.getYear());
        licenseDto.setRegistrationStatus(license.getRegistrationStatus());
        licenseDto.setContractStatus(license.getContractStatus());
        licenseDto.setClerkControlStatus(license.getClerkControlStatus());
        licenseDto.setCreatedAt(license.getCreatedAt());
        licenseDto.setUpdatedAt(license.getUpdatedAt());

        // Replicate nested includes with where clauses
        if (license.getTermination() != null) {
            licenseDto.setTermination(mapTerminationToDetailsDto(license.getTermination()));
        }

        licenseDto.setFiles(license.getFiles()
                                    .stream()
                                    .filter(file -> file.getDeletedAt() == null) // Replicates where clause
                                    .map(file -> modelMapper.map(file, FileDetailsDto.class))
                                    .toList());

        licenseDto.setCertificates(license.getCertificates()
                                           .stream()
                                           .filter(cert -> cert.getDeletedAt() == null) // Replicates where clause
                                           .map(this::mapCertificateToDetailsDto)
                                           .toList());

        return licenseDto;
    }

    /**
     * Maps an {@link ActionGuide} entity to its projected DTO, {@link ActionGuideDetailsDto}.
     * <p>
     * This helper method manually replicates the {@code select} clause from the original
     * Prisma query for the {@code action_guides} relation. It constructs the DTO by
     * copying only the specified fields from the source entity, ensuring the response
     * shape matches the API contract. It also handles the mapping of the nested
     * {@code termination} relation.
     *
     * @param actionGuide The {@link ActionGuide} entity to map.
     * @return A populated {@link ActionGuideDetailsDto} containing only the selected fields.
     */
    private ActionGuideDetailsDto mapActionGuideToDetailsDto(ActionGuide actionGuide) {
        // This method manually replicates the `select` clause
        ActionGuideDetailsDto actionGuideDto = new ActionGuideDetailsDto();
        actionGuideDto.setCountryId(actionGuide.getCountryId());
        actionGuideDto.setCountryCode(actionGuide.getCountryCode());
        actionGuideDto.setCountryName(actionGuide.getCountryName());
        actionGuideDto.setCountryFlag(actionGuide.getCountryFlag());
        actionGuideDto.setContractStatus(actionGuide.getContractStatus());
        actionGuideDto.setCreatedAt(actionGuide.getCreatedAt());
        actionGuideDto.setUpdatedAt(actionGuide.getUpdatedAt());

        if (actionGuide.getTermination() != null) {
            actionGuideDto.setTermination(mapTerminationToDetailsDto(actionGuide.getTermination()));
        }

        return actionGuideDto;
    }

    /**
     * Maps a {@link Certificate} entity to its detailed DTO, {@link CertificateDetailsDto}.
     * <p>
     * This helper method handles the mapping of a certificate and its nested {@code files}
     * collection. It replicates the {@code where: { deleted_at: null }} clause from the
     * original Prisma query by streaming the files and filtering out any that have been
     * soft-deleted before mapping them to {@link FileDetailsDto} objects.
     *
     * @param certificate The {@link Certificate} entity to map.
     * @return A populated {@link CertificateDetailsDto} with a correctly filtered list of files.
     */
    private CertificateDetailsDto mapCertificateToDetailsDto(Certificate certificate) {
        CertificateDetailsDto certDto = modelMapper.map(certificate, CertificateDetailsDto.class);
        certDto.setFiles(certificate.getFiles()
                                 .stream()
                                 .filter(file -> file.getDeletedAt() == null) // Replicates where clause
                                 .map(file -> modelMapper.map(file, FileDetailsDto.class))
                                 .toList());
        return certDto;
    }

    /**
     * Maps a {@link Termination} entity to its detailed DTO, {@link TerminationDetailsDto}.
     * <p>
     * This helper method handles the mapping of a termination record and its nested
     * {@code files} collection. It faithfully replicates the {@code where: { deleted_at: null }}
     * clause from the original Prisma query by streaming the files and filtering out any
     * soft-deleted records before mapping them to {@link FileDetailsDto} objects.
     *
     * @param termination The {@link Termination} entity to map.
     * @return A populated {@link TerminationDetailsDto} with a correctly filtered list of files.
     */
    private TerminationDetailsDto mapTerminationToDetailsDto(Termination termination) {
        TerminationDetailsDto termDto = modelMapper.map(termination, TerminationDetailsDto.class);
        termDto.setFiles(termination.getFiles()
                                 .stream()
                                 .filter(file -> file.getDeletedAt() == null) // Replicates where clause
                                 .map(file -> modelMapper.map(file, FileDetailsDto.class))
                                 .toList());
        return termDto;
    }

    /**
     * @ts-legacy The original method uses multiple parallel database calls.
     * This is translated directly to multiple repository method calls.
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getSummary() {
        Instant now = Instant.now();
        Instant currentMonthStart = now.atZone(ZoneOffset.UTC)
                .withDayOfMonth(1)
                .truncatedTo(ChronoUnit.DAYS)
                .toInstant();
        Instant previousMonthStart = currentMonthStart.minus(1, ChronoUnit.MONTHS);

        long currentMonthCreated = customerRepository.countByCreatedAtBetween(currentMonthStart, now);
        long previousMonthCreated = customerRepository.countByCreatedAtBetween(previousMonthStart, currentMonthStart);
        long currentMonthActive = customerRepository.countActiveContractsBeforeDate(now);
        long previousMonthActive = customerRepository.countActiveContractsBeforeDate(currentMonthStart);
        long currentMonthTerminated = customerRepository.countTerminatedContractsInDateRange(currentMonthStart, now);
        long previousMonthTerminated = customerRepository.countTerminatedContractsInDateRange(
                previousMonthStart,
                currentMonthStart);

        return Map.of(
                "created", Map.of(
                        CURRENT,
                        currentMonthCreated,
                        GROWTH_RATE,
                        calculatePercentage(currentMonthCreated, previousMonthCreated)), "active", Map.of(
                        CURRENT,
                        currentMonthActive,
                        GROWTH_RATE,
                        calculatePercentage(currentMonthActive, previousMonthActive)), "terminated", Map.of(
                        CURRENT,
                        currentMonthTerminated,
                        GROWTH_RATE,
                        calculatePercentage(
                                currentMonthTerminated,
                                previousMonthTerminated)));
    }

    /**
     * Calculates and aggregates the resource and greenhouse gas savings for a customer
     * based on their reported direct license volumes.
     * <p>
     * This method first fetches the customer's single active, direct license contract. It then
     * iterates through each license, its associated packaging services, volume reports, and
     * individual report items. For each reported item, it converts the weight, looks up
     * environmental factors from a static source ({@code GermanyFractions}), and calculates
     * the corresponding savings. The results are aggregated into a comprehensive DTO,
     * providing both a grand total across all years and a year-by-year breakdown.
     *
     * @param id The unique identifier of the customer for whom to calculate the resources.
     * @return A {@link DirectLicenseResourcesDto} containing the aggregated totals and
     * yearly breakdowns of resource savings.
     * @throws ResponseStatusException with {@code HttpStatus.NOT_FOUND} if the customer or
     *                                 their active direct license contract cannot be found.
     */
    @Transactional(readOnly = true)
    public DirectLicenseResourcesDto getDirectLicenseResources(Integer id) {
        if (!customerRepository.existsById(id)) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, CUSTOMER_NOT_FOUND);
        }

        Contract directLicenseContract = contractRepository.findActiveDirectLicenseContractWithDetails(id)
                .orElseThrow(() -> new ResponseStatusException(
                        HttpStatus.NOT_FOUND,
                        "Direct license contract not found"));

        DirectLicenseResourcesDto resources = new DirectLicenseResourcesDto();

        for (License license : directLicenseContract.getLicenses()) {
            Integer licenseYear = license.getYear();
            if (license.getPackagingServices().isEmpty()) {
                continue;
            }
            LicensePackagingService packagingService = license.getPackagingServices().getFirst();

            resources.getTotal().getYears().add(licenseYear);
            resources.getYears().computeIfAbsent(
                    licenseYear, k -> {
                        YearlyResourcesDto yearlyDto = new YearlyResourcesDto();
                        yearlyDto.setYear(k);
                        return yearlyDto;
                    });

            for (LicenseVolumeReport volumeReport : packagingService.getVolumeReports()) {
                for (LicenseVolumeReportItem item : volumeReport.getVolumeReportItems()) {
                    BigDecimal reportedWeightInKg = BigDecimal.valueOf(item.getValue())
                            .divide(BigDecimal.valueOf(1000), 6, RoundingMode.HALF_UP);

                    GermanyFractions.FractionData fraction = GermanyFractions.findByCode(item.getSetupFractionCode())
                            .orElse(null);
                    if (fraction == null) {
                        continue;
                    }

                    BigDecimal resourcesSavedInKg = fraction.getResourceSavingFactor()
                            .multiply(reportedWeightInKg)
                            .setScale(2, RoundingMode.HALF_UP);
                    BigDecimal greenhouseGasesInKg = fraction.getGreenhouseGasesFactor()
                            .multiply(reportedWeightInKg)
                            .setScale(3, RoundingMode.HALF_UP);
                    BigDecimal reportedWeightRounded = reportedWeightInKg.setScale(2, RoundingMode.HALF_UP);

                    // Aggregate totals
                    TotalResourcesDto total = resources.getTotal();
                    total.setReportedWeight(total.getReportedWeight().add(reportedWeightRounded));
                    total.setResourcesSaved(total.getResourcesSaved().add(resourcesSavedInKg));
                    total.setGreenhouseGases(total.getGreenhouseGases().add(greenhouseGasesInKg));

                    FractionResourcesDto totalFraction = total.getFractions().computeIfAbsent(
                            fraction.getKey(), k -> {
                                FractionResourcesDto dto = new FractionResourcesDto();
                                dto.setKey(fraction.getKey());
                                dto.setCode(fraction.getCode());
                                dto.setName(fraction.getName());
                                return dto;
                            });
                    totalFraction.setReportedWeight(totalFraction.getReportedWeight().add(reportedWeightRounded));
                    totalFraction.setResourcesSaved(totalFraction.getResourcesSaved().add(resourcesSavedInKg));
                    totalFraction.setGreenhouseGases(totalFraction.getGreenhouseGases().add(greenhouseGasesInKg));

                    // Aggregate by year
                    YearlyResourcesDto yearly = resources.getYears().get(licenseYear);
                    yearly.setReportedWeight(yearly.getReportedWeight().add(reportedWeightRounded));
                    yearly.setResourcesSaved(yearly.getResourcesSaved().add(resourcesSavedInKg));
                    yearly.setGreenhouseGases(yearly.getGreenhouseGases().add(greenhouseGasesInKg));

                    FractionResourcesDto yearlyFraction = yearly.getFractions().computeIfAbsent(
                            fraction.getKey(), k -> {
                                FractionResourcesDto dto = new FractionResourcesDto();
                                dto.setKey(fraction.getKey());
                                dto.setCode(fraction.getCode());
                                dto.setName(fraction.getName());
                                return dto;
                            });
                    yearlyFraction.setReportedWeight(yearlyFraction.getReportedWeight().add(reportedWeightRounded));
                    yearlyFraction.setResourcesSaved(yearlyFraction.getResourcesSaved().add(resourcesSavedInKg));
                    yearlyFraction.setGreenhouseGases(yearlyFraction.getGreenhouseGases().add(greenhouseGasesInKg));
                }
            }
        }
        return resources;
    }

    /**
     * Calculates the percentage growth rate between a current and a previous value.
     * <p>
     * This is a direct translation of the {@code calculatePercentage} helper function
     * from the {@code getSummary} method in {@code customer.service.ts}. It handles
     * the edge case where the previous value is zero.
     *
     * @param current  The current period's value.
     * @param previous The previous period's value for comparison.
     * @return The growth rate as a percentage, rounded to one decimal place.
     */
    private double calculatePercentage(long current,
                                       long previous) {
        if (previous == 0) {
            return current > 0 ? 100.0 : 0.0;
        }
        double percentage = ((double) (current - previous) / previous) * 100.0;
        return Math.round(percentage * 10.0) / 10.0; // Round to one decimal place
    }

    /**
     * Validates if the authenticated user has permission to access a specific customer's data.
     * <p>
     * This is a direct translation of the {@code validatingUserPermissionCustomer} method
     * from {@code customer.service.ts}. An ADMIN or other system role can access any existing
     * customer, whereas a CUSTOMER can only access their own data record.
     *
     * @param id   The ID of the customer record to validate access for.
     * @param user The authenticated user principal containing their ID and role.
     * @throws ResponseStatusException with {@code HttpStatus.BAD_REQUEST} if the ID is null.
     * @throws ResponseStatusException with {@code HttpStatus.NOT_FOUND} if the customer does not exist.
     * @throws ResponseStatusException with {@code HttpStatus.FORBIDDEN} if a user with role CUSTOMER
     *                                 tries to access another customer's data.
     */
    private void validateUserPermissionCustomer(Integer id,
                                                AuthenticatedUser user) {
        if (id == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Id is required");
        }
        if (user.getRole() == Role.CUSTOMER) {
            customerRepository.findByUserIdAndDeletedAtIsNull(Integer.valueOf(user.getId()))
                    .filter(c -> c.getId().equals(id))
                    .orElseThrow(() -> new ResponseStatusException(
                            HttpStatus.FORBIDDEN,
                            "You do not have permission to access this customer"));
        }
        if (!customerRepository.existsById(id)) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, CUSTOMER_NOT_FOUND);
        }
    }

    /**
     * Updates the customer entity with new data, including handling of related phones, email change propagation,
     * CustomerIO sync, Monday.com updates, and sending notification email if required.
     *
     * @param id   the customer ID to update
     * @param dto  the DTO with update data
     * @param user the authenticated user requesting the change
     * @return the updated customer
     */
    @SuppressWarnings({"java:S6809", "java:S3776"})
    @Transactional
    public Customer update(Integer id,
                           UpdateCustomerDto dto,
                           AuthenticatedUser user) {
        log.info("CustomerService / update - {}", dto);

        this.validatingUserPermissionCustomer(id, user);

        Customer customer = customerRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, CUSTOMER_NOT_FOUND));

        String oldEmail = customer.getEmail();

        if (dto.getPhones() != null) {
            customerPhoneRepository.deleteAllByCustomer_Id(customer.getId());

            List<CustomerPhone> newPhones = dto.getPhones().stream().map(phone -> {
                CustomerPhone cp = new CustomerPhone();
                cp.setCustomer(customer);
                cp.setPhoneNumber(phone);
                cp.setPhoneType(CustomerPhone.Type.PHONE);
                cp.setCreatedAt(Instant.now());
                cp.setUpdatedAt(Instant.now());
                return cp;
            }).toList();

            customerPhoneRepository.saveAll(newPhones);
        }

        if (StringUtils.hasText(dto.getFirstName()) && StringUtils.hasText(dto.getLastName())) {
            UpdateUserDto userDto = new UpdateUserDto();
            userDto.setName(dto.getFirstName() + " " + dto.getLastName());
            userService.updateByEmail(customer.getEmail(), userDto);
        }

        if (StringUtils.hasText(dto.getEmail()) && !dto.getEmail()
                .equals(customer.getEmail()) && customerRepository.findByEmail(dto.getEmail()).isPresent()) {
            throw new ResponseStatusException(HttpStatus.CONFLICT, "This email is already in use");
        }


        customer.setEmail(dto.getEmail() != null ? dto.getEmail() : customer.getEmail());
        customer.setFirstName(dto.getFirstName() != null ? dto.getFirstName() : customer.getFirstName());
        customer.setLastName(dto.getLastName() != null ? dto.getLastName() : customer.getLastName());
        customer.setCompanyName(dto.getCompanyName() != null ? dto.getCompanyName() : customer.getCompanyName());
        customer.setSalutation(dto.getSalutation() != null ? dto.getSalutation() : customer.getSalutation());
        customer.setDocumentId(dto.getDocumentId() != null ? dto.getDocumentId() : customer.getDocumentId());
        customer.setLanguage(dto.getLanguage() != null ? dto.getLanguage() : customer.getLanguage());
        customer.setCurrency(dto.getCurrency() != null ? dto.getCurrency() : customer.getCurrency());
        customer.setUpdatedAt(Instant.now());

        Customer updatedCustomer = customerRepository.save(customer);

        if (StringUtils.hasText(dto.getEmail())) {
            shoppingCartRepository.updateEmailByOldEmailAndDeletedAtIsNull(customer.getEmail(), dto.getEmail());
        }

        Map<String, Object> customerIoAttributes = new HashMap<>();

        if (updatedCustomer.getFirstName() != null) {
            customerIoAttributes.put("first_name", updatedCustomer.getFirstName());
        }

        if (updatedCustomer.getLastName() != null) {
            customerIoAttributes.put("last_name", updatedCustomer.getLastName());
        }

        String language = dto.getLanguage() != null ? dto.getLanguage() : customer.getLanguage();
        if (language != null) {
            customerIoAttributes.put("language", language);
        }

        customerIoService.updateAttributesByCustomerId(updatedCustomer.getId(), customerIoAttributes);


        if (StringUtils.hasText(dto.getEmail())) {
            customerIoService.updateEmailByCustomerId(updatedCustomer.getId(), oldEmail);
            crmInterface.updateCustomerEmailMonday(dto.getEmail(), customer.getId());
        }

        try {
            crmInterface.updateBoardAccountsMonday(mapToCrmCompanyDto(dto), customer.getId());
        } catch (Exception e) {
            log.warn("MONDAY ERROR", e);
        }

        if (customer.getSalutation() != null && !StringUtils.hasText(dto.getEmail())) {
            try {
                EmailMessage message = new EmailMessage(
                        "28",
                        updatedCustomer.getEmail(),
                        "Lizenzero <<EMAIL>>",
                        "Personal information changed",
                        Map.of(EMAIL, updatedCustomer.getEmail()));
                emailOutboxGateway.sendEmail(message);
            } catch (Exception e) {
                log.warn("Email notification failed", e);
            }
        }

        return updatedCustomer;
    }

    /**
     * Validates whether the authenticated user has permission to access or modify the given customer.
     *
     * @param id   the customer ID to check
     * @param user the authenticated user
     * @throws ResponseStatusException if the customer is not found or access is denied
     */
    @Transactional(readOnly = true)
    public void validatingUserPermissionCustomer(Integer id,
                                                 AuthenticatedUser user) {
        if (id == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Id is required");
        }

        Customer customer = customerRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, CUSTOMER_NOT_FOUND));

        if (user.getRole() == Role.CUSTOMER && !customer.getUserId().equals(Integer.valueOf(user.getId()))) {
            throw new ResponseStatusException(
                    HttpStatus.FORBIDDEN,
                                              "You do not have permission to access this customer");
        }
    }

    /**
     * Maps a general customer update DTO to a DTO specifically formatted for a CRM system update.
     * <p>
     * This helper method transforms an {@link UpdateCustomerDto} into an {@link UpdateCrmCompanyDto}.
     * The mapping is selective, extracting only the company-related fields such as VAT, TIN,
     * company name, and the nested address details to prepare the data for an external
     * CRM service call.
     *
     * @param dto The source {@link UpdateCustomerDto} containing combined customer and company data.
     * @return A new {@link UpdateCrmCompanyDto} instance populated with company data.
     */
    private UpdateCrmCompanyDto mapToCrmCompanyDto(UpdateCustomerDto dto) {
        UpdateCrmCompanyDto crmDto = new UpdateCrmCompanyDto();

        crmDto.setVat(dto.getVat());
        crmDto.setTin(dto.getTin());
        crmDto.setName(dto.getCompanyName());

        if (dto.getAddress() != null) {
            UpdateCrmCompanyDto.AddressDto address = new UpdateCrmCompanyDto.AddressDto();
            address.setCity(dto.getAddress().getCity());
            address.setZipCode(dto.getAddress().getZipCode());
            address.setCountryCode(dto.getAddress().getCountryCode());
            address.setStreetAndNumber(dto.getAddress().getStreetAndNumber());
            crmDto.setAddress(address);
        }

        return crmDto;
    }

    /**
     * Creates a new customer and associated data (user, consents, customer.io, Monday, coupon).
     *
     * @param dto CreateCustomerDto with customer information
     * @return created Customer entity
     */
    @SuppressWarnings("java:S6809")
    public Customer create(CreateCustomerDto dto) {
        if (customerRepository.findByEmail(dto.getEmail()).isPresent()) {
            throw new ResponseStatusException(HttpStatus.CONFLICT, "This email is already in use");
        }

        User user;
        if (dto.getUserId() == null) {
            user = this.createUserCustomer( // @ts-legacy: preserving original structure from NestJS
                                            dto.getFirstName(),
                                            dto.getLastName(),
                                            dto.getEmail(),
                                            dto.getPassword(),
                                            dto.getTokenMagicLink(),
                                            "true");
        } else {
            try {
                user = userService.findOne(dto.getUserId().toString());
            } catch (Exception e) {
                throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Error finding user", e);
            }
        }

        Instant now = Instant.now();

        Customer customer = new Customer();
        customer.setUserId(user.getId());
        customer.setEmail(dto.getEmail());
        customer.setFirstName(dto.getFirstName());
        customer.setLastName(dto.getLastName());
        customer.setCompanyName(dto.getCompanyName());
        customer.setSalutation(dto.getSalutation());
        customer.setDocumentId(dto.getDocumentId());
        customer.setCurrency(dto.getCurrency());
        customer.setIsActive(false);
        customer.setType(Customer.Type.REGULAR);
        customer.setCreatedAt(now);
        customer.setUpdatedAt(now);

        Customer savedCustomer = customerRepository.save(customer);

        List<Consent> consents = consentRepository.findAllByTypeAndDeletedAtIsNull(Consent.Type.ACCOUNT);

        List<CustomerConsent> customerConsents = consents.stream().map(consent -> {
            CustomerConsent cc = new CustomerConsent();
            cc.setCustomer(savedCustomer);
            cc.setConsent(consent);
            cc.setGiven(true);
            cc.setCreatedAt(now);
            cc.setUpdatedAt(now);
            return cc;
        }).toList();

        customerConsentRepository.saveAll(customerConsents);

        // Async operations
        try {
            customerIoService.updateAttributesByCustomerId(
                    savedCustomer.getId(), Map.of(
                            "cio_subscription_preferences", Map.of(
                                    "topics",
                                    Map.of("topic_1", true, "topic_3", true, "topic_5", true, "topic_6", true))));

            customerIoService.updateAttributesByCustomerId(
                    savedCustomer.getId(),
                    Map.of("first_name", savedCustomer.getFirstName(), "last_name", savedCustomer.getLastName()));
        } catch (Exception e) {
            log.info("CUSTOMER IO ERROR", e);
        }

        try {
            crmInterface.sendDataBoardCustomer(dto, savedCustomer.getId());
        } catch (Exception e) {
            log.info("MONDAY ERROR", e);
        }

        LocalDate nextYear = LocalDate.now().plusYears(1);

        CreateCouponDto createCouponDto = new CreateCouponDto();
        createCouponDto.setCode(String.valueOf(savedCustomer.getId()));
        createCouponDto.setDiscountType(Coupon.DiscountType.ABSOLUTE);
        createCouponDto.setValue(1000);
        createCouponDto.setIsActive(true);
        createCouponDto.setMaxAmount(100);
        createCouponDto.setMaxUsesPerCustomer(1);
        createCouponDto.setRedeemableByNewCustomers(true);
        createCouponDto.setType(Coupon.Type.CUSTOMER);
        createCouponDto.setEndDate(nextYear.atStartOfDay(ZoneId.systemDefault()).toInstant());
        createCouponDto.setMode(Coupon.Mode.GENERAL);
        createCouponDto.setCustomers(List.of(savedCustomer.getId()));

        couponService.create(createCouponDto);


        return savedCustomer;
    }

    /**
     * Creates a new user from provided fields and invokes {@link UserService#create(CreateUserDto, String)}.
     * If {@code createByAdminRaw} is set to "true", user will be immediately activated and notified.
     *
     * @param firstName        First name of the user
     * @param lastName         Last name of the user
     * @param email            Email address of the user
     * @param password         Optional password for the user
     * @param tokenMagicLink   Optional magic link token
     * @param createByAdminRaw Raw string ("true"/"false") indicating if user is created by admin
     * @return created {@link User}
     */
    @Transactional(timeout = 20)
    public User createUserCustomer(String firstName,
                                   String lastName,
                                   String email,
                                   @Nullable String password,
                                   @Nullable String tokenMagicLink,
                                   String createByAdminRaw) {
        CreateUserDto dto = new CreateUserDto();
        dto.setName(firstName + " " + lastName);
        dto.setEmail(email);
        dto.setIsActive(true);
        dto.setRoleId(1); // Customer
        dto.setPassword(password);
        dto.setTokenMagicLink(tokenMagicLink);

        return userService.create(dto, createByAdminRaw);
    }


}