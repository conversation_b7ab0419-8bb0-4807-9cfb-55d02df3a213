package de.interzero.oneepr.customer.company.util;

import de.interzero.oneepr.customer.company.dto.EvatrResponse;
import lombok.extern.slf4j.Slf4j;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.ByteArrayInputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * Utility class for parsing XML responses from EVATR service.
 * Converted from TypeScript XML parsing logic.
 */
@Slf4j
public class XmlParserUtil {

    private XmlParserUtil() {
    }

    /**
     * Parse EVATR XML response to EvatrResponse object.
     * Equivalent to TypeScript parseEvatResponse function.
     *
     * @param xml XML response from EVATR
     * @return EvatrResponse object or null if parsing fails
     */
    public static EvatrResponse parseEvatrResponse(String xml) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new ByteArrayInputStream(xml.getBytes()));

            // Parse the XML structure similar to TypeScript logic
            Map<String, String> formattedParams = extractParamsFromXml(document);

            if (formattedParams.isEmpty()) {
                log.warn("No parameters found in EVATR XML response");
                return null;
            }

            // Transform to EvatrResponse - exact same field mapping as TypeScript
            return EvatrResponse.builder()
                    .ownVatId(formattedParams.getOrDefault("UstId_1", ""))
                    .partnerVatId(formattedParams.getOrDefault("UstId_2", ""))
                    .errorCode(formattedParams.getOrDefault("ErrorCode", ""))
                    .print("ja".equals(formattedParams.get("Druck")) ? "yes" : "no")
                    .resultPostalCode(formattedParams.getOrDefault("Erg_PLZ", ""))
                    .city(formattedParams.getOrDefault("Ort", ""))
                    .date(formattedParams.getOrDefault("Datum", ""))
                    .postalCode(formattedParams.getOrDefault("PLZ", ""))
                    .resultCity(formattedParams.getOrDefault("Erg_Ort", ""))
                    .time(formattedParams.getOrDefault("Uhrzeit", ""))
                    .resultName(formattedParams.getOrDefault("Erg_Name", ""))
                    .validFrom(formattedParams.getOrDefault("Gueltig_ab", ""))
                    .validUntil(formattedParams.getOrDefault("Gueltig_bis", ""))
                    .street(formattedParams.getOrDefault("Strasse", ""))
                    .companyName(formattedParams.getOrDefault("Firmenname", ""))
                    .resultStreet(formattedParams.getOrDefault("Erg_Strasse", ""))
                    .build();

        } catch (Exception e) {
            log.error("Error parsing EVATR XML response: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Extract parameters from EVATR XML response.
     * This method handles the specific XML structure returned by EVATR service.
     *
     * @param document Parsed XML document
     * @return Map of parameter names to values
     */
    private static Map<String, String> extractParamsFromXml(Document document) {
        Map<String, String> params = new HashMap<>();

        try {
            // Look for param elements in the XML
            NodeList paramNodes = document.getElementsByTagName("param");

            for (int i = 0; i < paramNodes.getLength(); i++) {
                Node paramNode = paramNodes.item(i);
                if (paramNode.getNodeType() == Node.ELEMENT_NODE) {
                    Element paramElement = (Element) paramNode;

                    // Extract the parameter structure
                    String[] keyValue = extractKeyValueFromParam(paramElement);
                    if (keyValue != null && keyValue.length == 2) {
                        params.put(keyValue[0], keyValue[1]);
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error extracting parameters from XML: {}", e.getMessage());
        }

        return params;
    }

    /**
     * Extract key-value pair from a param element.
     * Handles the nested structure of EVATR XML response.
     *
     * @param paramElement The param XML element
     * @return Array with [key, value] or null if extraction fails
     */
    @SuppressWarnings("java:S3776")
    private static String[] extractKeyValueFromParam(Element paramElement) {
        try {
            // Navigate through the nested structure: param -> value -> array -> data -> value[]
            NodeList valueNodes = paramElement.getElementsByTagName("value");

            if (valueNodes.getLength() > 0) {
                Element valueElement = (Element) valueNodes.item(0);
                NodeList arrayNodes = valueElement.getElementsByTagName("array");

                if (arrayNodes.getLength() > 0) {
                    Element arrayElement = (Element) arrayNodes.item(0);
                    NodeList dataNodes = arrayElement.getElementsByTagName("data");

                    if (dataNodes.getLength() > 0) {
                        Element dataElement = (Element) dataNodes.item(0);
                        NodeList innerValueNodes = dataElement.getElementsByTagName("value");

                        if (innerValueNodes.getLength() >= 2) {
                            // First value contains the key, second contains the value
                            String key = extractStringFromValue((Element) innerValueNodes.item(0));
                            String value = extractStringFromValue((Element) innerValueNodes.item(1));

                            if (key != null && value != null) {
                                return new String[]{key, value};
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.debug("Error extracting key-value from param element: {}", e.getMessage());
        }

        return new String[]{};
    }

    /**
     * Extract string value from a value element.
     *
     * @param valueElement The value XML element
     * @return String content or null if extraction fails
     */
    private static String extractStringFromValue(Element valueElement) {
        try {
            NodeList stringNodes = valueElement.getElementsByTagName("string");
            if (stringNodes.getLength() > 0) {
                Element stringElement = (Element) stringNodes.item(0);
                return stringElement.getTextContent();
            }
        } catch (Exception e) {
            log.debug("Error extracting string from value element: {}", e.getMessage());
        }

        return null;
    }
}
