package de.interzero.oneepr.customer.customer.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * Data Transfer Object representing a price list item for a license in the user profile view.
 * <p>
 * This DTO is a direct translation of the {@code LicensePriceList} model, included
 * within the {@code licenses} relation of the {@code customer.service.ts#findByUserId} method.
 */
@Data
@NoArgsConstructor
public class UserViewLicensePriceListDto {

    @Schema(description = "Unique identifier of the price list item.")
    @JsonProperty("id")
    private Integer id;

    @Schema(description = "ID of the corresponding price list setup item.")
    @JsonProperty("setup_price_list_id")
    private Integer setupPriceListId;

    @Schema(description = "Name of the price list item.")
    @JsonProperty("name")
    private String name;

    @Schema(description = "Description of the price list item.")
    @JsonProperty("description")
    private String description;

    @Schema(description = "The type of condition for this price item.")
    @JsonProperty("condition_type")
    private String conditionType;

    @Schema(description = "The value associated with the condition type.")
    @JsonProperty("condition_type_value")
    private String conditionTypeValue;

    @Schema(description = "The start date for this price item's validity.")
    @JsonProperty("start_date")
    private Instant startDate;

    @Schema(description = "The end date for this price item's validity.")
    @JsonProperty("end_date")
    private Instant endDate;

    @Schema(description = "The basic price.")
    @JsonProperty("basic_price")
    private Integer basicPrice;

    @Schema(description = "The minimum price.")
    @JsonProperty("minimum_price")
    private Integer minimumPrice;

    @Schema(description = "The registration fee.")
    @JsonProperty("registration_fee")
    private Integer registrationFee;

    @Schema(description = "The handling fee.")
    @JsonProperty("handling_fee")
    private Integer handlingFee;

    @Schema(description = "The variable handling fee as a percentage.")
    @JsonProperty("variable_handling_fee")
    private Float variableHandlingFee;

    @Schema(description = "JSON object containing pricing thresholds.")
    @JsonProperty("thresholds")
    private JsonNode thresholds;

    @Schema(description = "Timestamp of when the record was created.")
    @JsonProperty("created_at")
    private Instant createdAt;

    @Schema(description = "Timestamp of when the record was last updated.")
    @JsonProperty("updated_at")
    private Instant updatedAt;
}