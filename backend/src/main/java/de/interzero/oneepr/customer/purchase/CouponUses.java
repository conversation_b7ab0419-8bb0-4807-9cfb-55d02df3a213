package de.interzero.oneepr.customer.purchase;

import de.interzero.oneepr.customer.entity.Coupon;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.time.Instant;

@Getter
@Setter
@Entity
@Table(
        name = "coupon_uses",
        schema = "public"
)
public class CouponUses {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "coupon_uses_id_gen"
    )
    @SequenceGenerator(
            name = "coupon_uses_id_gen",
            sequenceName = "coupon_uses_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    private Integer id;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "coupon_id",
            nullable = false
    )
    private Coupon coupon;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "shopping_cart_id",
            nullable = false
    )
    private ShoppingCart shoppingCart;

    @Column(
            name = "order_id",
            length = Integer.MAX_VALUE
    )
    private String orderId;

    @NotNull
    @Column(
            name = "customer_id",
            nullable = false
    )
    private Integer customerId;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    private Instant createdAt;

    @NotNull
    @Column(
            name = "is_first_purchase",
            nullable = false
    )
    private Boolean isFirstPurchase = false;

}