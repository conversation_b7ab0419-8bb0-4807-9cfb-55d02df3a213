package de.interzero.oneepr.customer.license_representative_tier.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Data Transfer Object for creating a new representative tier associated with a license.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CreateLicenseRepresentativeTierDto extends BaseDto {

    @NotNull
    @JsonProperty("license_id")
    @Schema(
            description = "The license ID",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer licenseId;

    @NotNull
    @JsonProperty("setup_representative_tier_id")
    @Schema(
            description = "The representative tier ID",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer setupRepresentativeTierId;

    @NotBlank
    @JsonProperty("name")
    @Schema(
            description = "The name of the representative tier",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String name;

    @NotNull
    @JsonProperty("price")
    @Schema(
            description = "The price of the representative tier (in cents)",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer price;
}
