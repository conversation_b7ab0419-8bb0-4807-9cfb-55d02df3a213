package de.interzero.oneepr.customer.customer.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * Data Transfer Object representing the billing information for a company.
 * <p>
 * This DTO is a direct translation of the {@code CompanyBilling} model included in
 * the {@code companies} relation of the {@code customer.service.ts#details} method.
 */
@Data
@NoArgsConstructor
public class CompanyBillingDto {

    @Schema(description = "Unique identifier of the billing record.")
    @JsonProperty("id")
    private Integer id;

    @Schema(description = "Indicates if the billing address is custom.")
    @JsonProperty("is_custom")
    private Boolean isCustom;

    @Schema(description = "Full name for the billing contact.")
    @JsonProperty("full_name")
    private String fullName;

    @Schema(description = "ISO country code for the billing address.")
    @JsonProperty("country_code")
    private String countryCode;

    @Schema(description = "Country name for the billing address.")
    @JsonProperty("country_name")
    private String countryName;

    @Schema(description = "Company name for billing purposes.")
    @JsonProperty("company_name")
    private String companyName;

    @Schema(description = "Street name and number for the billing address.")
    @JsonProperty("street_and_number")
    private String streetAndNumber;

    @Schema(description = "City for the billing address.")
    @JsonProperty("city")
    private String city;

    @Schema(description = "ZIP or postal code for the billing address.")
    @JsonProperty("zip_code")
    private String zipCode;

    @Schema(description = "Timestamp of when the record was created.")
    @JsonProperty("created_at")
    private Instant createdAt;

    @Schema(description = "Timestamp of when the record was last updated.")
    @JsonProperty("updated_at")
    private Instant updatedAt;
}