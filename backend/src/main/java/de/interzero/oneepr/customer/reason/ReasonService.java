package de.interzero.oneepr.customer.reason;

import de.interzero.oneepr.customer.reason.dto.FindAllReasonsDto;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;

/**
 * Service for managing reasons
 */
@Service
@RequiredArgsConstructor
public class ReasonService {

    private final ReasonRepository reasonRepository;

    /**
     * Find all reasons by type
     *
     * @param findAllReasonsDto the DTO with reason type to search for
     * @return the found reasons by type
     */
    @SuppressWarnings({"UnnecessaryLocalVariable", "java:S1488"})
    public List<Reason> findAll(FindAllReasonsDto findAllReasonsDto) {
        if (findAllReasonsDto == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid type");
        }
        if (findAllReasonsDto.getType() == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Type is required");
        }

        List<Reason> reasons = this.reasonRepository.findAllByDeletedAtIsNullAndType(findAllReasonsDto.getType());
        return reasons;
    }
}