package de.interzero.oneepr.customer.company.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.Instant;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CreateCompanyDto extends BaseDto {

    @Schema(description = "The ID of the customer")
    @JsonProperty("customer_id")
    private Integer customerId;

    @JsonProperty("partner_id")
    private Integer partnerId;

    @Schema(description = "The name of the company")
    @JsonProperty("name")
    private String name;

    @Schema(description = "The description of the company")
    @JsonProperty("description")
    private String description;

    @Schema(description = "The address of the company")
    @JsonProperty("address")
    private AddressDto address;

    @Schema(description = "The VAT ID of the company")
    @JsonProperty("vat")
    private String vat;

    @Schema(description = "The TIN of the company")
    @JsonProperty("tin")
    private String tin;

    @Schema(description = "The Lucid ID of the company")
    @JsonProperty("lucid")
    private String lucid;

    @Schema(description = "The contact of the company")
    @JsonProperty("contact")
    private CreateCompanyContactDto contact;

    @Schema(description = "The emails of the company")
    @JsonProperty("emails")
    private List<String> emails;

    @Schema(description = "The managing director of the company")
    @JsonProperty("managing_director")
    private String managingDirector;

    @Schema(description = "The starting date of the company")
    @JsonProperty("starting")
    private Instant starting;

    @Schema(description = "The website of the company")
    @JsonProperty("website")
    private String website;

    @Schema(description = "The billing of the company")
    @JsonProperty("billing")
    private BillingDto billing;

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AddressDto {

        @JsonProperty("country_code")
        private String countryCode;

        @JsonProperty("address_line")
        private String addressLine;

        @JsonProperty("city")
        private String city;

        @JsonProperty("zip_code")
        private String zipCode;

        @JsonProperty("street_and_number")
        private String streetAndNumber;

        @JsonProperty("additional_address")
        private String additionalAddress;

        @JsonProperty("place_id")
        private String placeId;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BillingDto {

        @JsonProperty("full_name")
        private String fullName;

        @JsonProperty("country_code")
        private String countryCode;

        @JsonProperty("country_name")
        private String countryName;

        @JsonProperty("company_name")
        private String companyName;

        @JsonProperty("street_and_number")
        private String streetAndNumber;

        @JsonProperty("city")
        private String city;

        @JsonProperty("zip_code")
        private String zipCode;
    }
}