package de.interzero.oneepr.customer.license_packaging_service;

import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface LicensePackagingServiceRepository extends JpaRepository<LicensePackagingService, Integer> {

    Optional<LicensePackagingService> findByIdAndDeletedAtIsNull(Integer licensePackagingServiceId);

    List<LicensePackagingService> findAll(Specification<LicensePackagingService> spec,
                                          Sort sort);
}
