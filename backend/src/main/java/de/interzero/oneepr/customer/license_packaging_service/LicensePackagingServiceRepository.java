package de.interzero.oneepr.customer.license_packaging_service;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface LicensePackagingServiceRepository extends JpaRepository<LicensePackagingService, Integer> {

    Optional<LicensePackagingService> findByIdAndDeletedAtIsNull(Integer licensePackagingServiceId);
}
