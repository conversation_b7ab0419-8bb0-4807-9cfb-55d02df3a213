package de.interzero.oneepr.customer.license_packaging_service;

import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface LicensePackagingServiceRepository extends JpaRepository<LicensePackagingService, Integer> {

    @EntityGraph(attributePaths = {
            "reportSet",
            "reportSetFrequency",
            "volumeReports",
            "volumeReports.volumeReportItems",
            "volumeReports.volumeReportItems.decline",
            "volumeReports.volumeReportItems.decline.declineReasons",
            "volumeReports.volumeReportItems.decline.declineReasons.reason",
            "volumeReports.decline",
            "volumeReports.decline.declineReasons",
            "volumeReports.decline.declineReasons.reason"
    })
    Optional<LicensePackagingService> findByIdAndDeletedAtIsNull(Integer licensePackagingServiceId);

    @EntityGraph(attributePaths = {
            "reportSet",
            "reportSetFrequency",
            "volumeReports",
            "volumeReports.volumeReportItems",
            "volumeReports.volumeReportItems.decline",
            "volumeReports.volumeReportItems.decline.declineReasons",
            "volumeReports.volumeReportItems.decline.declineReasons.reason",
            "volumeReports.decline",
            "volumeReports.decline.declineReasons",
            "volumeReports.decline.declineReasons.reason"
    })
    List<LicensePackagingService> findAll(Specification<LicensePackagingService> spec,
                                          Sort sort);
}
