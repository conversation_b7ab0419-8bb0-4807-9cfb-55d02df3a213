package de.interzero.oneepr.customer.license_packaging_service.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Data Transfer Object representing the weight reported for a packaging service.
 */
@Data
public class PackagingServiceWeightReportedDto {

    @JsonProperty("setup_fraction_code")
    @Schema(
            description = "The setup fraction code",
            example = "DEOK34",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String setupFractionCode;


    @JsonProperty("total_weight")
    @Schema(
            description = "The total weight",
            example = "1",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private int totalWeight;


}
