package de.interzero.oneepr.customer.license_packaging_service.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PackagingServiceTurnoverDto {

    @JsonProperty("period")
    @Schema(
            description = "The period",
            example = "2024-01",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String period;

    @JsonProperty("period_label")
    @Schema(
            description = "The period label",
            example = "January 2024",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String periodLabel;

    @JsonProperty("revenue")
    @Schema(
            description = "The revenue",
            example = "1",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private int revenue;


}
