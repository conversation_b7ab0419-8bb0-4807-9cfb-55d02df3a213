package de.interzero.oneepr.customer.customer.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.contract.Contract;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

/**
 * Data Transfer Object representing the detailed view of a customer's contract.
 * <p>
 * This DTO is a direct translation of the {@code Contract} model and its complex
 * nested relations as defined in the {@code contracts} include block of the
 * {@code customer.service.ts#details} method.
 */
@Data
@NoArgsConstructor
public class ContractDetailsDto {

    @Schema(description = "Unique identifier of the contract.")
    @JsonProperty("id")
    private Integer id;

    @Schema(description = "The type of the contract.")
    @JsonProperty("type")
    private Contract.Type type;

    @Schema(description = "The status of the contract.")
    @JsonProperty("status")
    private Contract.Status status;

    @Schema(description = "The title of the contract.")
    @JsonProperty("title")
    private String title;

    @Schema(description = "The start date of the contract.")
    @JsonProperty("start_date")
    private Instant startDate;

    @Schema(description = "The end date of the contract.")
    @JsonProperty("end_date")
    private Instant endDate;

    @Schema(description = "Timestamp of when the record was created.")
    @JsonProperty("created_at")
    private Instant createdAt;

    @Schema(description = "Timestamp of when the record was last updated.")
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Schema(description = "List of license projections for this contract, ordered by creation date.")
    @JsonProperty("licenses")
    private List<LicenseDetailsDto> licenses;

    @Schema(description = "List of action guide projections for this contract.")
    @JsonProperty("action_guides")
    private List<ActionGuideDetailsDto> actionGuides;

    @Schema(description = "The associated non-deleted termination details, if any.")
    @JsonProperty("termination")
    private TerminationDetailsDto termination;
}