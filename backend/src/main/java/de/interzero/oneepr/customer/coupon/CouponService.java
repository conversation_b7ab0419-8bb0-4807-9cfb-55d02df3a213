package de.interzero.oneepr.customer.coupon;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.common.config.ModelMapperConfig;
import de.interzero.oneepr.customer.contract.ContractRepository;
import de.interzero.oneepr.customer.coupon.dto.CreateCouponDto;
import de.interzero.oneepr.customer.coupon.dto.FindAllCouponsPaginatedDto;
import de.interzero.oneepr.customer.coupon.dto.InviteCustomersManager;
import de.interzero.oneepr.customer.coupon.dto.UpdateCouponDto;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import de.interzero.oneepr.customer.entity.CouponCustomer;
import de.interzero.oneepr.customer.http.AdminInterface;
import de.interzero.oneepr.customer.purchase.CouponUses;
import de.interzero.oneepr.customer.purchase.CouponUsesRepository;
import de.interzero.oneepr.customer.purchase.ShoppingCart;
import jakarta.persistence.criteria.Predicate;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ts-legacy This service replicates the original NestJS CouponService logic 1:1.
 */
@Service
@RequiredArgsConstructor
public class CouponService {

    private final CouponRepository couponRepository;

    private final CouponUsesRepository couponUsesRepository;

    private final ContractRepository contractRepository;

    private final CustomerRepository customerRepository;

    private final ObjectMapper objectMapper;

    private static final String ERROR_COUPON_NOT_FOUND = "Coupon not found";

    /**
     * Creates a new coupon.
     *
     * @param dto DTO containing coupon data
     * @return created Coupon
     */
    @Transactional
    @SuppressWarnings("java:S3776")
    public Coupon create(CreateCouponDto dto) {
        if (isCustomerCoupon(dto)) {
            InviteCustomersManager settings = loadInviteCustomerSettings();

            // @ts-legacy these validation checks should be extracted
            if (dto.getLink() != null && Boolean.FALSE.equals(settings.getIsLinkEnabled())) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Link is not enabled");
            }

            if (dto.getCode() != null && Boolean.FALSE.equals(settings.getIsVoucherEnabled())) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Coupon by code is not enabled");
            }

            String pattern = settings.getLinkPattern();
            if (dto.getLink() != null && pattern != null && !dto.getLink().startsWith(pattern)) {
                throw new ResponseStatusException(
                        HttpStatus.BAD_REQUEST,
                                                  "Invalid link pattern, please start the coupon link with " + pattern);
            }

            String prefix = settings.getCouponPrefix();
            if (dto.getCode() != null && prefix != null && !dto.getCode().startsWith(prefix)) {
                dto.setCode(prefix + dto.getCode());
            }

            if (dto.getMinAmount() != null && settings.getMinimumOrderValue() != null && dto.getMinAmount() < settings.getMinimumOrderValue()) {
                throw new ResponseStatusException(
                        HttpStatus.BAD_REQUEST,
                                                  "Minimum order value from settings is " + settings.getMinimumOrderValue() + ", but the coupon minimum order value is " + dto.getMinAmount());
            }

            if (dto.getMaxUses() != null && settings.getMaximumReferrals() != null && dto.getMaxUses() > settings.getMaximumReferrals()) {
                throw new ResponseStatusException(
                        HttpStatus.BAD_REQUEST,
                                                  "Maximum referrals from settings is " + settings.getMaximumReferrals() + ", but the coupon maximum referrals is " + dto.getMaxUses());
            }
        }

        Coupon coupon = new Coupon();

        // @ts-legacy preserve inline structure from NestJS create
        ModelMapperConfig.mapPresentFields(dto, coupon);

        if (dto.getPresentFields().contains("shoppingCartId") && dto.getShoppingCartId() != null) {
            ShoppingCart shoppingCart = new ShoppingCart();
            shoppingCart.setId(dto.getShoppingCartId());
            coupon.setShoppingCarts(Set.of(shoppingCart));
        }

        if (dto.getPresentFields().contains("customers") && dto.getCustomers() != null) {
            coupon.setCustomers(mapCustomerIds(dto.getCustomers()));
        }

        return couponRepository.save(coupon);
    }

    /**
     * Finds all coupons that are not soft-deleted.
     *
     * @return list of active (not soft-deleted) coupons
     */
    @Transactional
    public List<Coupon> findAll() {
        return couponRepository.findAllByDeletedAtIsNull();
    }

    /**
     * Finds paginated list of coupons with filtering.
     *
     * @param dto DTO with pagination and optional filtering options
     * @return Map containing filtered coupons, total count, page info
     * @ts-legacy Translated from original NestJS filtering logic.
     */
    @Transactional()
    public Map<String, Object> findAllPaginated(FindAllCouponsPaginatedDto dto) {
        int page = dto.getPage() != null ? dto.getPage() - 1 : 0; // JS 1-based → Java 0-based
        int limit = dto.getLimit() != null ? dto.getLimit() : 10;
        Pageable pageable = PageRequest.of(page, limit);

        Specification<Coupon> specification = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            predicates.add(cb.isNull(root.get("deletedAt")));

            if (dto.getIsActive() != null) {
                predicates.add(cb.equal(root.get("isActive"), Boolean.parseBoolean(dto.getIsActive())));
            }

            if ("link".equals(dto.getLeadType())) {
                predicates.add(cb.isNotNull(root.get("link")));
            } else if ("code".equals(dto.getLeadType())) {
                predicates.add(cb.isNull(root.get("link")));
            }

            if (dto.getCode() != null && !dto.getCode().isBlank()) {
                predicates.add(cb.like(cb.lower(root.get("code")), dto.getCode().toLowerCase() + "%"));
            }

            if (dto.getStartDate() != null && dto.getEndDate() != null) {
                Instant start = Instant.parse(dto.getStartDate());
                Instant end = Instant.parse(dto.getEndDate());
                predicates.add(cb.greaterThanOrEqualTo(root.get("startDate"), start));
                predicates.add(cb.lessThanOrEqualTo(root.get("endDate"), end));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };

        Page<Coupon> pageResult = couponRepository.findAll(specification, pageable);

        return Map.of(
                "coupons",
                pageResult.getContent(),
                "count",
                pageResult.getTotalElements(),
                "pages",
                pageResult.getTotalPages(),
                "current_page",
                page + 1,
                //back to JS 1-based page number
                "limit",
                limit);
    }

    /**
     * Finds a coupon by ID.
     *
     * @param id ID of the coupon
     * @return the found Coupon, or throws 404 if not found or deleted
     */
    @Transactional
    public Coupon findOne(Integer id) {
        return couponRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, ERROR_COUPON_NOT_FOUND));
    }

    /**
     * Finds a coupon by its code (case-sensitive), ignoring soft-deleted ones.
     *
     * @ts-legacy This should throw error if nothing is found
     *
     * @param code the coupon code
     * @return the Coupon if found, otherwise null
     */
    @Transactional
    public Coupon findByCode(String code) {
        return couponRepository.findByCodeAndDeletedAtIsNull(code).orElse(null);
    }

    /**
     * Updates a coupon identified by its ID with the data from DTO.
     * Also validates that no other coupon has the same code.
     *
     * @param id  ID of the coupon to update
     * @param dto DTO with updated fields
     * @return updated Coupon
     */
    @Transactional
    @SuppressWarnings("squid:FunctionCognitiveComplexity") // @ts-legacy: preserving original structure from NestJS
    public Coupon update(Integer id,
                         UpdateCouponDto dto) {
        Coupon existingCoupon = couponRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, ERROR_COUPON_NOT_FOUND));

        // Check if another coupon with the same code exists
        if (dto.getPresentFields()
                .contains("code") && couponRepository.existsByCodeAndDeletedAtIsNullAndIdNot(dto.getCode(), id)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Coupon with same code already exists");
        }
        ModelMapperConfig.mapPresentFields(dto, existingCoupon);

        if (dto.getPresentFields().contains("shoppingCartId")) {
            if (dto.getShoppingCartId() != null) {
                ShoppingCart shoppingCart = new ShoppingCart();
                shoppingCart.setId(dto.getShoppingCartId());
                existingCoupon.setShoppingCarts(Set.of(shoppingCart));
            } else {
                existingCoupon.setShoppingCarts(null);
            }
        }

        if (dto.getPresentFields().contains("customers")) {
            if (existingCoupon.getCustomers() != null) {
                // Clear existing links to customers
                existingCoupon.getCustomers().clear(); // deleteMany
            }
            if (dto.getCustomers() != null && !dto.getCustomers().isEmpty()) {
                List<Customer> customers = customerRepository.findAllById(dto.getCustomers());
                Set<CouponCustomer> links = customers.stream().map(customer -> {
                    CouponCustomer cc = new CouponCustomer();
                    cc.setCustomer(customer);
                    cc.setCoupon(existingCoupon);
                    return cc;
                }).collect(Collectors.toSet());
                existingCoupon.setCustomers(links);
            }
        }

        existingCoupon.setUpdatedAt(Instant.now());

        return couponRepository.save(existingCoupon);
    }

    /**
     * Handles the usage of a coupon by a customer.
     * Tracks coupon usage, sets coupon inactive if it's for commission, and
     * records usage via CouponUses.
     *
     * @param code           the coupon code
     * @param customerId     the ID of the customer using the coupon
     * @param shoppingCartId the ID of the related shopping cart
     * @param orderId        the ID of the order (optional)
     * @return the used Coupon
     */
    @Transactional
    public Coupon useCoupon(String code,
                            Integer customerId,
                            String shoppingCartId,
                            Integer orderId) {

        Coupon coupon = checkCoupon(code, customerId);

        if (!Boolean.TRUE.equals(coupon.getIsActive()) || coupon.getDeletedAt() != null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, ERROR_COUPON_NOT_FOUND);
        }

        boolean isFirstPurchase = contractRepository.findByCustomerIdAndDeletedAtIsNull(customerId).isEmpty();

        if (Boolean.TRUE.equals(coupon.getForCommission())) {
            coupon.setIsActive(false);
            coupon.setUsedAt(Instant.now());
            couponRepository.save(coupon);
        }

        ShoppingCart shoppingCart = new ShoppingCart();
        shoppingCart.setId(shoppingCartId);

        CouponUses couponUses = new CouponUses();
        couponUses.setCoupon(coupon);
        couponUses.setCustomerId(customerId);
        couponUses.setShoppingCart(shoppingCart);
        couponUses.setOrderId(orderId != null ? String.valueOf(orderId) : null);
        couponUses.setIsFirstPurchase(isFirstPurchase);

        couponUsesRepository.save(couponUses);

        return coupon;
    }

    /**
     * Validates coupon eligibility by checking global and per-customer usage limits.
     * Also verifies customer restrictions, if any.
     *
     * @param code       the coupon code
     * @param customerId the customer attempting to use the coupon
     * @return valid Coupon if checks pass
     */
    @Transactional
    public Coupon checkCoupon(String code,
                              Integer customerId) {
        Coupon coupon = couponRepository.findByCodeAndIsActiveTrueAndDeletedAtIsNull(code)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, ERROR_COUPON_NOT_FOUND));

        // @ts-legacy: Incomplete translation — couponUses and customers not yet linked
        int totalUses = 0;
        int customerUses = 0;

        if (coupon.getMaxUses() != null && totalUses >= coupon.getMaxUses()) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid Coupon");
        }

        if (coupon.getMaxUsesPerCustomer() != null && customerUses >= coupon.getMaxUsesPerCustomer()) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid Coupon");
        }

        boolean customerIsRestricted = coupon.getCustomers()
                .stream()
                .map(couponCustomer -> couponCustomer.getCustomer().getId())
                .anyMatch(id -> id.equals(customerId));

        if (customerId != null && !Boolean.TRUE.equals(coupon.getForCommission()) && customerIsRestricted) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid Coupon");
        }


        return coupon;
    }

    /**
     * Soft-deletes a coupon by setting its deletedAt field.
     *
     * @param id ID of the coupon to remove
     * @return the updated Coupon with deletedAt set
     */
    @Transactional
    public Coupon remove(Integer id) {
        Coupon coupon = couponRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, ERROR_COUPON_NOT_FOUND));

        coupon.setDeletedAt(LocalDate.now());

        return couponRepository.save(coupon);
    }

    private Set<CouponCustomer> mapCustomerIds(List<Integer> customerIds) {
        if (customerIds == null) {
            return Set.of();
        }

        return customerIds.stream().map(id -> {
            var customer = new Customer();
            customer.setId(id);
            var cc = new CouponCustomer();
            cc.setCustomer(customer);
            return cc;
        }).collect(Collectors.toSet());
    }

    private InviteCustomersManager parseInviteSettings(Map<String, Object> rawData) {
        try {
            String json = rawData.get("value").toString();
            return objectMapper.readValue(json, InviteCustomersManager.class);
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to parse referral settings", e);
        }
    }

    private boolean isCustomerCoupon(CreateCouponDto dto) {
        return "CUSTOMER".equals(dto.getType().name());
    }

    @SuppressWarnings("unchecked")
    private InviteCustomersManager loadInviteCustomerSettings() {
        // @ts-legacy This call is a placeholder for real settings integration
        Map<String, Object> response = (Map<String, Object>) AdminInterface.admin(
                "/settings/INVITE_CUSTOMERS_MANAGER",
                Map.of(),
                HttpMethod.GET);

        if (response == null || response.get("data") == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Referral settings not found");
        }

        Map<String, Object> data = (Map<String, Object>) response.get("data");
        return parseInviteSettings(data);
    }
}
