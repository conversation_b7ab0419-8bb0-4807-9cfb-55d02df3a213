package de.interzero.oneepr.customer.license_packaging_service.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Get License Packaging Service Turnover DTO
 * Converted from TypeScript license-packaging-service-turnover-dto.ts
 * Maintains exact same structure, variable names, and property names.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class GetLicensePackagingServiceTurnoverDto {

    /**
     * The grouped by period type
     * Equivalent to TypeScript: group_by: "MONTH" | "QUARTER" | "YEAR";
     */
    @JsonProperty("group_by")
    @Pattern(regexp = "^(MONTH|QUARTER|YEAR)$", message = "group_by must be one of: MONTH, QUARTER, YEAR")
    @Schema(
            description = "The grouped by",
            example = "MONTH",
            allowableValues = {"MONTH", "QUARTER", "YEAR"},
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String groupBy;
}
