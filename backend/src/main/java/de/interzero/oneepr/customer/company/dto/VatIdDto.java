package de.interzero.oneepr.customer.company.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Data Transfer Object for VAT ID validation.
 * Converted from TypeScript VatIdDto with exact same field names.
 */
@Schema(description = "Fields required for VAT ID validation")
@Data
public class VatIdDto {

    @JsonProperty("vat_id")
    @Schema(
            description = "The VAT ID to validate",
            example = "DE123456789"
    )
    private String vatId;

    @JsonProperty("country_code")
    @Schema(
            description = "The country code",
            example = "DE"
    )
    private String countryCode;

    @JsonProperty("company_name")
    @Schema(
            description = "The company name",
            example = "Acme Corporation"
    )
    private String companyName;

    @JsonProperty("company_zipcode")
    @Schema(
            description = "The company zip code",
            example = "12345"
    )
    private String companyZipcode;

    @JsonProperty("company_city")
    @Schema(
            description = "The company city",
            example = "Berlin"
    )
    private String companyCity;

    @JsonProperty("company_street")
    @Schema(
            description = "The company street",
            example = "Main Street 123"
    )
    private String companyStreet;
}
