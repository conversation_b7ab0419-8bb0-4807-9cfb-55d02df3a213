package de.interzero.oneepr.customer.customer_phone;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface CustomerPhoneRepository extends JpaRepository<CustomerPhone, Integer> {

    @Query("SELECT cp FROM CustomerPhone cp LEFT JOIN FETCH cp.customer c WHERE cp.id = :id")
    Optional<CustomerPhone> findByIdAndFetchCustomer(@Param("id") Integer id);

    /**
     * Deletes all phone records by the given customer ID.
     *
     * @param customerId the ID of the customer whose phones should be deleted
     */
    void deleteAllByCustomer_Id(Integer customerId);
}
