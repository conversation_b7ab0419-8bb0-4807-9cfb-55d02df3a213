package de.interzero.oneepr.customer.license_third_party_invoice.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.license_third_party_invoice.LicenseThirdPartyInvoice;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CreateLicenseThirdPartyInvoiceDto {

    @NotNull
    @Schema(
            description = "The license ID this invoice pertains to.",
            example = "789",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("license_id")
    private Integer licenseId;

    @NotBlank
    @Schema(
            description = "The title of the invoice (e.g., 'Annual Fee 2025').",
            example = "Annual License Fee 2025",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("title")
    private String title;

    @NotNull
    @Schema(
            description = "The status of the invoice.",
            implementation = LicenseThirdPartyInvoice.Status.class,
            example = "OPEN",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("status")
    private LicenseThirdPartyInvoice.Status status;

    @NotNull
    @Min(
            value = 0,
            message = "Price cannot be negative."
    )
    @Schema(
            description = "The price of the invoice (in cents).",
            example = "10000",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("price")
    private Integer price;

    @NotNull
    @Schema(
            description = "The issuer of the invoice.",
            implementation = LicenseThirdPartyInvoice.Issuer.class,
            example = "OTHER_THIRD_PARTY",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("issuer")
    private LicenseThirdPartyInvoice.Issuer issuer;

    @NotBlank
    @Schema(
            description = "The issued date of the invoice (e.g., '2025-06-01'). Expected format should be parsable to an Instant.",
            example = "2025-06-01T10:00:00Z",
            format = "date-time",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("issued_at")
    private String issuedAt;

    @NotBlank
    @Schema(
            description = "The due date of the invoice (e.g., '2025-07-01'). Expected format should be parsable to an Instant.",
            example = "2025-07-01T10:00:00Z",
            format = "date-time",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("due_date")
    private String dueDate;

    @Schema(
            description = "Optional: The file ID associated with the invoice document.",
            example = "file_xyz789",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("file_id")
    private String fileId;
}