package de.interzero.oneepr.customer.entity;

import de.interzero.oneepr.customer.company.Company;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.time.Instant;
import java.time.LocalDate;

@Getter
@Setter
@Entity
@Table(
        name = "company_contact",
        schema = "public"
)
public class CompanyContact {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "company_contact_id_gen"
    )
    @SequenceGenerator(
            name = "company_contact_id_gen",
            sequenceName = "company_contact_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    private Integer id;

    @OneToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "company_id")
    private Company company;

    @Column(
            name = "name",
            length = Integer.MAX_VALUE
    )
    private String name;

    @Column(
            name = "email",
            length = Integer.MAX_VALUE
    )
    private String email;

    @Column(
            name = "phone_mobile",
            length = Integer.MAX_VALUE
    )
    private String phoneMobile;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    private Instant updatedAt;

    @Column(name = "deleted_at")
    private LocalDate deletedAt;

}