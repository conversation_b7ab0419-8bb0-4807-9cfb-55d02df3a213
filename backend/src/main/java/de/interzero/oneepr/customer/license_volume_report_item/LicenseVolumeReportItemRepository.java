package de.interzero.oneepr.customer.license_volume_report_item;

import de.interzero.oneepr.customer.entity.LicenseVolumeReportItem;
import de.interzero.oneepr.customer.license_volume_report.LicenseVolumeReport;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Repository
public interface LicenseVolumeReportItemRepository extends JpaRepository<LicenseVolumeReportItem, Integer> , JpaSpecificationExecutor<LicenseVolumeReportItem> {

    List<LicenseVolumeReportItem> findByLicenseVolumeReportAndDeletedAtIsNull(LicenseVolumeReport volumeReport);

    List<LicenseVolumeReportItem> findByLicenseVolumeReport(LicenseVolumeReport licenseVolumeReport);

    @Modifying
    @Query("UPDATE LicenseVolumeReportItem lvi SET lvi.value = :value, lvi.updatedAt = :now WHERE lvi.setupColumnCode = :setupColumnCode AND lvi.setupFractionCode = :setupFractionCode")
    void updateValueBySetupColumnCodeAndSetupFractionCode(String setupColumnCode,
                                                          String setupFractionCode,
                                                          Integer value,
                                                          Instant now);

    Optional<LicenseVolumeReportItem> findByIdAndDeletedAtIsNull(Integer id);

    List<LicenseVolumeReportItem> findAll(Object o);
}
