package de.interzero.oneepr.customer.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.time.LocalDate;

@Getter
@Setter
@Entity
@Table(
        name = "partner",
        schema = "public"
)
public class Partner {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "partner_id_gen"
    )
    @SequenceGenerator(
            name = "partner_id_gen",
            sequenceName = "partner_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    private Integer id;

    @NotNull
    @Column(
            name = "first_name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String firstName;

    @NotNull
    @Column(
            name = "last_name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String lastName;

    @NotNull
    @Column(
            name = "email",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String email;

    @NotNull
    @Column(
            name = "user_id",
            nullable = false
    )
    private Integer userId;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    private Instant updatedAt;

    @Column(name = "deleted_at")
    private LocalDate deletedAt;

    @Column(
            name = "commission_mode",
            length = Integer.MAX_VALUE
    )
    private String commissionMode;

    @Column(name = "commission_percentage")
    private Integer commissionPercentage;

    @Column(name = "no_provision_negotiated")
    private Boolean noProvisionNegotiated;

    @Column(
            name = "payout_cycle",
            length = Integer.MAX_VALUE
    )
    private String payoutCycle;

}