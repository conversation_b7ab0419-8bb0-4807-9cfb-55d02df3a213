package de.interzero.oneepr.customer.company;


import de.interzero.oneepr.customer.company.dto.CreateCompanyDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CompanyService {

    /**
     * TODO It's a placeholder for the real create function
     * @param dto
     * @return
     */
    public Company create(CreateCompanyDto dto) {
        Company company = new Company();
        return company;
    }
}
