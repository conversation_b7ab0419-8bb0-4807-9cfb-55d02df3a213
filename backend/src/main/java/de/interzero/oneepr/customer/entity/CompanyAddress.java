package de.interzero.oneepr.customer.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.time.LocalDate;

@Getter
@Setter
@Entity
@Table(
        name = "company_address",
        schema = "public"
)
public class CompanyAddress {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "company_address_id_gen"
    )
    @SequenceGenerator(
            name = "company_address_id_gen",
            sequenceName = "company_address_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    private Integer id;

    @NotNull
    @Column(
            name = "country_code",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String countryCode;

    @NotNull
    @Column(
            name = "address_line",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String addressLine;

    @NotNull
    @Column(
            name = "city",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String city;

    @NotNull
    @Column(
            name = "zip_code",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String zipCode;

    @NotNull
    @Column(
            name = "street_and_number",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String streetAndNumber;

    @NotNull
    @Column(
            name = "additional_address",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String additionalAddress;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    private Instant updatedAt;

    @Column(name = "deleted_at")
    private LocalDate deletedAt;

}