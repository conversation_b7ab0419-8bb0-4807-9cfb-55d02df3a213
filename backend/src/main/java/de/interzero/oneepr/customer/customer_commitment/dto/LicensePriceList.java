package de.interzero.oneepr.customer.customer_commitment.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class LicensePriceList extends BasePriceList {

    @JsonProperty("basic_price")
    private Double basicPrice;

    @JsonProperty("minimum_price")
    private Double minimumPrice;

    @JsonProperty("registration_fee")
    private Double registrationFee;

    @JsonProperty("handling_fee")
    private Double handlingFee;

    @JsonProperty("variable_handling_fee")
    private Double variableHandlingFee;

    public LicensePriceList() {
        super();
        this.setServiceType(ServiceType.EU_LICENSE);
    }
}
