package de.interzero.oneepr.customer.purchase;

import de.interzero.oneepr.common.MessageResponse;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.customer.purchase.dto.PurchaseDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(
        name = "Purchase",
        description = "Operations related to purchases"
)
@SecurityRequirement(name = "bearerAuth")
@RestController
@RequestMapping(Api.PURCHASE)
@RequiredArgsConstructor
public class PurchaseController {

    private final PurchaseService purchaseService;

    @Operation(summary = "Purchase services")
    @PostMapping
    public MessageResponse create(@io.swagger.v3.oas.annotations.parameters.RequestBody(
            required = true,
            content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = PurchaseDto.class)
            )
    ) @RequestBody PurchaseDto dto) {
        return purchaseService.create(dto);
    }
}
