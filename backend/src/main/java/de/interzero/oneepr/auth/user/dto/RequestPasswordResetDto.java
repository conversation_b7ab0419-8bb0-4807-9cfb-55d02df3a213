package de.interzero.oneepr.auth.user.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Schema(description = "DTO for requesting a password reset")
@Data
@EqualsAndHashCode(callSuper = true)
public class RequestPasswordResetDto extends BaseDto {

    @JsonProperty("email")
    @Schema(
            description = "User email",
            example = "<EMAIL>"
    )
    private String email;

    @JsonProperty("callbackUrl")
    @Schema(
            description = "Callback URL for reset confirmation",
            example = "https://app.in-deutsch.de/reset"
    )
    private String callbackUrl;
}
