package de.interzero.oneepr.auth.user;

import de.interzero.oneepr.auth.auth.dto.LoginResponse;
import de.interzero.oneepr.auth.role.Role;
import de.interzero.oneepr.auth.user.dto.*;
import de.interzero.oneepr.auth.user.dto.email.EmailCallbackDto;
import de.interzero.oneepr.auth.user.dto.email.EmailDto;
import de.interzero.oneepr.auth.user.dto.email.EmailResendTokenDto;
import de.interzero.oneepr.auth.user.dto.email.EmailTokenDto;
import de.interzero.oneepr.common.string.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

import static de.interzero.oneepr.common.string.Role.*;

/**
 * REST Controller for managing users.
 * Provides endpoints for creating, retrieving, updating, and deleting users,
 * as well as handling password resets and email verifications.
 */
@Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER, MARKETING_MANAGER})
@Tag(
        name = "User",
        description = "APIs for managing users"
)
@RestController
@RequestMapping(Api.USER)
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;

    @PostMapping
    @Operation(summary = "Create user")
    @ApiResponse(
            responseCode = "200",
            description = "User created successfully"
    )
    public ResponseEntity<UserResponseDto> create(@Valid @RequestBody CreateUserDto dto,
                                                  @RequestParam(
                                                          name = "create_by_admin",
                                                          required = false
                                                  ) String createByAdmin) {
        User user = userService.create(dto, createByAdmin);
        return ResponseEntity.ok(UserMapper.toDto(user));
    }

    @GetMapping("/all-roles")
    @Operation(summary = "Get all available roles")
    @ApiResponse(
            responseCode = "200",
            description = "List of roles returned"
    )
    public ResponseEntity<List<Role>> findAllRoles() {
        return ResponseEntity.ok(userService.findAllRoles());
    }

    @GetMapping
    @Operation(summary = "Get all users with filters")
    @ApiResponse(
            responseCode = "200",
            description = "List of users returned"
    )
    public ResponseEntity<List<UserResponseDto>> findAll(@RequestParam(
                                                                 name = "is_active",
                                                                 required = false
                                                         ) String isActive,
                                                         @RequestParam(
                                                                 name = "role",
                                                                 required = false
                                                         ) String role,
                                                         @RequestParam(
                                                                 name = "name",
                                                                 required = false
                                                         ) String name,
                                                         @RequestParam(
                                                                 name = "ids",
                                                                 required = false
                                                         ) String ids) {
        UserFindAllDto dto = new UserFindAllDto();
        dto.setIsActive(isActive);
        dto.setRole(role);
        dto.setName(name);
        dto.setIds(ids);
        List<User> users = userService.findAll(dto);
        return ResponseEntity.ok(UserMapper.toDtoList(users));
    }

    @GetMapping("/{idOrMail}")
    @Operation(summary = "Get user by ID or email")
    @ApiResponse(
            responseCode = "200",
            description = "User returned successfully"
    )
    public ResponseEntity<UserResponseDto> findOne(@Parameter(description = "ID or email of the user") @PathVariable String idOrMail) {
        User user = userService.findOne(idOrMail);
        return ResponseEntity.ok(UserMapper.toDto(user));
    }

    @GetMapping("/status/{email}")
    @Operation(summary = "Get user status by email")
    @ApiResponse(
            responseCode = "200",
            description = "User status returned"
    )
    public ResponseEntity<UserStatusResponse> findStatusByEmail(@Parameter(description = "Email of the user") @PathVariable String email) {
        return ResponseEntity.ok(userService.findStatusByEmail(email));
    }

    @PatchMapping("/{id}")
    @Operation(summary = "Update user by ID")
    @ApiResponse(
            responseCode = "200",
            description = "User updated successfully"
    )
    public ResponseEntity<UserResponseDto> update(@Parameter(description = "ID of the user to update") @PathVariable Integer id,
                                                  @RequestBody UpdateUserDto dto) {
        User user = userService.update(id, dto);
        return ResponseEntity.ok(UserMapper.toDto(user));
    }

    @PatchMapping("/by-email/{email}")
    @Operation(summary = "Update user by email")
    @ApiResponse(
            responseCode = "200",
            description = "User updated successfully"
    )
    public ResponseEntity<UserResponseDto> updateByEmail(@Parameter(description = "Email of the user") @PathVariable String email,
                                                         @RequestBody UpdateUserDto dto) {
        User user = userService.updateByEmail(email, dto);
        return ResponseEntity.ok(UserMapper.toDto(user));
    }

    @PatchMapping("/change-password/{id}")
    @Operation(summary = "Change user password")
    @ApiResponse(
            responseCode = "200",
            description = "Password changed successfully"
    )
    public ResponseEntity<UserResponseDto> updatePassword(@Parameter(description = "User ID") @PathVariable Integer id,
                                                          @RequestBody UpdatePasswordDto dto) {
        User user = userService.updatePassword(id, dto);
        return ResponseEntity.ok(UserMapper.toDto(user));
    }

    @PatchMapping("/change-email/{id}")
    @Operation(summary = "Change user email")
    @ApiResponse(
            responseCode = "200",
            description = "Email change initiated"
    )
    public ResponseEntity<Map<String, String>> updateEmail(@Parameter(description = "User ID") @PathVariable Integer id,
                                                           @RequestBody UpdateEmailDto dto) {
        return ResponseEntity.ok(userService.updateEmail(id, dto));
    }

    @PatchMapping("/change-email/verify/{id}")
    @Operation(summary = "Verify updated email")
    @ApiResponse(
            responseCode = "200",
            description = "Email verified successfully"
    )
    public ResponseEntity<UserResponseDto> updateVerifyEmail(@Parameter(description = "User ID") @PathVariable Integer id,
                                                             @RequestBody UpdateEmailVerifyDto dto) {
        User user = userService.updateVerifyEmail(id, dto);
        return ResponseEntity.ok(UserMapper.toDto(user));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete user by ID")
    @ApiResponse(
            responseCode = "204",
            description = "User deleted successfully"
    )
    public ResponseEntity<Object> remove(@Parameter(description = "ID of the user to delete") @PathVariable Integer id) {
        userService.remove(id);
        return ResponseEntity.noContent().build();
    }

    @PostMapping("/request/password")
    @Operation(summary = "Request password reset")
    @ApiResponse(
            responseCode = "200",
            description = "Password reset link sent"
    )
    public ResponseEntity<RequestAccessResponse> requestPasswordReset(@RequestBody RequestPasswordResetDto dto) {
        return ResponseEntity.ok(userService.requestPasswordReset(dto.getEmail(), dto.getCallbackUrl()));
    }

    @PostMapping("/reset/password")
    @Operation(summary = "Reset password")
    @ApiResponse(
            responseCode = "200",
            description = "Password reset successfully"
    )
    public ResponseEntity<UserResponseDto> resetPassword(@RequestBody ResetPasswordDto dto) {
        User user = userService.resetPassword(dto);
        return ResponseEntity.ok(UserMapper.toDto(user));
    }

    @PostMapping("/request/access")
    @Operation(summary = "Request access")
    @ApiResponse(
            responseCode = "200",
            description = "Access requested successfully"
    )
    public ResponseEntity<RequestAccessResponse> requestAccess(@RequestBody RequestAccessDto dto) {
        return ResponseEntity.ok(userService.requestAccess(dto));
    }

    @PostMapping("/account/verify/sendToken")
    @Operation(summary = "Send account verification token")
    @ApiResponse(
            responseCode = "200",
            description = "Verification token sent"
    )
    public ResponseEntity<Map<String, String>> sendVerificationToken(@RequestBody EmailCallbackDto dto) {
        return ResponseEntity.ok(userService.sendVerificationToken(dto.getEmail(), dto.getCallbackUrl()));
    }

    @PostMapping("/account/verify/confirmToken")
    @Operation(summary = "Confirm account verification token")
    @ApiResponse(
            responseCode = "200",
            description = "Token verified successfully"
    )
    public ResponseEntity<LoginResponse> confirmVerificationToken(@RequestBody EmailTokenDto dto) {
        return ResponseEntity.ok(userService.confirmVerificationToken(dto));
    }

    @PostMapping("/account/verify/magic-link")
    @Operation(summary = "Confirm verification via magic link")
    @ApiResponse(
            responseCode = "200",
            description = "Magic link verified successfully"
    )
    public ResponseEntity<LoginResponse> confirmVerificationMagicLink(@RequestBody MagicLinkDto dto) {
        return ResponseEntity.ok(userService.confirmVerificationMagicLink(dto));
    }

    @PostMapping("/account/create-password")
    @Operation(summary = "Create password for account")
    @ApiResponse(
            responseCode = "200",
            description = "Password created successfully"
    )
    public ResponseEntity<LoginResponse> createPassword(@RequestBody CreatePasswordDto dto) {
        return ResponseEntity.ok(userService.createPassword(dto));
    }

    @PostMapping("/account/verify/resend-token")
    @Operation(summary = "Resend verification token")
    @ApiResponse(
            responseCode = "200",
            description = "Token resent successfully"
    )
    public ResponseEntity<Map<String, String>> resendVerificationToken(@RequestBody EmailResendTokenDto dto) {
        return ResponseEntity.ok(userService.resendVerificationToken(dto.getEmail(), dto.getTokenMagicLink()));
    }

    @PostMapping("/request/email")
    @Operation(summary = "Get email of user")
    @ApiResponse(
            responseCode = "200",
            description = "Email returned"
    )
    public ResponseEntity<Map<String, String>> getUserByEmail(@RequestBody EmailDto dto) {
        return ResponseEntity.ok(userService.getEmail(dto.getEmail()));
    }
}
