package de.interzero.oneepr.auth.user;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;

@Getter
@Setter
@Entity
@Table(
        name = "password_reset_request",
        schema = "public"
)
public class PasswordResetRequest {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "password_reset_request_id_gen"
    )
    @SequenceGenerator(
            name = "password_reset_request_id_gen",
            sequenceName = "password_reset_request_id_seq",
            allocationSize = 1
    )
    @JsonProperty("id")
    @Column(
            name = "id",
            nullable = false
    )
    private Integer id;

    @JsonProperty("email")
    @Column(
            name = "email",
            nullable = false
    )
    private String email;

    @JsonProperty("user")
    @JsonIgnore
    @ManyToOne
    @JoinColumn(
            name = "user_id",
            nullable = false
    )
    private User user;

    @Transient
    @JsonProperty("user_id")
    public Integer getUserId() {
        return user != null ? user.getId() : null;
    }

    @JsonProperty("token")
    @Column(
            name = "token",
            nullable = false
    )
    private String token;

    @JsonProperty("status")
    @Column(
            name = "status",
            nullable = false
    )
    private String status = "PENDING";

    @JsonProperty("callback_url")
    @Column(name = "callback_url")
    private String callbackUrl;

    @JsonProperty("expires_at")
    @Column(name = "expires_at")
    private Instant expiresAt;
}
