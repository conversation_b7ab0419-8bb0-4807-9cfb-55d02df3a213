package de.interzero.oneepr.auth.user.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "Response returned after successful authentication")
@Data
public class ConfirmVerificationTokenResponse {

    @Schema(
            description = "Access token (e.g., JWT)",
            example = "eyJhbGciOiJIUzI1NiIsInR..."
    )
    @JsonProperty("access_token")
    private String accessToken;

    @Schema(
            description = "Refresh token (if applicable)",
            example = "dGhpc2lzYXJlZnJlc2h0b2tlbg==",
            nullable = true
    )
    @JsonProperty("refresh_token")
    private String refreshToken;

}
