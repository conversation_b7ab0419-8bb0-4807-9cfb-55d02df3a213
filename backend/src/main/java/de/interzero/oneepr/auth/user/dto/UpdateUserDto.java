package de.interzero.oneepr.auth.user.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Fields required to update a user.
 * Inherits all fields from CreateUserDto. All fields are optional.
 */
@Schema(description = "Fields required to update a user")
@Data
@EqualsAndHashCode(callSuper = true)
public class UpdateUserDto extends CreateUserDto {

}
