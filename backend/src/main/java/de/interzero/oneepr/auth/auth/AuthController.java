package de.interzero.oneepr.auth.auth;

import de.interzero.oneepr.auth.auth.dto.LoginResponse;
import de.interzero.oneepr.auth.auth.dto.RefreshTokenRequest;
import de.interzero.oneepr.common.string.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Slf4j
@Tag(
        name = "Auth",
        description = "Operations related to Authentication / Authorization"
)
@RestController
@RequestMapping(Api.AUTH)
@RequiredArgsConstructor
public class AuthController {

    private final TokenService tokenService;


    @SecurityRequirement(name = "basicAuth")
    @Operation(
            summary = "Generate a JWT token (access and refresh token) for the given user",
            description = "Uses BasicAuth to authenticate the user."
    )
    @PostMapping("/login")
    public LoginResponse login(Authentication authentication) {
        log.debug("Token requested for user: '{}'", authentication.getName());
        if (authentication.getPrincipal() instanceof UserDetails userDetails) {
            LoginResponse loginResponse = tokenService.login(userDetails, true);
            log.debug("Token granted: {}", loginResponse);
            return loginResponse;
        } else {
            throw new InvalidLoginException();
        }
    }

    @Operation(summary = "Refresh the access token with the refresh token")
    @PostMapping("/refresh/token")
    public LoginResponse refresh(@Valid @RequestBody RefreshTokenRequest refreshTokenRequest) {
        log.debug("Refresh Token requested");
        return tokenService.validateRefreshTokenAndGenerateAccessToken(refreshTokenRequest.refreshToken());
    }

    @Operation(summary = "Check if the token is valid")
    @GetMapping("/status")
    public ResponseEntity<Boolean> status(@RequestHeader(
            value = "Authorization",
            required = false
    ) String authorization) {
        log.debug("Status requested");
        return ResponseEntity.ok(tokenService.status(authorization));
    }

    @Operation(summary = "Logout the user")
    @PostMapping("/logout")
    public ResponseEntity<Boolean> logout(@RequestHeader(
            value = "Authorization",
            required = false
    ) String authorization) {
        log.debug("Logout requested with authorization: '{}'", authorization);
        tokenService.logout(authorization);
        return ResponseEntity.ok(true);
    }
}
