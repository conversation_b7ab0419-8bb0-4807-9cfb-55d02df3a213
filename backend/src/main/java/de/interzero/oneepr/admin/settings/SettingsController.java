package de.interzero.oneepr.admin.settings;

import de.interzero.oneepr.admin.settings.dto.UpsertSettingDto;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.Role;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST controller for managing application settings.
 */
@RestController
@RequestMapping(Api.SETTINGS)
@Tag(name = "Settings")
@RequiredArgsConstructor
@Secured({Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK})
public class SettingsController {

    private final SettingsService settingsService;

    /**
     * Handles the HTTP GET request to retrieve all settings.
     *
     * @return A list of all {@link Settings} entities.
     */
    @GetMapping
    @Operation(summary = "Get all settings")
    @ApiResponse(
            responseCode = "200",
            description = "Settings retrieved successfully"
    )
    public List<Settings> findAll() {
        return settingsService.findAll();
    }

    /**
     * Handles the HTTP GET request to retrieve a single setting by its key.
     *
     * @param key The unique key of the setting.
     * @return The found {@link Settings} entity.
     */
    @GetMapping("/{key}")
    @Operation(summary = "Get setting by key")
    @ApiResponse(
            responseCode = "200",
            description = "Setting retrieved successfully"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Setting not found"
    )
    public Settings findOne(@PathVariable String key) {
        return settingsService.findOne(key);
    }

    /**
     * Handles the HTTP PUT request to create or update (upsert) a setting.
     *
     * @param key The unique key of the setting.
     * @param dto The DTO containing the data for the setting.
     * @return The created or updated {@link Settings} entity.
     */
    @PutMapping("/{key}")
    @Operation(summary = "Upsert setting by key")
    @ApiResponse(
            responseCode = "200",
            description = "Setting upserted successfully"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Setting not found"
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid value or request"
    )
    public Settings upsert(@PathVariable String key,
                           @RequestBody UpsertSettingDto dto) {
        return settingsService.upsert(key, dto);
    }

    /**
     * Handles the HTTP DELETE request to remove a setting by its key.
     * The original NestJS endpoint was @Delete(":id") but used @Param("key").
     * This has been corrected to use the key in the path for consistency.
     *
     * @param key The unique key of the setting to delete.
     * @return A response entity indicating the success of the operation.
     */
    @DeleteMapping("/{key}")
    @Operation(summary = "Delete setting by key")
    @ApiResponse(
            responseCode = "200",
            description = "Setting deleted successfully"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Setting not found"
    )
    public ResponseEntity<Void> remove(@PathVariable String key) {
        settingsService.remove(key);
        return ResponseEntity.ok().build();
    }
}
