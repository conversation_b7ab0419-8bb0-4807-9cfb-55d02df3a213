package de.interzero.oneepr.admin.packaging_service;

import de.interzero.oneepr.admin.packaging_service.dto.CreatePackagingServiceDto;
import de.interzero.oneepr.admin.packaging_service.dto.UpdatePackagingServiceDto;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.Role;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST controller for managing packaging services.
 */
@RestController
@RequestMapping(Api.PACKAGING_SERVICES)
@Tag(name = "Packaging Services")
@RequiredArgsConstructor
@Secured({Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK})
public class PackagingServiceController {

    private final PackagingServiceService packagingServiceService;

    /**
     * Creates a new packaging service.
     *
     * @param data The DTO containing the data for the new packaging service.
     * @return The newly created {@link PackagingService} entity.
     */
    @PostMapping
    @Operation(summary = "Create a new packaging service")
    @ApiResponse(
            responseCode = "201",
            description = "Packaging service created successfully"
    )
    @ApiResponse(
            responseCode = "400",
            description = "Bad Request"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Country not found"
    )
    public ResponseEntity<PackagingService> create(@RequestBody CreatePackagingServiceDto data) {
        PackagingService createdService = packagingServiceService.create(data);
        return new ResponseEntity<>(createdService, HttpStatus.CREATED);
    }

    /**
     * Retrieves all non-deleted packaging services, with an optional filter for country ID.
     *
     * @param countryId Optional ID of the country to filter results by.
     * @return A list of {@link PackagingService} objects.
     */
    @GetMapping
    @Operation(summary = "Get all packaging services")
    @ApiResponse(
            responseCode = "200",
            description = "Packaging services retrieved successfully"
    )
    public ResponseEntity<List<PackagingService>> findAll(@Parameter(description = "ID of the country to filter by") @RequestParam(
            name = "countryId",
            required = false
    ) Integer countryId) {
        List<PackagingService> services = packagingServiceService.findAll(countryId);
        return ResponseEntity.ok(services);
    }

    /**
     * Retrieves a specific packaging service by its ID.
     *
     * @param id The ID of the packaging service to retrieve.
     * @return The requested {@link PackagingService} object.
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get packaging service by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Packaging service retrieved successfully"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Packaging service not found"
    )
    public ResponseEntity<PackagingService> findOne(@PathVariable String id) {
        PackagingService service = packagingServiceService.findOne(Integer.valueOf(id));
        return ResponseEntity.ok(service);
    }

    /**
     * Updates an existing packaging service.
     *
     * @param id   The ID of the packaging service to update.
     * @param data DTO containing the fields to update.
     * @return The updated {@link PackagingService} object.
     */
    @PutMapping("/{id}")
    @Operation(summary = "Update packaging service by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Packaging service updated successfully"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Packaging service not found"
    )
    public ResponseEntity<PackagingService> update(@PathVariable String id,
                                                   @RequestBody UpdatePackagingServiceDto data) {
        PackagingService updatedService = packagingServiceService.update(Integer.valueOf(id), data);
        return ResponseEntity.ok(updatedService);
    }

    /**
     * Soft-deletes a packaging service by its ID.
     *
     * @param id The ID of the packaging service to delete.
     * @return The other cost object with its deletion date set.
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete packaging service by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Packaging service deleted successfully"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Packaging service not found"
    )
    public ResponseEntity<PackagingService> remove(@PathVariable String id) {
        PackagingService removedService = packagingServiceService.remove(Integer.valueOf(id));
        return ResponseEntity.ok(removedService);
    }
}
