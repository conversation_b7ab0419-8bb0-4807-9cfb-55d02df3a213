package de.interzero.oneepr.admin.upload_data;

import de.interzero.oneepr.admin.entity.UploadFileHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UploadFileHistoryRepository extends JpaRepository<UploadFileHistory, Integer>{

    List<UploadFileHistory> findAllByBrokerIdAndDeletedAtIsNull(int id);
}
