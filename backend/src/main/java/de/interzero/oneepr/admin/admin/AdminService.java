package de.interzero.oneepr.admin.admin;

import de.interzero.oneepr.admin.admin.dto.CreateAdminDto;
import de.interzero.oneepr.admin.admin.dto.FindAllAdminDto;
import de.interzero.oneepr.admin.admin.dto.UpdateAdminDto;
import de.interzero.oneepr.auth.user.User;
import de.interzero.oneepr.auth.user.UserService;
import de.interzero.oneepr.auth.user.dto.CreateUserDto;
import de.interzero.oneepr.auth.user.dto.UpdateUserDto;
import de.interzero.oneepr.auth.user.dto.UserFindAllDto;
import de.interzero.oneepr.common.string.Role;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
public class AdminService {

    private final UserService userService;

    /**
     * Creates a new admin user using the provided admin creation DTO.
     * Delegates the actual user creation to {@link UserService}.
     * Automatically sets `is_active` to true and `type` to "ADMIN".
     *
     * @param createAdminDto the DTO containing email, name, password, and role ID
     * @return the created user object
     */
    public User create(CreateAdminDto createAdminDto) {
        CreateUserDto dto = new CreateUserDto();
        dto.setEmail(createAdminDto.getEmail());
        dto.setName(createAdminDto.getName());
        dto.setPassword(createAdminDto.getPassword());
        dto.setRoleId(createAdminDto.getRoleId());
        dto.setIsActive(true);
        dto.setType("ADMIN");

        return userService.create(dto, "true");
    }

    /**
     * Retrieve all admin users with optional filters (role, name, isActive).
     * This method wraps the {@link UserService#findAll(UserFindAllDto)} and
     * injects default admin roles if not explicitly specified.
     *
     * @param query the filter parameters for admin retrieval
     * @return list of admin users
     */
    public List<User> findAll(FindAllAdminDto query) {
        UserFindAllDto userFindAllDto = new UserFindAllDto();

        String roles = query.getRole() != null && !query.getRole().isBlank() ? query.getRole() : String.join(
                ",",
                List.of(
                        Role.SUPER_ADMIN,
                        Role.ADMIN,
                        Role.CLERK,
                        Role.MARKETING_MANAGER,
                        Role.BROKER_MANAGER));

        userFindAllDto.setRole(roles);
        userFindAllDto.setIsActive(query.getIsActive());
        userFindAllDto.setName(query.getName());

        return userService.findAll(userFindAllDto);
    }

    /**
     * Retrieve a specific admin user by ID or email.
     * Wraps {@link UserService#findOne(String)} and throws a consistent exception message.
     *
     * @param idOrEmail ID or email of the user
     * @return admin user if found
     * @throws IllegalArgumentException if user not found
     */
    public User findOne(String idOrEmail) {
        try {
            User user = userService.findOne(idOrEmail);

            if (user == null || user.getRole() == null) {
                throw new IllegalArgumentException("Admin user not found");
            }

            return user;
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Admin user not found");
        }
    }

    /**
     * Update an admin user by ID.
     * <p>
     * This method receives {@link UpdateAdminDto}, transforms it into {@link UpdateUserDto},
     * and delegates the update operation to {@link de.interzero.oneepr.auth.user.UserService}.
     *
     * @param id  the ID of the admin user to update
     * @param dto admin-specific update DTO
     * @return a map containing the updated user information (without password)
     * @throws org.springframework.web.server.ResponseStatusException if user not found or email already exists
     */
    public User update(Integer id,
                       UpdateAdminDto dto) {
        try {
            UpdateUserDto updateUserDto = new UpdateUserDto();

            updateUserDto.setEmail(dto.getEmail());
            updateUserDto.setName(dto.getName());
            updateUserDto.setPassword(dto.getPassword());
            updateUserDto.setRoleId(dto.getRoleId());
            updateUserDto.setType("ADMIN");

            return userService.update(id, updateUserDto);

        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Admin user not found");
        }
    }

    /**
     * Deletes an admin user by ID.
     * <p>
     * This method delegates the deletion to {@link de.interzero.oneepr.auth.user.UserService}.
     * If the user with the given ID does not exist, a {@link org.springframework.web.server.ResponseStatusException}
     * with status 404 is thrown.
     *
     * @param id ID of the admin user to delete
     */
    public Integer remove(Integer id) {
        try {
            userService.remove(id);
            return id;
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Admin user not found");
        }
    }
}
