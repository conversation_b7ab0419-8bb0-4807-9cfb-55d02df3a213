package de.interzero.oneepr.admin.upload_data;

import de.interzero.oneepr.admin.entity.BrokerCompany;
import de.interzero.oneepr.admin.entity.BrokerCompanyOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface BrokerCompanyOrderRepository extends JpaRepository<BrokerCompanyOrder, Integer> {

    Optional<BrokerCompanyOrder> findFirstByCompanyAndYear(BrokerCompany company,
                                                           Integer year);

}