package de.interzero.oneepr.admin.report_set_column_fractions;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for managing {@link ReportSetColumnFraction} entities.
 */
@Repository
public interface ReportSetColumnFractionRepository extends JpaRepository<ReportSetColumnFraction, Integer> {

    /**
     * Finds all non-deleted report set column fractions.
     *
     * @return A list of entities.
     */
    List<ReportSetColumnFraction> findAllByDeletedAtIsNull();

    /**
     * Finds a single non-deleted report set column fraction by its ID.
     *
     * @param id The ID of the entity.
     * @return An optional containing the found entity.
     */
    @NonNull
    Optional<ReportSetColumnFraction> findByIdAndDeletedAtIsNull(@NonNull Integer id);


    @Modifying
    @Query("DELETE FROM ReportSetColumnFraction f WHERE f.reportSetColumn.code in :codes")
    void deleteReportSetColumnFractionsByColumnCodeIn(List<String> codes);
}