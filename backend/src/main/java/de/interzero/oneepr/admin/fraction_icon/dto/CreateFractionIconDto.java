package de.interzero.oneepr.admin.fraction_icon.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Data Transfer Object for creating a new fraction icon.
 * It contains the ID of the file to be associated as the icon.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CreateFractionIconDto extends BaseDto {

    @JsonProperty("file_id")
    @Schema(
            description = "File ID of the fraction icon",
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = "file_abc123xyz789"
    )
    private String fileId;
}
