package de.interzero.oneepr.admin.upload_data;

import de.interzero.oneepr.admin.entity.UploadFileHistory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

import static de.interzero.oneepr.common.string.Api.UPLOAD_DATA;

@Tag(name = "Upload Data")
@RestController
@RequestMapping(UPLOAD_DATA)
@RequiredArgsConstructor
public class UploadDataController {

    private static final String EXAMPLE = """
            [
              {
                "id": 1,
                "fileName": "example.txt",
                "uploadedAt": "2023-01-01T00:00:00Z",
                "size": 1024
              }
            ]
            """;

    private final UploadDataService uploadDataService;

    @GetMapping
    @Operation(summary = "Retrieve all upload data by broker ID")
    @Parameter(
            name = "id",
            required = true,
            description = "The ID of the broker",
            example = "1"
    )
    @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved upload data",
            content = @Content(
                    mediaType = "application/json",
                    examples = @ExampleObject(
                            value = EXAMPLE
                    )
            )
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid ID provided"
    )
    public List<UploadFileHistory> findAll(@RequestParam("id") String id) {
        return uploadDataService.findAll(Integer.parseInt(id));
    }

    @PostMapping("/preview")
    @Operation(summary = "Upload an Excel file with orders and specify company_id")
    @ApiResponse(
            responseCode = "201",
            description = "File uploaded and processed successfully"
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid file format or missing broker_id"
    )
    @SuppressWarnings({"java:S3740", "java:S2259"})
    public List<?> preview(@RequestParam("file") MultipartFile file,
                          @RequestParam("type") String type) throws IOException {
        if (file == null || file.getOriginalFilename() == null || !file.getOriginalFilename()
                .matches(".*\\.(xls|xlsx)$")) {
            throw new IllegalArgumentException("Only Excel files are allowed!");
        }
        return uploadDataService.preview(file, type);
    }
}
