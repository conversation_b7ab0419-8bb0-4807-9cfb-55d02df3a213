package de.interzero.oneepr.admin.price_list;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.report_set_fractions.ReportSetFraction;
import de.interzero.oneepr.admin.report_set_price_list.ReportSetPriceList;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.time.Instant;


@Getter
@Setter
@Entity
@Table(
        name = "report_set_price_list_item",
        schema = "public"
)
public class ReportSetPriceListItem {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @Column(
            name = "price",
            nullable = false
    )
    @JsonProperty("price")
    private Integer price;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false,
            updatable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private Instant deletedAt;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "price_list_id",
            nullable = false
    )
    @JsonIgnore
    @JsonProperty("price_list")
    private ReportSetPriceList priceList;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "fraction_code",
            referencedColumnName = "code",
            nullable = false
    )
    @JsonIgnore
    @JsonProperty("fraction")
    private ReportSetFraction reportSetFraction;

    @PrePersist
    protected void onCreate() {
        this.createdAt = this.updatedAt = Instant.now();
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = Instant.now();
    }

    // --- Transient Getters for Foreign Keys ---

    @Transient
    @JsonProperty("price_list_id")
    public Integer getPriceListId() {
        return (this.priceList != null) ? this.priceList.getId() : null;
    }

    @Transient
    @JsonProperty("fraction_code")
    public String getFractionCode() {
        return (this.reportSetFraction != null) ? this.reportSetFraction.getCode() : null;
    }
}