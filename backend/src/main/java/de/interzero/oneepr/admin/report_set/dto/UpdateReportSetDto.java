package de.interzero.oneepr.admin.report_set.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.report_set_fractions.ReportSetFraction;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class UpdateReportSetDto {

    @Schema(
            description = "Name of the report set",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("name")
    private String name;

    @Schema(
            description = "Sheet file id of the report set",
            example = "123e4567-e89b-12d3-a456-426614174000",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("sheet_file_id")
    private String sheetFileId;

    @Schema(
            description = "Description of the sheet file",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("sheet_file_description")
    private String sheetFileDescription;

    @Schema(
            description = "Fractions of the report set",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("fractions")
    private List<ReportSetFraction> fractions;

    @Schema(
            description = "Columns of the report set",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("columns")
    private List<ReportSetColumnCreateDto> columns;

    @Schema(
            description = "Price lists of the report set",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("price_lists")
    private List<ReportSetPriceListDto> priceLists;

}