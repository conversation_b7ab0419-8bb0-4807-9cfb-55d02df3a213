package de.interzero.oneepr.admin.report_set_columns;

import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for managing {@link ReportSetColumn} entities.
 */
@Repository
public interface ReportSetColumnRepository extends JpaRepository<ReportSetColumn, Integer>, JpaSpecificationExecutor<ReportSetColumn> {

    /**
     * Finds all columns that have not been soft-deleted.
     *
     * @return A list of active ReportSetColumn entities.
     */
    List<ReportSetColumn> findAllByDeletedAtIsNull();

    /**
     * Finds a single column by its ID, but only if it has not been soft-deleted.
     *
     * @param id The ID of the column.
     * @return An Optional containing the column if found and active, otherwise empty.
     */
    Optional<ReportSetColumn> findByIdAndDeletedAtIsNull(Integer id);

    /**
     * Finds all direct children of a given parent column ID.
     *
     * @param parentId The ID of the parent column.
     * @return A list of child ReportSetColumn entities.
     */
    List<ReportSetColumn> findByParentId(Integer parentId);

    /**
     * <p>
     * Finds all {@link ReportSetColumn} entities matching the given criteria.
     *
     * @param spec the {@link Specification} to filter by; can be {@code null} to return all entities.
     * @param sort the {@link Sort} criteria to apply; must not be {@code null}.
     * @return a non-null list of matching {@link ReportSetColumn} entities; will be empty if none are found.
     */
    @Override
    @NonNull
    List<ReportSetColumn> findAll(@Nullable Specification<ReportSetColumn> spec,
                                  @NonNull Sort sort);

    /**
     * Finds a single ReportSetColumn by its unique code.
     *
     * @param code The unique code to search for.
     * @return An optional containing the found entity.
     **/
    Optional<ReportSetColumn> findByCode(String code);

    @Modifying
    @Query("DELETE FROM ReportSetColumn c WHERE c.reportSet.id = :reportSetId")
    void deleteByReportSetId(Integer reportSetId);
}