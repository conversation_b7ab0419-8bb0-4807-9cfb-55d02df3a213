package de.interzero.oneepr.admin.admin;

import de.interzero.oneepr.admin.admin.dto.AdminUserResponse;
import de.interzero.oneepr.auth.role.Role;
import de.interzero.oneepr.auth.user.User;

import java.util.List;
import java.util.stream.Collectors;

public final class AdminMapper {

    private AdminMapper() {
    }

    /**
     * Maps a {@link User} entity to an {@link AdminUserResponse} DTO.
     *
     * @param user the user entity
     * @return the corresponding admin response DTO
     */
    public static AdminUserResponse toDto(User user) {
        if (user == null) {
            return null;
        }

        AdminUserResponse dto = new AdminUserResponse();
        dto.setId(user.getId());
        dto.setEmail(user.getEmail());
        dto.setName(user.getName());
        //
        dto.setIsActive(user.getIsActive());
        dto.setCreatedAt(user.getCreatedAt());
        dto.setUpdatedAt(user.getUpdatedAt());

        Role role = user.getRole();
        if (role != null) {
            AdminUserResponse.RoleDto roleDto = new AdminUserResponse.RoleDto();
            roleDto.setId(role.getId());
            roleDto.setName(role.getName());
            roleDto.setDisplayName(role.getDisplayName());
            dto.setRole(roleDto);
        }

        return dto;
    }

    /**
     * Maps a list of {@link User} entities to a list of {@link AdminUserResponse} DTOs.
     *
     * @param users list of user entities
     * @return list of admin response DTOs
     */
    @SuppressWarnings("java:S6204")
    public static List<AdminUserResponse> toDtoList(List<User> users) {
        return users == null ? List.of() :
                users.stream()
                        .map(AdminMapper::toDto)
                        .collect(Collectors.toList());
    }
}
