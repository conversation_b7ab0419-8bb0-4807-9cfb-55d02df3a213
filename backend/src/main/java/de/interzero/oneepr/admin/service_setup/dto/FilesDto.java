package de.interzero.oneepr.admin.service_setup.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.Instant;

/**
 * A helper Data Transfer Object representing a Files entity.
 * <p>
 * This DTO is used within other response DTOs, such as RequiredInformationResponseDto,
 * to represent a nested 'file' object that was included via a Prisma 'include' statement.
 * Its purpose is to provide a structured representation of a file record.
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class FilesDto {

    @Schema(
            description = "The unique identifier of the file (UUID).",
            example = "a1b2c3d4-e5f6-7890-1234-567890abcdef"
    )
    @JsonProperty("id")
    private String id;

    @Schema(
            description = "The name of the file.",
            example = "document.pdf"
    )
    @JsonProperty("name")
    private String name;

    @Schema(
            description = "The file extension.",
            example = "pdf"
    )
    @JsonProperty("extension")
    private String extension;

    @Schema(
            description = "The size of the file.",
            example = "1024KB"
    )
    @JsonProperty("size")
    private String size;

    @Schema(
            description = "The type of entity that created the file.",
            example = "USER"
    )
    @JsonProperty("creator_type")
    private String creatorType;

    @Schema(
            description = "The type of document.",
            example = "INVOICE"
    )
    @JsonProperty("document_type")
    private String documentType;

    @Schema(
            description = "The ID of the user who created the file.",
            example = "123"
    )
    @JsonProperty("user_id")
    private String userId;

    @Schema(
            description = "The ID of the country associated with the file.",
            example = "49"
    )
    @JsonProperty("country_id")
    private Integer countryId;

    @Schema(
            description = "The original name of the file upon upload.",
            example = "original_document_name.pdf"
    )
    @JsonProperty("original_name")
    private String originalName;

    @Schema(
            description = "The timestamp of when the record was created."
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @Schema(
            description = "The timestamp of the last update."
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;
}