package de.interzero.oneepr.admin.service_setup.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.report_set.ReportSet;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Represents a detailed, fully populated view of a single {@link ReportSet} for the service setup interface.
 *
 * <p><b>Why this DTO exists:</b>
 * This DTO is designed to be the comprehensive data structure for displaying or editing a single report set.
 * It encapsulates not only the ReportSet's own data but also its complete hierarchy of related collections
 * (fractions, columns, price lists) and a calculated boolean flag indicating if its parent packaging service
 * has relevant commitment criteria.
 *
 * <p><b>What this DTO contains:</b>
 * It is a direct mapping of a {@code ReportSet} entity after it has been fully populated with its
 * related collections by the service layer, plus the {@code has_criteria} boolean flag.
 *
 * <p><b>Where this DTO is used:</b>
 * It is the return type for the {@code ServiceSetupService.findServiceSetupReportSet} method and is serialized
 * as the JSON response for the corresponding controller endpoint.
 *
 */
@Data
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ReportSetDetailDto {

    @JsonProperty("report_set")
    private ReportSet reportSet;

    @JsonProperty("has_criteria")
    private boolean hasCriteria;

    /**
     * Factory method to create a {@code ReportSetDetailDto}.
     *
     * @param reportSet The fully populated {@link ReportSet} entity.
     * @param hasCriteria A boolean indicating if the parent packaging service has the relevant criteria.
     * @return A new, populated {@code ReportSetDetailDto} instance.
     */
    public static ReportSetDetailDto fromEntity(ReportSet reportSet, boolean hasCriteria) {
        ReportSetDetailDto dto = new ReportSetDetailDto();
        dto.setReportSet(reportSet);
        dto.setHasCriteria(hasCriteria);
        return dto;
    }
}