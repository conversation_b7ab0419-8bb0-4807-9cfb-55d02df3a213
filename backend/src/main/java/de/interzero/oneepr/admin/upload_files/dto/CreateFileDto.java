package de.interzero.oneepr.admin.upload_files.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CreateFileDto {

    @Schema
    @JsonProperty("document_type")
    private DocumentType documentType;

    @Schema
    @JsonProperty("country_id")
    private Integer countryId;


    public enum DocumentType {
        INVOICE,
        PAYMENT,
        CONTRACT,
        GENERAL_DOCUMENT,
        COUNTRY_DOCUMENT,
        INVITE_CUSTOMER_MANAGER_TERMS;

    }

}



