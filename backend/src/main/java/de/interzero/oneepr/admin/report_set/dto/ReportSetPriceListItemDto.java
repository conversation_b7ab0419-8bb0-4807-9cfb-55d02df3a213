package de.interzero.oneepr.admin.report_set.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * This DTO is a translation of the fields required to create or update a ReportSetPriceListItem.
 * It is used as a nested object within the price list DTOs to specify the price for a given fraction.
 * It contains the price value and the unique code of the fraction to which the price applies.
 */
@Getter
@Setter
public class ReportSetPriceListItemDto extends BaseDto {

    @Schema(
            description = "The price for the fraction in this price list, in cents.",
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = "15000"
    )
    @JsonProperty("price")
    @NotNull
    private Integer price;

    @Schema(
            description = "The unique code of the fraction to which this price applies.",
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = "PAPER_CARDBOARD"
    )
    @JsonProperty("fraction_code")
    @NotNull
    private String fractionCode;
}