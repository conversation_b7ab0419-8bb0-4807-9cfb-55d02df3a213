package de.interzero.oneepr.admin.country.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * Data Transfer Object for partially updating a country.
 * All fields are optional for a PATCH operation.
 */
@Getter
@Setter
public class UpdateCountryDto extends CreateCountryDto {

    @Schema(description = "Whether an authorized representative is obligated for this country")
    @JsonProperty("authorize_representative_obligated")
    private Boolean authorizeRepresentativeObligated;

    @Schema(description = "Whether other costs are obligated for this country")
    @JsonProperty("other_costs_obligated")
    private Boolean otherCostsObligated;

    @Schema(
            description = "The published status of the country",
            example = "true"
    )
    @JsonProperty("is_published")
    private Boolean isPublished;
}