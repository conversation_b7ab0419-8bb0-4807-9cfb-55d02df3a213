package de.interzero.oneepr.admin.mail;

import jakarta.mail.internet.MimeMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Sandbox implementation of {@link EmailOutboxGateway}, used for local/dev testing.
 * It sends an email via SMTP with raw message data (as plain text).
 */
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(
        prefix = "mail",
        name = "gateway",
        havingValue = "sandbox"
)
@Slf4j
public class SandboxEmailGateway implements EmailOutboxGateway {

    private final JavaMailSender javaMailSender;

    @Override
    public void sendEmail(EmailMessage emailMessage) {
        try {
            MimeMessage message = javaMailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);

            helper.setTo(emailMessage.getTo());
            helper.setFrom(emailMessage.getFrom());
            helper.setSubject("SANDBOX: " + emailMessage.getSubject());

            String body = formatPayload(emailMessage);
            helper.setText(body, false);

            log.info(body);

            javaMailSender.send(message);

            log.info("Sandbox email sent to: {}", emailMessage.getTo());
        } catch (Exception e) {
            log.error("Failed to send sandbox email", e);
            throw new EmailDeliveryException("Failed to send sandbox email", e);
        }
    }

    private String formatPayload(EmailMessage emailMessage) {
        StringBuilder sb = new StringBuilder();
        sb.append("Transactional Message ID: ")
                .append(emailMessage.getTransactionalMessageId())
                .append("\n")
                .append("To: ")
                .append(emailMessage.getTo())
                .append("\n")
                .append("From: ")
                .append(emailMessage.getFrom())
                .append("\n")
                .append("Subject: ")
                .append(emailMessage.getSubject())
                .append("\n")
                .append("Message Data:\n");

        for (Map.Entry<String, Object> entry : emailMessage.getMessageData().entrySet()) {
            sb.append(" - ").append(entry.getKey()).append(": ").append(entry.getValue()).append("\n");
        }

        return sb.toString();
    }
}
