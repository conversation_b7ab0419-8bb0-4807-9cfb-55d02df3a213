package de.interzero.oneepr.admin.criteria;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface CriteriaRepository extends JpaRepository<Criteria, Integer> {

    List<Criteria> findAllByDeletedAtIsNull();

    Optional<Criteria> findByIdAndDeletedAtIsNull(Integer id);

    /**
     * Soft-deletes all Criteria entities associated with a specific packaging service ID.
     *
     * @param packagingServiceId The ID of the parent packaging service.
     * @param now                The current timestamp to set as the deleted_at value.
     */
    @Modifying
    @Query("UPDATE Criteria c SET c.deletedAt = :now WHERE c.packagingService.id = :packagingServiceId")
    void softDeleteByPackagingServiceId(@Param("packagingServiceId") Integer packagingServiceId,
                                        @Param("now") Instant now);

    List<Criteria> findByRequiredInformation_Id(Integer requiredInformationId);

    /**
     * Finds all active Criteria for a given Country ID that match a list of types.
     * This query performs the filtering at the database level.
     *
     * @param countryId The ID of the parent Country.
     * @param types     A collection of Criteria.Type enums to filter by.
     * @return A list of matching Criteria entities.
     */
    @Query(
            "SELECT c FROM Criteria c " + "WHERE c.country.id = :countryId " + "AND c.deletedAt IS NULL " + "AND c.type IN :types"
    )
    List<Criteria> findActiveByCountryAndTypeIn(@Param("countryId") Integer countryId,
                                                @Param("types") Collection<Criteria.Type> types);

}