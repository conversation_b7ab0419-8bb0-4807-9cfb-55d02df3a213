package de.interzero.oneepr.admin.admin.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Schema(description = "DTO for filtering admin users")
@Data
@EqualsAndHashCode(callSuper = true)
public class FindAllAdminDto extends BaseDto {

    @JsonProperty("role")
    @Schema(
            description = "Role of the admin",
            example = "ADMIN,CLERK",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String role;

    @JsonProperty("is_active")
    @Schema(
            description = "Whether the user is active",
            example = "true",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private String isActive;

    @JsonProperty("name")
    @Schema(
            description = "Name of the user",
            example = "John Doe",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private String name;
}
