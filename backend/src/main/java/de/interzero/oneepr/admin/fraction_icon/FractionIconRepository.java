package de.interzero.oneepr.admin.fraction_icon;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for managing {@link FractionIcon} entities.
 */
@Repository
public interface FractionIconRepository extends JpaRepository<FractionIcon, Integer> {

    /**
     * Finds all non-deleted fraction icons.
     *
     * @return A list of fraction icons.
     */
    List<FractionIcon> findAllByDeletedAtIsNull();

    /**
     * Finds a single non-deleted fraction icon by its ID.
     *
     * @param id The ID of the icon.
     * @return An optional containing the found icon.
     */
    @NonNull
    Optional<FractionIcon> findByIdAndDeletedAtIsNull(@NonNull Integer id);
}