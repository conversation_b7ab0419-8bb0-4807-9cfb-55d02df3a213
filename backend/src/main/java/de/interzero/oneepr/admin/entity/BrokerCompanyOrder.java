package de.interzero.oneepr.admin.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.time.Instant;

@Getter
@Setter
@Entity
@Table(
        name = "broker_company_order",
        schema = "public"
)
public class BrokerCompanyOrder {

    @Id
    @ColumnDefault("nextval('broker_company_order_id_seq')")
    @Column(
            name = "id",
            nullable = false
    )
    private Integer id;

    @NotNull
    @Column(
            name = "customer_number",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String customerNumber;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "company_id",
            nullable = false
    )
    private BrokerCompany company;

    @NotNull
    @Column(
            name = "transfer_date",
            nullable = false
    )
    private Instant transferDate;

    @NotNull
    @Column(
            name = "order_number",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String orderNumber;

    @NotNull
    @Column(
            name = "year",
            nullable = false
    )
    private Integer year;

    /* TODO figure out if this will really be a list and use the appropriate type
    @Column(name = "fractions")
    private List<Map<String, Object>> fractions;
    */
}