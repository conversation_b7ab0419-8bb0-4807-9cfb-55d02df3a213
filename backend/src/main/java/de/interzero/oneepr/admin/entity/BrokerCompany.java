package de.interzero.oneepr.admin.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.time.Instant;

@Getter
@Setter
@Entity
@Table(
        name = "broker_company",
        schema = "public"
)
public class BrokerCompany {

    @Id
    @ColumnDefault("nextval('broker_company_id_seq')")
    @Column(
            name = "id",
            nullable = false
    )
    private Integer id;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "broker_id",
            nullable = false
    )
    private Broker broker;

    @NotNull
    @Column(
            name = "name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String name;

    @NotNull
    @Column(
            name = "register_number",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String registerNumber;

    @Column(
            name = "vat",
            length = Integer.MAX_VALUE
    )
    private String vat;

    @Column(
            name = "tax",
            length = Integer.MAX_VALUE
    )
    private String tax;

    @Column(
            name = "country_code",
            length = Integer.MAX_VALUE
    )
    private String countryCode;

    @Column(
            name = "address_number",
            length = Integer.MAX_VALUE
    )
    private String addressNumber;

    @NotNull
    @Column(
            name = "address_street",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String addressStreet;

    @NotNull
    @Column(
            name = "city",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String city;

    @NotNull
    @Column(
            name = "contact_name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String contactName;

    @NotNull
    @Column(
            name = "contact_email",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String contactEmail;

    @NotNull
    @Column(
            name = "phone_number",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String phoneNumber;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "file_id")
    private de.interzero.oneepr.admin.entity.UploadFileHistory file;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    private Instant createdAt;

    @Column(name = "updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    private Instant deletedAt;

}