package de.interzero.oneepr.admin.service_setup.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

/**
 * Data Transfer Object representing the country details within a commitment submission result.
 * <p>
 * This DTO originates from the {@link de.interzero.oneepr.admin.country.Country} entity. It is
 * needed to provide a simplified, flattened view of the country's information tailored
 * for the {@code CommitmentSubmitResultDto} response. By using a dedicated DTO, we can
 * avoid exposing the entire complex {@code Country} entity with all its relationships,
 * which improves API clarity, performance, and security. The {@code @Builder} annotation
 * facilitates its programmatic construction within the service layer.
 */
@Data
@Builder
public class CommitmentResultCountryDto {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("name")
    private String name;

    @JsonProperty("code")
    private String code;

    @JsonProperty("flag_url")
    private String flagUrl;

    @JsonProperty("authorize_representative_obligated")
    private boolean authorizeRepresentativeObligated;

    @JsonProperty("other_costs_obligated")
    private boolean otherCostsObligated;
}