package de.interzero.oneepr.admin.service_setup.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.packaging_service.PackagingService;
import de.interzero.oneepr.admin.report_set.ReportSet;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * Represents a specialized view of a {@link ReportSet} for the service setup administration interface.
 *
 * <p><b>Why this DTO exists:</b>
 * This DTO is designed to provide a specific data structure for the frontend when an administrator is configuring
 * report sets within a country's service setup. Its primary purpose is to flatten and denormalize information
 * from the parent {@link PackagingService}, specifically whether that service has a "REPORT_SET" commitment criteria.
 * This pre-calculation simplifies client-side logic by providing a direct boolean flag.
 *
 * <p><b>What this DTO contains:</b>
 * It includes all essential fields from the {@code ReportSet} entity, the full parent {@code PackagingService} object,
 * and a calculated boolean field {@code has_criteria}. This flag is {@code true} if the associated packaging service
 * has at least one commitment criteria of type 'REPORT_SET'.
 *
 * <p><b>Where this DTO is used:</b>
 * This DTO is the return type for the {@code ServiceSetupService.findServiceSetupReportSets} method and is serialized
 * as the JSON response for the corresponding endpoint in the {@code ServiceSetupController}.
 *
 * @see de.interzero.oneepr.admin.service_setup.ServiceSetupService#findServiceSetupReportSets(String)
 * @see de.interzero.oneepr.admin.service_setup.ServiceSetupController#findServiceSetupReportSets(String)
 */
@Data
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ReportSetSetupDto {

    private Integer id;

    private String name;

    private ReportSet.ReportSetMode mode;

    private ReportSet.ReportSetType type;

    private String sheetFileDescription;

    private Instant createdAt;

    private Instant updatedAt;

    @JsonProperty("packaging_service")
    private PackagingService packagingService;

    @JsonProperty("has_criteria")
    private boolean hasCriteria;

    /**
     * Factory method to create and populate a {@code ReportSetSetupDto} from a source entity and a calculated flag.
     * <p>
     * This method ensures that every DTO instance is created in a consistent and valid state.
     *
     * @param reportSet   The source {@link ReportSet} entity from which to map the primary fields.
     * @param hasCriteria A pre-calculated boolean indicating if the report set's parent packaging service has the relevant criteria.
     * @return A new, fully populated {@code ReportSetSetupDto} instance.
     */
    public static ReportSetSetupDto fromEntity(ReportSet reportSet,
                                               boolean hasCriteria) {
        ReportSetSetupDto dto = new ReportSetSetupDto();
        dto.setId(reportSet.getId());
        dto.setName(reportSet.getName());
        dto.setMode(reportSet.getMode());
        dto.setType(reportSet.getType());
        dto.setSheetFileDescription(reportSet.getSheetFileDescription());
        dto.setCreatedAt(reportSet.getCreatedAt());
        dto.setUpdatedAt(reportSet.getUpdatedAt());
        dto.setPackagingService(reportSet.getPackagingService());
        dto.setHasCriteria(hasCriteria);
        return dto;
    }
}
