package de.interzero.oneepr.common.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Set;

/**
 * Configuration class for ModelMapper to customize the mapping behavior.
 * This configuration allows mapping only the fields that are present in the BaseDto, if applicable.
 */
@Configuration
public class ModelMapperConfig {

    @Bean
    public ModelMapper modelMapper() {
        ModelMapper mapper = new ModelMapper();

        // Basic configuration
        mapper.getConfiguration()
                .setMatchingStrategy(MatchingStrategies.STRICT)
                .setFieldMatchingEnabled(true)
                .setFieldAccessLevel(org.modelmapper.config.Configuration.AccessLevel.PRIVATE);

        return mapper;
    }

    /**
     * Maps only the fields that are present in the presentFields set of the BaseDto.
     * If the source doesn't extend BaseDto, maps all fields.
     *
     * @param source      The source object
     * @param destination The destination object
     * @param <T>         The type of the destination object
     * @return The mapped destination object
     */
    public static <T> T mapPresentFields(Object source,
                                         T destination) {
        // Get the presentFields set from the source if it's a BaseDto, otherwise use an empty set
        Set<String> presentFields = source instanceof BaseDto ? ((BaseDto) source).getPresentFields() : Collections.emptySet();

        // If not a BaseDto, return the regular mapping
        if (!(source instanceof BaseDto)) {
            ModelMapper mapper = new ModelMapper();
            mapper.getConfiguration()
                    .setMatchingStrategy(MatchingStrategies.STRICT)
                    .setFieldMatchingEnabled(true)
                    .setFieldAccessLevel(org.modelmapper.config.Configuration.AccessLevel.PRIVATE)
                    .setSkipNullEnabled(true);
            mapper.map(source, destination);
            return destination;
        }

        // Otherwise, map only the present fields
        for (String fieldName : presentFields) {
            try {
                Field sourceField = getFieldByJsonProperty(source.getClass(), fieldName);
                if (sourceField == null) {
                    continue;
                }

                sourceField.setAccessible(true);
                Object value = sourceField.get(source);

                String destinationFieldName = sourceField.getName();
                Field destinationField = findField(destination.getClass(), destinationFieldName);

                if (destinationField != null) {
                    destinationField.setAccessible(true);
                    Object convertedValue = convertValue(value, destinationField.getType());
                    destinationField.set(destination, convertedValue);
                }
            } catch (Exception e) {
                System.err.println("Could not map field " + fieldName + ": " + e.getMessage());
            }
        }

        return destination;
    }

    /**
     * Maps only the fields that are present in the presentFields set of the BaseDto.
     * If the source doesn't extend BaseDto, maps all fields.
     * This overloaded version creates a new instance of the destination class.
     *
     * @param source           The source object
     * @param destinationClass The class of the destination object
     * @param <T>              The type of the destination object
     * @return A new instance of the destination object with mapped fields
     */
    public static <T> T mapPresentFields(Object source,
                                         Class<T> destinationClass) {
        try {
            T destination = destinationClass.getDeclaredConstructor().newInstance();
            return mapPresentFields(source, destination);
        } catch (Exception e) {
            throw new RuntimeException("Could not create instance of " + destinationClass.getName(), e);
        }
    }

    /**
     * Finds a field in the class hierarchy by its JSON property name.
     *
     * @param clazz            the class to search in
     * @param jsonPropertyName the JSON property name to search for
     * @return the field if found, otherwise null
     */
    private static Field getFieldByJsonProperty(Class<?> clazz,
                                                String jsonPropertyName) {
        while (clazz != null && clazz != Object.class) {
            for (Field field : clazz.getDeclaredFields()) {
                JsonProperty jsonProp = field.getAnnotation(JsonProperty.class);
                if (jsonProp != null && jsonProp.value().equals(jsonPropertyName)) {
                    return field;
                }
                if (field.getName().equals(jsonPropertyName)) {
                    return field;
                }
            }
            clazz = clazz.getSuperclass();
        }
        return null;
    }

    /**
     * Finds a field in the class hierarchy by its name.
     *
     * @param clazz     the class to search in
     * @param fieldName the name of the field to search for
     * @return the field if found, otherwise null
     */
    private static Field findField(Class<?> clazz,
                                   String fieldName) {
        while (clazz != null && clazz != Object.class) {
            try {
                return clazz.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                clazz = clazz.getSuperclass();
            }
        }
        return null;
    }

    /**
     * Converts the value to the target type if necessary.
     *
     * @param value      the value to convert
     * @param targetType the target type to convert to
     * @return the converted value, or the original value if no conversion is needed
     */
    private static Object convertValue(Object value,
                                       Class<?> targetType) {
        if (value == null || targetType.isAssignableFrom(value.getClass())) {
            return value;
        }

        if (targetType == String.class) {
            return value.toString();
        } else if (targetType == Integer.class || targetType == int.class) {
            if (value instanceof Number) {
                return ((Number) value).intValue();
            } else if (value instanceof String) {
                return Integer.parseInt((String) value);
            }
        } else if (targetType == Long.class || targetType == long.class) {
            if (value instanceof Number) {
                return ((Number) value).longValue();
            } else if (value instanceof String) {
                return Long.parseLong((String) value);
            }
        }

        return value;
    }
}