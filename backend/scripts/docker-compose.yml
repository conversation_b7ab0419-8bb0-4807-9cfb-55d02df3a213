services:
  epr-db:
    image: postgres:17.3
    restart: always
    environment:
      PGUSER: epr-local-user
      POSTGRES_USER: epr-local-user
      POSTGRES_PASSWORD: epr-local-password
    healthcheck:
      test: ["CMD-SHELL", "pg_isready"]
      interval: 1s
      timeout: 5s
      retries: 10
    ports:
      - 5432:5432

  oneepr-mailpit:
    image: axllent/mailpit:v1.22
    restart: always
    ports:
      - 1025:1025
      - 8025:8025

volumes:
  pgdata: