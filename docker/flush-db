#!/usr/bin/env bash
source ./common

docker-compose down -v oneepr-db

check_error "=> There was an error flushing the database. Please investigate. Something is off.\n"

docker-compose up -d oneepr-db

check_error "=> There was an error starting the database. Please investigate. Something is off.\n"

print_success "\n=> Postgres DB should be running and be empty at http://localhost:5432\n\n"

exit 0
