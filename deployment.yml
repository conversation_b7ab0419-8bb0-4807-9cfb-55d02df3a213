---
apiVersion: v1
kind: Namespace
metadata:
  name: <NAMESPACE>
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: <PROJECT_NAME>-<COMPONENT>
  namespace: <NAMESPACE>
  labels:
    loadbalancer: <PROJECT_NAME>-<COMPONENT>-<BRANCH>
spec:
  replicas: 1
  selector:
    matchLabels:
      loadbalancer: <PROJECT_NAME>-<COMPONENT>-<BRANCH>
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 33%
  template:
    metadata:
      labels:
        loadbalancer: <PROJECT_NAME>-<COMPONENT>-<BRANCH>
        app: <PROJECT_NAME>-<COMPONENT>
        ignore-gnp: "y"
    spec:
      containers:
        - envFrom:
          name: <PROJECT_NAME>-<COMPONENT>
          image: nexus.interzero.de:<NEXUS_PORT>/<PROJECT_NAME>-<COMPONENT>:<BRANCH>
          imagePullPolicy: Always
          resources:
            requests:
              memory: 60Mi
              cpu: 20m
            limits:
              memory: 120Mi
              cpu: 100m
---
apiVersion: v1
kind: Service
metadata:
  name: <PROJECT_NAME>-<COMPONENT>
  namespace: <NAMESPACE>
spec:
  ports:
    - protocol: TCP
      port: 3000
      targetPort: 3000
  selector:
    loadbalancer: <PROJECT_NAME>-<COMPONENT>-<BRANCH>
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: <PROJECT_NAME>-<COMPONENT>
  namespace: <NAMESPACE>
  annotations:
    alb.ingress.kubernetes.io/scheme: <ALB_SCHEME>
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/group.name: <ALB_GROUP>
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
spec:
  ingressClassName: alb
  rules:
    - host: <HOSTNAME>
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: <PROJECT_NAME>-<COMPONENT>
                port:
                  number: 3000
